import boto3
import configparser
import os
import time

config_parser = configparser.ConfigParser()
with open(os.path.dirname(os.path.abspath(__file__)) + "/web/config.py") as stream:
    config_parser.read_string("[cfg]\n" + stream.read())
s3_client = boto3.client("s3",
    aws_access_key_id=config_parser["cfg"]["SES_ACCESS_KEY"].replace("'", ""),
    aws_secret_access_key=config_parser["cfg"]["SES_SECRET"].replace("'", ""),
    region_name="us-east-2")

for log_file in sorted(os.listdir("/var/log/uwsgi")):
    if "uwsgi" not in log_file:
        continue
    if log_file == "uwsgi.log":
        continue
    
    file_time = int(log_file.split(".")[2])
    if time.time() - file_time < 86400:
        continue
    
    response = s3_client.upload_file("/var/log/uwsgi/" + log_file, "vandra-server-logs", log_file)
    os.remove("/var/log/uwsgi/" + log_file)

for log_file in sorted(os.listdir("/var/log/nginx")):
    if ".gz" not in log_file:
        continue
    
    response = s3_client.upload_file("/var/log/nginx/" + log_file, "vandra-server-logs", log_file)
    os.remove("/var/log/nginx/" + log_file)