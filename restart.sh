# Try running app.py to make sure there are no compile issues
rm nohup.out
nohup python3 web/app.py &
sleep 5
sudo kill $(pgrep -f 'python3')

# Restart the server if app.py compiled
if grep -qF "* Running on" nohup.out;then
    rm nohup.out
    #nohup python3 web/test_routes.py &
    #sleep 5
    #sudo kill $(pgrep -f 'python3')
    sudo touch web/uwsgi.ini
else
    echo "Does not compile. Not restarting"
fi
