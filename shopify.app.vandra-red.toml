# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "a7f74e11bea00ca6d36b351e76d61a6a"
name = "Vandra Red"
handle = "vandra-red"
application_url = "https://vandra-merchant-red-36fd48355dee.herokuapp.com/dashboard"
embedded = true

[build]
include_config_on_deploy = true

[access_scopes]
scopes="read_products,write_products,read_orders,write_orders,read_price_rules,write_price_rules,read_discounts,write_discounts,read_themes,read_checkouts,write_checkouts"

[auth]
redirect_urls = []

[webhooks]
api_version = "2022-07"

  [[webhooks.subscriptions]]
  uri = "https://staging-red.vandra.ai/shop_delete_request"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://staging-red.vandra.ai/user_data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://staging-red.vandra.ai/user_delete_request"
  compliance_topics = [ "customers/redact" ]