{"name": "merchant-dashboard", "version": "0.1.0", "private": true, "engines": {"node": ">=20.15.1"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.14.16", "@mui/x-date-pickers": "^6.17.0", "@shopify/app-bridge-react": "^4.1.6", "@shopify/app-bridge-utils": "^3.5.1", "@shopify/polaris": "^10.50.1", "@shopify/polaris-icons": "^9.3.0", "@tailwindcss/cli": "^4.0.9", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "chart.js": "^4.3.0", "dayjs": "^1.11.10", "lucide-react": "^0.509.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-device-frameset": "^1.3.4", "react-dom": "^18.2.0", "react-responsive-carousel": "^3.2.23", "react-router": "^6.6.1", "react-router-dom": "^6.6.1", "react-scripts": "5.0.1", "react-toastify": "^9.1.1", "serve": "^14.2.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "serve": "serve -s build", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint \"src/**/*.{js,jsx,ts,tsx}\" --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": ".", "devDependencies": {"@chromatic-com/storybook": "^3.2.5", "@rushstack/eslint-patch": "^1.3.3", "@storybook/addon-essentials": "^8.6.4", "@storybook/addon-interactions": "^8.6.4", "@storybook/blocks": "^8.6.4", "@storybook/preset-create-react-app": "^8.6.4", "@storybook/react": "^8.6.4", "@storybook/react-webpack5": "^8.6.4", "@storybook/test": "^8.6.4", "@tailwindcss/postcss": "^4.0.9", "autoprefixer": "^10.4.20", "eslint": "^8.56.0", "eslint-config-prettier": "10.1.1", "eslint-config-react-app": "^7.0.1", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "^7.33.2", "eslint-plugin-storybook": "^0.11.4", "postcss": "^8.5.3", "prettier": "3.5.3", "prettier-eslint": "^16.3.0", "prop-types": "^15.8.1", "storybook": "^8.6.4", "tailwindcss": "^3.4.17", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}}