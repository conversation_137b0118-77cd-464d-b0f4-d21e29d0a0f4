{"name": "<PERSON><PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"shopify": "shopify", "build": "shopify app build", "dev": "shopify app dev", "info": "shopify app info", "generate": "shopify app generate", "deploy": "shopify app deploy", "build:components": "npm run build --prefix ./vandra-components"}, "dependencies": {"@shopify/app": "^3.49.0", "@shopify/app-bridge": "^3.5.0", "@shopify/app-bridge-react": "^3.5.0", "@shopify/app-bridge-utils": "^3.5.0", "@shopify/cli": "^3.64.1", "@shopify/ui-extensions": "2023.10.0", "@shopify/ui-extensions-react": "2023.10.0", "graphql-request": "^6.1.0", "react": "^18.2.0"}}