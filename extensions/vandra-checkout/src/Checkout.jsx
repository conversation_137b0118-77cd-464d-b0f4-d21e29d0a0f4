import { useState, useEffect } from "react";
import {
  Banner,
  useApi,
  useTranslate,
  reactExtension,
} from "@shopify/ui-extensions-react/checkout";

export default reactExtension("purchase.checkout.block.render", () => (
  <Extension />
));

const DEFAULT_SHOW_BANNER = false;
const DEFAULT_DISCOUNT_PERCENT = undefined;
const DEFAULT_IS_BANNER_LOADING = false;

function Extension() {
  const [showBanner, setShowBanner] = useState(DEFAULT_SHOW_BANNER);
  const [discountPercentage, setDiscountPercentage] = useState(
    DEFAULT_DISCOUNT_PERCENT
  );
  const [isBannerLoading, setIsBannerLoading] = useState(
    DEFAULT_IS_BANNER_LOADING
  );
  const translate = useTranslate();
  const { shop, checkoutToken, cost, discountCodes, note, query } = useApi();

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    setIsBannerLoading(true);
    await getCheckoutStatus();
    setIsBannerLoading(DEFAULT_IS_BANNER_LOADING);
  };

  const getCheckoutStatus = async () => {
    try {
      let vandraSessionID = undefined;
      const noteText = note?.current;
      if (noteText && noteText.includes("__vandra_session")) {
        vandraSessionID = noteText.split("__vandra_session:")[1];
      }
      if (vandraSessionID) {
        await fetch(
          `https://app.vandra.ai/get_session?` +
            new URLSearchParams({
              shop_url: shop.myshopifyDomain,
              vandra_session_id: vandraSessionID,
            }),
          {
            method: "GET",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
      }
      const discountCodeCodes = discountCodes.current.map((discountCode) => {
        return discountCode["code"];
      });
      const vandraDiscountCodes = await fetch(
        `https://app.vandra.ai/get_discount_codes?` +
          new URLSearchParams({
            shop_url: shop.myshopifyDomain,
          }),
        {
          method: "GET",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      const vandraDiscountCodesText = await vandraDiscountCodes.text();
      const vandraDiscountCodesJSON = JSON.parse(vandraDiscountCodesText);
      const vandraDiscountCodesCodes =
        vandraDiscountCodesJSON["vandra_discount_codes"];
      const vandraDiscountPercentage =
        vandraDiscountCodesJSON["vandra_discount_percentage"];
      const discountCodeIntersection = discountCodeCodes.filter(
        (discountCode) => vandraDiscountCodesCodes.includes(discountCode)
      );
      const match = discountCodeIntersection.length > 0;
      setShowBanner(match);
      setDiscountPercentage(vandraDiscountPercentage);
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <>
      {showBanner ? (
        <Banner>
          DON'T MISS OUT: {discountPercentage}% OFF ONLY GOOD FOR ONE DAY
        </Banner>
      ) : (
        <></>
      )}
    </>
  );
}
