# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "33885cb2db2bf3c59f4845abb018c23a"
name = "Vandra Blue"
handle = "vandra-blue"
application_url = "https://vandra-blue-dev.ngrok.app"
embedded = true

[build]
dev_store_url = "vandra-blue.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_checkouts,read_discounts,read_orders,read_price_rules,read_products,read_themes,write_checkouts,write_discounts,write_orders,write_price_rules,write_products"

[auth]
redirect_urls = [ "https://vandra-blue-dev.ngrok.app/api/auth/callback" ]

[webhooks]
api_version = "2022-07"

  [[webhooks.subscriptions]]
  uri = "https://staging-blue.vandra.ai/shop_delete_request"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://staging-blue.vandra.ai/user_data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://staging-blue.vandra.ai/user_delete_request"
  compliance_topics = [ "customers/redact" ]
