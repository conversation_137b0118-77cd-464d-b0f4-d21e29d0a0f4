{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "prettier.requireConfig": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnPaste": false, "prettier.useEditorConfig": false, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}