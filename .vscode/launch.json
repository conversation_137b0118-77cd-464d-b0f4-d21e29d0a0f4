{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Merchant Admin App",
            "type": "node-terminal",
            "request": "launch",
            "command": "BROWSER=none npm start",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "ngrok",
            "type": "node-terminal",
            "request": "launch",
            "command": "sleep 30 && ngrok http --domain=vandra-blue-dev.ngrok.app 3000",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "StoryBook Merchant Admin App",
            "type": "node-terminal",
            "request": "launch",
            "command": "npm run storybook",
            "cwd": "${workspaceFolder}"
        },
    ],
    "compounds": [
        {
            "name": "Run All",
            "configurations": [
                "Merchant Admin App",
                "ngrok",
                "StoryBook Merchant Admin App"
            ],
            "stopAll": true
        }
    ]
}