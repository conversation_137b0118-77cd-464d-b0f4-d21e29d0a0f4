{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Backend",
            "type": "node-terminal",
            "request": "launch",
            "command": "source .venv/bin/activate && cd web && doppler run -- python -m debugpy --listen 5678 -m flask --app web/app.py --debug run --port=8080",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "Attach Backend",
            "type": "python",
            "request": "attach",
            "connect": {
                "host": "localhost",
                "port": 5678
            }
        },
        {
            "name": "Model",
            "type": "node-terminal",
            "request": "launch",
            "command": "source .venv/bin/activate && REDIS_URL=redis://localhost:6379 doppler run --preserve-env -- python web/model_utilities/run_prediction_utility.py --action run-worker --queue-name live",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "Web",
            "type": "node-terminal",
            "request": "launch",
            "command": "doppler run -- npm run dev -- --config vandra-blue",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "Components",
            "type": "node-terminal",
            "request": "launch",
            "command": "cd vandra-components && npm run dev",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "Storybook",
            "type": "node-terminal",
            "request": "launch",
            "command": "cd vandra-components && npm run storybook",
            "cwd": "${workspaceFolder}"
        }
    ],
    "compounds": [
        {
            "name": "Run All",
            "configurations": [
                "Backend",
                "Model",
                "Web",
                "Components",
                "Storybook"
            ]
        }
    ]
}