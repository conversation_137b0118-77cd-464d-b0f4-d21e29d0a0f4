{"autorun": false, "terminals": [{"name": "Backend", "commands": ["source .venv/bin/activate && cd web && doppler run -- python -m debugpy --listen 5678 -m flask --app web/app.py --debug run --port=8080"]}, {"name": "Model", "commands": ["source .venv/bin/activate && doppler run -- python web/model_utilities/run_prediction_utility.py --action run-worker --queue-name live"]}, {"name": "Web", "commands": ["doppler run -- npm run dev -- --reset"]}, {"name": "Components", "commands": ["cd vandra-components && npm run dev"]}, {"name": "Storybook", "commands": ["cd vandra-components && npm run storybook"]}]}