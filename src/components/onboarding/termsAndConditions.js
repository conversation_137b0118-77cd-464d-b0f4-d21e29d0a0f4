import React from 'react';
import { useEffect, useState, useCallback } from 'react';
import { useAppBridge } from '@shopify/app-bridge-react';
import { makeRequest } from '../../utils/makeRequest';
import { toast } from 'react-toastify';
import Loader from '../UI/Loader';
import PropTypes from 'prop-types';

export default function TermsAndConditions(props) {
  const shopify = useAppBridge();
  const [isLoading, setIsLoading] = useState(false);
  const [checked, setChecked] = useState(false);
  const { onBoardingDetails, setCurrentStep } = props;

  const startTerms = useCallback(async () => {
    try {
      if (!onBoardingDetails.terms_start) {
        const form = new FormData();
        form.append('field', 'terms_start');
        await makeRequest({
          path: `/react/merchant_update_onboarding?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: form,
        });
      }
    } catch (e) {
      console.log(e);
    }
  }, [onBoardingDetails.terms_start, shopify]);

  const updateTerms = async () => {
    if (!checked) return;
    setIsLoading(true);
    const form = new FormData();
    form.append('field', 'terms_signed');
    const data = await makeRequest({
      path: `/react/merchant_update_onboarding?id_token=${await shopify.idToken()}`,
      method: 'POST',
      body: form,
    });
    if (data.okay) {
      setCurrentStep(2);
    } else {
      toast.error('Please try again.');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    startTerms();
  }, [startTerms]);

  return (
    <>
      <div className="flex w-full pt-[8%] pr-[15%] justify-end">
        <div className="flex flex-col leading-[34px]">
          <div className="text-[24px] font-[600] mb-[15px]">Just some Ts and Cs.</div>
          <div className="text-[16px] mb-[15px]">
            Please accept our{' '}
            <a
              className="text-[#856CF8] no-underline"
              href="https://www.vandra.ai/terms-of-service"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms & Conditions
            </a>
            .
          </div>
          <div className="flex items-center">
            <input
              className="cursor-pointer h-[18px] w-[18px] mr-[7px]"
              type="checkbox"
              value={checked}
              onChange={e => {
                setChecked(prev => !prev);
              }}
            />
            <span className="text-[16px]">I accept</span>
          </div>
          <button
            disabled={isLoading || !checked}
            onClick={updateTerms}
            className={`text-[20px] cursor-pointer font-[600] mt-[24px] border-none ${isLoading || !checked ? 'text-black' : 'text-white'} w-[385px] px-[60px] ${isLoading || !checked ? 'bg-[#F9FAFB]' : 'bg-[#856CF8]'} py-[20px] rounded-[15px]`}
          >
            {isLoading ? <Loader height="18px" /> : 'Next'}
          </button>
        </div>
      </div>
      <img
        className="w-[38%] absolute left-0 bottom-0"
        src="/img/onboard_t&c.png"
        alt="Terms and Conditions"
      />
    </>
  );
}

TermsAndConditions.propTypes = {
  onBoardingDetails: PropTypes.shape({
    terms_start: PropTypes.bool,
    terms_signed: PropTypes.bool,
  }).isRequired,
  setCurrentStep: PropTypes.func.isRequired,
};
