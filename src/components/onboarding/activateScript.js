import React, { useEffect, useState, useCallback } from 'react';
import { makeRequest } from '../../utils/makeRequest';
import { useAppBridge } from '@shopify/app-bridge-react';
import Loader from '../UI/Loader';
import PropTypes from 'prop-types';

const SHOPIFY_DOMAIN = new URL(window.location.href).searchParams.get('shop');
const DEEP_LINK =
  'https://' +
  SHOPIFY_DOMAIN +
  '/admin/themes/current/editor?context=apps&activateAppId=804c0672-ed41-4841-a61e-32f7241766ed/js_embed';

export default function ActivateScript(props) {
  const shopify = useAppBridge();
  const { setCurrentStep, onBoardingDetails } = props;
  const [isScriptActivated, setIsScriptActivated] = useState(false);
  const [scriptCheckLoading, setScriptCheckLoading] = useState(false);
  const [clickedActivate, setClickedActivate] = useState(false);
  const [wasManuallyChecked, setWasManuallyChecked] = useState(false);

  const startScript = useCallback(async () => {
    try {
      if (!onBoardingDetails.script_start) {
        const form = new FormData();
        form.append('field', 'script_start');
        await makeRequest({
          path: `/react/merchant_update_onboarding?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: form,
        });
      }
    } catch (e) {
      console.log(e);
    }
  }, [onBoardingDetails.script_start, shopify]);

  const handleVerifyScriptActivationClick = useCallback(
    async clicked => {
      try {
        setScriptCheckLoading(true);
        const checkScriptActive = await makeRequest({
          path: `/react/merchant_onboarding_script?id_token=${await shopify.idToken()}`,
          method: 'GET',
        });
        if (checkScriptActive.okay) {
          const form = new FormData();
          form.append('field', 'script_activated');
          await makeRequest({
            path: `/react/merchant_update_onboarding?id_token=${await shopify.idToken()}`,
            method: 'POST',
            body: form,
          });
          setIsScriptActivated(true);
        } else {
          if (clicked) {
            setWasManuallyChecked(true);
            setClickedActivate(false);
          }
        }
      } catch (e) {
        console.log(e);
      } finally {
        setScriptCheckLoading(false);
      }
    },
    [
      shopify,
      setWasManuallyChecked,
      setClickedActivate,
      setIsScriptActivated,
      setScriptCheckLoading,
    ]
  );

  useEffect(() => {
    document.addEventListener('visibilitychange', handleVerifyScriptActivationClick);
    return () => {
      document.removeEventListener('visibilitychange', handleVerifyScriptActivationClick);
    };
  }, [handleVerifyScriptActivationClick]);

  useEffect(() => {
    startScript();
    handleVerifyScriptActivationClick();
  }, [startScript, handleVerifyScriptActivationClick]);

  return (
    <div className="flex items-start px-[5%] h-full pt-[8%] gap-[7%]">
      <div className="w-[56%]">
        <video controls className="w-full" src="/img/video/enable_script_video.mp4" />
      </div>
      {scriptCheckLoading ? (
        <div className="flex flex-col items-center w-[27%]">
          <div className="mb-[40px] text-[24px] font-[600]">Checking script...</div>
          <Loader style={{ height: 40, width: 40 }} />
        </div>
      ) : !isScriptActivated ? (
        <div className="flex flex-col items-start justify-start h-full w-[27%]">
          <div className="text-[24px] font-[600] mb-[34px]">Activate our script</div>
          <div className="text-[16px] mb-[25px]">
            Activating our script allows us to start collecting data and intelligently surface
            personalized nudges for customers on your site.
          </div>
          <div className="text-[16px] font-[700] mb-[40px]">
            Watch this 30 second video to learn more about how to activate our script for your
            theme.
          </div>
          {wasManuallyChecked && (
            <div className="text-[#856CF8] mb-[40px]">
              <div>
                We just checked and our script has not been activated yet. Please try again.
              </div>
              <div onClick={handleVerifyScriptActivationClick} className="underline cursor-pointer">
                Re-run Script Check
              </div>
            </div>
          )}
          <button
            onClick={() => {
              if (!clickedActivate) {
                if (!wasManuallyChecked) {
                  setClickedActivate(true);
                }
                window.open(DEEP_LINK, '_blank');
              } else {
                handleVerifyScriptActivationClick(true);
              }
            }}
            className="flex justify-center items-center text-[20px] cursor-pointer font-[600] mt-[24px] border-none text-white w-[385px] px-[60px] bg-[#856CF8] py-[20px] rounded-[15px]"
          >
            <span>{clickedActivate ? 'Check Script' : ' Activate Script'}</span>
            {!clickedActivate && (
              <img
                className="w-[16px] h-[16px] ml-[7px]"
                src="/img/icons/link.png"
                alt="External link"
              />
            )}
          </button>
        </div>
      ) : (
        <div className="flex flex-col justify-center items-center">
          <div className="mb-[40px] w-[40px] h-[40px] bg-#856CF8 rounded-full flex justify-center items-center">
            <img className="h-[15px]" src="/img/icons/checkMark.png" alt="Check mark" />
          </div>
          <div className="text-[24px] font-[600] mb-[40px]">Script Enabled</div>
          <button
            onClick={() => {
              setCurrentStep(3);
            }}
            className="text-[20px] cursor-pointer font-[600] border-none text-white w-[385px] px-[60px] bg-[#856CF8] py-[20px] rounded-[15px]"
          >
            <span>Next</span>
          </button>
        </div>
      )}
    </div>
  );
}

ActivateScript.propTypes = {
  setCurrentStep: PropTypes.func.isRequired,
  onBoardingDetails: PropTypes.shape({
    script_start: PropTypes.bool,
    script_activated: PropTypes.bool,
  }).isRequired,
};
