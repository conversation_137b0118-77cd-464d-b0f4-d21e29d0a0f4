import React from 'react';
import PropTypes from 'prop-types';
import { Carousel } from 'react-responsive-carousel';

const imgInlineStyles = {
  width: '90px',
  transform: 'translateY(-50%)',
  pointerEvents: 'all',
};

export default function FeatureCarousel(props) {
  return (
    <div className="h-full relative flex flex-col items-center content-center justify-between p-[50px]">
      <img
        className="w-[11%] min-w-[100px] pb-[5%]"
        src="/img/vandra_purple_logo.png"
        alt="Vandra logo"
      />
      <Carousel
        renderArrowNext={(clickHandler, hasNext) =>
          hasNext && (
            <img
              onClick={clickHandler}
              className="absolute cursor-pointer z-[1] top-[50%] right-0"
              style={imgInlineStyles}
              src="/img/right_arrow.png"
              alt="Next slide"
            />
          )
        }
        renderArrowPrev={(clickHandler, hasPrev) =>
          hasPrev && (
            <img
              onClick={clickHandler}
              className="absolute cursor-pointer z-[1] top-[50%]"
              style={imgInlineStyles}
              src="/img/left_arrow.png"
              alt="Previous slide"
            />
          )
        }
        showThumbs={false}
        showStatus={false}
        infiniteLoop
        renderIndicator={(clickHandler, isSelected, index, label) => (
          <span
            onClick={clickHandler}
            className={`${index !== 0 && 'ml-[10px]'} cursor-pointer inline-block w-[8px] h-[8px] rounded-full ${isSelected ? 'bg-[#856CF8]' : 'bg-[#D9D9D9]'}`}
          />
        )}
      >
        <div className="flex gap-[5%] w-[80%] m-auto mb-[3%] items-center justify-between">
          <img
            src="/img/onboard_carousel_1.png"
            style={{ width: '51%' }}
            alt="Personalized nudges"
          />
          <span className="text-[24px] align-start font-[600]">
            Pre-built, personalized nudges that surface at the right place and right time
          </span>
        </div>
        <div className="flex gap-[5%] w-[80%] m-auto mb-[3%] items-center justify-between">
          <img src="/img/onboard_carousel_2.png" style={{ width: '47%' }} alt="No-code tool" />
          <span className="pl-[4%] text-[24px] align-start font-[600]">
            A no-code tool that makes it easy to customize, edit, and launch without designers or
            developers
          </span>
        </div>
        <div className="flex gap-[5%] w-[80%] m-auto mb-[3%] items-center justify-between">
          <img
            src="/img/onboard_carousel_3.png"
            style={{ width: '47%' }}
            alt="Performance metrics"
          />
          <span className="pl-[4%] text-[24px] align-start font-[600]">
            Robust performance metrics tied to your business goals
          </span>
        </div>
      </Carousel>
      <button
        onClick={() => props.setCurrentStep(1)}
        className="text-[20px] cursor-pointer font-[600] mt-[24px] border-none text-white w-[385px] pl-[60px] pr-[60px] bg-[#856CF8] py-[20px] rounded-[15px]"
      >
        Begin Setup
      </button>
    </div>
  );
}

FeatureCarousel.propTypes = {
  setCurrentStep: PropTypes.func.isRequired,
};
