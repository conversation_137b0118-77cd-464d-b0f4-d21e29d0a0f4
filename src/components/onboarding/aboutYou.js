import React from 'react';
import { useEffect, useState, useCallback } from 'react';
import { useAppBridge } from '@shopify/app-bridge-react';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import Loader from '../UI/Loader';
import { makeRequest } from '../../utils/makeRequest';
import { useWindowWidth } from '../../hooks/width';

export default function AboutYou(props) {
  const shopify = useAppBridge();
  const width = useWindowWidth();
  const { setCurrentStep, onBoardingDetails } = props;
  const [fetchLoading, setFetchLoading] = useState(false);
  const [nameError, setNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('');

  const startDetails = useCallback(async () => {
    try {
      if (!onBoardingDetails.details_start) {
        const form = new FormData();
        form.append('field', 'details_start');
        await makeRequest({
          path: `/react/merchant_update_onboarding?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: form,
        });
      }
    } catch (e) {
      console.log(e);
    }
  }, [onBoardingDetails.details_start, shopify]);

  const fetchDetails = useCallback(async () => {
    const data = await makeRequest({
      path: `/react/merchant_fetch_details?id_token=${await shopify.idToken()}`,
      method: 'GET',
    });
    if (data) {
      setFirstName(data.firstName ?? '');
      setLastName(data.lastName ?? '');
      setEmail(data.email ?? '');
      setRole(data.role ?? '');
    }
    setFetchLoading(false);
  }, [shopify]);

  const updateDetails = async () => {
    setNameError('');
    setEmailError('');
    if (!firstName) {
      setNameError('Please enter your first name.');
      return;
    }
    if (!lastName) {
      setNameError('Please enter your last name.');
      return;
    }
    if (!email || !email.match(/^([a-z0-9_.-]+)@([\da-z.-]+)\.([a-z.]{2,6})$/)) {
      setEmailError('Please enter a valid email.');
      return;
    }
    setIsLoading(true);
    const form = new FormData();
    form.append('first_name', firstName);
    form.append('last_name', lastName);
    form.append('email', email);
    form.append('role', role);
    const data = await makeRequest({
      path: `/react/merchant_update_details?id_token=${await shopify.idToken()}`,
      method: 'POST',
      body: form,
    });
    if (data.okay) {
      setCurrentStep(4);
    } else {
      toast.error('Please try again.');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    startDetails();
    fetchDetails();
  }, [startDetails, fetchDetails]);

  if (fetchLoading) return <></>;

  return (
    <>
      <div
        className={`flex w-full pt-[8%] px-[15%] ${width > 1050 ? 'justify-end' : 'justify-center'}`}
      >
        <div className="flex flex-col leading-[34px]">
          <div className="text-[24px] font-[600] mb-[34px]">Tell us about you.</div>
          <div className="flex flex-col">
            <div className="flex gap-[24px]">
              <input
                type="text"
                className="rounded-[15px] p-[22px] text-[16px] border border-[#98A2B3]"
                placeholder="First Name"
                value={firstName}
                onChange={e => setFirstName(e.currentTarget.value)}
              />
              <input
                type="text"
                className="rounded-[15px] p-[22px] text-[16px] border border-[#98A2B3]"
                placeholder="Last Name"
                value={lastName}
                onChange={e => setLastName(e.currentTarget.value)}
              />
            </div>
            {nameError ? (
              <div className="flex items-center">
                <img
                  className="w-[21px] h-[21px] mr-[6px]"
                  src="/img/icons/error_triangle.png"
                  alt="Error"
                />
                <span className="text-[#D71920]">{nameError}</span>
              </div>
            ) : (
              <div className="h-[34px]" />
            )}
          </div>
          <div className="flex flex-col">
            <input
              type="text"
              className="rounded-[15px] p-[22px] text-[16px] border border-[#98A2B3]"
              placeholder="Email"
              value={email}
              onChange={e => setEmail(e.currentTarget.value)}
            />
            {emailError ? (
              <div className="flex items-center">
                <img
                  className="w-[21px] h-[21px] mr-[6px]"
                  src="/img/icons/error_triangle.png"
                  alt="Error"
                />
                <span className="text-[#D71920]">{emailError}</span>
              </div>
            ) : (
              <div className="h-[34px]" />
            )}
          </div>
          <input
            type="text"
            className="rounded-[15px] p-[22px] text-[16px] border border-[#98A2B3]"
            placeholder="Role (Optional)"
            value={role}
            onChange={e => setRole(e.currentTarget.value)}
          />
          <button
            disabled={isLoading}
            onClick={updateDetails}
            className={`text-[20px] cursor-pointer font-[600] mt-[34px] border-none ${isLoading ? 'text-black' : 'text-white'} w-full px-[60px] ${isLoading ? 'bg-[#F9FAFB]' : 'bg-[#856CF8]'} py-[20px] rounded-[15px]`}
          >
            {isLoading ? <Loader height="18px" /> : 'Next'}
          </button>
        </div>
      </div>
      {width > 1050 && (
        <img
          className="w-[35%] absolute left-[50px] bottom-[0]"
          src="/img/onboard_about_you.png"
          alt="About You"
        />
      )}
    </>
  );
}

AboutYou.propTypes = {
  setCurrentStep: PropTypes.func.isRequired,
  onBoardingDetails: PropTypes.shape({
    details_start: PropTypes.bool,
    details_confirmed: PropTypes.bool,
  }).isRequired,
};
