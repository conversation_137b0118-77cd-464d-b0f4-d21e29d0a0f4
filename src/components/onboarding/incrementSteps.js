import React from 'react';
import TermsAndConditions from './termsAndConditions';
import ActivateScript from './activateScript';
import AboutYou from './aboutYou';
import PickAPlan from './pickAPlan';
import PropTypes from 'prop-types';

const progressStyles = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  position: 'absolute',
  top: '-10px',
};
const dotStyles = {
  width: '20px',
  height: '20px',
  borderRadius: '100%',
  backgroundColor: 'white',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};
const innerDotStyles = {
  borderRadius: '100%',
  width: '5px',
  height: '5px',
  backgroundColor: '#856CF8',
  display: 'none',
};
const activatedBorder = {
  border: '1px solid #856CF8',
};
const comingUpBorder = {
  border: '1px solid #98A2B3',
};
const activatedFont = {
  fontSize: '12px',
  color: '#856CF8',
};
const comingUpFont = {
  fontSize: '12px',
  color: '#98A2B3',
};

const renderSteps = (currentStep, setCurrentStep, onBoardingDetails) => {
  switch (currentStep) {
    case 1:
      return (
        <TermsAndConditions setCurrentStep={setCurrentStep} onBoardingDetails={onBoardingDetails} />
      );
    case 2:
      return (
        <ActivateScript setCurrentStep={setCurrentStep} onBoardingDetails={onBoardingDetails} />
      );
    case 3:
      return <AboutYou setCurrentStep={setCurrentStep} onBoardingDetails={onBoardingDetails} />;
    case 4:
      return <PickAPlan onBoardingDetails={onBoardingDetails} />;
    default:
      return (
        <TermsAndConditions setCurrentStep={setCurrentStep} onBoardingDetails={onBoardingDetails} />
      );
  }
};

export default function IncrementSteps(props) {
  const { currentStep, setCurrentStep, onBoardingDetails } = props;
  return (
    <div className="w-full h-full relative">
      <img className="w-[100px]" src="/img/vandra_purple_logo.png" alt="Vandra logo" />
      <div className="flex justify-center w-full">
        <div className="w-[290px] border-b border-color-[#98A2B3] relative flex">
          <div style={{ ...progressStyles, left: '-5%' }}>
            <div
              style={{
                ...dotStyles,
                ...(currentStep >= 1 ? activatedBorder : comingUpBorder),
                backgroundColor: currentStep >= 2 ? '#856CF8' : 'white',
              }}
            >
              {currentStep >= 2 ? (
                <img
                  className="w-[6px] h-[6px] mt-[1px]"
                  src="/img/icons/checkMark.png"
                  alt="Completed"
                />
              ) : (
                <div style={{ ...innerDotStyles, display: currentStep === 1 ? 'block' : 'none' }} />
              )}
            </div>
            <span style={currentStep >= 1 ? activatedFont : comingUpFont}>T&Cs</span>
          </div>
          <div style={{ ...progressStyles, left: '20%' }}>
            <div
              style={{
                ...dotStyles,
                ...(currentStep >= 2 ? activatedBorder : comingUpBorder),
                backgroundColor: currentStep >= 3 ? '#856CF8' : 'white',
              }}
            >
              {currentStep >= 3 ? (
                <img
                  className="w-[6px] h-[6px] mt-[1px]"
                  src="/img/icons/checkMark.png"
                  alt="Completed"
                />
              ) : (
                <div style={{ ...innerDotStyles, display: currentStep === 2 ? 'block' : 'none' }} />
              )}
            </div>
            <span style={currentStep >= 2 ? activatedFont : comingUpFont}>Activate Script</span>
          </div>
          <div style={{ ...progressStyles, left: '57%' }}>
            <div
              style={{
                ...dotStyles,
                ...(currentStep >= 3 ? activatedBorder : comingUpBorder),
                backgroundColor: currentStep >= 4 ? '#856CF8' : 'white',
              }}
            >
              {currentStep >= 4 ? (
                <img
                  className="w-[6px] h-[6px] mt-[1px]"
                  src="/img/icons/checkMark.png"
                  alt="Completed"
                />
              ) : (
                <div style={{ ...innerDotStyles, display: currentStep === 3 ? 'block' : 'none' }} />
              )}
            </div>
            <span style={currentStep >= 3 ? activatedFont : comingUpFont}>About You</span>
          </div>
          <div style={{ ...progressStyles, left: '88%' }}>
            <div
              style={{
                ...dotStyles,
                ...(currentStep >= 4 ? activatedBorder : comingUpBorder),
                backgroundColor: currentStep >= 5 ? '#856CF8' : 'white',
              }}
            >
              {currentStep >= 5 ? (
                <img
                  className="w-[6px] h-[6px] mt-[1px]"
                  src="/img/icons/checkMark.png"
                  alt="Completed"
                />
              ) : (
                <div style={{ ...innerDotStyles, display: currentStep === 4 ? 'block' : 'none' }} />
              )}
            </div>
            <span
              style={{
                whiteSpace: 'nowrap',
                ...(currentStep >= 4 ? activatedFont : comingUpFont),
              }}
            >
              Free Trial
            </span>
          </div>
        </div>
      </div>
      {renderSteps(currentStep, setCurrentStep, onBoardingDetails)}
    </div>
  );
}

IncrementSteps.propTypes = {
  currentStep: PropTypes.number.isRequired,
  setCurrentStep: PropTypes.func.isRequired,
  onBoardingDetails: PropTypes.object.isRequired,
};
