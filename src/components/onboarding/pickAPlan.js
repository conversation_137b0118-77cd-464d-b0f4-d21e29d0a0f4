import React from 'react';
import { useEffect, useState, useRef, useCallback } from 'react';
import { useAppBridge } from '@shopify/app-bridge-react';
import { makeRequest } from '../../utils/makeRequest';
import Loader from '../UI/Loader';
import PropTypes from 'prop-types';

export default function PickAPlan(props) {
  const shopify = useAppBridge();
  const { onBoardingDetails } = props;
  const min = 500;
  const max = 10000;
  const step = 500;
  const [value, setValue] = useState(min);
  const [currentPlan, setCurrentPlan] = useState('Starter');
  const [loading, setLoading] = useState(false);
  const [planDetails, setPlanDetails] = useState(undefined);
  const rangeRef = useRef(null);

  const calculatePercentage = val => ((val - min) / (max - min)) * 100;

  useEffect(() => {
    if (value <= 500) {
      setCurrentPlan('Starter');
    } else if (value <= 1000) {
      setCurrentPlan('Pro');
    } else if (value <= 2500) {
      setCurrentPlan('Advanced');
    } else if (value <= 5000) {
      setCurrentPlan('Growth');
    } else {
      setCurrentPlan('Enterprise');
    }
  }, [value]);

  const fetchDetails = useCallback(async () => {
    try {
      const data = await makeRequest({
        path: `/react/merchant_fetch_pricing_plan?id_token=${await shopify.idToken()}`,
        method: 'GET',
      });
      setPlanDetails(JSON.parse(data.pricing_details));
    } catch (e) {
      console.log(e);
    }
  }, [shopify]);

  const startBilling = useCallback(async () => {
    try {
      if (!onBoardingDetails.billing_start) {
        const form = new FormData();
        form.append('field', 'billing_start');
        await makeRequest({
          path: `/react/merchant_update_onboarding?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: form,
        });
      }
    } catch (e) {
      console.log(e);
    }
  }, [onBoardingDetails.billing_start, shopify]);

  const confirmBilling = async () => {
    if (loading) return;
    try {
      setLoading(true);
      const form = new FormData();
      form.append('billing_orders_selected', value);
      form.append('billing_plan_selected', currentPlan);
      const data = await makeRequest({
        path: `/react/merchant_confirm_billing?id_token=${await shopify.idToken()}`,
        method: 'POST',
        body: form,
      });
      if (data.redirect_url) {
        window.open(data.redirect_url, '_top');
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const handleMouseMove = event => {
    if (!rangeRef.current) return;

    const { left, width } = rangeRef.current.getBoundingClientRect();
    let newValue = ((event.clientX - left) / width) * (max - min) + min;

    // Snap to closest step
    newValue = Math.round(newValue / step) * step;

    // Ensure within bounds
    newValue = Math.max(min, Math.min(max, newValue));

    setValue(newValue);
  };

  useEffect(() => {
    fetchDetails();
    startBilling();
  }, [fetchDetails, startBilling]);

  const mainContainerStyles = {
    paddingTop: '6rem',
    paddingLeft: '8rem',
    paddingRight: '8rem',
    paddingBottom: '2rem',
    display: 'flex',
    justifyContent: 'space-between',
    height: '100%',
    flexDirection: 'column',
  };
  const textContainerStyles = {
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
  };
  const contentContainerStyles = {
    width: '70%',
    margin: 'auto',
    marginBottom: 0,
  };
  const sliderContainerStlyes = {
    marginTop: '40px',
    position: 'relative',
  };
  const sliderLineStyles = {
    border: '5px solid rgb(0, 0, 0, .2)',
    borderRadius: 5,
  };
  const slideButtonStyles = {
    position: 'absolute',
    left: `${calculatePercentage(value)}%`,
    transform: 'translate(-50%, -50%)',
    top: 5,
    width: 35,
    height: 35,
    borderRadius: '100%',
    backgroundColor: 'white',
    cursor: 'pointer',
    border: '1px solid #98A2B3',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  };
  const slideButtonInnerStyles = {
    width: 18,
    height: 18,
    borderRadius: '100%',
    backgroundColor: '#856CF8',
  };
  const valueTextStyles = {
    borderRadius: '10px',
    backgroundColor: '#101828',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: 92,
    height: 40,
    position: 'absolute',
    left: `${calculatePercentage(value)}%`,
    transform: 'translate(-50%, -50%)',
    top: 50,
  };
  const carrotTopStyles = {
    position: 'absolute',
    top: -6,
  };
  const detailsContainer = {
    width: '60%',
    minWidth: '700px',
    margin: 'auto',
  };
  const planDetailsContainer = {
    borderRadius: 20,
    backgroundColor: '#F9FAFB',
    border: '1px solid #000',
    padding: 40,
    marginTop: 50,
    display: 'flex',
    width: '100%',
  };
  const planDescriptionStyles = {
    display: 'flex',
    flexDirection: 'column',
    width: '40%',
    gap: 32,
    borderRight: '1px solid #98A2B3',
  };
  const planFeaturesStyles = {
    display: 'flex',
    flexDirection: 'column',
    width: '60%',
    gap: 34,
    paddingLeft: 32,
  };
  const featureContainer = {
    display: 'flex',
    alignItems: 'center',
    gap: 16,
  };
  const featureImage = {
    width: 31,
    height: 31,
  };
  const featureText = {
    color: 'black',
    fontSize: 16,
    fontWeight: 600,
  };

  if (!planDetails) return <></>;

  return (
    <div style={mainContainerStyles}>
      <div>
        <div style={textContainerStyles}>
          <div style={{ fontSize: 24, fontWeight: 600 }}>
            A plan that grows with you. Start your free trial today!
          </div>
        </div>
        <div style={contentContainerStyles}>
          <div className="mt-[40px] text-[#98A2B3] text-[16px] font-[700]">
            NUMBER OF ORDERS PER MONTH
          </div>
          <div style={sliderContainerStlyes}>
            <div ref={rangeRef} style={sliderLineStyles}></div>
            <div
              style={slideButtonStyles}
              onMouseDown={e => {
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener(
                  'mouseup',
                  () => {
                    document.removeEventListener('mousemove', handleMouseMove);
                  },
                  { once: true }
                );
              }}
            >
              <div style={slideButtonInnerStyles} />
            </div>
            <div style={valueTextStyles}>
              <div style={{ color: 'white', userSelect: 'none' }}>
                {value.toLocaleString()}
                {value === 10000 ? '+' : ''}
              </div>
              <div style={carrotTopStyles}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="9"
                  height="7"
                  viewBox="0 0 9 7"
                  fill="none"
                >
                  <path d="M4.5 0L8.39711 6.75H0.602886L4.5 0Z" fill="#101828" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style={detailsContainer}>
        <div style={planDetailsContainer}>
          <div style={planDescriptionStyles}>
            <div className="text-[#856CF8] text-[32px] font-[600]">{currentPlan}</div>
            <div>
              <span className="text-[32px] font-[600] text-[#101828]">
                ${planDetails[currentPlan].price.toLocaleString()}
              </span>
              <span className="text-[32px] font-[500] text-[#98A2B3]">/month</span>
            </div>
            <div className="text-black text-[16px] font-[600]">
              Up to {planDetails[currentPlan].maximumOrders.toLocaleString()} orders / month
              {planDetails[currentPlan].overflow_orders && (
                <div>
                  {`+ $${planDetails[currentPlan].overflow_cost} / mo / ${planDetails[currentPlan].overflow_orders.toLocaleString()} orders over`}
                </div>
              )}
            </div>
          </div>
          <div style={planFeaturesStyles}>
            <div style={featureContainer}>
              <img style={featureImage} src="/img/icons/book.png" alt="Library feature" />
              <span style={featureText}>Library of pre-built behavioral nudges</span>
            </div>
            <div style={featureContainer}>
              <img style={featureImage} src="/img/icons/chart.png" alt="Analytics feature" />
              <span style={featureText}>Robust analytics</span>
            </div>
            <div style={featureContainer}>
              <img style={featureImage} src="/img/icons/brush.png" alt="Customization feature" />
              <span style={featureText}>No-code brand customizations</span>
            </div>
            <div style={featureContainer}>
              <img style={featureImage} src="/img/icons/star.png" alt="Customer Success feature" />
              <span style={featureText}>Access to personal Customer Success Manager</span>
            </div>
          </div>
        </div>
        <div className="flex justify-center pt-[20px]">
          <button
            disabled={loading}
            onClick={confirmBilling}
            className={`w-[40%] ${loading ? 'bg-[#F9FAFB]' : 'bg-[#856CF8]'} rounded-[15px] py-[20px] border-none cursor-pointer flex justify-center`}
          >
            <span className={`${loading ? 'text-black' : 'text-white'} text-[20px] font-[600]`}>
              {loading ? <Loader height="18px" /> : 'Start a 1-Month Free Trial'}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}

PickAPlan.propTypes = {
  onBoardingDetails: PropTypes.shape({
    billing_start: PropTypes.bool,
    billing_confirmed: PropTypes.bool,
    confirmed_charge: PropTypes.bool,
  }).isRequired,
};
