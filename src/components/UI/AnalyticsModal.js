import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { makeRequest } from '../../utils/makeRequest';
import Loader from './Loader';
import { useAppBridge } from '@shopify/app-bridge-react';
import PropTypes from 'prop-types';

const AnalyticsModal = ({ isOpen, onClose, interventionName, title }) => {
  const shopify = useAppBridge();
  const [iframe, setIframe] = useState('');
  const [iFrameLoading, setIFrameLoading] = useState(true);

  const getIframe = async () => {
    try {
      setIFrameLoading(true);

      // Check for emulation mode first
      const emulatingStoreData = sessionStorage.getItem('emulating_store');
      const emulatingStore = emulatingStoreData ? JSON.parse(emulatingStoreData) : null;
      const emulatingStoreId = emulatingStore?.id;

      // Fallback to URL parameter for store_id_override
      const storeIdOverride = new URLSearchParams(window.location.search).get('store_id_override');

      // Build the API path
      let apiPath = `/react/metabase_iframe?intervention_type_name=${interventionName}&id_token=${await shopify.idToken()}`;

      // Add store override parameter if available
      if (emulatingStoreId) {
        // In emulation mode, the makeRequest function will automatically add emulate_store parameter
        // No need to manually add it here
      } else if (storeIdOverride) {
        apiPath += `&store_id_override=${storeIdOverride}`;
      }

      const data = await makeRequest({
        path: apiPath,
      });
      setIframe(data.url);
    } catch (e) {
      console.error('Analytics loading error:', e);
      toast.error('Error loading analytics data');
    } finally {
      setIFrameLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && interventionName) {
      getIframe();
    }
  }, [isOpen, interventionName]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex">
      <div className="relative p-6 bg-white w-11/12 max-w-5xl m-auto rounded-lg max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">{title} Analytics</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="mt-4">
          {iFrameLoading ? (
            <div className="w-full flex-row-center py-12">
              <Loader />
            </div>
          ) : iframe ? (
            <div className="w-full overflow-hidden">
              <iframe
                src={iframe}
                className="w-full border-0"
                style={{ height: '70vh' }}
                title="Analytics Dashboard"
              />
            </div>
          ) : (
            <div>
              {' '}
              <h2 className="text-center py-8 text-gray-500">No analytics data available</h2>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

AnalyticsModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  interventionName: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
};

export default AnalyticsModal;
