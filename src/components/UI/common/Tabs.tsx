import React, { useState, useCallback } from 'react';

export interface TabsProps {
  tabs: {
    id: string;
    content: string;
    panelContent: React.ReactNode;
  }[];
  selected?: number;
  onSelect?: (selectedTabIndex: number) => void;
  className?: string;
}

export const Tabs = ({ tabs, selected = 0, onSelect, className }: TabsProps) => {
  const [selectedTab, setSelectedTab] = useState(selected);

  const handleTabChange = useCallback(
    (selectedTabIndex: number) => {
      setSelectedTab(selectedTabIndex);
      onSelect?.(selectedTabIndex);
    },
    [onSelect]
  );

  return (
    <div className={className}>
      <ul className="sticky top-0 bg-white z-10 flex flex-wrap -mb-px text-sm font-medium">
        {tabs.map((tab, index) => (
          <li key={tab.id} className="mr-2">
            <button
              onClick={() => handleTabChange(index)}
              className={`inline-block px-4 py-2 border-b-2 rounded-t-lg transition-colors duration-200 text-base ${
                selectedTab === index
                  ? 'text-primary border-primary font-bold'
                  : 'text-text-dark border-transparent hover:text-primary-hover hover:border-primary-hover'
              }`}
              aria-current={selectedTab === index ? 'page' : undefined}
            >
              {tab.content}
            </button>
          </li>
        ))}
      </ul>
      <div className="mt-4">{tabs[selectedTab].panelContent}</div>
    </div>
  );
};
