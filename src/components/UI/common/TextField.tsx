import React, { useState, useRef, useCallback } from 'react';
import { ErrorCircleIcon } from './icons/ErrorCircleIcon';

interface TextFieldProps {
  label?: React.ReactNode;
  labelHidden?: boolean;
  value: string;
  onChange: (value: string) => void;
  maxLength?: number;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  autoComplete?: string;
  multiline?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  helpText?: React.ReactNode;
  type?: string;
  id?: string;
  name?: string;
  readOnly?: boolean;
  clearButton?: boolean;
  onClearButtonClick?: () => void;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  requiredIndicator?: boolean;
  showCharacterCount?: boolean;
  headerText?: string;
  inputClassName?: string;
  inputRef?: React.RefObject<HTMLInputElement | HTMLTextAreaElement>;
}

export const TextField: React.FC<TextFieldProps> = ({
  label,
  labelHidden = false,
  value,
  onChange,
  maxLength,
  placeholder,
  error,
  disabled = false,
  autoComplete = 'off',
  multiline = false,
  onFocus,
  onBlur,
  helpText,
  type = 'text',
  id,
  name,
  readOnly = false,
  prefix,
  suffix,
  requiredIndicator = false,
  showCharacterCount = false,
  headerText,
  inputClassName = 'h-12',
  inputRef: externalInputRef,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const internalInputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const inputRef = externalInputRef || internalInputRef;

  const handleFocus = useCallback(() => {
    setIsFocused(true);
    onFocus && onFocus();
  }, [onFocus]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onBlur && onBlur();
  }, [onBlur]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      let newValue = e.target.value;
      if (type === 'number') {
        // Replace commas with dots and remove invalid characters (allow only digits, one dot, and optional minus at start)
        newValue = newValue.replace(/,/g, '.');
        // Only allow one dot and digits (and optional minus at start)
        newValue = newValue.replace(/(?!^[-]?\d*)\./g, ''); // Remove extra dots
        newValue = newValue.replace(/[^0-9.-]/g, '');
        // Only allow minus at the start
        newValue = newValue.replace(/(?!^)-/g, '');
      }
      onChange(newValue);
    },
    [onChange, type]
  );

  const InputComponent = multiline ? 'textarea' : 'input';

  // Generate a random ID if none provided
  const inputId = id || `TextField-${Math.random().toString(36).substring(2, 10)}`;

  // Determine container classes based on variant
  const containerClasses = 'w-full';

  // Determine input container classes based on variant, focus, error state
  const inputContainerClasses = `
    relative border rounded flex items-center bg-white rounded-2xl py-2 px-3
    ${isFocused ? 'border-purple-500 ring-1 ring-purple-500' : 'border-text-dark'}
    ${error ? 'border-red-500' : ''}
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
    ${readOnly ? 'bg-gray-50 cursor-pointer' : ''}
    ${inputClassName}
  `;

  // Input classes
  const inputClasses = `
    w-full bg-transparent outline-none text-base
    ${disabled ? 'cursor-not-allowed text-gray-500' : ''}
    ${readOnly ? 'cursor-pointer' : ''}
  `;

  return (
    <div className={containerClasses}>
      {headerText && (
        <div className="mb-2">
          <h2 className="text-base font-medium text-gray-900">{headerText}</h2>
        </div>
      )}

      {label && !labelHidden && (
        <div className="mb-3">
          <label htmlFor={inputId} className="block text-sm font-medium text-text-dark">
            {label}
            {requiredIndicator && <span className="text-red-500 ml-1">*</span>}
          </label>
        </div>
      )}

      <div className="w-full">
        <div className={inputContainerClasses}>
          {prefix && <div className="flex-shrink-0 mr-2">{prefix}</div>}

          <InputComponent
            id={inputId}
            name={name}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            className={inputClasses}
            maxLength={maxLength}
            placeholder={placeholder}
            disabled={disabled}
            readOnly={readOnly}
            autoComplete={autoComplete}
            aria-invalid={error ? true : false}
            aria-describedby={error ? `${inputId}Error` : undefined}
            ref={inputRef as any}
            type={multiline ? undefined : type}
            rows={multiline ? 3 : undefined}
          />

          {suffix && <div className="flex-shrink-0 ml-2">{suffix}</div>}

          {/* Character count */}
          {showCharacterCount && maxLength && (
            <div className="absolute right-2 bottom-0 text-xs text-gray-500">
              {value.length}/{maxLength}
            </div>
          )}
        </div>
      </div>

      {(error || helpText) && (
        <div className="mt-1">
          {error && (
            <div id={`${inputId}Error`} className="flex items-center text-sm text-red-500">
              <ErrorCircleIcon className="mr-1" />
              {error}
            </div>
          )}

          {helpText && !error && (
            <div id={`${inputId}HelpText`} className="text-sm text-gray-500">
              {helpText}
            </div>
          )}
        </div>
      )}

      {showCharacterCount && maxLength && !error && !helpText && (
        <div className="mt-1 text-right text-xs text-gray-500">
          {value.length}/{maxLength}
        </div>
      )}
    </div>
  );
};
