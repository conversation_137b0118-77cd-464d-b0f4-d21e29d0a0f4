import React from 'react';

interface ToggleProps {
  isEnabled: boolean;
  onToggle: () => void;
  label?: string;
  disabled?: boolean;
}

export const Toggle: React.FC<ToggleProps> = ({ isEnabled, onToggle, label, disabled = false }) => {
  return (
    <div className="flex items-center justify-between">
      {label && <span className="text-sm text-gray-700">{label}</span>}
      <button
        type="button"
        onClick={onToggle}
        disabled={disabled}
        className={`relative inline-flex h-4 w-7 items-center rounded-full transition-colors focus:outline-none ${
          isEnabled ? 'bg-primary' : 'bg-gray-200'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
      >
        <span
          className={`${
            isEnabled ? 'translate-x-3.5' : 'translate-x-0.5'
          } inline-block h-3 w-3 transform rounded-full bg-white transition-transform`}
        />
      </button>
    </div>
  );
};
