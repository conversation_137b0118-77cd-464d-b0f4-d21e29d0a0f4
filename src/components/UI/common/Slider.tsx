import React from 'react';

interface SliderProps {
  value: number;
  onChange: (value: number) => void;
  label?: string;
  min?: number;
  max?: number;
  step?: number;
  className?: string;
}

// Add global styles for the range slider
const sliderStyles = `
  input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #856CF8;
    cursor: pointer;
    transition: background .3s ease-in-out;
  }
  input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #856CF8;
    cursor: pointer;
    border: none;
    transition: background .3s ease-in-out;
  }
  input[type="range"]::-webkit-slider-thumb:hover {
    background: #6B4FF7;
  }
  input[type="range"]::-moz-range-thumb:hover {
    background: #6B4FF7;
  }
  input[type="range"]:focus {
    outline: none;
  }
`;

export const Slider = ({
  value,
  onChange,
  label,
  min = 0,
  max = 100,
  step = 5,
  className = '',
}: SliderProps) => {
  // Calculate the percentage for styling the range track
  const percentage = ((value - min) / (max - min)) * 100;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(Number(e.target.value));
  };

  // Add the styles to the document head
  React.useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = sliderStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <div className={`w-full ${className}`}>
      {label && <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>}
      <div className="relative">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={handleChange}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          style={{
            background: `linear-gradient(to right, #856CF8 0%, #856CF8 ${percentage}%, #E5E7EB ${percentage}%, #E5E7EB 100%)`,
          }}
        />
      </div>
    </div>
  );
};
