import React, { useEffect, useMemo, useState } from 'react';
import { Dropdown, DropdownOption } from './Dropdown';

interface FontSelectorProps {
  label?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
}

export const FontSelector = ({
  label,
  value,
  onChange,
  placeholder = 'Arial',
  error,
  disabled = false,
  className,
}: FontSelectorProps) => {
  // Track custom input value
  const [inputValue, setInputValue] = useState<string>(value);

  // Common fonts that will be suggested
  const commonFonts = useMemo<DropdownOption[]>(
    () => [
      // System/Web-safe Fonts (Reliable fallbacks)
      { value: 'Arial', label: 'Arial' },
      { value: 'Helvetica', label: 'Helvetica' },
      { value: 'Verdana', label: 'Verdana' },
      { value: 'Tahoma', label: 'Tahoma' },
      { value: 'Trebuchet MS', label: 'Trebuchet MS' },
      { value: 'Georgia', label: 'Georgia' },
      { value: 'Times New Roman', label: 'Times New Roman' },

      // Shopify-friendly Sans-Serif Fonts
      { value: 'Poppins', label: 'Poppins' },
      { value: 'Montserrat', label: 'Montserrat' },
      { value: 'Roboto', label: 'Roboto' },
      { value: 'Open Sans', label: 'Open Sans' },
      { value: 'Lato', label: 'Lato' },
      { value: 'Inter', label: 'Inter' },
      { value: 'Nunito Sans', label: 'Nunito Sans' },
      { value: 'Source Sans Pro', label: 'Source Sans Pro' },
      { value: 'Work Sans', label: 'Work Sans' },
      { value: 'Karla', label: 'Karla' },
      { value: 'Raleway', label: 'Raleway' },

      // Shopify-friendly Serif Fonts
      { value: 'Playfair Display', label: 'Playfair Display' },
      { value: 'Merriweather', label: 'Merriweather' },
      { value: 'Lora', label: 'Lora' },
      { value: 'EB Garamond', label: 'EB Garamond' },
      { value: 'Libre Baskerville', label: 'Libre Baskerville' },
      { value: 'DM Serif Display', label: 'DM Serif Display' },

      // Shopify Headline/Accent Fonts (tasteful options)
      { value: 'Quicksand', label: 'Quicksand' },
      { value: 'Tenor Sans', label: 'Tenor Sans' },
      { value: 'Questrial', label: 'Questrial' },
      { value: 'Cormorant Garamond', label: 'Cormorant Garamond' },
      { value: 'Libre Franklin', label: 'Libre Franklin' },
      { value: 'Josefin Sans', label: 'Josefin Sans' },
      { value: 'Cabin', label: 'Cabin' },
    ],
    []
  );

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Custom filter function that allows keeping custom input
  const customFilterOptions = (options: DropdownOption[], input: string) => {
    if (input === '') return options;

    const filterRegex = new RegExp(input, 'i');
    return options.filter(option => option.label.match(filterRegex));
  };

  // Custom handler for input changes
  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    // Pass the input value directly to parent component
    onChange(newValue);
  };

  // Custom handler for option selection
  const handleOptionSelect = (selectedValue: string) => {
    // When an option is selected from dropdown, update both
    setInputValue(selectedValue);
    onChange(selectedValue);
  };

  return (
    <Dropdown
      options={commonFonts}
      value={value}
      onChange={handleOptionSelect}
      label={label}
      placeholder={placeholder}
      error={error}
      disabled={disabled}
      className={`font-sans ${className || ''}`}
      inputClassName="h-12"
      filterOptions={customFilterOptions}
      // Override the default input handling
      onInputChange={handleInputChange}
      // Use controlled input value
      inputValue={inputValue}
      // Allow empty results without showing "No options found"
      hideEmptyResultsMessage={true}
    />
  );
};
