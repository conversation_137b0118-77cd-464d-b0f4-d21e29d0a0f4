import React from 'react';
import { CheckmarkIcon } from './icons/CheckmarkIcon';

interface CheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label: string;
}

export const Checkbox: React.FC<CheckboxProps> = ({ checked, onChange, label }) => {
  return (
    <label className="flex items-center gap-2 cursor-pointer">
      <div className="relative flex items-center justify-center w-5 h-5">
        <input
          type="checkbox"
          checked={checked}
          onChange={e => onChange(e.target.checked)}
          className="absolute opacity-0 w-5 h-5 cursor-pointer"
        />
        <div
          className={`w-5 h-5 rounded border ${
            checked
              ? 'bg-purple-600 border-purple-600 flex items-center justify-center'
              : 'border-gray-300'
          }`}
        >
          {checked && <CheckmarkIcon className="text-white" />}
        </div>
      </div>
      <span>{label}</span>
    </label>
  );
};
