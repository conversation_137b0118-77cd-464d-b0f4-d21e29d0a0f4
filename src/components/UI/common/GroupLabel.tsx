import React, { ReactNode } from 'react';
import { Tooltip } from './Tooltip';
import { InfoIcon } from './icons/InfoIcon';
interface GroupLabelProps {
  label: string;
  tooltip?: string | ReactNode;
  className?: string;
}

export const GroupLabel = ({ label, tooltip, className }: GroupLabelProps) => {
  // Function to convert newlines to React fragments with <br/> elements
  const formatTooltipText = (text: string) => {
    if (!text) return text;

    return (
      <div className="flex flex-col gap-2 text-secondary p-3">
        {text.split('\n').map((line, index) => (
          <span key={index}>{line}</span>
        ))}
      </div>
    );
  };

  // Process tooltip content based on type
  const tooltipContent = typeof tooltip === 'string' ? formatTooltipText(tooltip) : tooltip;

  return (
    <div className="flex flex-row items-start gap-2">
      <div className={`text-base text-text-dark ${className || ''}`}>{label}</div>
      {tooltip && (
        <Tooltip content={tooltipContent} preferredPosition="above">
          <InfoIcon />
        </Tooltip>
      )}
    </div>
  );
};
