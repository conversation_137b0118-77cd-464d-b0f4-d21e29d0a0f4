import React, { useState, useRef, useEffect } from 'react';
import { TextField } from './TextField';
import ReactDOM from 'react-dom';
import { ChevronIcon } from './icons/ChevronIcon';
import { useDebounce } from '../../../hooks/useDebounce';
import Loader from '../Loader';

export interface DropdownOption<T = string> {
  value: T;
  label: string;
}

interface DropdownProps<T = string> {
  options: DropdownOption<T>[];
  value: T;
  onChange: (value: T) => void;
  label?: string;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
  inputClassName?: string;
  suffix?: React.ReactNode; // For custom suffix beyond the dropdown arrow
  filterOptions?: (options: DropdownOption<T>[], inputValue: string) => DropdownOption<T>[];
  onInputChange?: (value: string) => void; // Callback for direct input changes
  inputValue?: string; // Controlled input value
  hideEmptyResultsMessage?: boolean; // Hide "No options found" message
  selectionOnly?: boolean; // Prevent custom text input, only select from options
  isLoading?: boolean; // Add isLoading prop
  debounceTime?: number; // Time in ms to debounce the input change
  onDebouncedInputChange?: (value: string) => void; // Debounced callback for input changes
  inputRef?: React.RefObject<HTMLInputElement | HTMLTextAreaElement>; // Ref for the input element
  onOpenChange?: (isOpen: boolean) => void; // Callback when dropdown open state changes
  serverSideFiltering?: boolean; // Flag indicating if filtering is handled by the server
}

export function Dropdown<T = string>({
  options,
  value,
  onChange,
  label,
  placeholder,
  error,
  disabled = false,
  className = '',
  inputClassName = '',
  suffix,
  filterOptions,
  onInputChange,
  inputValue: controlledInputValue,
  hideEmptyResultsMessage = false,
  selectionOnly = false, // Default to false for backward compatibility
  isLoading = false, // Default isLoading to false
  debounceTime = 300, // Default debounce time
  onDebouncedInputChange,
  inputRef,
  onOpenChange,
  serverSideFiltering = false, // Default to client-side filtering
}: DropdownProps<T>) {
  const [internalInputValue, setInternalInputValue] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);
  const [isOpen, setIsOpen] = useState(false);
  const [positionAbove, setPositionAbove] = useState(true);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownListRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{
    top: number;
    left: number;
    width: number;
  }>({
    top: 0,
    left: 0,
    width: 0,
  });
  // Prevent dropdown from re-opening after selection
  const suppressNextOpenRef = useRef(false);
  // Track if the last input change was user-initiated
  const userInputChangeRef = useRef(false);

  // Use controlled input value if provided
  const inputValue = controlledInputValue !== undefined ? controlledInputValue : internalInputValue;

  // Debounce the input value
  const debouncedInputValue = useDebounce(inputValue, debounceTime);

  // Track the last value passed to onDebouncedInputChange
  const lastDebouncedValue = useRef<string | undefined>(undefined);

  // Call the debounced callback when the debounced value changes, only if different from last
  useEffect(() => {
    if (
      onDebouncedInputChange &&
      debouncedInputValue !== undefined &&
      debouncedInputValue !== '' &&
      debouncedInputValue !== lastDebouncedValue.current
    ) {
      lastDebouncedValue.current = debouncedInputValue;
      onDebouncedInputChange(debouncedInputValue);
    }
  }, [debouncedInputValue, onDebouncedInputChange]);

  // Separate effect for opening dropdown to avoid loops
  useEffect(() => {
    if (suppressNextOpenRef.current) {
      suppressNextOpenRef.current = false;
      return;
    }
    // Only open if the last input change was user-initiated
    if (userInputChangeRef.current && debouncedInputValue && options.length > 0 && !isOpen) {
      setIsOpen(true);
    }
    // Reset the flag after handling
    userInputChangeRef.current = false;
  }, [debouncedInputValue, options.length, isOpen]);

  // Calculate dropdown position when opening
  useEffect(() => {
    if (isOpen && dropdownRef.current && dropdownListRef.current) {
      const dropdownRect = dropdownRef.current.getBoundingClientRect();
      const listHeight = dropdownListRef.current.scrollHeight;

      // Get available space above and below
      const spaceAbove = dropdownRect.top;
      const spaceBelow = window.innerHeight - dropdownRect.bottom;

      // Default to above if there's enough space (approximately 200px minimum)
      // Otherwise, position below if there's more space below than above
      const shouldPositionAbove =
        spaceAbove >= Math.min(listHeight, 200) &&
        (spaceAbove >= spaceBelow || spaceBelow < listHeight);

      setPositionAbove(shouldPositionAbove);
    }
  }, [isOpen]);

  // Update dropdown position when opening
  useEffect(() => {
    if (isOpen && dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();

      setDropdownPosition({
        top: positionAbove ? rect.top - 5 : rect.bottom + 5,
        left: rect.left,
        width: rect.width,
      });
    }
  }, [isOpen, positionAbove]);

  // Notify parent component when dropdown open state changes
  useEffect(() => {
    onOpenChange && onOpenChange(isOpen);
  }, [isOpen, onOpenChange]);

  // Update filtered options when options change
  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  // Filter options when input changes
  const handleInputChange = (newValue: string) => {
    // If readonly, don't update the input value
    if (selectionOnly) return;

    // Mark this as a user-initiated change
    userInputChangeRef.current = true;

    // Use external handler if provided
    if (onInputChange) {
      onInputChange(newValue);
    } else {
      setInternalInputValue(newValue);
    }

    // Only apply client-side filtering if serverSideFiltering is false
    if (!serverSideFiltering) {
      if (filterOptions) {
        setFilteredOptions(filterOptions(options, newValue));
      } else if (newValue === '') {
        setFilteredOptions(options);
      } else {
        const filterRegex = new RegExp(newValue, 'i');
        const resultOptions = options.filter(option => option.label.match(filterRegex));
        setFilteredOptions(resultOptions);
      }
    } else {
      // With server-side filtering, always use the full options list provided
      setFilteredOptions(options);
    }
  };

  // Handle option selection
  const handleOptionSelect = (selectedOption: DropdownOption<T>) => {
    suppressNextOpenRef.current = true;
    // Mark this as a programmatic change
    userInputChangeRef.current = false;
    // In multi-select mode, the parent component will control the input value
    // Only set internal input value if not using controlled input
    if (controlledInputValue === undefined) {
      setInternalInputValue(selectedOption.label);
    }

    onChange(selectedOption.value);

    // Reset filtered options to show all options again after selection
    setFilteredOptions(options);

    setIsOpen(false);
  };

  // Refine the click outside handler to work with portals
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Skip if the click is on the dropdown trigger or if dropdown is closed
      if (!isOpen || (dropdownRef.current && dropdownRef.current.contains(event.target as Node))) {
        return;
      }

      // Check if the click is on the dropdown menu in the portal
      if (dropdownListRef.current && dropdownListRef.current.contains(event.target as Node)) {
        return;
      }

      // Close the dropdown if click is outside both dropdown trigger and menu
      setIsOpen(false);
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Update inputValue when selected option changes (only if not controlled)
  useEffect(() => {
    if (controlledInputValue === undefined) {
      const selectedOption = options.find(option => option.value === value);
      setInternalInputValue(selectedOption ? selectedOption.label : '');
    }
  }, [value, options, controlledInputValue]);

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className={`relative w-full ${className}`} ref={dropdownRef}>
      <TextField
        label={label}
        value={inputValue}
        onChange={handleInputChange}
        placeholder={placeholder}
        error={error}
        disabled={disabled}
        readOnly={selectionOnly}
        onFocus={() => setIsOpen(true)}
        inputClassName={`h-12 ${inputClassName}`}
        suffix={
          <div className="flex items-center">
            {suffix}
            <button
              type="button"
              className={`flex items-center justify-center w-5 h-5 ${disabled ? 'opacity-50' : ''}`}
              onClick={toggleDropdown}
              disabled={disabled}
            >
              <ChevronIcon isOpen={isOpen} />
            </button>
          </div>
        }
        inputRef={inputRef}
      />

      {isOpen &&
        !disabled &&
        typeof document !== 'undefined' &&
        ReactDOM.createPortal(
          <div
            ref={dropdownListRef}
            style={{
              position: 'fixed',
              top: positionAbove ? 'auto' : dropdownPosition.top,
              bottom: positionAbove ? window.innerHeight - dropdownPosition.top : 'auto',
              left: dropdownPosition.left,
              // width: dropdownPosition.width,
              zIndex: 9999,
            }}
            className={`bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-y-auto min-w-64 justify-start`}
          >
            {isLoading ? (
              <div className="flex flex-row items-center justify-start px-3 py-3 text-sm text-gray-500 text-left gap-2">
                <Loader style={{ width: '2rem', height: '2rem' }} />
                <span>Loading...</span>
              </div>
            ) : filteredOptions.length > 0 ? (
              filteredOptions.map(option => (
                <div
                  key={String(option.value)}
                  onClick={e => {
                    e.stopPropagation(); // Prevent event bubbling
                    handleOptionSelect(option);
                  }}
                  className={`px-3 py-2 cursor-pointer hover:bg-gray-100 transition-colors text-base ${
                    option.value === value ? 'bg-gray-50 font-medium' : ''
                  }`}
                >
                  {option.label}
                </div>
              ))
            ) : (
              !hideEmptyResultsMessage && (
                <div className="px-3 py-3 text-sm text-gray-500 text-left">No options found</div>
              )
            )}
          </div>,
          document.body
        )}
    </div>
  );
}
