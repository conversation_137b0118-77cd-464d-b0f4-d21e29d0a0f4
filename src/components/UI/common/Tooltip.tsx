import React, { useState, useRef, ReactNode, useEffect } from 'react';

interface TooltipProps {
  /** The content to display within the tooltip */
  content: ReactNode;
  /** The element that triggers the tooltip */
  children: ReactNode;
  /** Whether the tooltip is currently visible (controlled mode) */
  active?: boolean;
  /** Preferred position of the tooltip relative to trigger */
  preferredPosition?: 'above' | 'below' | 'left' | 'right';
  /** Whether to persist the tooltip on click */
  persistOnClick?: boolean;
  /** Additional classes to apply to the tooltip container */
  className?: string;
}

export const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  active: controlledActive,
  preferredPosition = 'above',
  persistOnClick = false,
  className = '',
}) => {
  const [isActive, setIsActive] = useState(false);
  const [position, setPosition] = useState(preferredPosition);
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [coordinates, setCoordinates] = useState({ top: 0, left: 0 });

  // Use controlled state if provided, otherwise use internal state
  const showTooltip = controlledActive !== undefined ? controlledActive : isActive;

  // Calculate tooltip position based on trigger element
  useEffect(() => {
    if (showTooltip && triggerRef.current && tooltipRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      const spacing = 8; // Spacing between tooltip and trigger

      let top = 0;
      let left = 0;
      let calculatedPosition = preferredPosition;

      // Check if the preferred position works or needs adjustment
      if (preferredPosition === 'above') {
        top = triggerRect.top - tooltipRect.height - spacing;
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;

        if (top < 0) {
          calculatedPosition = 'below';
        }
      }

      if (preferredPosition === 'below' || calculatedPosition === 'below') {
        top = triggerRect.bottom + spacing;
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
      }

      if (preferredPosition === 'left') {
        top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
        left = triggerRect.left - tooltipRect.width - spacing;

        if (left < 0) {
          calculatedPosition = 'right';
        }
      }

      if (preferredPosition === 'right' || calculatedPosition === 'right') {
        top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
        left = triggerRect.right + spacing;
      }

      // Ensure tooltip stays within viewport
      const rightEdgePosition = left + tooltipRect.width;
      const viewportWidth = window.innerWidth;

      if (rightEdgePosition > viewportWidth) {
        left = viewportWidth - tooltipRect.width - spacing;
      }

      if (left < spacing) {
        left = spacing;
      }

      setCoordinates({ top, left });
      setPosition(calculatedPosition);
    }
  }, [showTooltip, preferredPosition]);

  // Event handlers
  const handleMouseEnter = () => setIsActive(true);
  const handleMouseLeave = () => {
    if (!persistOnClick) {
      setIsActive(false);
    }
  };
  const handleClick = () => {
    if (persistOnClick) {
      setIsActive(!isActive);
    }
  };

  // Position-based arrow classes
  const arrowClasses = {
    above:
      'bottom-[-6px] left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-0',
    below:
      'top-[-6px] left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-0',
    left: 'right-[-6px] top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-0',
    right:
      'left-[-6px] top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-0',
  };

  return (
    <div className="relative inline-flex">
      <div
        ref={triggerRef}
        className="inline-flex"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
      >
        {children}
      </div>

      {showTooltip && (
        <div
          ref={tooltipRef}
          style={{
            position: 'fixed',
            top: `${coordinates.top}px`,
            left: `${coordinates.left}px`,
            zIndex: 9999,
          }}
          className={`
            fixed bg-white text-secondary text-base rounded-2xl p-2 max-w-[300px] break-words whitespace-normal
            shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)]
            font-normal
            ${className}
          `}
        >
          {content}
          <div
            className={`
              absolute w-0 h-0 
              border-solid border-white border-[8px]
              drop-shadow-sm
              ${arrowClasses[position]}
            `}
          />
        </div>
      )}
    </div>
  );
};
