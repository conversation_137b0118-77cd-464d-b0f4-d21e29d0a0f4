import React from 'react';
import { Modal } from './Modal';

interface ConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  message: string;
  confirmText?: string;
  cancelText?: string;
  destructive?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  onClose,
  onConfirm,
  message,
  confirmText = 'Yes',
  cancelText = 'No',
  destructive = false,
  size = 'medium',
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      size={size}
      primaryAction={{
        content: confirmText,
        onAction: onConfirm,
        destructive: destructive,
      }}
      secondaryActions={[
        {
          content: cancelText,
          onAction: onClose,
        },
      ]}
    >
      <Modal.Section>
        <p className="text-text-dark text-base font-medium">{message}</p>
      </Modal.Section>
    </Modal>
  );
};
