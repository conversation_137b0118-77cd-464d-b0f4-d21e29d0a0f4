import React from 'react';
import { Modal } from './Modal';

interface AlertModalProps {
  open: boolean;
  onClose: () => void;
  message: string;
  buttonText?: string;
  destructive?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export const AlertModal: React.FC<AlertModalProps> = ({
  open,
  onClose,
  message,
  buttonText = 'OK',
  destructive = false,
  size = 'medium',
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      size={size}
      primaryAction={{
        content: buttonText,
        onAction: onClose,
        destructive: destructive,
      }}
    >
      <Modal.Section>
        <p className="text-text-dark text-base font-medium">{message}</p>
      </Modal.Section>
    </Modal>
  );
};
