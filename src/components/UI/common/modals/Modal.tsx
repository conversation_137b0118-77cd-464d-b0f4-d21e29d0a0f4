import React, { useEffect, useRef } from 'react';
import { Button } from '../Button';

export interface ModalAction {
  content: string;
  onAction: () => void;
  destructive?: boolean;
  primary?: boolean;
  loading?: boolean;
  disabled?: boolean;
}

export interface ModalProps {
  open: boolean;
  onClose: () => void;
  children: React.ReactNode;
  primaryAction?: ModalAction;
  secondaryActions?: ModalAction[];
  size?: 'small' | 'medium' | 'large';
}

interface ModalSectionProps {
  children: React.ReactNode;
}

interface ModalComponent extends React.FC<ModalProps> {
  Section: React.FC<ModalSectionProps>;
}

export const Modal: ModalComponent = ({
  open,
  onClose,
  children,
  primaryAction,
  secondaryActions = [],
  size = 'medium',
}: ModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && open) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [open, onClose]);

  // Handle clicking outside to close modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node) && open) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open, onClose]);

  // Prevent body scrolling when modal is open
  useEffect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [open]);

  if (!open) return null;

  // Determine width based on size prop
  const sizeClass = {
    small: 'max-w-md',
    medium: 'max-w-2xl',
    large: 'max-w-4xl',
  }[size];

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen p-4 text-center">
        {/* Backdrop */}
        <div
          className="fixed inset-0 transition-opacity bg-black bg-opacity-30 backdrop-blur-sm"
          aria-hidden="true"
        />

        {/* Modal */}
        <div
          ref={modalRef}
          className={`${sizeClass} w-full p-6 bg-white rounded-lg shadow-xl transform transition-all animate-[fadeIn_0.2s_ease-in-out] relative`}
        >
          {/* Content */}
          <div className="mb-2">{children}</div>

          {/* Footer with actions */}
          {(primaryAction || secondaryActions.length > 0) && (
            <div className="flex flex-wrap justify-center gap-2">
              {primaryAction && (
                <Button
                  onClick={primaryAction.onAction}
                  primary={!primaryAction.destructive}
                  destructive={primaryAction.destructive}
                  disabled={primaryAction.disabled}
                  loading={primaryAction.loading}
                  className="w-32"
                >
                  {primaryAction.content}
                </Button>
              )}

              {secondaryActions.map((action, index) => (
                <Button
                  key={`secondary-${index}`}
                  onClick={action.onAction}
                  destructive={action.destructive}
                  disabled={action.disabled}
                  loading={action.loading}
                  className="w-32"
                >
                  {action.content}
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Add Section component
Modal.Section = ({ children }: ModalSectionProps) => <div className="pb-5">{children}</div>;
Modal.Section.displayName = 'ModalSection';
