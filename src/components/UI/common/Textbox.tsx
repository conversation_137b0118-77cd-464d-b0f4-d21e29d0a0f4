import React, { useState, useRef, useEffect, useCallback } from 'react';
import { TextField } from './TextField';

interface TextboxProps {
  type?: string;
  label?: string;
  value: string;
  onChange: (value: string) => void;
  maxLength?: number;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
  suffix?: React.ReactNode;
  autocompleteParams?: string[];
}

export const Textbox = ({
  type,
  label,
  value,
  onChange,
  maxLength,
  placeholder,
  error,
  disabled = false,
  className,
  suffix = null,
  autocompleteParams = [],
}: TextboxProps) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0);
  const [triggerPosition, setTriggerPosition] = useState(-1);
  const [paramError, setParamError] = useState<string | undefined>();
  const [shouldShowError, setShouldShowError] = useState(false);
  const suggestionsRef = useRef<HTMLDivElement | null>(null);
  const wrapperRef = useRef<HTMLDivElement | null>(null);

  // Validate parameters in text
  const validateParameters = useCallback((text: string): string | undefined => {
    const stack: number[] = [];

    for (let i = 0; i < text.length; i++) {
      if (text[i] === '{') {
        stack.push(i);
      } else if (text[i] === '}') {
        if (stack.length === 0) {
          return 'Please close the parentheses with "{".';
        }
        stack.pop();
      }
    }

    if (stack.length > 0) {
      return 'Please close the parentheses with "{".';
    }

    return undefined;
  }, []);

  // Handle outside clicks to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !wrapperRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
        setTriggerPosition(-1);
        // Don't validate immediately on click outside
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Reset selected index when suggestions are shown
  useEffect(() => {
    if (showSuggestions) {
      setSelectedSuggestionIndex(0);
    } else {
      setTriggerPosition(-1);
    }
  }, [showSuggestions]);

  // Handle text input events
  const handleTextChange = useCallback(
    (newValue: string) => {
      // Don't set the error during typing, just update the value
      setShouldShowError(false);
      onChange(newValue);

      // Check for autocomplete trigger
      const lastChar = newValue.slice(-1);
      if (lastChar === '{') {
        setShowSuggestions(true);
        setTriggerPosition(newValue.length - 1);
        setCursorPosition(newValue.length); // Update cursor position to end of input
      }
    },
    [onChange]
  );

  // Handle blur event to validate and show errors
  const handleBlur = useCallback(() => {
    // Use timeout to prevent error display when selecting a parameter
    setTimeout(() => {
      // Only validate if suggestions aren't showing (user has actually left the input)
      if (!showSuggestions) {
        const errorMessage = validateParameters(value);
        setParamError(errorMessage);
        setShouldShowError(true);
      }
    }, 200); // 200ms delay to allow parameter selection to complete
  }, [validateParameters, value, showSuggestions]);

  // Helper function to calculate effective length excluding parameters
  const getEffectiveLength = useCallback((text: string) => {
    // Replace all instances of {...} with a single character placeholder
    const textWithoutParams = text.replace(/{[^}]*}/g, '•');
    return textWithoutParams.length;
  }, []);

  // Handle click events
  const handleClick = useCallback((e: React.MouseEvent) => {
    const input = e.target as HTMLInputElement;
    setCursorPosition(input.selectionStart || 0);
  }, []);

  // Insert parameter at cursor position
  const insertParameter = useCallback(
    (param: string) => {
      let newValue = '';

      if (triggerPosition >= 0) {
        // If the cursor is still directly after the trigger {
        if (cursorPosition === triggerPosition + 1) {
          // We need to replace the entire trigger (just {)
          newValue =
            value.substring(0, triggerPosition) +
            '{' +
            param +
            '}' +
            value.substring(triggerPosition + 1); // Skip {
        } else {
          // The cursor has moved, handle carefully
          newValue =
            value.substring(0, triggerPosition) +
            '{' +
            param +
            '}' +
            value.substring(cursorPosition);
        }
      } else {
        // Fallback to searching for the trigger
        const triggerPos = value.lastIndexOf('{', cursorPosition);

        if (triggerPos !== -1) {
          // Check if cursor is right after the {
          if (cursorPosition === triggerPos + 1) {
            newValue =
              value.substring(0, triggerPos) + '{' + param + '}' + value.substring(triggerPos + 1); // Skip {
          } else {
            newValue =
              value.substring(0, triggerPos) + '{' + param + '}' + value.substring(cursorPosition);
          }
        } else {
          // Last resort fallback
          newValue =
            value.substring(0, cursorPosition) +
            '{' +
            param +
            '}' +
            value.substring(cursorPosition);
        }
      }

      onChange(newValue);

      // Hide suggestions, but don't validate immediately
      setShowSuggestions(false);

      // Use timeout to delay trigger position reset and error validation
      setTimeout(() => {
        setTriggerPosition(-1);

        // Only validate and show errors after a slight delay
        // This gives time for the component to stabilize
        const errorMessage = validateParameters(newValue);
        setParamError(errorMessage);
        setShouldShowError(true);
      }, 300);
    },
    [value, cursorPosition, triggerPosition, onChange, validateParameters]
  );

  // Handle key events
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      // Update cursor position for all key events
      const input = e.target as HTMLInputElement;
      setCursorPosition(input.selectionStart || 0);

      if (!showSuggestions) {
        return;
      }

      switch (e.key) {
        case 'Escape':
          setShowSuggestions(false);
          break;
        case 'ArrowDown':
          e.preventDefault(); // Prevent cursor movement
          setSelectedSuggestionIndex(prev =>
            prev < autocompleteParams.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault(); // Prevent cursor movement
          setSelectedSuggestionIndex(prev => (prev > 0 ? prev - 1 : 0));
          break;
        case 'Enter':
          if (showSuggestions && autocompleteParams.length > 0) {
            e.preventDefault(); // Prevent form submission
            insertParameter(autocompleteParams[selectedSuggestionIndex]);
          }
          break;
        // We don't need the default case because we already updated cursor position at the beginning
      }
    },
    [showSuggestions, autocompleteParams, selectedSuggestionIndex, insertParameter]
  );

  // Calculate the effective maxLength considering parameters
  const effectiveMaxLength = maxLength
    ? maxLength + (value.length - getEffectiveLength(value))
    : undefined;

  // Position the suggestions dropdown
  const positionSuggestions = useCallback(() => {
    if (wrapperRef.current) {
      const rect = wrapperRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const spaceBelow = viewportHeight - rect.bottom;
      const dropdownHeight = Math.min(autocompleteParams.length * 34, 200); // Approximate height

      // If not enough space below, position above
      if (spaceBelow < dropdownHeight && rect.top > dropdownHeight) {
        return {
          bottom: `${viewportHeight - rect.top - 24}px`,
          left: `${rect.left}px`,
          width: `${rect.width}px`,
        };
      }

      // Default: position below
      return {
        top: `${rect.bottom - 8}px`,
        left: `${rect.left}px`,
        width: `${rect.width}px`,
      };
    }
    return {};
  }, [autocompleteParams.length]);

  // Update position when scrolling or resizing
  useEffect(() => {
    if (!showSuggestions) return;

    // Create a function to force re-rendering of component
    const handlePositionUpdate = () => {
      // Use forceUpdate pattern without toggling the dropdown visibility
      setShouldShowError(prev => prev); // This is a harmless state update to trigger re-render
    };

    window.addEventListener('scroll', handlePositionUpdate, true);
    window.addEventListener('resize', handlePositionUpdate);

    return () => {
      window.removeEventListener('scroll', handlePositionUpdate, true);
      window.removeEventListener('resize', handlePositionUpdate);
    };
  }, [showSuggestions]);

  return (
    <div
      className={className}
      style={{ position: 'relative' }}
      ref={wrapperRef}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
    >
      <TextField
        type={type}
        label={label}
        value={value}
        onChange={handleTextChange}
        onBlur={handleBlur}
        maxLength={effectiveMaxLength}
        placeholder={placeholder}
        error={shouldShowError ? paramError || error : error}
        disabled={disabled}
        autoComplete="off"
        suffix={suffix}
      />

      {showSuggestions && autocompleteParams.length > 0 && (
        <div
          ref={suggestionsRef}
          style={{
            position: 'fixed',
            maxHeight: '200px',
            overflowY: 'auto',
            borderRadius: '15px',
            background: '#FFF',
            boxShadow: '0px 0px 24px 0px rgba(0, 0, 0, 0.20)',
            zIndex: 9999,
            ...positionSuggestions(),
          }}
          className="w-64"
        >
          {autocompleteParams.map((param, index) => (
            <button
              key={index}
              className={`block w-full text-left px-4 py-2 ${
                selectedSuggestionIndex === index ? 'bg-blue-100 font-medium' : 'hover:bg-gray-100'
              }`}
              onMouseDown={e => {
                // Use onMouseDown instead of onClick to prevent blur event
                e.preventDefault();
                e.stopPropagation();
                insertParameter(param);
              }}
              onMouseEnter={() => setSelectedSuggestionIndex(index)}
            >
              {param}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
