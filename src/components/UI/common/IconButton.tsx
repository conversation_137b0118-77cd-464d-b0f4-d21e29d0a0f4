import React, { ReactNode } from 'react';

export interface IconButtonProps {
  onClick: () => void;
  children: ReactNode;
  className?: string;
  ariaLabel?: string;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'primary' | 'secondary' | 'ghost';
}

export const IconButton: React.FC<IconButtonProps> = ({
  onClick,
  children,
  className = '',
  ariaLabel,
  disabled = false,
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      aria-label={ariaLabel}
      disabled={disabled}
      className={`
        flex items-center justify-center
        rounded-2xl
        font-bold
        w-11 h-11
        bg-white
        transition-all duration-200
        shadow-md
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-gray-50 hover:shadow-lg active:bg-gray-100 active:shadow-inner active:scale-95'}
        ${className}
      `}
    >
      {children}
    </button>
  );
};
