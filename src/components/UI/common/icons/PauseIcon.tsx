import React from 'react';
import PropTypes from 'prop-types';

export const PauseIcon = ({ size = 20, color = '#7C3AED' }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="5" y="4" width="3" height="12" rx="1" fill={color} />
    <rect x="12" y="4" width="3" height="12" rx="1" fill={color} />
  </svg>
);

PauseIcon.propTypes = {
  size: PropTypes.number,
  color: PropTypes.string,
};
