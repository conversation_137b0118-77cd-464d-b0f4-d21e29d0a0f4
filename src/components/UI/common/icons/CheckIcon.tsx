import React from 'react';

interface CheckIconProps {
  className?: string;
  size?: number;
  color?: string;
}

export const CheckIcon: React.FC<CheckIconProps> = ({
  className = '',
  size = 16,
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M13.3334 4L6.00008 11.3333L2.66675 8"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CheckIcon;
