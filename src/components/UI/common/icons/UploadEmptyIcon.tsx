import React from 'react';

interface UploadEmptyIconProps {
  className?: string;
  size?: number;
}

export const UploadEmptyIcon: React.FC<UploadEmptyIconProps> = ({ className = '', size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 76 76"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <ellipse opacity="0.1" cx="37.7788" cy="73.2911" rx="37.7788" ry="2.26673" fill="#98A2B3" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M45.9079 13.6013C45.7923 13.6033 45.6984 13.654 45.6287 13.7464C45.5548 13.8443 45.5121 13.9878 45.5379 14.1459C45.5842 14.4291 45.8936 14.7291 46.3688 14.7578C48.0467 14.8594 49.7898 15.5289 51.2624 16.5227C52.7336 17.5157 53.9968 18.8748 54.656 20.4176C54.8406 20.8497 55.2091 21.0329 55.47 20.9903C55.6178 20.9662 55.7314 20.8856 55.7949 20.7928C55.8542 20.7059 55.8697 20.6124 55.8379 20.514C54.5538 16.5418 50.1516 13.5289 45.9079 13.6013ZM44.0465 14.3895C43.867 13.2906 44.6171 12.1119 45.8821 12.0904C50.7526 12.0073 55.7756 15.4087 57.2758 20.0492C57.6602 21.2381 56.7902 22.3058 55.7136 22.4817C54.5742 22.6678 53.6392 21.884 53.2663 21.0114C52.7483 19.799 51.7112 18.6488 50.417 17.7753C49.1242 16.9028 47.637 16.3486 46.2775 16.2662C45.2949 16.2067 44.2357 15.5477 44.0465 14.3895ZM38.2489 16.5204C30.0018 16.5204 23.1192 22.3076 21.548 29.9837C14.9875 30.6078 9.82223 36.0055 9.82223 42.6552C9.82223 44.2524 10.1204 45.7826 10.6654 47.1937C9.65215 48.8165 9.06665 50.7339 9.06665 52.788C9.06665 58.6301 13.8026 63.3661 19.6447 63.3661C25.4868 63.3661 30.2228 58.6301 30.2228 52.788C30.2228 46.9459 25.4868 42.21 19.6447 42.21C16.5148 42.21 13.7024 43.5693 11.7657 45.7299C11.4841 44.753 11.3334 43.7218 11.3334 42.6552C11.3334 36.6453 16.1481 31.7536 22.222 31.4495C22.5765 31.4318 22.8708 31.1697 22.9294 30.8196C24.1398 23.5846 30.5287 18.0315 38.2489 18.0315C46.8364 18.0315 53.7944 24.8915 53.7944 33.305V35.9765C53.7944 36.3938 54.1327 36.7321 54.55 36.7321H55.7318C60.5647 36.7321 64.4852 40.6053 64.4852 45.3267C64.4852 50.0482 60.5647 53.9214 55.7318 53.9214H48.7767C48.3594 53.9214 48.0211 54.2597 48.0211 54.677C48.0211 55.0943 48.3594 55.4326 48.7767 55.4326H55.7318C61.3752 55.4326 65.9964 50.9066 65.9964 45.3267C65.9964 39.7468 61.3752 35.2209 55.7318 35.2209H55.3056V33.305C55.3056 24.0331 47.6469 16.5204 38.2489 16.5204ZM32.1876 42.9656C30.9303 42.9656 30.2228 41.5197 30.9945 40.527L36.716 33.1667C37.321 32.3885 38.4971 32.3885 39.1021 33.1667L44.8236 40.527C45.5953 41.5197 44.8879 42.9656 43.6305 42.9656H39.798V53.3548C39.798 54.1893 39.1214 54.8659 38.2868 54.8659H37.5313C36.6967 54.8659 36.0201 54.1893 36.0201 53.3548V42.9656H32.1876ZM10.5778 52.788C10.5778 47.7805 14.6372 43.7211 19.6447 43.7211C24.6522 43.7211 28.7116 47.7805 28.7116 52.788C28.7116 57.7956 24.6522 61.855 19.6447 61.855C14.6372 61.855 10.5778 57.7956 10.5778 52.788ZM17.7558 47.8768C17.7558 46.8336 18.6015 45.9879 19.6447 45.9879C20.6879 45.9879 21.5337 46.8336 21.5337 47.8768V52.4103C21.5337 53.4535 20.6879 54.2992 19.6447 54.2992C18.6015 54.2992 17.7558 53.4535 17.7558 52.4103V47.8768ZM19.6447 47.499C19.4361 47.499 19.2669 47.6682 19.2669 47.8768V52.4103C19.2669 52.6189 19.4361 52.788 19.6447 52.788C19.8534 52.788 20.0225 52.6189 20.0225 52.4103V47.8768C20.0225 47.6682 19.8534 47.499 19.6447 47.499ZM19.6447 59.5882C18.6015 59.5882 17.7558 58.7425 17.7558 57.6993C17.7558 56.6561 18.6015 55.8104 19.6447 55.8104C20.6879 55.8104 21.5337 56.6561 21.5337 57.6993C21.5337 58.7425 20.6879 59.5882 19.6447 59.5882ZM19.2669 57.6993C19.2669 57.9079 19.4361 58.0771 19.6447 58.0771C19.8534 58.0771 20.0225 57.9079 20.0225 57.6993C20.0225 57.4906 19.8534 57.3215 19.6447 57.3215C19.4361 57.3215 19.2669 57.4906 19.2669 57.6993Z"
        fill="#856CF8"
      />
    </svg>
  );
};
