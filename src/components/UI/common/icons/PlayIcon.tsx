import React from 'react';

interface PlayIconProps {
  className?: string;
  size?: number;
  color?: string;
}

export const PlayIcon: React.FC<PlayIconProps> = ({
  className = '',
  size = 24,
  color = '#101828',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <polygon points="8,5 19,12 8,19" fill={color} />
    </svg>
  );
};
