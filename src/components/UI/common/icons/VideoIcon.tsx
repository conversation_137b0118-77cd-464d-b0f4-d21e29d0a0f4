import React from 'react';

interface VideoIconProps {
  className?: string;
  size?: number;
}

export const VideoIcon: React.FC<VideoIconProps> = ({ className = '', size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 21 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M7.54712 5.17969V10.1797"
        stroke="#101828"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
      <path
        d="M9.83991 7.28458L7.87291 5.31758C7.83018 5.27479 7.77944 5.24085 7.72359 5.2177C7.66774 5.19454 7.60787 5.18262 7.54741 5.18262C7.48694 5.18262 7.42707 5.19454 7.37122 5.2177C7.31537 5.24085 7.26463 5.27479 7.22191 5.31758L5.25391 7.28458"
        stroke="#101828"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 1H4.59501C4.12223 0.999999 3.6541 1.09325 3.21741 1.27442C2.78072 1.45558 2.38406 1.7211 2.05013 2.05578C1.7162 2.39045 1.45156 2.78771 1.27136 3.2248C1.09117 3.66189 0.998957 4.13023 1.00001 4.603V10.765C1.00001 11.7185 1.37877 12.6329 2.05296 13.307C2.38679 13.6409 2.7831 13.9057 3.21926 14.0863C3.65543 14.267 4.12291 14.36 4.59501 14.36H10.5C11.4535 14.36 12.3679 13.9812 13.0421 13.307C13.7163 12.6329 14.095 11.7185 14.095 10.765V4.604C14.0963 4.1311 14.0043 3.6626 13.8242 3.22533C13.6441 2.78807 13.3795 2.39064 13.0455 2.05584C12.7116 1.72103 12.3148 1.45542 11.878 1.27424C11.4412 1.09306 10.9729 0.999867 10.5 1ZM20 5.118V10.253C20 10.503 19.929 10.749 19.795 10.961C19.6593 11.1736 19.4671 11.3443 19.24 11.454C19.0144 11.5669 18.7603 11.6101 18.51 11.578C18.2633 11.5484 18.0293 11.4523 17.833 11.3L14.608 8.712C14.4531 8.58453 14.3279 8.42482 14.241 8.244C14.1541 8.06318 14.1077 7.86559 14.105 7.665C14.105 7.465 14.15 7.269 14.238 7.09C14.33 6.922 14.456 6.775 14.608 6.658L17.833 4.091C18.0293 3.93805 18.2638 3.84189 18.511 3.813C18.761 3.781 19.015 3.824 19.24 3.937C19.4643 4.04334 19.6543 4.2103 19.7886 4.41901C19.9229 4.62772 19.9961 4.86984 20 5.118Z"
        stroke="#101828"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
