import React from 'react';

interface ChevronIconProps {
  isOpen?: boolean;
  className?: string;
}

export const ChevronIcon: React.FC<ChevronIconProps> = ({ isOpen = false, className = '' }) => {
  return (
    <svg
      width="10"
      height="6"
      viewBox="0 0 10 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`transition-transform ${isOpen ? 'rotate-180' : ''} ${className}`}
    >
      <path
        d="M1 1L5 5L9 1"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
