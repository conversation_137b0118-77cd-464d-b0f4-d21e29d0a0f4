import React from 'react';

interface TrashIconProps {
  className?: string;
  size?: number;
  color?: string;
}

const TrashIcon: React.FC<TrashIconProps> = ({ className = '', size = 24, color = '#E11D48' }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect x="5" y="7" width="14" height="12" rx="2" stroke={color} strokeWidth="2" />
      <path d="M3 7h18" stroke={color} strokeWidth="2" strokeLinecap="round" />
      <path d="M10 11v4" stroke={color} strokeWidth="2" strokeLinecap="round" />
      <path d="M14 11v4" stroke={color} strokeWidth="2" strokeLinecap="round" />
      <path d="M9 7V5a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2" stroke={color} strokeWidth="2" />
    </svg>
  );
};

export default TrashIcon;
export { TrashIcon };
