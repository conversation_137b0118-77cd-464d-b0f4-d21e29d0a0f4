import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Dropdown } from './Dropdown';
import { GroupLabel } from './GroupLabel';
import { NudgeType } from '../PersonalizationNudge';
import { CartAbandonmentNudgeData } from '../../../types/CartAbandonmentTypes';
import { DiscountNudgeData } from '../../../types/DiscountNudgeTypes';
import { PickUpWhereYouLeftOffNudgeData } from '../../../types/PickUpWhereYouLeftOffTypes';
import { SocialMediaContentNudgeData } from '../../../types/SocialMediaContentTypes';
import { NavigationNudgeData } from '../../../types/NavigationNudgeTypes';
import { SavingsNudgeData } from '../../../types/SavingsNudgeTypes';
import { NotFoundIcon } from './icons/NotFoundIcon';
import { Device<PERSON>rameset } from 'react-device-frameset';
import 'react-device-frameset/styles/marvel-devices.min.css';

declare global {
  interface Window {
    vandraComponents?: {
      CartAbandonment?: any;
      DiscountNudge?: any;
      PickUpWhereYouLeftOff?: any;
      SocialMediaContent?: any;
      NavigationNudge?: any;
      SavingsNudge?: any;
      mount?: (
        component: any,
        options: {
          target: HTMLElement;
          props: any;
        }
      ) => {
        $destroy: () => void;
      };
    };
  }
}

interface NudgePreviewProps {
  nudgeType: NudgeType;
  data:
    | CartAbandonmentNudgeData
    | DiscountNudgeData
    | PickUpWhereYouLeftOffNudgeData
    | SocialMediaContentNudgeData
    | NavigationNudgeData
    | SavingsNudgeData;
  dropdownOptions?: { label: string; value: string }[];
  selectedOption?: string;
  onSelectChange?: (value: string) => void;
  paramOptions?: Record<string, string>;
  previewVideoUrl?: string;
}

export const NudgePreview: React.FC<NudgePreviewProps> = ({
  nudgeType,
  data,
  dropdownOptions,
  selectedOption,
  onSelectChange,
  paramOptions = [],
  previewVideoUrl,
}) => {
  // Fallback for previewVideoUrl for socialMediaContent
  let effectivePreviewVideoUrl = previewVideoUrl;
  if (
    nudgeType === 'socialMediaContent' &&
    (!previewVideoUrl || previewVideoUrl.length === 0) &&
    (data as SocialMediaContentNudgeData).defaultPreviewVideo
  ) {
    effectivePreviewVideoUrl = (data as SocialMediaContentNudgeData).defaultPreviewVideo;
  }

  const [selected, setSelected] = useState(selectedOption);
  const svelteContainerRef = useRef<HTMLDivElement>(null);
  const svelteInstanceRef = useRef<any>(null);
  const componentTypeRef = useRef<string>(nudgeType);
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoomLevel, setZoomLevel] = useState<number>(0.6);

  // Update selected if the prop changes
  useEffect(() => {
    setSelected(selectedOption);
  }, [selectedOption]);

  const replaceParamValuesInText = useCallback(
    (text: string) => {
      // Replace all params values in the text with the actual values
      return text.replace(/{[^}]*}/g, match => {
        const param = match.slice(1, -1);
        // Add type guard to safely access paramOptions
        if (Array.isArray(paramOptions)) {
          return 'N/A';
        } else {
          return paramOptions[param] || 'N/A';
        }
      });
    },
    [paramOptions]
  );

  // Helper function to show fallback message
  const showFallbackMessage = (componentType?: string) => {
    // Show fallback preview
    const fallbackPreview = document.getElementById('fallback-preview');
    if (fallbackPreview) {
      fallbackPreview.style.display = 'block';

      // Set component type data attribute if provided
      if (componentType) {
        fallbackPreview.setAttribute('data-component-type', componentType);

        // Update the message text
        const messageElement = fallbackPreview.querySelector('[data-message-component]');
        if (messageElement) {
          messageElement.textContent = `The ${componentType} component could not be loaded.`;
        }
      }
    }

    // Hide Svelte container
    if (svelteContainerRef.current) {
      svelteContainerRef.current.style.display = 'none';
    }
  };

  // Load Svelte component dynamically
  useEffect(() => {
    const loadSvelteComponent = async () => {
      try {
        // Get base URL from environment variable
        const componentsBaseUrl =
          process.env.REACT_APP_VANDRA_COMPONENTS_URL || 'http://localhost:9292/assets/';

        // Dynamically load CSS
        if (!document.getElementById('vandra-components-css')) {
          const link = document.createElement('link');
          link.id = 'vandra-components-css';
          link.rel = 'stylesheet';
          link.type = 'text/css';
          link.href = `${componentsBaseUrl}/vandra-components.css`;
          document.head.appendChild(link);
        }

        // Dynamically load JS
        if (!window.vandraComponents) {
          const script = document.createElement('script');
          script.src = `${componentsBaseUrl}/vandra-components.iife.js`;
          script.async = true;

          // Wait for script to load
          await new Promise((resolve, reject) => {
            script.onload = resolve;
            script.onerror = reject;
            document.body.appendChild(script);
          });
        }

        // Reset display
        const fallbackPreview = document.getElementById('fallback-preview');
        if (fallbackPreview) {
          fallbackPreview.style.display = 'none';
        }

        if (svelteContainerRef.current) {
          svelteContainerRef.current.style.display = 'block';
          // Only clear the container if we're mounting a new component
          if (componentTypeRef.current !== nudgeType) {
            svelteContainerRef.current.innerHTML = '';
          }
        }

        // Ensure the container is available and the script is loaded
        if (svelteContainerRef.current && window.vandraComponents) {
          let componentLoaded = false;

          // If component type changed or no instance exists, create a new one
          const needsNewInstance =
            componentTypeRef.current !== nudgeType || !svelteInstanceRef.current;

          // If we need to create a new instance, destroy the old one first
          if (needsNewInstance && svelteInstanceRef.current) {
            if (typeof svelteInstanceRef.current.$destroy === 'function') {
              svelteInstanceRef.current.$destroy();
              svelteInstanceRef.current = null;
            }
          }

          // Update the component type reference
          componentTypeRef.current = nudgeType;

          switch (nudgeType) {
            case 'cartAbandonment': {
              const cartAbandonmentData = data as CartAbandonmentNudgeData;
              const hasDiscount = selected === 'savings';

              const props = {
                primaryColor: cartAbandonmentData.buttonAndTextColor || '#8F0067',
                storeAdImage: cartAbandonmentData.storeAdImage || '',
                hasDiscount,
                discountTotal: hasDiscount ? 10000 : 0,
                cartType: 'returning',
                dwellTimeStartCounter: Date.now(),
                pageViewId: 'preview',
                inSessionCartAbandonmentHeading1: replaceParamValuesInText(
                  cartAbandonmentData.group1Line1 || "Don't leave your"
                ),
                inSessionCartAbandonmentHeading2: replaceParamValuesInText(
                  cartAbandonmentData.group1Line2 || 'cart hanging!'
                ),
                savingsCartAbandonmentHeading1: replaceParamValuesInText(
                  cartAbandonmentData.group2Line1 || 'You have {savings} in'
                ),
                savingsCartAbandonmentHeading2: replaceParamValuesInText(
                  cartAbandonmentData.group2Line2 || 'savings in your cart!'
                ),
                storeFont: cartAbandonmentData.fontName || 'Arial',
                isPreview: true,
                animate: false,
              };

              if (window.vandraComponents?.CartAbandonment) {
                if (needsNewInstance) {
                  // Create new instance
                  svelteInstanceRef.current = window.vandraComponents?.mount?.(
                    window.vandraComponents?.CartAbandonment,
                    {
                      target: svelteContainerRef.current,
                      props,
                    }
                  );
                } else if (svelteInstanceRef.current?.updateProps) {
                  // Update existing instance
                  svelteInstanceRef.current.updateProps(props);
                }
                componentLoaded = true;
              }
              break;
            }
            case 'inSessionCartAbandonment': {
              const cartAbandonmentData = data as CartAbandonmentNudgeData;
              const hasDiscount = selected === 'savings';

              const props = {
                primaryColor: cartAbandonmentData.buttonAndTextColor || '#0067A3',
                storeAdImage: cartAbandonmentData.storeAdImage || '',
                hasDiscount,
                discountTotal: hasDiscount ? 10000 : 0,
                cartType: 'inSession',
                dwellTimeStartCounter: Date.now(),
                pageViewId: 'preview',
                inSessionCartAbandonmentHeading1: replaceParamValuesInText(
                  cartAbandonmentData.group1Line1 || "Don't abandon your"
                ),
                inSessionCartAbandonmentHeading2: replaceParamValuesInText(
                  cartAbandonmentData.group1Line2 || 'shopping cart!'
                ),
                savingsCartAbandonmentHeading1: replaceParamValuesInText(
                  cartAbandonmentData.group2Line1 || 'You have {savings} in'
                ),
                savingsCartAbandonmentHeading2: replaceParamValuesInText(
                  cartAbandonmentData.group2Line2 || 'savings in your cart!'
                ),
                storeFont: cartAbandonmentData.fontName || 'Arial',
                isPreview: true,
                animate: false,
              };

              if (window.vandraComponents?.CartAbandonment) {
                if (needsNewInstance) {
                  // Create new instance
                  svelteInstanceRef.current = window.vandraComponents?.mount?.(
                    window.vandraComponents?.CartAbandonment,
                    {
                      target: svelteContainerRef.current,
                      props,
                    }
                  );
                } else if (svelteInstanceRef.current?.updateProps) {
                  // Update existing instance
                  svelteInstanceRef.current.updateProps(props);
                }
                componentLoaded = true;
              }
              break;
            }
            case 'discount': {
              const discountData = data as DiscountNudgeData;

              const props = {
                isPreview: true,
                discount_code: discountData.discountPrefix + '-12345',
                discount_rate: discountData.discountPercentage,
                discount_ends_at_time: discountData.expiryDays,
                tabText: discountData.minimizedTabText,
                expandedHeadline: discountData.headerText,
                expandedHeadlineAuto: discountData.headerText,
                expandedBody: discountData.bodyText,
                buttonText: discountData.buttonText,
                font: discountData.fontName,
                primaryColor: discountData.primaryColor,
                backgroundColor: discountData.backgroundColor,
                tabDisabled: discountData.tabDisabled,
              };

              if (window.vandraComponents?.DiscountNudge) {
                if (needsNewInstance) {
                  // Create new instance
                  svelteInstanceRef.current = window.vandraComponents?.mount?.(
                    window.vandraComponents?.DiscountNudge,
                    {
                      target: svelteContainerRef.current,
                      props,
                    }
                  );
                } else if (svelteInstanceRef.current?.updateProps) {
                  // Update existing instance
                  svelteInstanceRef.current.updateProps(props);
                }
                componentLoaded = true;
              }
              break;
            }
            case 'pickUpWhereYouLeftOff': {
              const pickUpData = data as PickUpWhereYouLeftOffNudgeData;

              const props = {
                primaryColor: pickUpData.primaryColor || '#8F0067',
                backgroundColor: '#FFFFFF',
                headlineLine1: replaceParamValuesInText(
                  pickUpData.headlineLine1 || 'Pick up where you'
                ),
                headlineLine2: replaceParamValuesInText(pickUpData.headlineLine2 || 'left off'),
                fontName: pickUpData.fontName || 'Arial',
                isPreview: true,
                animate: false,
                items: pickUpData.items,
              };

              if (window.vandraComponents?.PickUpWhereYouLeftOff) {
                if (needsNewInstance) {
                  // Create new instance
                  svelteInstanceRef.current = window.vandraComponents?.mount?.(
                    window.vandraComponents?.PickUpWhereYouLeftOff,
                    {
                      target: svelteContainerRef.current,
                      props,
                    }
                  );
                } else if (svelteInstanceRef.current?.updateProps) {
                  // Update existing instance
                  svelteInstanceRef.current.updateProps(props);
                }
                componentLoaded = true;
              }
              break;
            }
            case 'socialMediaContent': {
              const socialMediaData = data as SocialMediaContentNudgeData;

              const props = {
                videoUrl: effectivePreviewVideoUrl || '',
                videoSelectionMethod: socialMediaData.videoSelectionMethod || '',
                isPreview: true,
                animate: true,
                productData: { productHandle: '', productId: '', variantId: '' },
                pageViewId: 'preview',
                dwellTimeStartCounter: Date.now(),
              };

              if (window.vandraComponents?.SocialMediaContent) {
                if (needsNewInstance) {
                  // Create new instance
                  svelteInstanceRef.current = window.vandraComponents?.mount?.(
                    window.vandraComponents?.SocialMediaContent,
                    {
                      target: svelteContainerRef.current,
                      props,
                    }
                  );
                } else if (svelteInstanceRef.current?.updateProps) {
                  // Update existing instance
                  svelteInstanceRef.current.updateProps(props);
                }
                componentLoaded = true;
              }
              break;
            }
            case 'navigation': {
              const navigationData = data as NavigationNudgeData;

              const props = {
                headline: navigationData.headline || 'Sample Headline',
                subheader: navigationData.subheader || 'Sample Subheader',
                headlineColor: navigationData.headlineColor || '#000000',
                bodyTextColor: navigationData.bodyTextColor || '#000000',
                backgroundColor: navigationData.backgroundColor || '#FFFFFF',
                itemBackgroundColor: navigationData.itemBackgroundColor || '#F3F4F6',
                font: navigationData.fontName || 'Arial',
                timeDelay: navigationData.timeDelay || 15,
                answerOptions: navigationData.answerOptions || [
                  { label: 'Dog 🐶', url: '/page1' },
                  { label: 'Cat 🐈', url: '/page2' },
                  { label: 'Both', url: '/page3' },
                ],
                isPreview: true,
                animate: false,
                pageViewId: 'preview',
                show: true,
              };

              if (window.vandraComponents?.NavigationNudge) {
                if (needsNewInstance) {
                  // Create new instance
                  svelteInstanceRef.current = window.vandraComponents?.mount?.(
                    window.vandraComponents?.NavigationNudge,
                    {
                      target: svelteContainerRef.current,
                      props,
                    }
                  );
                } else if (svelteInstanceRef.current?.updateProps) {
                  // Update existing instance
                  svelteInstanceRef.current.updateProps(props);
                }
                componentLoaded = true;
              }
              break;
            }
            case 'savings': {
              const savingsData = data as SavingsNudgeData;

              // Safely extract total_savings parameter
              const totalSavings =
                typeof paramOptions === 'object' && !Array.isArray(paramOptions)
                  ? paramOptions['total_savings'] || '$45.00'
                  : '$45.00';

              const props = {
                tabColor: savingsData.tabColor || '#2B3336',
                tabFontColor: savingsData.tabFontColor || '#FFFFFF',
                tabText: savingsData.tabText || 'Savings: {total_savings}',
                primaryColor: savingsData.primaryColor || '#232323',
                primaryFontColor: savingsData.primaryFontColor || '#232323',
                backgroundColor: savingsData.backgroundColor || '#FFFFFF',
                font: savingsData.font || 'Inter',
                expandedHeadline:
                  savingsData.expandedHeadline || 'Lucky you… {total_savings} in savings!',
                expandedBody:
                  savingsData.expandedBody ||
                  "When you're ready, head to checkout and lock in these savings.",
                buttonText: savingsData.buttonText || 'Checkout',
                buttonDestination: savingsData.buttonDestination || '/checkout',
                positioning: savingsData.positioning || 'Right',
                anchor: savingsData.anchor || 'Top',
                distanceFromAnchor: savingsData.distanceFromAnchor || 30,
                total_savings: totalSavings,
                pageViewId: 'preview',
                dwellTimeStartCounter: Date.now(),
                isPreview: true,
                animate: false,
                minimized: true,
              };

              if (window.vandraComponents?.SavingsNudge) {
                if (needsNewInstance) {
                  // Create new instance
                  svelteInstanceRef.current = window.vandraComponents?.mount?.(
                    window.vandraComponents?.SavingsNudge,
                    {
                      target: svelteContainerRef.current,
                      props,
                    }
                  );
                } else if (svelteInstanceRef.current?.updateProps) {
                  // Update existing instance
                  svelteInstanceRef.current.updateProps(props);
                }
                componentLoaded = true;
              }
              break;
            }
            default:
              console.error('Unsupported nudge type');
              break;
          }

          if (!componentLoaded) {
            console.error('Component not found');
            showFallbackMessage(nudgeType);
          }
        } else {
          showFallbackMessage(nudgeType);
        }
      } catch (error) {
        console.error('Failed to load Svelte component:', error);
        showFallbackMessage(nudgeType);
      }
    };

    loadSvelteComponent().catch(error => {
      console.error('Error in Svelte component loading:', error);
      showFallbackMessage(nudgeType);
    });

    // Cleanup function
    return () => {
      if (svelteInstanceRef.current && typeof svelteInstanceRef.current.$destroy === 'function') {
        svelteInstanceRef.current.$destroy();
      }
    };
  }, [selected, data, replaceParamValuesInText, nudgeType, paramOptions, effectivePreviewVideoUrl]);

  // Calculate optimal zoom level based on container size
  useEffect(() => {
    const calculateZoom = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        const containerHeight = containerRef.current.clientHeight;

        // iPhone X dimensions: 375 x 812
        const deviceWidth = 375;
        const deviceHeight = 812;

        // Calculate zoom to fit container while maintaining aspect ratio
        const widthRatio = (containerWidth * 0.9) / deviceWidth;
        const heightRatio = (containerHeight * 0.9) / deviceHeight;

        // Use the smaller ratio to ensure device fits in container
        const newZoom = Math.min(widthRatio, heightRatio, 1);

        // Update zoom only if it's significantly different to avoid unnecessary rerenders
        if (Math.abs(newZoom - zoomLevel) > 0.05) {
          setZoomLevel(newZoom);
        }
      }
    };

    calculateZoom();

    // Add resize listener to recalculate zoom when window size changes
    window.addEventListener('resize', calculateZoom);
    return () => window.removeEventListener('resize', calculateZoom);
  }, [zoomLevel]);

  // Calculate optimal zoom level based on container size
  useEffect(() => {
    const calculateZoom = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        const containerHeight = containerRef.current.clientHeight;

        // iPhone X dimensions: 375 x 812
        const deviceWidth = 375;
        const deviceHeight = 812;

        // Calculate zoom to fit container while maintaining aspect ratio
        const widthRatio = (containerWidth * 0.9) / deviceWidth;
        const heightRatio = (containerHeight * 0.9) / deviceHeight;

        // Use the smaller ratio to ensure device fits in container
        const newZoom = Math.min(widthRatio, heightRatio, 1);

        // Update zoom only if it's significantly different to avoid unnecessary rerenders
        if (Math.abs(newZoom - zoomLevel) > 0.05) {
          setZoomLevel(newZoom);
        }
      }
    };

    calculateZoom();

    // Add resize listener to recalculate zoom when window size changes
    window.addEventListener('resize', calculateZoom);
    return () => window.removeEventListener('resize', calculateZoom);
  }, [zoomLevel]);

  const handleSelectChange = (value: string) => {
    setSelected(value);
    if (onSelectChange) {
      onSelectChange(value);
    }
  };

  const renderPreviewContent = () => (
    <>
      {/* Svelte container preview */}
      <div
        ref={svelteContainerRef}
        className="h-full w-full flex items-center justify-center will-change-transform"
        style={{
          backfaceVisibility: 'hidden',
          position: 'relative',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />
      {/* Fallback preview */}
      <div
        id="fallback-preview"
        className="absolute inset-0 flex items-center justify-center rounded-md bg-gray-50 border border-gray-200 shadow-sm"
        style={{ display: 'none' }}
      >
        <div className="px-6 py-8 text-center">
          <NotFoundIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-base font-semibold text-gray-900 mb-1">Component Not Available</h3>
          <p className="text-sm text-gray-500" data-message-component>
            The requested component could not be loaded.
          </p>
        </div>
      </div>
    </>
  );

  return (
    <div className="flex flex-col h-full items-center">
      <GroupLabel label="Preview" className="font-bold" />
      <div className="mt-1 mb-2">
        {/* Conditionally render the interaction text */}
        {nudgeType !== 'pickUpWhereYouLeftOff' &&
          nudgeType !== 'cartAbandonment' &&
          nudgeType !== 'inSessionCartAbandonment' &&
          nudgeType !== 'navigation' && (
            <span className="italic text-gray-500 text-sm">
              Click on the nudge in the preview to interact with it.
            </span>
          )}
      </div>
      {dropdownOptions && (
        <div className="mt-4 mb-4 w-60">
          <Dropdown
            options={dropdownOptions}
            value={selected || ''}
            onChange={handleSelectChange}
            className="w-full"
            placeholder="Select an option"
            selectionOnly={true}
          />
        </div>
      )}
      {/* Conditionally wrap preview in device frame */}
      <div
        ref={containerRef}
        className="p-4 relative h-full w-full flex items-center justify-center overflow-hidden"
      >
        {nudgeType === 'savings' ||
        nudgeType === 'socialMediaContent' ||
        nudgeType === 'discount' ? (
          <DeviceFrameset
            device={'iPhone X'}
            color="black"
            zoom={zoomLevel}
            style={{
              maxWidth: '90%',
              maxHeight: '90%',
              margin: 'auto',
            }}
          >
            {renderPreviewContent()}
          </DeviceFrameset>
        ) : (
          renderPreviewContent()
        )}
      </div>
    </div>
  );
};
