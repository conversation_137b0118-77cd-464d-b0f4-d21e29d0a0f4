import React from 'react';

interface NudgeHeaderProps {
  title: string;
  iconSrc: string;
  iconAlt: string;
  description: string;
}

export const NudgeHeader: React.FC<NudgeHeaderProps> = ({
  title,
  iconSrc,
  iconAlt,
  description,
}) => {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 items-center">
        <div className="text-2xl font-bold">{title}</div>
        {/* <img src={iconSrc} alt={iconAlt} className="w-10 h-10" /> */}
      </div>
      <div className="text-base text-text-dark">{description}</div>
    </div>
  );
};
