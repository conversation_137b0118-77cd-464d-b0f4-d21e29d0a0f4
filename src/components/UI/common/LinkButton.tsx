import React from 'react';

interface LinkButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  className?: string;
  color?: 'default' | 'red' | 'blue' | 'green';
  size?: 'sm' | 'md' | 'lg';
  ariaLabel?: string;
  disabled?: boolean;
}

export const LinkButton: React.FC<LinkButtonProps> = ({
  onClick,
  children,
  className = '',
  color = 'default',
  size = 'sm',
  ariaLabel,
  disabled = false,
}) => {
  const getColorClass = () => {
    switch (color) {
      case 'red':
        return 'text-red-500 hover:text-red-700';
      case 'blue':
        return 'text-blue-600 hover:text-blue-800';
      case 'green':
        return 'text-green-500 hover:text-green-700';
      default:
        return 'text-gray-500 hover:text-gray-700';
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'lg':
        return 'text-base';
      case 'md':
        return 'text-sm';
      default:
        return 'text-xs';
    }
  };

  return (
    <button
      onClick={onClick}
      className={`${getColorClass()} ${getSizeClass()} ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} ${className}`}
      aria-label={ariaLabel}
      disabled={disabled}
    >
      {children}
    </button>
  );
};
