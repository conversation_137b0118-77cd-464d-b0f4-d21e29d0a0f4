import React from 'react';

interface RadioOptionProps {
  value: string;
  label: string;
  checked?: boolean;
  onChange?: (value: string, label: string) => void;
  name?: string;
}

export const RadioOption: React.FC<RadioOptionProps> = ({
  value,
  label,
  checked,
  onChange,
  name,
}) => {
  return (
    <label className="flex items-center gap-2 cursor-pointer">
      <div className="relative flex items-center justify-center w-5 h-5">
        <input
          type="radio"
          value={value}
          checked={checked}
          onChange={() => onChange && onChange(value, label)}
          name={name}
          className="absolute opacity-0 w-5 h-5 cursor-pointer"
        />
        <div
          className={`w-5 h-5 rounded-full border ${
            checked ? 'border-purple-600 flex items-center justify-center' : 'border-gray-300'
          }`}
        >
          {checked && <div className="w-3 h-3 rounded-full bg-purple-600" />}
        </div>
      </div>
      <span>{label}</span>
    </label>
  );
};

interface RadioGroupProps {
  value: string;
  onChange: (value: string, label: string) => void;
  name: string;
  children: React.ReactNode;
  layout?: 'horizontal' | 'vertical';
  className?: string;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  value,
  onChange,
  name,
  children,
  layout = 'vertical',
  className = '',
}) => {
  // Modify children to inject props
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement<RadioOptionProps>(child) && child.type === RadioOption) {
      return React.cloneElement(child, {
        checked: child.props.value === value,
        onChange,
        name,
      });
    }
    return child;
  });

  const layoutClass = layout === 'horizontal' ? 'flex-row gap-6' : 'flex-col gap-3';

  return <div className={`flex ${layoutClass} ${className}`}>{childrenWithProps}</div>;
};
