import React, { useState, KeyboardEvent, useRef, useCallback, useEffect } from 'react';
import { Chip } from './Chip';
import { TextField } from './TextField';

interface ChipInputProps {
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  className?: string;
  validate?: (value: string) => boolean;
  maxChips?: number;
  label?: string;
  error?: string;
}

export const ChipInput: React.FC<ChipInputProps> = ({
  value = [],
  onChange,
  placeholder = '',
  className = '',
  validate = () => true,
  maxChips,
  label,
  error,
}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const inputContainerRef = useRef<HTMLDivElement>(null);

  // Add a chip based on the current input value
  const addChip = useCallback(
    (chipValue: string) => {
      // Don't add if maxChips is set and reached
      if (maxChips !== undefined && value.length >= maxChips) {
        return;
      }

      // Don't add empty values or duplicates
      if (chipValue && !value.includes(chipValue) && validate(chipValue)) {
        onChange([...value, chipValue]);
        setInputValue('');
      }
    },
    [maxChips, onChange, validate, value]
  );

  // Remove a chip
  const removeChip = useCallback(
    (index: number) => {
      const newChips = [...value];
      newChips.splice(index, 1);
      onChange(newChips);
    },
    [onChange, value]
  );

  // When pasting content, split by commas, newlines or spaces
  const handlePaste = useCallback(
    (e: React.ClipboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      e.preventDefault();
      const pastedText = e.clipboardData.getData('text');
      const values = pastedText
        .split(/[,\n\s]/)
        .map(item => item.trim())
        .filter(item => item && !value.includes(item) && validate(item));

      // Calculate how many new chips we can add
      const availableSlots = maxChips !== undefined ? maxChips - value.length : values.length;
      const newValues = values.slice(0, availableSlots);

      if (newValues.length > 0) {
        onChange([...value, ...newValues]);
      }
    },
    [maxChips, onChange, validate, value]
  );

  // Add a chip when pressing Enter or comma
  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      // Add chip on Enter or comma
      if ((e.key === 'Enter' || e.key === ',') && inputValue.trim()) {
        e.preventDefault();
        addChip(inputValue.trim());
      }
    },
    [addChip, inputValue]
  );

  // Setup event listeners for keydown and paste
  useEffect(() => {
    const handleEvents = (e: Event) => {
      const input = inputContainerRef.current?.querySelector('input');

      if (input && e.target === input) {
        if (e.type === 'keydown') {
          handleKeyDown(e as unknown as KeyboardEvent<HTMLInputElement>);
        } else if (e.type === 'paste') {
          handlePaste(e as unknown as React.ClipboardEvent<HTMLInputElement>);
        }
      }
    };

    // Add event listeners to the container
    const container = inputContainerRef.current;
    if (container) {
      container.addEventListener('keydown', handleEvents as EventListener);
      container.addEventListener('paste', handleEvents as EventListener);
    }

    // Cleanup
    return () => {
      if (container) {
        container.removeEventListener('keydown', handleEvents as EventListener);
        container.removeEventListener('paste', handleEvents as EventListener);
      }
    };
  }, [handleKeyDown, handlePaste]);

  // Custom handler for TextField
  const handleChange = (newValue: string) => {
    setInputValue(newValue);
  };

  return (
    <div className={`w-full ${className}`} ref={inputContainerRef}>
      <TextField
        label={label}
        value={inputValue}
        onChange={handleChange}
        placeholder={placeholder}
        error={error}
        suffix={
          maxChips !== undefined && (
            <span className="text-xs text-gray-500">
              {value.length}/{maxChips}
            </span>
          )
        }
      />

      {value.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-2">
          {value.map((item, index) => (
            <Chip key={index} label={item} onDelete={() => removeChip(index)} variant="default" />
          ))}
        </div>
      )}
    </div>
  );
};
