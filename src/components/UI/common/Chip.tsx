import React from 'react';

interface ChipProps {
  label: string;
  onDelete?: () => void;
  className?: string;
  variant?: 'default' | 'primary' | 'secondary';
  disabled?: boolean;
}

export const Chip: React.FC<ChipProps> = ({
  label,
  onDelete,
  className = '',
  variant = 'default',
  disabled = false,
}) => {
  // Base styles for the chip
  const baseStyles =
    'inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-medium transition-all duration-200';

  // Variant styles matching app's design system
  const variantStyles = {
    default:
      'bg-white text-text-light border border-border hover:bg-bg-hover hover:text-text-light hover:border-border-hover',
    primary: 'bg-[#856CF8] text-white border-[#856CF8] hover:bg-[#6B4FF7] hover:border-[#6B4FF7]',
    secondary:
      'bg-white text-text-light border border-border hover:bg-bg-hover hover:text-text-light hover:border-border-hover',
  }[variant];

  // Disabled styles matching app's disabled state
  const disabledStyles = disabled
    ? 'bg-[#F9FAFB] !text-text-dark border-border cursor-not-allowed'
    : '';

  // Delete button styles matching app's interactive elements
  const deleteButtonStyles = disabled
    ? 'text-text-dark cursor-not-allowed'
    : 'text-text-light hover:text-text-dark focus:outline-none focus:ring-2 focus:ring-border rounded-md p-0.5 transition-colors duration-200';

  return (
    <div
      className={`${baseStyles} ${variantStyles} ${disabledStyles} ${className}`}
      role="listitem"
    >
      <span className="mr-1.5">{label}</span>
      {onDelete && !disabled && (
        <button
          onClick={onDelete}
          className={`ml-0.5 ${deleteButtonStyles}`}
          aria-label={`Remove ${label}`}
          type="button"
        >
          ×
        </button>
      )}
    </div>
  );
};
