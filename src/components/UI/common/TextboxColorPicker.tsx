import React from 'react';
import { useState, useCallback, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { TextField } from './TextField';

interface TextboxColorPickerProps {
  color: string; // Hex color string (e.g. "#8F0067")
  onChange: (color: string) => void; // Returns hex color string
  className?: string;
  forceFullOpacity?: boolean; // When true, alpha is read-only and fixed at 100%
}

// Custom color picker types - replacing Polaris HSBAColor
interface HSBAColor {
  hue: number;
  saturation: number;
  brightness: number;
  alpha: number;
}

// Simple custom Popover component that uses a portal
const PopoverPortal = ({
  active,
  activator,
  onClose,
  children,
}: {
  active: boolean;
  activator: React.ReactNode;
  onClose: () => void;
  children: React.ReactNode;
}) => {
  const triggerRef = useRef<HTMLDivElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);
  const [portalContainer] = useState(() => document.createElement('div'));
  const [triggerRect, setTriggerRect] = useState<DOMRect | null>(null);

  // Position the popover above the trigger
  useEffect(() => {
    if (active && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setTriggerRect(rect);
    }
  }, [active]);

  // Setup portal container
  useEffect(() => {
    portalContainer.classList.add('color-picker-portal');
    document.body.appendChild(portalContainer);

    return () => {
      document.body.removeChild(portalContainer);
    };
  }, [portalContainer]);

  // Add click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (active) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', e => {
        if (e.key === 'Escape') onClose();
      });
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', e => {
        if (e.key === 'Escape') onClose();
      });
    };
  }, [active, onClose]);

  // Calculate popover position
  const getPopoverStyle = (): React.CSSProperties => {
    if (!triggerRect) {
      return { visibility: 'hidden' };
    }

    return {
      position: 'fixed',
      width: '250px',
      bottom: `${window.innerHeight - triggerRect.top + 5}px`,
      left: `${triggerRect.left}px`,
      backgroundColor: 'white',
      boxShadow: '0 0 0 1px rgba(63, 63, 68, 0.05), 0 1px 3px 0 rgba(63, 63, 68, 0.15)',
      borderRadius: '4px',
      zIndex: 9999,
    };
  };

  return (
    <>
      <div ref={triggerRef}>{activator}</div>
      {active &&
        createPortal(
          <div
            id="color-picker-popover"
            ref={popoverRef}
            style={getPopoverStyle()}
            role="dialog"
            aria-modal="true"
          >
            {children}
          </div>,
          portalContainer
        )}
    </>
  );
};

// Custom ColorPicker component - replacing Polaris ColorPicker
const CustomColorPicker = ({
  color,
  onChange,
  allowAlpha = true,
}: {
  color: HSBAColor;
  onChange: (color: HSBAColor) => void;
  allowAlpha?: boolean;
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isDraggingHue, setIsDraggingHue] = useState(false);
  const [isDraggingAlpha, setIsDraggingAlpha] = useState(false);

  const colorFieldRef = useRef<HTMLDivElement>(null);
  const hueSliderRef = useRef<HTMLDivElement>(null);
  const alphaSliderRef = useRef<HTMLDivElement>(null);

  // Convert HSB to RGB for background display
  const getBackgroundColor = (h: number, s: number, b: number) => {
    const hsb2rgb = (h: number, s: number, b: number) => {
      s /= 100;
      b /= 100;
      const k = (n: number) => (n + h / 60) % 6;
      const f = (n: number) => b * (1 - s * Math.max(0, Math.min(k(n), 4 - k(n), 1)));
      return [Math.round(255 * f(5)), Math.round(255 * f(3)), Math.round(255 * f(1))];
    };

    const [r, g, b1] = hsb2rgb(h, s, b);
    return `rgb(${r}, ${g}, ${b1})`;
  };

  const getRgbColor = () => {
    const { hue, saturation, brightness } = color;
    const hsb2rgb = (h: number, s: number, b: number) => {
      h = h / 360;
      const i = Math.floor(h * 6);
      const f = h * 6 - i;
      const p = b * (1 - s);
      const q = b * (1 - f * s);
      const t = b * (1 - (1 - f) * s);

      let r, g, b1;
      switch (i % 6) {
        case 0:
          r = b;
          g = t;
          b1 = p;
          break;
        case 1:
          r = q;
          g = b;
          b1 = p;
          break;
        case 2:
          r = p;
          g = b;
          b1 = t;
          break;
        case 3:
          r = p;
          g = q;
          b1 = b;
          break;
        case 4:
          r = t;
          g = p;
          b1 = b;
          break;
        case 5:
          r = b;
          g = p;
          b1 = q;
          break;
        default:
          r = 0;
          g = 0;
          b1 = 0;
      }

      return [Math.round(r * 255), Math.round(g * 255), Math.round(b1 * 255)];
    };

    const [r, g, b] = hsb2rgb(hue, saturation, brightness);
    return `rgb(${r}, ${g}, ${b})`;
  };

  // Calculate gradient backgrounds
  const colorFieldBackground = `
    linear-gradient(to bottom, transparent, #000), 
    linear-gradient(to right, #fff, hsl(${color.hue}, 100%, 50%))
  `;

  const alphaBackground = `
    linear-gradient(to right, transparent, ${getRgbColor()})
  `;

  // Handle color field (saturation/brightness) interaction
  const handleColorFieldInteraction = useCallback(
    (e: React.MouseEvent | MouseEvent) => {
      if (!colorFieldRef.current) return;

      const rect = colorFieldRef.current.getBoundingClientRect();
      const x = Math.max(0, Math.min(e.clientX - rect.left, rect.width));
      const y = Math.max(0, Math.min(e.clientY - rect.top, rect.height));

      const saturation = (x / rect.width) * 100;
      const brightness = 100 - (y / rect.height) * 100;

      onChange({
        ...color,
        saturation: saturation / 100,
        brightness: brightness / 100,
      });
    },
    [color, onChange]
  );

  // Handle hue slider interaction
  const handleHueInteraction = useCallback(
    (e: React.MouseEvent | MouseEvent) => {
      if (!hueSliderRef.current) return;

      const rect = hueSliderRef.current.getBoundingClientRect();
      const x = Math.max(0, Math.min(e.clientX - rect.left, rect.width));
      const hue = (x / rect.width) * 360;

      onChange({
        ...color,
        hue,
      });
    },
    [color, onChange]
  );

  // Handle alpha slider interaction
  const handleAlphaInteraction = useCallback(
    (e: React.MouseEvent | MouseEvent) => {
      if (!allowAlpha || !alphaSliderRef.current) return;

      const rect = alphaSliderRef.current.getBoundingClientRect();
      const x = Math.max(0, Math.min(e.clientX - rect.left, rect.width));
      const alpha = x / rect.width;

      onChange({
        ...color,
        alpha,
      });
    },
    [allowAlpha, color, onChange]
  );

  // Setup mouse event handlers for dragging
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) handleColorFieldInteraction(e);
      if (isDraggingHue) handleHueInteraction(e);
      if (isDraggingAlpha) handleAlphaInteraction(e);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsDraggingHue(false);
      setIsDraggingAlpha(false);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [
    isDragging,
    isDraggingHue,
    isDraggingAlpha,
    handleColorFieldInteraction,
    handleHueInteraction,
    handleAlphaInteraction,
  ]);

  return (
    <div className="w-full">
      {/* Color field (saturation/brightness) */}
      <div
        ref={colorFieldRef}
        className="w-full h-48 relative cursor-pointer rounded-md mb-2"
        style={{ background: colorFieldBackground }}
        onMouseDown={e => {
          setIsDragging(true);
          handleColorFieldInteraction(e);
        }}
      >
        {/* Indicator for current selection */}
        <div
          className="absolute w-5 h-5 rounded-full border-2 border-white shadow-sm transform -translate-x-1/2 -translate-y-1/2"
          style={{
            left: `${color.saturation * 100}%`,
            top: `${100 - color.brightness * 100}%`,
            backgroundColor: getBackgroundColor(
              color.hue,
              color.saturation * 100,
              color.brightness * 100
            ),
          }}
        />
      </div>

      {/* Hue slider */}
      <div
        ref={hueSliderRef}
        className="w-full h-5 my-2 relative cursor-pointer rounded-md"
        style={{
          background: 'linear-gradient(to right, #f00, #ff0, #0f0, #0ff, #00f, #f0f, #f00)',
        }}
        onMouseDown={e => {
          setIsDraggingHue(true);
          handleHueInteraction(e);
        }}
      >
        {/* Hue slider indicator */}
        <div
          className="absolute w-4 h-5 bg-white border border-gray-200 shadow-sm transform -translate-x-1/2 rounded-sm"
          style={{ left: `${(color.hue / 360) * 100}%` }}
        />
      </div>

      {/* Alpha slider */}
      {allowAlpha && (
        <div
          ref={alphaSliderRef}
          className="w-full h-5 my-2 relative cursor-pointer rounded-md opacity-slider"
          style={{
            background: 'repeating-conic-gradient(#e5e5e5 0% 25%, white 0% 50%) 0% 0% / 10px 10px',
            position: 'relative',
            overflow: 'hidden',
          }}
          onMouseDown={e => {
            setIsDraggingAlpha(true);
            handleAlphaInteraction(e);
          }}
        >
          {/* Alpha gradient overlay */}
          <div
            className="absolute inset-0 w-full h-full"
            style={{
              background: alphaBackground,
            }}
          />

          {/* Alpha slider indicator */}
          <div
            className="absolute w-4 h-5 bg-white border border-gray-200 shadow-sm transform -translate-x-1/2 rounded-sm z-10"
            style={{ left: `${color.alpha * 100}%` }}
          />
        </div>
      )}
    </div>
  );
};

// Convert hex color to HSBA
const hexToHSBA = (hex: string): HSBAColor => {
  // Remove # if present
  hex = hex.replace(/^#/, '');

  // Check if the hex includes alpha (like FF0000FF for red with full opacity)
  let alpha = 1;
  if (hex.length === 8) {
    alpha = parseInt(hex.substring(6, 8), 16) / 255;
    hex = hex.substring(0, 6);
  }

  // Parse the hex value to RGB
  let r = 0,
    g = 0,
    b = 0;
  if (hex.length === 3) {
    r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
    g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
    b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
  } else if (hex.length === 6) {
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
  }

  // Normalize RGB values
  r /= 255;
  g /= 255;
  b /= 255;

  // Find max and min values
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const delta = max - min;

  // Initialize HSBA values
  let hue = 0;
  let saturation = max === 0 ? 0 : delta / max;
  let brightness = max;

  // Calculate hue
  if (delta === 0) {
    hue = 0;
  } else if (max === r) {
    hue = ((g - b) / delta) % 6;
  } else if (max === g) {
    hue = (b - r) / delta + 2;
  } else {
    hue = (r - g) / delta + 4;
  }

  // Convert hue to degrees
  hue = Math.round(hue * 60);
  if (hue < 0) hue += 360;

  return {
    hue,
    saturation,
    brightness,
    alpha,
  };
};

// Convert HSBA color to hex
const hsbaToHex = (color: HSBAColor): string => {
  const { hue, saturation, brightness } = color;

  let h = hue / 360;
  let s = saturation;
  let v = brightness;

  let r = 0,
    g = 0,
    b = 0;

  let i = Math.floor(h * 6);
  let f = h * 6 - i;
  let p = v * (1 - s);
  let q = v * (1 - f * s);
  let t = v * (1 - (1 - f) * s);

  switch (i % 6) {
    case 0:
      r = v;
      g = t;
      b = p;
      break;
    case 1:
      r = q;
      g = v;
      b = p;
      break;
    case 2:
      r = p;
      g = v;
      b = t;
      break;
    case 3:
      r = p;
      g = q;
      b = v;
      break;
    case 4:
      r = t;
      g = p;
      b = v;
      break;
    case 5:
      r = v;
      g = p;
      b = q;
      break;
  }

  r = Math.round(r * 255);
  g = Math.round(g * 255);
  b = Math.round(b * 255);

  // Convert rgb to hex
  const toHex = (c: number): string => {
    const hex = c.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  // We only return the RGB part of the hex, without alpha
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
};

const colorPresets = [
  '#FF4D4F',
  '#FF7A45',
  '#FFC53D',
  '#52C41A',
  '#36CFC9',
  '#2F7EC7',
  '#5C6AC4',
  '#F74D75',
  '#B37FEB',
  '#7B61FF',
  '#40A9FF',
  '#39B54A',
  '#8CD211',
  '#722ED1',
];

// Validate hex color format
const isValidHexColor = (hex: string): boolean => {
  // Remove # if present for the validation check
  const colorValue = hex.replace(/^#/, '');

  // Valid hex colors are either 3, 6 or 8 characters (with alpha)
  const validHexPattern = /^([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6}|[0-9A-Fa-f]{8})$/;
  return validHexPattern.test(colorValue);
};

// Format and validate hex color - returns a valid hex or fallback color
const validateAndFormatHexColor = (input: string, fallback: string = '#000000'): string => {
  // Add # if missing
  let formattedColor = input.startsWith('#') ? input : `#${input}`;

  // Check if it's valid
  if (!isValidHexColor(formattedColor)) {
    console.warn(`Invalid hex color: ${input}, using fallback color instead`);
    return fallback;
  }

  // Normalize to uppercase for consistency
  return formattedColor.toUpperCase();
};

// Validate opacity is between 0-100%
const validateOpacity = (value: string): string => {
  // Remove % if present
  let numValue = parseInt(value.replace('%', ''), 10);

  // Handle invalid numbers
  if (isNaN(numValue)) {
    return '100%';
  }

  // Clamp between 0-100
  numValue = Math.max(0, Math.min(100, numValue));

  // Return formatted value
  return `${numValue}%`;
};

export const TextboxColorPicker = ({
  color,
  onChange,
  className,
  forceFullOpacity = true,
}: TextboxColorPickerProps) => {
  const [popoverActive, setPopoverActive] = useState(false);
  // State to track user input during typing
  const [hexInputValue, setHexInputValue] = useState(color.replace(/^#/, ''));

  // Convert the hex color to HSBA for the ColorPicker
  const [hsbaColor, setHsbaColor] = useState(() => {
    const colorHsba = hexToHSBA(validateAndFormatHexColor(color));
    return forceFullOpacity ? { ...colorHsba, alpha: 1 } : colorHsba;
  });

  // Update internal HSBA color when external hex color changes
  useEffect(() => {
    const colorHsba = hexToHSBA(validateAndFormatHexColor(color));
    setHsbaColor(forceFullOpacity ? { ...colorHsba, alpha: 1 } : colorHsba);
    setHexInputValue(color.replace(/^#/, ''));
  }, [color, forceFullOpacity]);

  const togglePopoverActive = useCallback(
    () => setPopoverActive(popoverActive => !popoverActive),
    []
  );

  // Helper function to normalize color for comparison
  const normalizeColorForComparison = (colorStr: string) => {
    // Remove # and get only the RGB part (first 6 chars after removing #)
    return colorStr.replace(/^#/, '').substring(0, 6).toUpperCase();
  };

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string) => {
    // Remove # if present
    hex = hex.replace(/^#/, '');

    // Parse hex to RGB
    const bigint = parseInt(hex, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;

    return { r, g, b };
  };

  // Check if a preset color is currently selected
  const isPresetSelected = (presetColor: string) => {
    try {
      // Get the RGB values for both colors
      const currentRgb = hexToRgb(normalizeColorForComparison(color));
      const presetRgb = hexToRgb(normalizeColorForComparison(presetColor));

      // Check if they're visually the same (allowing for a small difference due to rounding)
      const tolerance = 5; // Allow for slight differences in color values
      return (
        Math.abs(currentRgb.r - presetRgb.r) <= tolerance &&
        Math.abs(currentRgb.g - presetRgb.g) <= tolerance &&
        Math.abs(currentRgb.b - presetRgb.b) <= tolerance
      );
    } catch (e) {
      // If there's any error in conversion, fall back to simple string comparison
      return normalizeColorForComparison(color) === normalizeColorForComparison(presetColor);
    }
  };

  const handleColorChange = (newColor: HSBAColor) => {
    // Convert HSBA to hex and call the onChange prop
    // If forceFullOpacity is true, ensure alpha is always 1
    const finalColor = forceFullOpacity ? { ...newColor, alpha: 1 } : newColor;
    const newHexColor = hsbaToHex(finalColor);
    onChange(newHexColor);
    setHsbaColor(finalColor);
  };

  const handleOpacityChange = (value: string) => {
    // If forceFullOpacity is true, do nothing
    if (forceFullOpacity) return;

    // Validate and format the opacity value
    const validatedOpacity = validateOpacity(value);

    // Convert percentage to a decimal for alpha (0-1)
    const numValue = parseInt(validatedOpacity.replace('%', ''), 10);

    // Update the alpha value
    const newColor = {
      ...hsbaColor,
      alpha: numValue / 100,
    };

    setHsbaColor(newColor);
    onChange(hsbaToHex(newColor));
  };

  const handlePresetColorClick = (presetColor: string) => {
    // Keep the current alpha value when clicking a preset
    // If forceFullOpacity is true, ensure alpha is always 1
    const alpha = forceFullOpacity ? 1 : hsbaColor.alpha;
    const newColor = {
      ...hexToHSBA(presetColor),
      alpha,
    };

    onChange(hsbaToHex(newColor));
    setHsbaColor(newColor);
  };

  const textFieldActivator = (
    <div className={`cursor-pointer ${className}`} onClick={togglePopoverActive}>
      <TextField
        label="Color"
        labelHidden
        value={color}
        autoComplete="off"
        prefix={
          <div
            className="h-4 w-4 rounded-sm border border-gray-200"
            style={{ backgroundColor: color }}
            aria-label="Color swatch"
          />
        }
        onChange={value => {
          // Automatically add # if missing
          const formattedColor = value.startsWith('#') ? value : `#${value}`;
          onChange(formattedColor);
        }}
        inputClassName="h-8 rounded-md"
      />
    </div>
  );

  // Calculate opacity percentage from alpha value
  const opacityPercentage = `${Math.round(hsbaColor.alpha * 100)}%`;

  return (
    <PopoverPortal
      active={popoverActive}
      activator={textFieldActivator}
      onClose={togglePopoverActive}
    >
      <div className="p-4">
        <div className="flex flex-col space-y-3">
          {/* Color picker with enhanced styling */}
          <div className="rounded-md overflow-hidden">
            <CustomColorPicker
              color={hsbaColor}
              onChange={handleColorChange}
              allowAlpha={!forceFullOpacity}
            />
          </div>

          <div className="mt-2">
            {/* Labels for inputs */}
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-sm font-medium text-gray-700 w-36">Hex</span>
              {!forceFullOpacity && (
                <span className="text-sm font-medium text-gray-700">Opacity</span>
              )}
            </div>

            {/* Hex input and opacity fields */}
            <div className="flex items-center space-x-2">
              <div className={forceFullOpacity ? 'w-full' : 'flex-grow'}>
                <TextField
                  labelHidden
                  label="Hex"
                  value={hexInputValue}
                  onChange={value => {
                    // Allow any input while typing (only hex characters)
                    const hexOnly = value.replace(/[^0-9A-Fa-f]/g, '');
                    setHexInputValue(hexOnly);

                    // Only validate and update the actual color if we have a valid hex
                    if (isValidHexColor(`#${hexOnly}`)) {
                      const validatedColor = validateAndFormatHexColor(`#${hexOnly}`);
                      onChange(validatedColor);
                    }
                  }}
                  onBlur={() => {
                    // On blur, validate and correct the input
                    const validatedColor = validateAndFormatHexColor(`#${hexInputValue}`);
                    onChange(validatedColor);
                    setHexInputValue(validatedColor.replace(/^#/, ''));
                  }}
                  autoComplete="off"
                  prefix="#"
                  inputClassName="h-8 rounded-sm border-border-light drop-shadow-sm"
                />
              </div>
              {!forceFullOpacity && (
                <div className="w-28">
                  <TextField
                    label="Opacity"
                    labelHidden
                    value={opacityPercentage}
                    onChange={handleOpacityChange}
                    autoComplete="off"
                    inputClassName="h-8 rounded-sm border-border-light drop-shadow-sm"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Color swatches */}
          <div className="grid grid-cols-7 gap-2 mt-3">
            {colorPresets.map((presetColor, index) => (
              <div
                key={index}
                className={`w-8 h-8 rounded-full cursor-pointer flex items-center justify-center ${
                  isPresetSelected(presetColor) ? 'border-2 border-gray-400 ring-2 ring-white' : ''
                }`}
                onClick={() => handlePresetColorClick(presetColor)}
                aria-label={`Color preset ${presetColor}`}
              >
                <div className="w-6 h-6 rounded-full" style={{ backgroundColor: presetColor }} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </PopoverPortal>
  );
};
