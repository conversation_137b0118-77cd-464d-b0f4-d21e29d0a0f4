import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  primary?: boolean;
  destructive?: boolean;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  fullWidth?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  primary = false,
  destructive = false,
  disabled = false,
  loading = false,
  className = '',
  fullWidth = false,
  type = 'button',
}) => {
  const isDisabled = disabled || loading;

  // Base button styles
  const baseStyles =
    'inline-flex items-center justify-center min-h-[36px] px-4 py-2 text-sm font-medium rounded border transition-all duration-200';

  // Variant styles - only applied when not disabled/loading
  const variantStyles = !isDisabled
    ? primary
      ? 'bg-[#856CF8] text-white border-[#856CF8] hover:bg-[#6B4FF7] hover:border-[#6B4FF7]'
      : destructive
        ? 'bg-white text-error-light border-error-light hover:bg-error-bg hover:border-error-dark hover:text-error-dark'
        : 'bg-white text-text-light border-border hover:bg-bg-hover hover:text-text-light hover:border-border-hover'
    : '';

  // Disabled/Loading styles - these will override variant styles
  const disabledStyles = isDisabled
    ? 'bg-[#F9FAFB] !text-text-dark border-border cursor-not-allowed'
    : '';

  // Width styles
  const widthStyles = fullWidth ? 'w-full' : '';

  // Loading indicator
  const loadingIndicator = loading ? (
    <span className="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em]"></span>
  ) : null;

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={isDisabled}
      className={`${baseStyles} ${variantStyles} ${disabledStyles} ${widthStyles} ${className}`}
    >
      {loading && loadingIndicator}
      {children}
    </button>
  );
};
