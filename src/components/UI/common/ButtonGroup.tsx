import React from 'react';

export interface ButtonGroupOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
}

interface ButtonGroupProps {
  options: ButtonGroupOption[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  options,
  value,
  onChange,
  className = '',
}) => {
  return (
    <div className={`inline-flex rounded-md shadow-sm ${className}`} role="group">
      {options.map((option, index) => {
        const isSelected = value === option.value;
        const isFirst = index === 0;
        const isLast = index === options.length - 1;

        return (
          <button
            key={option.value}
            type="button"
            className={`
              flex items-center justify-center min-h-[36px] px-4 py-2 
              text-sm font-medium transition-all duration-200
              ${isFirst ? 'rounded-l-md' : ''} 
              ${isLast ? 'rounded-r-md' : ''}
              ${
                isSelected
                  ? 'bg-primary text-white shadow-inner hover:bg-primary-hover relative z-10'
                  : 'bg-white text-text-light hover:bg-bg-hover border border-border'
              }
              ${!isLast && !isSelected ? 'border-r-0' : ''}
              focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-30
            `}
            aria-pressed={isSelected}
            onClick={() => onChange(option.value)}
          >
            {option.icon && (
              <span className={`mr-1.5 ${isSelected ? 'text-white' : 'text-inherit'}`}>
                {option.icon}
              </span>
            )}
            {option.label}
          </button>
        );
      })}
    </div>
  );
};
