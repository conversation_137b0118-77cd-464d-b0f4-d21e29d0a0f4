import React, { useState } from 'react';
import { NavigationNudgeData } from '../../../../types/NavigationNudgeTypes';
import { BasicTab } from './BasicTab';
import { AdvancedTab } from './AdvancedTab';
import { Tabs } from '../../common/Tabs';

export interface NavigationNudgeProps {
  paramOptions: Record<string, string>;
  onSave: (data: NavigationNudgeData) => void;
  data: NavigationNudgeData;
}

export const NavigationNudge = ({ paramOptions, onSave, data }: NavigationNudgeProps) => {
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  const handleTabChange = (selectedTabIndex: number) => {
    setSelectedTabIndex(selectedTabIndex);
  };

  const handleBasicTabChange = (newData: Partial<NavigationNudgeData>) => {
    onSave({
      ...data,
      ...newData,
    });
  };

  const handleAdvancedTabSave = (newData: NavigationNudgeData) => {
    onSave(newData);
  };

  const tabs = [
    {
      id: 'basic',
      content: 'Basic',
      panelContent: <BasicTab data={data} onChange={handleBasicTabChange} />,
    },
    {
      id: 'advanced',
      content: 'Advanced',
      panelContent: <AdvancedTab data={data} onSave={handleAdvancedTabSave} />,
    },
  ];

  return (
    <Tabs
      tabs={tabs.map(tab => ({
        ...tab,
        panelContent: (
          <div className="flex flex-col h-full min-h-0 overflow-y-auto">
            <div className="ml-1">{tab.panelContent}</div>
          </div>
        ),
      }))}
      selected={selectedTabIndex}
      onSelect={handleTabChange}
    />
  );
};
