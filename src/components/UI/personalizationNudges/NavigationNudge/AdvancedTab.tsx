import React from 'react';
import { NavigationNudgeData } from '../../../../types/NavigationNudgeTypes';
import { TreatmentControlSplit } from '../TreatmentControlSplit';
import { GroupLabel } from '../../common/GroupLabel';
import { Slider } from '../../common/Slider';

interface AdvancedTabProps {
  data: NavigationNudgeData;
  onSave: (data: NavigationNudgeData) => void;
}

export const AdvancedTab = ({ data, onSave }: AdvancedTabProps) => {
  const holdoutPercentage = data.holdoutPercentage || 0;

  const handleSplitChange = (value: number) => {
    onSave({
      ...data,
      holdoutPercentage: value,
    });
  };

  const handleTimeDelayChange = (value: number) => {
    onSave({
      ...data,
      timeDelay: value,
    });
  };

  return (
    <div className="w-full">
      <TreatmentControlSplit
        holdoutPercentage={holdoutPercentage}
        onHoldoutPercentageChange={handleSplitChange}
      />

      {/* Display Delay */}
      <div className="mt-8">
        <div className="mb-2">
          <GroupLabel
            label="Time Delay (in seconds)"
            tooltip="This is the amount of time you want to wait before surfacing this nudge for a user. The time starts when the user enters your site."
            className="font-bold"
          />
        </div>
        <div className="flex items-center gap-4">
          <Slider
            value={data.timeDelay || 5}
            onChange={handleTimeDelayChange}
            min={0}
            max={60}
            step={5}
            className="w-full sm:w-[300px]"
            label={`${data.timeDelay || 5} seconds`}
          />
        </div>
      </div>
    </div>
  );
};
