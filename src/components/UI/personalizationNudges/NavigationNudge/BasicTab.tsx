import React from 'react';
import { GroupLabel } from '../../common/GroupLabel';
import { Textbox } from '../../common/Textbox';
import { NavigationNudgeData } from '../../../../types/NavigationNudgeTypes';
import { TextboxColorPicker } from '../../common/TextboxColorPicker';
import { Button } from '../../common/Button';
import { FontSelector } from '../../common/FontSelector';
import { IconButton } from '../../common/IconButton';
import { TrashIcon } from '../../common/icons/TrashIcon';
import { getValueOrDefault } from '../../../../utils/helpers';

export interface BasicTabProps {
  data: NavigationNudgeData;
  onChange: (data: Partial<NavigationNudgeData>) => void;
}

export const BasicTab = ({ data, onChange }: BasicTabProps) => {
  // Directly use answerOptions from props, providing an empty array as default if undefined/null
  const currentAnswerOptions = data.answerOptions ?? [];

  // <PERSON>le adding a new empty option - limit to 3 options total
  const handleAddOption = () => {
    if (currentAnswerOptions.length >= 3) return;
    const newOptions = [...currentAnswerOptions, { label: '', url: '' }];
    onChange({ answerOptions: newOptions });
  };

  // Handle updating an option at a specific index
  const handleOptionChange = (index: number, field: 'label' | 'url', value: string) => {
    const updatedOptions = currentAnswerOptions.map((option, i) =>
      i === index ? { ...option, [field]: value } : option
    );
    onChange({ answerOptions: updatedOptions });
  };

  // Handle deleting an option
  const handleDeleteOption = (index: number) => {
    const filteredOptions = currentAnswerOptions.filter((_, i) => i !== index);
    onChange({ answerOptions: filteredOptions });
  };

  return (
    <div className="w-full sm:w-[600px] flex flex-col gap-6 py-4">
      {/* Question (formerly Headline) */}
      <div>
        <div className="mb-2">
          <GroupLabel label="Question" className="font-bold" />
          <div className="text-sm italic text-text-dark">Maximum 50 characters (2 lines)</div>
        </div>
        <Textbox
          value={getValueOrDefault(data.headline, 'What are you shopping for?')}
          onChange={value => onChange({ headline: value })}
          placeholder="What are you shopping for?"
          className="w-full sm:w-[400px]"
          maxLength={50}
        />
      </div>

      {/* Answer Options - moved below Question */}
      <div>
        <div className="mb-2">
          <GroupLabel label="Answer Options" className="font-bold" />
          <div className="text-sm italic text-text-dark">Maximum of 3 options allowed</div>
        </div>

        {/* Display current options */}
        <div className="mb-4">
          {currentAnswerOptions.length === 0 ? (
            <div className="text-gray-500 italic mb-2">No options added yet</div>
          ) : (
            <div className="space-y-4">
              {currentAnswerOptions.map((option, index) => (
                <div
                  key={index}
                  className="bg-white rounded-lg border border-gray-200 shadow-sm p-4 transition-all hover:shadow-md"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium text-gray-700">Option {index + 1}</h3>
                    <IconButton
                      onClick={() => handleDeleteOption(index)}
                      ariaLabel="Delete option"
                      className="border border-gray-200 hover:bg-red-50"
                    >
                      <TrashIcon size={20} />
                    </IconButton>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="mb-1">
                        <GroupLabel label="Label (max 25 characters)" className="text-sm" />
                      </div>
                      <Textbox
                        value={option.label}
                        onChange={value => handleOptionChange(index, 'label', value)}
                        placeholder={`Option ${index + 1}`}
                        className="w-full"
                        maxLength={25}
                      />
                    </div>
                    <div>
                      <div className="mb-1">
                        <GroupLabel label="Destination URL" className="text-sm" />
                      </div>
                      <Textbox
                        value={option.url}
                        onChange={value => handleOptionChange(index, 'url', value)}
                        placeholder="Enter URL (e.g., /products)"
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Add button - only show if less than 3 options */}
        {currentAnswerOptions.length < 3 && (
          <div className="mt-4">
            <Button onClick={handleAddOption} primary className="flex items-center">
              <svg
                className="w-4 h-4 mr-1.5"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 5V19M5 12H19"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Add Option
            </Button>
          </div>
        )}
      </div>

      {/* Subtext (formerly Subheader) */}
      <div>
        <div className="mb-2">
          <GroupLabel label="Subheader" className="font-bold" />
          <div className="text-sm italic text-text-dark">Maximum 35 characters (1 line)</div>
        </div>
        <Textbox
          value={getValueOrDefault(data.subheader, "We'll take you to the right spot!")}
          onChange={value => onChange({ subheader: value })}
          placeholder="We'll take you to the right spot!"
          className="w-full sm:w-[400px]"
          maxLength={35}
        />
      </div>

      {/* Question Color - Moved before Font */}
      <div>
        <div className="mb-2">
          <GroupLabel label="Question Color" className="font-bold" />
        </div>
        <div className="grid grid-cols-1 gap-4">
          <div className="flex flex-col gap-2">
            <TextboxColorPicker
              color={getValueOrDefault(data.headlineColor, '#000000')}
              onChange={color => onChange({ headlineColor: color.toUpperCase() })}
              className="w-full sm:w-[150px]"
            />
          </div>
        </div>
      </div>

      {/* Font */}
      <div>
        <div className="mb-2">
          <GroupLabel label="Font" className="font-bold" />
          <div className="text-sm italic text-text-dark">
            Note: Some custom fonts may not display in the live preview but will open on your site.
          </div>
        </div>
        <FontSelector
          value={getValueOrDefault(data.fontName, 'Arial')}
          onChange={value => onChange({ fontName: value })}
          placeholder="Arial"
          className="w-full sm:w-[400px]"
        />
      </div>
    </div>
  );
};
