import React from 'react';
import { <PERSON>lider } from '../common/Slider';
import { GroupLabel } from '../common/GroupLabel';

export interface TreatmentControlSplitProps {
  holdoutPercentage: number;
  onHoldoutPercentageChange: (value: number) => void;
}

// Custom tooltip component that matches the design
const TreatmentControlTooltip = () => {
  return (
    <div className="flex flex-col gap-2 text-secondary p-3">
      <span>Treatment: Visitors who will receive the nudge.</span>
      <span>Control: Visitors who are eligible for the nudge but won&apos;t receive it.</span>
    </div>
  );
};

export const TreatmentControlSplit = ({
  holdoutPercentage = 20, // Default to 20% holdout (80% treatment)
  onHoldoutPercentageChange,
}: TreatmentControlSplitProps) => {
  // Ensure treatment is within the 10-90 range, holdout within 10-90
  const treatmentPercentage = Math.min(Math.max(100 - holdoutPercentage, 10), 90);

  // Handle changes to ensure the 10% minimum for treatment and 10% minimum for holdout
  const handleTreatmentPercentageChange = (value: number) => {
    // Clamp the value between 10 and 90
    const clampedValue = Math.min(Math.max(value, 10), 90);
    // Holdout is 100 - treatment, clamp between 10 and 90
    const newHoldout = Math.min(Math.max(100 - clampedValue, 10), 90);
    onHoldoutPercentageChange(newHoldout);
  };

  return (
    <div className="mb-8">
      <GroupLabel
        label="Treatment / Control Split"
        tooltip={<TreatmentControlTooltip />}
        className="font-bold"
      />

      <div className="mt-4 max-w-96">
        {/* Treatment/Control labels */}
        <div className="flex justify-between mb-2">
          <span className="text-primary font-bold text-base">Treatment</span>
          <span className="text-secondary font-bold text-base">Control</span>
        </div>

        {/* Slider with inline percentages */}
        <div className="flex items-center gap-2">
          {/* Treatment percentage */}
          <div className="min-w-[40px]">
            <span className="text-primary font-medium text-base">{`${treatmentPercentage}%`}</span>
          </div>

          {/* Slider */}
          <div className="flex-grow">
            <Slider
              value={treatmentPercentage}
              onChange={handleTreatmentPercentageChange}
              min={10}
              max={90}
              step={5}
            />
          </div>

          {/* Control percentage */}
          <div className="min-w-[40px]">
            <span className="text-secondary font-bold text-base">{`${holdoutPercentage}%`}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
