import React from 'react';
import { FontSelector } from '../../common/FontSelector';
import { GroupLabel } from '../../common/GroupLabel';
import { Textbox } from '../../common/Textbox';
import { TextboxColorPicker } from '../../common/TextboxColorPicker';
import { DiscountNudgeData } from '../../../../types/DiscountNudgeTypes';
import { MAX_LENGTH } from '../../../../types/constants';
import { getValueOrDefault } from '../../../../utils/helpers';

export interface BasicTabProps {
  data: DiscountNudgeData;
  paramOptions: Record<string, string>;
  onSave: (data: DiscountNudgeData) => void;
}

export const BasicTab = ({ data, paramOptions, onSave }: BasicTabProps) => {
  const handleChange = (field: keyof DiscountNudgeData, value: string | number) => {
    onSave({
      ...data,
      [field]: value,
    });
  };

  return (
    <div className="w-full flex flex-col gap-4">
      <GroupLabel label="Header Text" className="font-bold" />
      <Textbox
        value={getValueOrDefault(data.headerText, 'GET {discount}% OFF NOW!')}
        onChange={value => handleChange('headerText', value)}
        maxLength={MAX_LENGTH}
        placeholder="GET {discount}% OFF NOW!"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />

      <GroupLabel label="Body Text" className="font-bold" />
      <Textbox
        value={getValueOrDefault(data.bodyText, 'Exclusions apply.')}
        onChange={value => handleChange('bodyText', value)}
        maxLength={MAX_LENGTH}
        placeholder="Exclusions apply."
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />

      <GroupLabel label="Button Text" className="font-bold" />
      <Textbox
        value={getValueOrDefault(data.buttonText, 'Apply')}
        onChange={value => handleChange('buttonText', value)}
        maxLength={MAX_LENGTH}
        placeholder="Apply"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />

      <GroupLabel label="Success Button Text" className="font-bold" />
      <Textbox
        value={getValueOrDefault(data.successButtonText, 'Applied')}
        onChange={value => handleChange('successButtonText', value)}
        maxLength={MAX_LENGTH}
        placeholder="Applied"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />

      <GroupLabel label="Minimized Tab Text" className="font-bold" />
      <Textbox
        value={getValueOrDefault(data.minimizedTabText, 'GET {discount}% OFF NOW!')}
        onChange={value => handleChange('minimizedTabText', value)}
        maxLength={MAX_LENGTH}
        placeholder="GET {discount}% OFF NOW!"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />

      <div className="flex flex-col gap-2">
        <GroupLabel label="Primary Color" className="font-bold" />
        <TextboxColorPicker
          color={getValueOrDefault(data.primaryColor, '#8F0067')}
          onChange={color => handleChange('primaryColor', color)}
          className="w-full sm:w-[150px]"
        />
      </div>

      <div className="flex flex-col gap-2">
        <GroupLabel label="Background Color" className="font-bold" />
        <TextboxColorPicker
          color={getValueOrDefault(data.backgroundColor, '#8F0067')}
          onChange={color => handleChange('backgroundColor', color)}
          className="w-full sm:w-[150px]"
        />
      </div>

      <div className="flex flex-col gap-2">
        <div>
          <GroupLabel label="Font" className="font-bold" />
          <div className="text-sm italic text-text-dark">
            Note: Some custom fonts may not display in the live preview but will open on your site.
          </div>
        </div>
        <FontSelector
          value={getValueOrDefault(data.fontName, 'Arial')}
          onChange={value => handleChange('fontName', value)}
          placeholder="Arial"
          className="w-full sm:w-[400px]"
        />
      </div>
    </div>
  );
};
