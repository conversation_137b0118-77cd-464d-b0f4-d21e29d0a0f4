import React from 'react';
import { Tabs } from '../../common/Tabs';
import { DiscountNudgeData } from '../../../../types/DiscountNudgeTypes';
import { AdvancedTab } from './AdvancedTab';
import { BasicTab } from './BasicTab';
import { DiscountSettingsTab } from './DiscountSettingsTab';
import { DropdownOption } from '../../common/Dropdown';

export interface DiscountNudgeProps {
  paramOptions: Record<string, string>;
  onSave: (data: DiscountNudgeData) => void;
  data: DiscountNudgeData;
  collectionOptions?: DropdownOption[];
  onCollectionSearch?: (searchTerm: string) => void;
  isLoadingCollections?: boolean;
  isSuperAdmin?: boolean;
}

export const DiscountNudge = ({
  paramOptions,
  onSave,
  data,
  onCollectionSearch,
  isLoadingCollections = false,
  isSuperAdmin = false,
}: DiscountNudgeProps) => {
  const tabs = [
    {
      id: 'basic',
      content: 'Basic',
      panelContent: <BasicTab paramOptions={paramOptions} onSave={onSave} data={data} />,
    },
    {
      id: 'discount-settings',
      content: 'Discount Settings',
      panelContent: (
        <DiscountSettingsTab
          data={data}
          onSave={onSave}
          collectionOptions={data.collections}
          onCollectionSearch={onCollectionSearch}
          isLoadingCollections={isLoadingCollections}
        />
      ),
    },
    {
      id: 'advanced',
      content: 'Advanced',
      panelContent: <AdvancedTab data={data} onSave={onSave} isSuperAdmin={isSuperAdmin} />,
    },
  ];

  return (
    <Tabs
      tabs={tabs.map(tab => ({
        ...tab,
        panelContent: <div className="ml-1">{tab.panelContent}</div>,
      }))}
    />
  );
};
