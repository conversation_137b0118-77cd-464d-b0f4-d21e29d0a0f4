import React from 'react';
import { TreatmentControlSplit } from '../TreatmentControlSplit';
import { AntiHoldoutSplit } from '../AntiHoldoutSplit';
import { Toggle } from '../../common/Toggle';
import { DiscountNudgeData } from '../../../../types/DiscountNudgeTypes';
import { ChipInput } from '../../common/ChipInput';

interface AdvancedTabProps {
  data: DiscountNudgeData;
  onSave: (data: DiscountNudgeData) => void;
  isSuperAdmin?: boolean;
}

export const AdvancedTab = ({ data, onSave, isSuperAdmin = false }: AdvancedTabProps) => {
  const handleChange = (
    field: keyof DiscountNudgeData,
    value: string | number | boolean | string[]
  ) => {
    onSave({
      ...data,
      [field]: value,
    });
  };

  const holdoutPercentage = data.holdoutPercentage || 0;
  const antiHoldoutPercentage = data.anti_holdout_percentage || 0;

  const handleSplitChange = (value: number) => {
    handleChange('holdoutPercentage', value);
  };

  const handleAntiHoldoutChange = (value: number) => {
    handleChange('anti_holdout_percentage', value);
  };

  // Convert between string and string[] for exclusionURLs
  const exclusionURLsArray = Array.isArray(data.exclusionURLs)
    ? data.exclusionURLs
    : (data.exclusionURLs?.split(/[,\s]/).filter(Boolean) ?? []);

  return (
    <div className="w-full">
      <TreatmentControlSplit
        holdoutPercentage={holdoutPercentage}
        onHoldoutPercentageChange={handleSplitChange}
      />

      {isSuperAdmin && (
        <AntiHoldoutSplit
          antiHoldoutPercentage={antiHoldoutPercentage}
          onAntiHoldoutPercentageChange={handleAntiHoldoutChange}
        />
      )}

      <div className="mt-6 max-w-md">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Tab Disabled</h3>
        <div className="space-y-4">
          <Toggle
            isEnabled={data.tabDisabled ?? false}
            onToggle={() => handleChange('tabDisabled', !data.tabDisabled)}
            label="Tab Disabled"
          />
        </div>
      </div>
      {/* Audience Section */}
      <div className="mt-6 max-w-md">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Audience</h3>
        <div className="space-y-4">
          <Toggle
            isEnabled={data.excludeLoggedInVisitors ?? false}
            onToggle={() => handleChange('excludeLoggedInVisitors', !data.excludeLoggedInVisitors)}
            label="Exclude Logged In Visitors"
          />
          <Toggle
            isEnabled={data.excludeVisitorsWhoBought ?? false}
            onToggle={() =>
              handleChange('excludeVisitorsWhoBought', !data.excludeVisitorsWhoBought)
            }
            label="Exclude Visitors Who Bought in the Past"
          />

          <div className="mt-4">
            <ChipInput
              label="Exclusion URLs"
              value={exclusionURLsArray}
              onChange={urls => handleChange('exclusionURLs', urls)}
              placeholder="Input URL and press Enter"
              validate={url => url.startsWith('http')}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
