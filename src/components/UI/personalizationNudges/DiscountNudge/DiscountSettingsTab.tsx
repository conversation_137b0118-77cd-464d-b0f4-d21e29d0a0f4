import React, { useState, useEffect, useRef, useMemo } from 'react';
import { GroupLabel } from '../../common/GroupLabel';
import { Textbox } from '../../common/Textbox';
import { RadioGroup, RadioOption } from '../../common/RadioGroup';
import { DiscountNudgeData, DiscountVariant } from '../../../../types/DiscountNudgeTypes';
import { useDebounce } from '../../../../hooks/useDebounce';
import { Checkbox } from '../../common/Checkbox';
import { isEqual } from '../../../../utils/helpers';
import { IconButton } from '../../common/IconButton';
import { PlusIcon } from '../../common/icons/PlusIcon';
import { TrashIcon } from '../../common/icons/TrashIcon';
import { Dropdown, DropdownOption } from '../../common/Dropdown';
import { Chip } from '../../common/Chip';

export interface DiscountSettingsTabProps {
  data: DiscountNudgeData;
  onSave: (data: DiscountNudgeData) => void;
  collectionOptions?: DropdownOption[];
  onCollectionSearch?: (searchTerm: string) => void;
  isLoadingCollections?: boolean;
}

export const DiscountSettingsTab = ({
  data,
  onSave,
  collectionOptions = [],
  onCollectionSearch,
  isLoadingCollections = false,
}: DiscountSettingsTabProps) => {
  // Initialize discount variants from data or create a new array with the current discount
  const [discountVariants, setDiscountVariants] = useState<DiscountVariant[]>(
    data.discountVariants ?? [
      {
        discountPercentage: data.discountPercentage ?? 10,
        discountPrefix: data.discountPrefix ?? 'save',
        discountCode: data.discountCode ?? '',
      },
    ]
  );
  const [discountPrefix, setDiscountPrefix] = useState<string>(data.discountPrefix ?? 'save');
  const [filteredCollections, setFilteredCollections] =
    useState<DropdownOption<string>[]>(collectionOptions);
  const [productApplicability, setProductApplicability] = useState(
    data.productApplicability ?? 'all'
  );
  const [selectedCollections, setSelectedCollections] = useState<string[]>(
    data.selectedCollections ?? []
  );
  const [collectionSearchInput, setCollectionSearchInput] = useState('');
  const [combinableDiscounts, setCombinableDiscounts] = useState({
    product: data.combinableDiscounts?.product ?? false,
    order: data.combinableDiscounts?.order ?? false,
    shipping: data.combinableDiscounts?.shipping ?? true,
  });
  const [eligibility, setEligibility] = useState({
    singleItem: data.eligibility?.singleItem ?? true,
    subscription: data.eligibility?.subscription ?? true,
  });

  // Use ref for previous data to compare values
  const prevDataRef = useRef<DiscountNudgeData>(data);

  // Update state when data prop changes
  useEffect(() => {
    if (data.discountVariants) {
      setDiscountVariants(data.discountVariants);
    } else {
      setDiscountVariants([
        {
          discountPercentage: data.discountPercentage ?? 10,
          discountPrefix: data.discountPrefix ?? 'save',
          discountCode: data.discountCode ?? '',
        },
      ]);
    }

    setProductApplicability(data.productApplicability ?? 'all');
    setSelectedCollections(data.selectedCollections ?? []);
    setCombinableDiscounts({
      product: data.combinableDiscounts?.product ?? false,
      order: data.combinableDiscounts?.order ?? false,
      shipping: data.combinableDiscounts?.shipping ?? true,
    });
    setEligibility({
      singleItem: data.eligibility?.singleItem ?? true,
      subscription: data.eligibility?.subscription ?? true,
    });

    // Update prevDataRef when data prop changes to prevent unnecessary updates
    prevDataRef.current = {
      ...data,
      discountVariants: data.discountVariants ?? [
        {
          discountPercentage: data.discountPercentage ?? 10,
          discountPrefix: data.discountPrefix ?? 'save',
          discountCode: data.discountCode ?? '',
        },
      ],
      discountPrefix: data.discountPrefix ?? 'save',
      productApplicability: data.productApplicability ?? 'all',
      selectedCollections: data.selectedCollections ?? [],
      combinableDiscounts: {
        product: data.combinableDiscounts?.product ?? false,
        order: data.combinableDiscounts?.order ?? false,
        shipping: data.combinableDiscounts?.shipping ?? true,
      },
      eligibility: {
        singleItem: data.eligibility?.singleItem ?? true,
        subscription: data.eligibility?.subscription ?? true,
      },
    };
  }, [data]);

  const currentData = useMemo(() => {
    // Create a new object with only the fields managed by this component
    // rather than spreading the entire data object which causes unnecessary change detection
    const relevantFields = {
      ...prevDataRef.current,
      // For backward compatibility, also keep the first discount variant info in the main fields
      discountPrefix: discountPrefix ?? 'save',
      discountVariants,
      productApplicability,
      selectedCollections,
      combinableDiscounts,
      eligibility,
    };

    return relevantFields;
  }, [
    discountPrefix,
    discountVariants,
    productApplicability,
    selectedCollections,
    combinableDiscounts,
    eligibility,
  ]);

  // Apply debounce to the form data with 500ms delay
  const debouncedData = useDebounce(currentData, 500);

  // Call onSave only when debounced data actually changes
  useEffect(() => {
    // Deep compare only the fields this component manages
    const hasChanges =
      !isEqual(debouncedData.discountVariants, prevDataRef.current.discountVariants) ||
      debouncedData.productApplicability !== prevDataRef.current.productApplicability ||
      !isEqual(debouncedData.selectedCollections, prevDataRef.current.selectedCollections) ||
      !isEqual(debouncedData.combinableDiscounts, prevDataRef.current.combinableDiscounts) ||
      !isEqual(debouncedData.eligibility, prevDataRef.current.eligibility) ||
      !isEqual(debouncedData.discountPrefix, prevDataRef.current.discountPrefix);

    if (hasChanges) {
      prevDataRef.current = debouncedData;
      onSave(debouncedData);
    }
  }, [debouncedData, onSave]);

  const handleDiscountChange = (index: number, field: keyof DiscountVariant, value: any) => {
    const updatedVariants = [...discountVariants];
    updatedVariants[index] = {
      ...updatedVariants[index],
      [field]: value,
    };
    setDiscountVariants(updatedVariants);

    // Special case for discountPrefix - needs to update the main field too for backward compatibility
    if (field === 'discountPrefix') {
      // This will be picked up by the currentData useMemo
      prevDataRef.current = {
        ...prevDataRef.current,
        discountPrefix: value,
      };
    }
  };

  const handleAddDiscountVariant = () => {
    // Limit to a maximum of 2 discount variants
    if (discountVariants.length < 2) {
      const newVariant: DiscountVariant = {
        discountPercentage: 20, // Default new discount percentage
        discountPrefix: data.discountPrefix ?? 'save',
        discountCode: '',
      };
      setDiscountVariants([...discountVariants, newVariant]);
    }
  };

  const handleRemoveDiscountVariant = (index: number) => {
    if (discountVariants.length > 1) {
      const updatedVariants = discountVariants.filter((_, i) => i !== index);
      setDiscountVariants(updatedVariants);
    }
  };

  const handleCombinableChange = (key: string, value: boolean) => {
    setCombinableDiscounts({
      ...combinableDiscounts,
      [key]: value,
    });
  };

  const handleEligibilityChange = (key: string, value: boolean) => {
    setEligibility({
      ...eligibility,
      [key]: value,
    });
  };

  const showAudienceMessage = discountVariants.length > 1;

  // Handle collection search
  const handleCollectionSearch = (searchTerm: string) => {
    setCollectionSearchInput(searchTerm);

    if (onCollectionSearch) {
      onCollectionSearch(searchTerm);
    }
    // Only handle clearing the selection
    if (searchTerm === '') {
      setFilteredCollections(collectionOptions);
    } else {
      const filtered = collectionOptions.filter(
        x => x.label.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1
      );
      setFilteredCollections(filtered);
    }
  };

  // Handle collection selection
  const handleCollectionSelect = (value: string) => {
    // Check if the collection is already selected
    if (!selectedCollections.includes(value)) {
      // Add the new collection to the array
      setSelectedCollections([...selectedCollections, value]);
    }

    // Reset the search input
    setCollectionSearchInput('');
    setFilteredCollections(collectionOptions);
  };

  const handleRemoveCollection = (collectionId: string) => {
    const updatedCollections = selectedCollections.filter(id => id !== collectionId);
    setSelectedCollections(updatedCollections);
  };

  // Get collection options for display
  const selectedCollectionOptions = selectedCollections
    .map(colId => collectionOptions.find(opt => opt.value === colId))
    .filter(Boolean) as DropdownOption[];

  return (
    <div className="w-full flex flex-col gap-6">
      <div>
        <div className="flex items-center justify-between mb-2">
          <GroupLabel label="Discount" className="font-bold" />
        </div>
        {showAudienceMessage && (
          <p className="text-gray-600 text-sm mb-4">Audience distributed evenly.</p>
        )}

        {discountVariants.map((variant, index) => (
          <div key={index} className="mb-6 relative">
            <div className="flex items-center gap-4 mb-2">
              {discountVariants.length > 1 && (
                <span className="text-gray-700 font-medium">Discount {index + 1}</span>
              )}
              {discountVariants.length > 1 && (
                <IconButton
                  onClick={() => handleRemoveDiscountVariant(index)}
                  ariaLabel="Remove discount"
                  className="border border-gray-200 hover:bg-red-50"
                >
                  <TrashIcon size={20} />
                </IconButton>
              )}
            </div>
            <div className="flex items-center gap-4">
              <Textbox
                value={variant.discountPercentage.toString()}
                onChange={value => {
                  const numValue = parseInt(value) || 0;
                  handleDiscountChange(index, 'discountPercentage', numValue);
                }}
                placeholder="20"
                className="w-full max-w-[450px]"
                suffix="%"
              />

              {index === discountVariants.length - 1 && discountVariants.length < 2 && (
                <IconButton
                  onClick={handleAddDiscountVariant}
                  ariaLabel="Add another discount"
                  size="medium"
                  variant="default"
                >
                  <PlusIcon size={20} />
                </IconButton>
              )}
            </div>
          </div>
        ))}
      </div>

      <div>
        <div className="flex items-center mb-2">
          <GroupLabel
            label="Discount Prefix"
            className="font-bold"
            tooltip="The first part of the discount code your customers will see. Example: save-20-3456"
          />
        </div>
        <Textbox
          value={discountPrefix}
          onChange={value => {
            setDiscountPrefix(value);
          }}
          placeholder="save"
          className="w-full max-w-[450px]"
        />
      </div>

      <div>
        <div className="flex items-center mb-2">
          <GroupLabel
            label="Specific Collections"
            className="font-bold"
            tooltip="The products the nudge will apply to."
          />
        </div>
        <div className="mb-4">
          <RadioGroup
            value={productApplicability}
            onChange={(value, label) => setProductApplicability(value as 'all' | 'collection')}
            name="productApplicability"
          >
            <RadioOption value="all" label="All Products" />
            <RadioOption value="collection" label="Specific Collections" />
          </RadioGroup>
        </div>

        {productApplicability === 'collection' && (
          <div className="mt-2">
            <Dropdown
              options={filteredCollections.filter(opt => !selectedCollections.includes(opt.value))}
              value=""
              onChange={handleCollectionSelect}
              inputValue={collectionSearchInput}
              onInputChange={handleCollectionSearch}
              placeholder={
                isLoadingCollections ? 'Searching collections...' : 'Search for a collection...'
              }
              className="w-full max-w-md"
              suffix={
                selectedCollections.length > 0 ? (
                  <div className="flex items-center mr-2">
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                      {selectedCollections.length}
                    </span>
                  </div>
                ) : isLoadingCollections ? (
                  <div className="flex items-center mr-2">
                    <span className="text-sm text-gray-500">Loading...</span>
                  </div>
                ) : undefined
              }
            />

            {selectedCollectionOptions.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {selectedCollectionOptions.map(option => (
                  <Chip
                    key={option.value}
                    label={option.label}
                    onDelete={() => handleRemoveCollection(option.value)}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      <div>
        <div className="flex items-center mb-2">
          <GroupLabel
            label="Discount can be combined with:"
            className="font-bold"
            tooltip="The discounts with which the Vandra discount can be combined with."
          />
        </div>
        <div className="flex flex-col gap-3">
          <Checkbox
            checked={combinableDiscounts.product}
            onChange={checked => handleCombinableChange('product', checked)}
            label="Product Discounts"
          />
          <Checkbox
            checked={combinableDiscounts.order}
            onChange={checked => handleCombinableChange('order', checked)}
            label="Order Discounts"
          />
          <Checkbox
            checked={combinableDiscounts.shipping}
            onChange={checked => handleCombinableChange('shipping', checked)}
            label="Shipping Discounts"
          />
        </div>
      </div>

      <div>
        <div className="flex items-center mb-2">
          <GroupLabel
            label="Purchase Type Eligibility"
            className="font-bold"
            tooltip="The type of items the Vandra discount nudge can apply to."
          />
        </div>
        <div className="flex flex-col gap-3">
          <Checkbox
            checked={eligibility.singleItem}
            onChange={checked => handleEligibilityChange('singleItem', checked)}
            label="One-time purchase"
          />
          <Checkbox
            checked={eligibility.subscription}
            onChange={checked => handleEligibilityChange('subscription', checked)}
            label="Subscription"
          />
        </div>
      </div>
    </div>
  );
};
