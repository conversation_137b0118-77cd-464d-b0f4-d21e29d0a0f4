import React from 'react';
import { Slider } from '../common/Slider';
import { GroupLabel } from '../common/GroupLabel';

export interface AntiHoldoutSplitProps {
  antiHoldoutPercentage: number;
  onAntiHoldoutPercentageChange: (value: number) => void;
}

// Custom tooltip component that explains anti-holdout
const AntiHoldoutTooltip = () => {
  return (
    <div className="flex flex-col gap-2 text-secondary p-3">
      <span>
        Anti-Holdout: Percentage share of medium-intent visitors to show the discount nudge to in
        order to feed the Uplift modeling process.
      </span>
    </div>
  );
};

export const AntiHoldoutSplit = ({
  antiHoldoutPercentage = 0, // Default to 0% anti-holdout
  onAntiHoldoutPercentageChange,
}: AntiHoldoutSplitProps) => {
  // Ensure anti-holdout is within the 0-50 range
  const clampedAntiHoldout = Math.min(Math.max(antiHoldoutPercentage, 0), 50);

  // Handle changes to ensure the 0-50% range
  const handleAntiHoldoutPercentageChange = (value: number) => {
    // Clamp the value between 0 and 50
    const clampedValue = Math.min(Math.max(value, 0), 50);
    onAntiHoldoutPercentageChange(clampedValue);
  };

  return (
    <div className="mb-8">
      <GroupLabel
        label="Medium-Intent Visitors Uplift Sample"
        tooltip={<AntiHoldoutTooltip />}
        className="font-bold"
      />

      <div className="mt-4 max-w-96">
        {/* Anti-Holdout labels */}
        <div className="flex justify-between mb-2">
          <span className="text-primary font-bold text-base">None</span>
          <span className="text-secondary font-bold text-base">50%</span>
        </div>

        {/* Slider with inline percentages */}
        <div className="flex items-center gap-2">
          {/* Current percentage value */}
          <div className="min-w-[40px]">
            <span className="text-primary font-medium text-base">{`${clampedAntiHoldout}%`}</span>
          </div>

          {/* Slider */}
          <div className="flex-grow">
            <Slider
              value={clampedAntiHoldout}
              onChange={handleAntiHoldoutPercentageChange}
              min={0}
              max={50}
              step={5}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
