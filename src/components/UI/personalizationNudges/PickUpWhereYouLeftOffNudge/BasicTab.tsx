import React from 'react';
import { FontSelector } from '../../common/FontSelector';
import { GroupLabel } from '../../common/GroupLabel';
import { Textbox } from '../../common/Textbox';
import { TextboxColorPicker } from '../../common/TextboxColorPicker';
import { PickUpWhereYouLeftOffNudgeData } from '../../../../types/PickUpWhereYouLeftOffTypes';
import { MAX_LENGTH } from '../../../../types/constants';
import { getValueOrDefault } from '../../../../utils/helpers';

export interface BasicTabProps {
  paramOptions: Record<string, string>;
  onSave: (data: PickUpWhereYouLeftOffNudgeData) => void;
  data: PickUpWhereYouLeftOffNudgeData;
}

export const BasicTab = ({ paramOptions, onSave, data }: BasicTabProps) => {
  const handleChange = (field: keyof PickUpWhereYouLeftOffNudgeData, value: string) => {
    onSave({
      ...data,
      [field]: value,
    });
  };

  return (
    <div className="w-full sm:w-[600px] flex flex-col gap-4 py-4">
      <GroupLabel label="Headline Text" className="font-bold" />
      <Textbox
        label={`Line 1 (max ${MAX_LENGTH} characters)`}
        value={getValueOrDefault(data.headlineLine1, 'Welcome back!')}
        onChange={value => handleChange('headlineLine1', value)}
        maxLength={MAX_LENGTH}
        placeholder="Please enter headline line 1"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />
      <Textbox
        label={`Line 2 (max ${MAX_LENGTH} characters)`}
        value={getValueOrDefault(data.headlineLine2, 'Pick up where you left off:')}
        onChange={value => handleChange('headlineLine2', value)}
        maxLength={MAX_LENGTH}
        placeholder="Please enter headline line 2"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />

      <div className="flex flex-col gap-2">
        <GroupLabel label="Primary Color" className="font-bold" />
        <TextboxColorPicker
          color={getValueOrDefault(data.primaryColor, '#000000')}
          onChange={color => handleChange('primaryColor', color)}
          className="w-full sm:w-[150px]"
        />
      </div>

      <div className="flex flex-col gap-2">
        <div>
          <GroupLabel label="Font" className="font-bold" />
          <div className="text-sm italic text-text-dark">
            Note: Some custom fonts may not display in the live preview but will open on your site.
          </div>
        </div>
        <FontSelector
          value={getValueOrDefault(data.fontName, 'Arial')}
          onChange={value => handleChange('fontName', value)}
          placeholder="Arial"
          className="w-full sm:w-[400px]"
        />
      </div>
    </div>
  );
};
