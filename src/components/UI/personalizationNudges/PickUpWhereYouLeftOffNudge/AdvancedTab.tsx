import React from 'react';
import { TreatmentControlSplit } from '../TreatmentControlSplit';

interface AdvancedTabProps {
  holdoutPercentage: number;
  onHoldoutPercentageChange: (value: number) => void;
}

export const AdvancedTab = ({ holdoutPercentage, onHoldoutPercentageChange }: AdvancedTabProps) => {
  const handleSplitChange = (value: number) => {
    onHoldoutPercentageChange(value);
  };

  return (
    <div className="w-full">
      <TreatmentControlSplit
        holdoutPercentage={holdoutPercentage}
        onHoldoutPercentageChange={handleSplitChange}
      />
    </div>
  );
};
