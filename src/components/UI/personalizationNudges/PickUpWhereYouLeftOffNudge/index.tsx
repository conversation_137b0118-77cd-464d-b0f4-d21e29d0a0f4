import React from 'react';
import { Tabs } from '../../common/Tabs';
import { BasicTab } from './BasicTab';
import { AdvancedTab } from './AdvancedTab';
import { PickUpWhereYouLeftOffNudgeData } from '../../../../types/PickUpWhereYouLeftOffTypes';

interface PickUpWhereYouLeftOffNudgeProps {
  paramOptions: Record<string, string>;
  onSave: (data: PickUpWhereYouLeftOffNudgeData) => void;
  data: PickUpWhereYouLeftOffNudgeData;
}

export const PickUpWhereYouLeftOffNudge = ({
  paramOptions,
  onSave,
  data,
}: PickUpWhereYouLeftOffNudgeProps) => {
  const handleSplitChange = (newPercentage: number) => {
    onSave({
      ...data,
      holdoutPercentage: newPercentage,
    });
  };

  const tabs = [
    {
      id: 'basic',
      content: 'Basic',
      panelContent: <BasicTab paramOptions={paramOptions} onSave={onSave} data={data} />,
    },
    {
      id: 'advanced',
      content: 'Advanced',
      panelContent: (
        <AdvancedTab
          holdoutPercentage={data.holdoutPercentage}
          onHoldoutPercentageChange={handleSplitChange}
        />
      ),
    },
  ];

  return (
    <Tabs
      tabs={tabs.map(tab => ({
        ...tab,
        panelContent: <div className="ml-1">{tab.panelContent}</div>,
      }))}
    />
  );
};
