import React from 'react';
import { FontSelector } from '../../common/FontSelector';
import { GroupLabel } from '../../common/GroupLabel';
import { Textbox } from '../../common/Textbox';
import { TextboxColorPicker } from '../../common/TextboxColorPicker';
import { CartAbandonmentNudgeData } from '../../../../types/CartAbandonmentTypes';
import { MAX_LENGTH } from '../../../../types/constants';
import { getValueOrDefault } from '../../../../utils/helpers';

export interface BasicTabProps {
  paramOptions: Record<string, string>;
  onSave: (data: CartAbandonmentNudgeData) => void;
  data: CartAbandonmentNudgeData;
}

export const BasicTab = ({ paramOptions, onSave, data }: BasicTabProps) => {
  const handleChange = (field: keyof CartAbandonmentNudgeData, value: string) => {
    onSave({
      ...data,
      [field]: value,
    });
  };

  return (
    <div className="w-full sm:w-[600px] flex flex-col gap-4 py-4">
      <GroupLabel label="Header Text" className="font-bold" />
      <GroupLabel
        label="Group 1: No Savings in Cart"
        tooltip='To add a variable, simply type "{" and the possible variables for this nudge will pop up for you to choose one.'
        className="underline"
      />
      <Textbox
        label={`Line 1 (max ${MAX_LENGTH} characters)`}
        value={getValueOrDefault(data.group1Line1, 'Forgot something?')}
        onChange={value => handleChange('group1Line1', value)}
        maxLength={MAX_LENGTH}
        placeholder="Please enter line 1"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />
      <Textbox
        label={`Line 2 (max ${MAX_LENGTH} characters)`}
        value={getValueOrDefault(data.group1Line2, 'Your cart is waiting for you.')}
        onChange={value => handleChange('group1Line2', value)}
        maxLength={MAX_LENGTH}
        placeholder="Please enter line 2"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />
      <GroupLabel
        label="Group 2: Savings in Cart"
        tooltip='To add a variable, simply type "{" and the possible variables for this nudge will pop up for you to choose one.'
        className="underline"
      />
      <Textbox
        label={`Line 1 (max ${MAX_LENGTH} characters)`}
        value={getValueOrDefault(data.group2Line1, "Don't miss out on your savings!")}
        onChange={value => handleChange('group2Line1', value)}
        maxLength={MAX_LENGTH}
        placeholder="Please enter line 1"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />
      <Textbox
        label={`Line 2 (max ${MAX_LENGTH} characters)`}
        value={getValueOrDefault(
          data.group2Line2,
          'You have {discountAmount} in discounts in your cart.'
        )}
        onChange={value => handleChange('group2Line2', value)}
        maxLength={MAX_LENGTH}
        placeholder="Please enter line 2"
        className="w-full sm:w-[400px]"
        autocompleteParams={Object.keys(paramOptions)}
      />
      <div className="flex flex-col gap-2">
        <GroupLabel label="Button and Text Color" className="font-bold" />
        <TextboxColorPicker
          color={getValueOrDefault(data.buttonAndTextColor, '#000000')}
          onChange={color => handleChange('buttonAndTextColor', color)}
          className="w-full sm:w-[150px]"
        />
      </div>

      <div className="flex flex-col gap-2">
        <div>
          <GroupLabel label="Font" className="font-bold" />
          <div className="text-sm italic text-text-dark">
            Note: Some custom fonts may not display in the live preview but will open on your site.
          </div>
        </div>
        <FontSelector
          value={getValueOrDefault(data.fontName, 'Arial')}
          onChange={value => handleChange('fontName', value)}
          placeholder="Arial"
          className="w-full sm:w-[400px]"
        />
      </div>
    </div>
  );
};
