import React, { useState, useMemo } from 'react';
import { GroupLabel } from '../../common/GroupLabel';
import { TreatmentControlSplit } from '../TreatmentControlSplit';
import { Dropdown, DropdownOption } from '../../common/Dropdown';
import { Chip } from '../../common/Chip';

// Helper to normalize product IDs to numeric strings
const normalizeProductId = (id: string | number): string => {
  const idStr = String(id);
  // Handle Shopify GraphQL IDs (gid://shopify/Product/1234567890)
  if (idStr.startsWith('gid://shopify/Product/')) {
    return idStr.split('/').pop() || '';
  }
  // Already a simple ID
  return idStr;
};

// Custom tooltip component for product exclusions
const ProductExclusionsTooltip = () => {
  return (
    <div className="flex flex-col gap-2 text-secondary p-3">
      <span>Products you do not want to highlight in the nudge.</span>
    </div>
  );
};

interface AdvancedTabProps {
  holdoutPercentage: number;
  onHoldoutPercentageChange: (value: number) => void;
  excludedProductIds: string[];
  onExcludedProductIdsChange: (value: string[]) => void;
  productOptions?: DropdownOption[];
  onProductSearch?: (searchTerm: string) => Promise<void>;
  isLoadingProducts?: boolean;
  isSuperAdmin?: boolean;
}

export const AdvancedTab = ({
  holdoutPercentage,
  onHoldoutPercentageChange,
  excludedProductIds,
  onExcludedProductIdsChange,
  productOptions = [],
  onProductSearch,
  isLoadingProducts = false,
  isSuperAdmin = false,
}: AdvancedTabProps) => {
  const [inputValue, setInputValue] = useState<string>('');

  // Filter out already selected products from dropdown options
  const filteredOptions = useMemo(() => {
    const normalizedExcludedIds = excludedProductIds.map(normalizeProductId);
    return productOptions.filter(
      option => !normalizedExcludedIds.includes(normalizeProductId(option.value))
    );
  }, [productOptions, excludedProductIds]);

  const handleHoldoutPercentageChange = (value: number) => {
    onHoldoutPercentageChange(value);
  };

  // Handle multi-select changes with improved consistency
  const handleProductChange = (newValue: string) => {
    setInputValue('');

    // Always use the normalized form for storage
    const normalizedValue = normalizeProductId(newValue);

    // Find the actual product option to get the full data
    const selectedOption = productOptions.find(
      opt => normalizeProductId(opt.value) === normalizedValue
    );

    if (selectedOption) {
      // Always store the normalized ID (not the full GID)
      onExcludedProductIdsChange([...excludedProductIds, normalizedValue]);
    }
  };

  // Handle removing a product
  const handleRemoveProduct = (productId: string) => {
    const updatedIds = excludedProductIds.filter(id => id !== productId);
    onExcludedProductIdsChange(updatedIds);
  };

  // Handle input change for product search
  const handleInputChange = async (value: string) => {
    setInputValue(value);
    if (onProductSearch) {
      await onProductSearch(value);
    }
  };

  // Map selected IDs to display names
  const selectedProducts = useMemo(() => {
    return excludedProductIds.map(id => {
      // Find matching product option
      const product = productOptions.find(
        opt => normalizeProductId(opt.value) === normalizeProductId(id)
      );
      return {
        id,
        label: product ? product.label : id,
      };
    });
  }, [excludedProductIds, productOptions]);

  return (
    <div className="w-full">
      <TreatmentControlSplit
        holdoutPercentage={holdoutPercentage}
        onHoldoutPercentageChange={handleHoldoutPercentageChange}
      />

      {/* Product Exclusions Section */}
      {isSuperAdmin && (
        <div>
          <GroupLabel label="Product Exclusions" tooltip={<ProductExclusionsTooltip />} />

          <div className="mt-4">
            <Dropdown
              options={filteredOptions}
              value={''}
              onChange={handleProductChange}
              inputValue={inputValue}
              onInputChange={handleInputChange}
              placeholder={isLoadingProducts ? 'Loading products...' : 'Search products...'}
              selectionOnly={false}
              className="w-full max-w-96"
              serverSideFiltering={true} // Let our code handle filtering, not the dropdown
              suffix={
                excludedProductIds.length > 0 ? (
                  <div className="flex items-center mr-2">
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                      {excludedProductIds.length}
                    </span>
                  </div>
                ) : isLoadingProducts ? (
                  <div className="flex items-center mr-2">
                    <span className="text-sm text-gray-500">Loading...</span>
                  </div>
                ) : undefined
              }
            />

            {/* Display selected products as tags */}
            {selectedProducts.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {selectedProducts.map(({ id, label }) => (
                  <Chip key={id} label={label} onDelete={() => handleRemoveProduct(id)} />
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
