import React from 'react';
import { CartAbandonmentNudgeData } from '../../../../types/CartAbandonmentTypes';
import { Tabs } from '../../common/Tabs';
import { BasicTab } from './BasicTab';
import { AdvancedTab } from './AdvancedTab';
import { DropdownOption } from '../../common/Dropdown';

interface CartAbandonmentNudgeProps {
  paramOptions: Record<string, string>;
  productOptions: DropdownOption[];
  onSave: (data: CartAbandonmentNudgeData) => void;
  data: CartAbandonmentNudgeData;
  onProductSearch?: (searchTerm: string) => Promise<void>;
  isLoadingProducts?: boolean;
  isSuperAdmin?: boolean;
}

export const CartAbandonmentNudge = ({
  paramOptions,
  productOptions,
  onSave,
  data,
  onProductSearch,
  isLoadingProducts = false,
  isSuperAdmin = false,
}: CartAbandonmentNudgeProps) => {
  const handleHoldoutPercentageChange = (newHoldoutPercentage: number) => {
    onSave({
      ...data,
      holdoutPercentage: newHoldoutPercentage,
    });
  };

  const handleExcludedProductIdsChange = (newExcludedIds: string[]) => {
    onSave({
      ...data,
      excludedProductIds: newExcludedIds,
    });
  };

  const tabs = [
    {
      id: 'basic',
      content: 'Basic',
      panelContent: <BasicTab paramOptions={paramOptions} onSave={onSave} data={data} />,
    },
    {
      id: 'advanced',
      content: 'Advanced',
      panelContent: (
        <AdvancedTab
          holdoutPercentage={data.holdoutPercentage}
          onHoldoutPercentageChange={handleHoldoutPercentageChange}
          excludedProductIds={data.excludedProductIds || []}
          onExcludedProductIdsChange={handleExcludedProductIdsChange}
          productOptions={productOptions}
          onProductSearch={onProductSearch}
          isLoadingProducts={isLoadingProducts}
          isSuperAdmin={isSuperAdmin}
        />
      ),
    },
  ];

  return (
    <Tabs
      tabs={tabs.map(tab => ({
        ...tab,
        panelContent: <div className="ml-1">{tab.panelContent}</div>,
      }))}
    />
  );
};
