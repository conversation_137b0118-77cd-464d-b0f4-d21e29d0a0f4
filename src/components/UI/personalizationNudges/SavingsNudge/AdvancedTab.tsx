import React from 'react';
import { SavingsNudgeData } from '../../../../types/SavingsNudgeTypes';
import { TreatmentControlSplit } from '../TreatmentControlSplit';

interface AdvancedTabProps {
  data: SavingsNudgeData;
  onChange: (field: keyof SavingsNudgeData, value: any) => void;
}

export const AdvancedTab: React.FC<AdvancedTabProps> = ({ data, onChange }) => {
  const holdoutPercentage = data.holdoutPercentage || 0;
  const handleSplitChange = (value: number) => onChange('holdoutPercentage', value);

  return (
    <div className="w-full sm:w-[600px] flex flex-col gap-4 py-4">
      <TreatmentControlSplit
        holdoutPercentage={holdoutPercentage}
        onHoldoutPercentageChange={handleSplitChange}
      />
    </div>
  );
};
