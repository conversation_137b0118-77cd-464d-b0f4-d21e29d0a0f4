import React, { useEffect, useState } from 'react';
import { SavingsNudgeData } from '../../../../types/SavingsNudgeTypes';
import { BasicTab } from './BasicTab';
import { AdvancedTab } from './AdvancedTab';
import { Tabs } from '../../common/Tabs';

interface SavingsNudgeProps {
  data: SavingsNudgeData;
  onSave: (data: SavingsNudgeData) => void;
  paramOptions: Record<string, string>;
}

export const SavingsNudge: React.FC<SavingsNudgeProps> = ({ data, paramOptions, onSave }) => {
  const [formData, setFormData] = useState<SavingsNudgeData>(data);
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  // Update form data when props change
  useEffect(() => {
    setFormData(data);
  }, [data]);

  // Handle form changes and trigger save
  const handleChange = (field: keyof SavingsNudgeData, value: any) => {
    setFormData(prev => {
      const updatedData = { ...prev, [field]: value };
      setTimeout(() => onSave(updatedData), 0);
      return updatedData;
    });
  };

  // Handle number input changes
  const handleNumberChange = (field: keyof SavingsNudgeData, value: string) => {
    // For threshold field, we want to allow empty string in the UI but store as 0
    if (field === 'threshold' && value === '') {
      handleChange(field, 0);
      return;
    }
    const numValue = value === '' ? 0 : Number(value);
    handleChange(field, numValue);
  };

  // Tabs configuration
  const handleTabChange = (index: number) => setSelectedTabIndex(index);
  const tabs = [
    {
      id: 'basic',
      content: 'Basic',
      panelContent: (
        <BasicTab
          data={formData}
          paramOptions={paramOptions}
          onChange={handleChange}
          onNumberChange={handleNumberChange}
        />
      ),
    },
    {
      id: 'advanced',
      content: 'Advanced',
      panelContent: <AdvancedTab data={formData} onChange={handleChange} />,
    },
  ];

  return (
    <Tabs
      tabs={tabs.map(tab => ({
        ...tab,
        panelContent: (
          <div className="flex flex-col h-full min-h-0 overflow-y-auto">
            <div className="ml-1">{tab.panelContent}</div>
          </div>
        ),
      }))}
      selected={selectedTabIndex}
      onSelect={handleTabChange}
    />
  );
};
