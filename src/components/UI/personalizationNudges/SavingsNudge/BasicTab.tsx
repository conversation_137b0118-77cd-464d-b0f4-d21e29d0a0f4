import React from 'react';
import { SavingsNudgeData } from '../../../../types/SavingsNudgeTypes';
import { GroupLabel } from '../../common/GroupLabel';
import { Textbox } from '../../common/Textbox';
import { TextboxColorPicker } from '../../common/TextboxColorPicker';
import { RadioGroup, RadioOption } from '../../common/RadioGroup';
import { Slider } from '../../common/Slider';
import { Tooltip } from '../../common/Tooltip';
import { InfoIcon } from '../../common/icons/InfoIcon';
import { ButtonGroup } from '../../common/ButtonGroup';
import { LeftIcon } from '../../common/icons/LeftIcon';
import { RightIcon } from '../../common/icons/RightIcon';
import { TopIcon } from '../../common/icons/TopIcon';
import { BottomIcon } from '../../common/icons/BottomIcon';
import { getValueOrDefault } from '../../../../utils/helpers';
import { FontSelector } from '../../common/FontSelector';

interface BasicTabProps {
  data: SavingsNudgeData;
  paramOptions: Record<string, string>;
  onChange: (field: keyof SavingsNudgeData, value: any) => void;
  onNumberChange: (field: keyof SavingsNudgeData, value: string) => void;
}

export const BasicTab: React.FC<BasicTabProps> = ({
  data,
  paramOptions,
  onChange,
  onNumberChange,
}) => {
  const handleDestinationChange = (destination: string, label: string) => {
    onChange('buttonDestination', destination);
    const prevDefault = data.buttonDestination === '/cart' ? 'Cart' : 'Checkout';
    if (!data.buttonText || data.buttonText === prevDefault) {
      onChange('buttonText', destination === '/cart' ? 'Cart' : 'Checkout');
    }
  };

  return (
    <div className="w-full sm:w-[600px] flex flex-col gap-4 py-4">
      <div>
        <GroupLabel
          label="Tab Text"
          className="font-bold mb-2"
          tooltip="Use {total_savings} to display the savings amount"
        />
        <div className="text-sm italic text-text-dark">Maximum 50 characters</div>
        <Textbox
          value={getValueOrDefault(data.tabText, 'Savings: {total_savings}')}
          onChange={value => onChange('tabText', value)}
          placeholder="Tab Text"
          className="w-full"
          maxLength={50}
          autocompleteParams={Object.keys(paramOptions)}
        />
      </div>

      <div>
        <GroupLabel
          label="Expanded Headline"
          className="font-bold mb-2"
          tooltip="Use {total_savings} to display the savings amount"
        />
        <div className="text-sm italic text-text-dark">Maximum 35 characters (1 line)</div>
        <Textbox
          value={getValueOrDefault(data.expandedHeadline, 'Lucky you… {total_savings} in savings!')}
          onChange={value => onChange('expandedHeadline', value)}
          placeholder="Expanded Headline"
          className="w-full"
          maxLength={35}
          autocompleteParams={Object.keys(paramOptions)}
        />
      </div>

      <div>
        <GroupLabel
          label="Expanded Body"
          className="font-bold mb-2"
          tooltip="Describe the benefits of checking out now"
        />
        <div className="text-sm italic text-text-dark">Maximum 75 characters (2 lines)</div>
        <Textbox
          value={getValueOrDefault(
            data.expandedBody,
            "When you're ready, head to checkout and lock in these savings."
          )}
          onChange={value => onChange('expandedBody', value)}
          placeholder="Expanded Body"
          className="w-full"
          maxLength={75}
        />
      </div>

      <div>
        <GroupLabel
          label="Button Destination"
          className="font-bold mb-2"
          tooltip="Choose where the button will take the customer"
        />
        <RadioGroup
          value={data.buttonDestination || '/checkout'}
          onChange={handleDestinationChange}
          name="buttonDestination"
          layout="horizontal"
        >
          <RadioOption value="/checkout" label="Checkout" />
          <RadioOption value="/cart" label="Cart" />
        </RadioGroup>
      </div>

      {/* Button Label Field */}
      <div>
        <GroupLabel
          label="Button Label"
          className="font-bold mb-2"
          tooltip="Customize the button label. It defaults to the button's destination."
        />
        <div className="text-sm italic text-text-dark">Maximum 25 characters</div>
        <Textbox
          value={
            data.buttonText !== undefined && data.buttonText !== null
              ? data.buttonText
              : data.buttonDestination === '/cart'
                ? 'Cart'
                : 'Checkout'
          }
          onChange={value => onChange('buttonText', value)}
          placeholder="Button Label"
          className="w-full"
          maxLength={25}
        />
      </div>

      <div className="flex flex-col gap-2">
        <GroupLabel label="Colors" className="font-bold" />
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <GroupLabel label="Tab Color" className="font-medium text-sm" />
            <TextboxColorPicker
              color={data.tabColor || '#2B3336'}
              onChange={color => onChange('tabColor', color)}
              className="w-full sm:w-[150px]"
            />
          </div>
          <div>
            <GroupLabel label="Tab Font Color" className="font-medium text-sm" />
            <TextboxColorPicker
              color={data.tabFontColor || '#FFFFFF'}
              onChange={color => onChange('tabFontColor', color)}
              className="w-full sm:w-[150px]"
            />
          </div>
          <div>
            <GroupLabel label="Expanded Font Color" className="font-medium text-sm" />
            <TextboxColorPicker
              color={data.primaryFontColor || '#232323'}
              onChange={color => onChange('primaryFontColor', color)}
              className="w-full sm:w-[150px]"
            />
          </div>
          <div>
            <GroupLabel label="Expanded Background Color" className="font-medium text-sm" />
            <TextboxColorPicker
              color={data.backgroundColor || '#FFFFFF'}
              onChange={color => onChange('backgroundColor', color)}
              className="w-full sm:w-[150px]"
            />
          </div>
          <div>
            <GroupLabel label="Button Color" className="font-medium text-sm" />
            <TextboxColorPicker
              color={data.primaryColor || '#232323'}
              onChange={color => onChange('primaryColor', color)}
              className="w-full sm:w-[150px]"
            />
          </div>
        </div>
      </div>

      {/* Font Selector Field */}
      <div className="flex flex-col gap-2">
        <div>
          <GroupLabel label="Font" className="font-bold" />
          <div className="text-sm italic text-text-dark">
            Note: Some custom fonts may not display in the live preview but will open on your site.
          </div>
        </div>
        <FontSelector
          value={getValueOrDefault(data.font, 'Inter')}
          onChange={value => onChange('font', value)}
          placeholder="Inter"
          className="w-full sm:w-[250px]"
        />
      </div>

      <div>
        <GroupLabel label="Positioning" className="font-bold mt-4" />
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <GroupLabel label="Positioning" className="font-medium text-sm" />
            <ButtonGroup
              options={[
                { value: 'Left', label: 'Left', icon: <LeftIcon /> },
                { value: 'Right', label: 'Right', icon: <RightIcon /> },
              ]}
              value={data.positioning || 'Right'}
              onChange={value => onChange('positioning', value)}
              className="w-full sm:w-[150px]"
            />
          </div>
          <div>
            <GroupLabel label="Anchor" className="font-medium text-sm" />
            <ButtonGroup
              options={[
                { value: 'Top', label: 'Top', icon: <TopIcon /> },
                { value: 'Bottom', label: 'Bottom', icon: <BottomIcon /> },
              ]}
              value={data.anchor || 'Top'}
              onChange={value => onChange('anchor', value)}
              className="w-full sm:w-[150px]"
            />
          </div>
          <div>
            <div className="flex items-center">
              <GroupLabel label="Distance From Anchor (vh)" className="font-medium text-sm" />
              <Tooltip
                content="This controls the vertical distance from the edge of the screen (top or bottom)."
                preferredPosition="above"
              >
                <span className="ml-1">
                  <InfoIcon />
                </span>
              </Tooltip>
            </div>
            <Slider
              value={data.distanceFromAnchor || 30}
              onChange={value => onChange('distanceFromAnchor', value)}
              min={1}
              max={100}
              step={1}
              className="w-full sm:w-[150px]"
            />
            <div className="text-xs text-gray-500 mt-1">{data.distanceFromAnchor || 30}vh</div>
          </div>
        </div>
      </div>

      {/* Savings Threshold Field */}
      <div className="mt-4">
        <div className="flex items-center">
          <GroupLabel label="Minimum Savings Threshold ($)" className="font-bold" />
          <Tooltip
            content="The nudge will only be shown when cart savings are equal to or greater than this amount."
            preferredPosition="above"
          >
            <span className="ml-1">
              <InfoIcon />
            </span>
          </Tooltip>
        </div>
        <Textbox
          type="number"
          value={data.threshold?.toString() || ''}
          onChange={value => onNumberChange('threshold', value)}
          placeholder="0.00"
          className="w-full sm:w-[150px]"
        />
        <div className="text-xs text-gray-500 mt-1">Minimum $0.00</div>
      </div>
    </div>
  );
};
