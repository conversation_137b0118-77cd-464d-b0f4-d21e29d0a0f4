import React from 'react';
import { SocialMediaContentNudgeData } from '../../../../types/SocialMediaContentTypes';
import { TreatmentControlSplit } from '../TreatmentControlSplit';
import { Slider } from '../../common/Slider';

interface AdvancedTabProps {
  data: SocialMediaContentNudgeData;
  onSave: (data: SocialMediaContentNudgeData) => void;
  isSuperAdmin?: boolean;
}

export const AdvancedTab = ({ data, onSave, isSuperAdmin = false }: AdvancedTabProps) => {
  const holdoutPercentage = data.holdoutPercentage ?? 0;
  const minDwellTime = data.minDwellTime ?? 240;
  const maxDwellTime = data.maxDwellTime ?? 360;
  const interval = data.interval ?? 60;

  const handleHoldoutPercentageChange = (value: number) => {
    onSave({
      ...data,
      holdoutPercentage: value,
    });
  };

  const handleMinDwellTimeChange = (value: number) => {
    onSave({
      ...data,
      minDwellTime: value,
    });
  };

  const handleMaxDwellTimeChange = (value: number) => {
    onSave({
      ...data,
      maxDwellTime: value,
    });
  };

  const handleIntervalChange = (value: number) => {
    onSave({
      ...data,
      interval: value,
    });
  };

  return (
    <div className="w-full space-y-6">
      <TreatmentControlSplit
        holdoutPercentage={holdoutPercentage}
        onHoldoutPercentageChange={handleHoldoutPercentageChange}
      />

      {isSuperAdmin && (
        <div className="mt-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Timing Settings</h3>

          <div className="space-y-6 max-w-md">
            <div>
              <div className="flex justify-between items-center">
                <label className="block text-sm font-medium text-gray-700">
                  Minimum Dwell Time
                </label>
                <span className="text-sm text-gray-500">{minDwellTime} seconds</span>
              </div>
              <Slider
                value={minDwellTime}
                onChange={handleMinDwellTimeChange}
                min={30}
                max={600}
                step={30}
                className="mt-2"
              />
              <p className="mt-1 text-sm text-gray-500">
                Minimum time a user needs to spend on a page before the nudge appears
              </p>
            </div>

            <div>
              <div className="flex justify-between items-center">
                <label className="block text-sm font-medium text-gray-700">
                  Maximum Dwell Time
                </label>
                <span className="text-sm text-gray-500">{maxDwellTime} seconds</span>
              </div>
              <Slider
                value={maxDwellTime}
                onChange={handleMaxDwellTimeChange}
                min={60}
                max={1200}
                step={30}
                className="mt-2"
              />
              <p className="mt-1 text-sm text-gray-500">
                Maximum time after which the nudge will no longer appear
              </p>
            </div>

            <div>
              <div className="flex justify-between items-center">
                <label className="block text-sm font-medium text-gray-700">Check Interval</label>
                <span className="text-sm text-gray-500">{interval} seconds</span>
              </div>
              <Slider
                value={interval}
                onChange={handleIntervalChange}
                min={10}
                max={120}
                step={10}
                className="mt-2"
              />
              <p className="mt-1 text-sm text-gray-500">
                How often to check if the dwell time criteria has been met
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
