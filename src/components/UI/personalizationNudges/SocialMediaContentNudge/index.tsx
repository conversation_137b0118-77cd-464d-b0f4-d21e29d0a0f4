import React, { useState } from 'react';
import { AdvancedTab } from './AdvancedTab';
import { BasicTab } from './BasicTab';
import { SocialMediaContentNudgeData } from '../../../../types/SocialMediaContentTypes';
import { DropdownOption } from '../../common/Dropdown';
import { Tabs } from '../../common/Tabs';

export interface SocialMediaContentNudgeProps {
  paramOptions: Record<string, string>;
  onSave: (data: SocialMediaContentNudgeData) => void;
  onUpdate?: () => Promise<void>;
  data: SocialMediaContentNudgeData;
  productOptions?: DropdownOption[];
  onProductSearch?: (searchTerm: string) => Promise<void>;
  isLoadingProducts?: boolean;
  previewVideoUrl?: string;
  previewVideoFilename?: string;
  handleSetPreviewVideo: (url: string, filename?: string) => void;
  isSuperAdmin?: boolean;
}

export const SocialMediaContentNudge = ({
  paramOptions,
  onSave,
  onUpdate,
  data,
  onProductSearch,
  isLoadingProducts = false,
  productOptions = [],
  previewVideoUrl,
  previewVideoFilename,
  handleSetPreviewVideo,
  isSuperAdmin = false,
}: SocialMediaContentNudgeProps) => {
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  const handleTabChange = (selectedTabIndex: number) => {
    setSelectedTabIndex(selectedTabIndex);
  };

  const tabs = [
    {
      id: 'basic',
      content: 'Basic',
      panelContent: (
        <BasicTab
          data={data}
          onSave={onSave}
          onUpdate={onUpdate}
          onProductSearch={onProductSearch}
          isLoadingProducts={isLoadingProducts}
          productOptions={productOptions}
          handleSetPreviewVideo={handleSetPreviewVideo}
          previewVideoUrl={previewVideoUrl}
          previewVideoFilename={previewVideoFilename}
        />
      ),
    },
    {
      id: 'advanced',
      content: 'Advanced',
      panelContent: <AdvancedTab data={data} onSave={onSave} isSuperAdmin={isSuperAdmin} />,
    },
  ];

  return (
    <Tabs
      tabs={tabs.map(tab => ({
        ...tab,
        panelContent: (
          <div className="flex flex-col h-full min-h-0 overflow-y-auto mr-4">
            <div className="ml-1">{tab.panelContent}</div>
          </div>
        ),
      }))}
      selected={selectedTabIndex}
      onSelect={handleTabChange}
    />
  );
};
