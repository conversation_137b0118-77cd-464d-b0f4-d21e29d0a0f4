import React, { useState, useEffect, useRef } from 'react';
import { RadioGroup, RadioOption } from '../../common/RadioGroup';
import { VideoIcon } from '../../common/icons/VideoIcon';
import {
  SocialMediaContentNudgeData,
  UploadedVideo,
} from '../../../../types/SocialMediaContentTypes';
import { UploadEmptyIcon } from '../../common/icons/UploadEmptyIcon';
import { Button } from '../../common/Button';
import { Dropdown, DropdownOption } from '../../common/Dropdown';
import { makeRequest } from '../../../../utils/makeRequest';
import { useAppBridge } from '@shopify/app-bridge-react';
import { Tooltip } from '../../common/Tooltip';
import Loader from '../../Loader';
import { IconButton } from '../../common/IconButton';
import { PlayIcon } from '../../common/icons/PlayIcon';
import { TrashIcon } from '../../common/icons/TrashIcon';
import { PauseIcon } from '../../common/icons/PauseIcon';
import { ConfirmationModal } from '../../common/modals/ConfirmationModal';

interface BasicTabProps {
  data: SocialMediaContentNudgeData;
  onSave: (data: SocialMediaContentNudgeData) => void;
  onUpdate?: () => Promise<void>;
  onProductSearch?: (searchTerm: string) => Promise<void>;
  isLoadingProducts?: boolean;
  productOptions?: DropdownOption[];
  handleSetPreviewVideo?: (url: string, filename: string) => void;
  previewVideoUrl?: string;
  previewVideoFilename?: string;
}

// Video Row Component to avoid duplication
const VideoRow = ({
  video,
  videoType,
  productName,
  previewVideoUrl,
  previewVideoFilename,
  handleSetPreviewVideo,
  defaultPreviewVideo,
  removingIndex,
  onDeleteClick,
}: {
  video: UploadedVideo;
  videoType: string;
  productName: React.ReactNode;
  previewVideoUrl?: string;
  previewVideoFilename?: string;
  handleSetPreviewVideo?: (url: string, filename: string) => void;
  defaultPreviewVideo?: string;
  removingIndex: boolean;
  onDeleteClick: () => void;
}) => {
  // Check if this video is currently playing (both URL and filename must match)
  const isCurrentlyPlaying =
    previewVideoUrl === video.asset_url && previewVideoFilename === video.filename;

  return (
    <tr>
      <td className="py-4 px-4 whitespace-nowrap align-middle">{videoType}</td>
      <td className="py-4 px-4 whitespace-nowrap align-middle max-w-[200px] truncate">
        {productName}
      </td>
      <td className="py-4 px-4 whitespace-nowrap align-middle max-w-[200px] truncate">
        {video.filename}
      </td>
      <td className="py-4 px-4 align-middle flex gap-2 min-w-0">
        <Tooltip
          content={isCurrentlyPlaying ? 'Revert to default video' : 'Preview video'}
          preferredPosition="above"
        >
          <IconButton
            onClick={() => {
              if (isCurrentlyPlaying && defaultPreviewVideo) {
                handleSetPreviewVideo && handleSetPreviewVideo(defaultPreviewVideo, '');
              } else {
                handleSetPreviewVideo && handleSetPreviewVideo(video.asset_url, video.filename);
              }
            }}
            ariaLabel={isCurrentlyPlaying ? 'Revert to default video' : 'Preview video'}
            className={
              `border ${isCurrentlyPlaying ? 'border-primary-500 bg-primary-50' : 'border-gray-200'} ` +
              (isCurrentlyPlaying ? 'ring-2 ring-primary-400' : '')
            }
          >
            {isCurrentlyPlaying ? (
              <PauseIcon size={20} color={'#7C3AED'} />
            ) : (
              <PlayIcon size={20} />
            )}
          </IconButton>
        </Tooltip>
        <Tooltip content="Delete video" preferredPosition="above">
          <IconButton
            onClick={onDeleteClick}
            ariaLabel="Delete video"
            className="border border-gray-200 hover:bg-red-50"
            disabled={removingIndex}
          >
            {removingIndex ? (
              <Loader style={{ width: '1.5rem', height: '1.5rem' }} />
            ) : (
              <TrashIcon size={20} />
            )}
          </IconButton>
        </Tooltip>
      </td>
    </tr>
  );
};

export const BasicTab = ({
  data,
  onSave,
  onUpdate,
  onProductSearch,
  isLoadingProducts = false,
  productOptions = [],
  handleSetPreviewVideo,
  previewVideoUrl,
  previewVideoFilename,
}: BasicTabProps) => {
  const shopify = useAppBridge();
  const [videoType, setVideoType] = useState<string>('product');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [productSearchInput, setProductSearchInput] = useState<string>('');
  const [uploadedVideos, setUploadedVideos] = useState<UploadedVideo[]>(data.uploadedVideos || []);
  const [filteredProducts, setFilteredProducts] = useState<DropdownOption[]>(productOptions);
  const [loading, setLoading] = useState(false);
  const [removingIndex, setRemovingIndex] = useState<number | null>(null);
  const [confirmDeleteIndex, setConfirmDeleteIndex] = useState<number | null>(null);
  const [isDragActive, setIsDragActive] = useState(false);
  const [, setIsDropdownOpen] = useState(false);
  const hasInitialSearched = useRef(false);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);

  // Ref to suppress search when selecting from dropdown
  const suppressSearchRef = useRef(false);
  // Ref for the dropdown input to blur on selection
  const dropdownInputRef = useRef<HTMLInputElement | null>(null);
  // Ref for the file input
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  // Update radio selection based on data
  useEffect(() => {
    if (data.videoSelectionMethod) {
      setVideoType(data.videoSelectionMethod);
    }
  }, [data.videoSelectionMethod]);

  // Update uploadedVideos when data changes
  useEffect(() => {
    if (data.uploadedVideos) {
      setUploadedVideos(data.uploadedVideos);
    }
  }, [data.uploadedVideos]);

  // Fetch initial products when dropdown is opened
  const handleDropdownOpenChange = (isOpen: boolean) => {
    setIsDropdownOpen(isOpen);

    // Only trigger search when opening (not closing) and if we haven't searched before
    if (isOpen && !hasInitialSearched.current && onProductSearch) {
      hasInitialSearched.current = true;
      onProductSearch('');
    }
  };

  // Wrap onProductSearch to respect suppressSearchRef
  const wrappedOnProductSearch = (searchTerm: string) => {
    if (suppressSearchRef.current) {
      suppressSearchRef.current = false;
      return;
    }
    if (onProductSearch) {
      return onProductSearch(searchTerm);
    }
  };

  // Handle video type selection
  const handleVideoTypeChange = (value: string, label: string) => {
    setVideoType(value);
    // Clear selected product when switching to general
    if (value === 'general') {
      setSelectedProductId('');
    }
    onSave({
      ...data,
      videoSelectionMethod: value,
    });
  };

  // Handle product selection from Dropdown
  const handleProductSelect = (productId: string) => {
    suppressSearchRef.current = true;
    setSelectedProductId(productId);

    // Find the selected product from options
    const product = productOptions?.find(p => p.value === productId);

    if (product) {
      setProductSearchInput(product.label);
    }
    // Blur the dropdown input to close the list
    if (dropdownInputRef.current) {
      dropdownInputRef.current.blur();
    }
  };

  // Handle product search input change
  const handleProductSearchInputChange = (value: string) => {
    if (suppressSearchRef.current) {
      suppressSearchRef.current = false;
      return; // Don't trigger search
    }
    setProductSearchInput(value);
    // If input is cleared, always search to show the top 50 alphabetically again
    if (value === '' && onProductSearch) {
      onProductSearch('');
    }
    // Only handle clearing the selection
    if (value === '') {
      setFilteredProducts(productOptions);
      setSelectedProductId('');
    } else {
      const filtered = productOptions.filter(
        x => x.label.toLowerCase().indexOf(value.toLowerCase()) !== -1
      );
      setFilteredProducts(filtered);
    }
  };

  // Handle file selection for upload
  const handleFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
      fileInputRef.current.click();
    }
  };

  // Shared file validation function
  const validateAndSetFile = (file: File) => {
    const allowedTypes = [
      'video/mp4', // MP4
      'video/quicktime', // MOV
      'video/x-msvideo', // AVI
      'video/x-matroska', // MKV
      'video/webm', // WebM
    ];
    const maxSize = 100 * 1024 * 1024; // 100 MB
    if (!allowedTypes.includes(file.type)) {
      alert('Only MP4, MOV, AVI, MKV, and WebM files are allowed.');
      return false;
    }
    if (file.size > maxSize) {
      alert('File size must be less than 100 MB.');
      return false;
    }
    setSelectedFile(file);
    return true;
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      validateAndSetFile(e.target.files[0]);
    }
    // Reset value so the same file can be selected again
    e.target.value = '';
  };

  // Handle drag events for drag-and-drop upload
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  };
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
  };
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndSetFile(e.dataTransfer.files[0]);
    }
  };

  // Function to handle Add button click
  const handleAddClick = async () => {
    if (!selectedFile) return;
    setLoading(true);
    setUploadProgress(0); // Start progress at 0
    let idToken = await shopify.idToken();
    try {
      // 1. Prepare the API path
      let apiPath = '/react/merchant_get_social_video_upload_presigned_url?id_token=' + idToken;
      if (videoType === 'product' && selectedProductId) {
        apiPath += `&shopify_gid=${encodeURIComponent(selectedProductId)}`;
      }
      // Add content_type to the API path if selectedFile exists
      if (selectedFile) {
        apiPath += `&content_type=${encodeURIComponent(selectedFile.type)}`;
      }

      // 2. Get the presigned URL and filename from backend using makeRequest
      const { url, product_id, variant_id } = await makeRequest({
        path: apiPath,
        method: 'GET',
        body: null,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // 3. Upload the file to S3 with progress tracking using XMLHttpRequest
      const contentType = selectedFile.type || 'video/mp4';
      await new Promise<void>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('PUT', url, true);
        xhr.setRequestHeader('Content-Type', contentType);

        xhr.upload.onprogress = event => {
          if (event.lengthComputable) {
            const percent = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(percent);
          }
        };

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            setUploadProgress(100);
            resolve();
          } else {
            reject(new Error('Upload failed: ' + xhr.statusText));
          }
        };

        xhr.onerror = () => reject(new Error('Upload failed'));
        xhr.send(selectedFile);
      });

      // I need to get a new idToken because the old one can be expired if the upload takes a long time
      idToken = await shopify.idToken();

      // 4. Register the asset in the backend
      const assetPayload = {
        product_id: product_id || undefined,
        variant_id: variant_id || undefined,
        asset_url: url.split('?')[0], // S3 URL without query params
        is_fallback: videoType === 'general' || !product_id,
        original_filename: selectedFile.name,
      };
      await makeRequest({
        path: '/react/merchant_add_social_video_asset?id_token=' + idToken,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(assetPayload),
      });

      // 5. Add the uploaded video to the UI
      let productTitle = '—';
      if (videoType === 'product' && selectedProductId) {
        const selectedProduct = productOptions.find(p => p.value === selectedProductId);
        if (selectedProduct) {
          productTitle = selectedProduct.label;
        }
      }

      // Extract filename from S3 URL
      const s3Url = url.split('?')[0];

      const newVideo: UploadedVideo = {
        product_name: productTitle || '—',
        asset_type: 'product_video',
        asset_key: videoType === 'product' ? `${product_id}-${variant_id}` : 'fallback',
        asset_url: s3Url,
        filename: selectedFile.name, // Use the original filename
        product_id: videoType === 'product' ? product_id : '—',
        variant_id: videoType === 'product' ? variant_id : '—',
      };

      const updatedVideos = [...uploadedVideos, newVideo];
      setUploadedVideos(updatedVideos);
      onSave({
        ...data,
        uploadedVideos: updatedVideos,
      });

      if (onUpdate) {
        await onUpdate();
      }

      setSelectedFile(null);
      setSelectedProductId('');
      setProductSearchInput('');
    } catch (err: any) {
      alert('Upload failed: ' + err.message);
    } finally {
      setLoading(false);
      setUploadProgress(null); // Reset progress
    }
  };

  // Check if the Add button should be enabled
  const isAddButtonEnabled =
    selectedFile && (videoType === 'general' || (videoType === 'product' && selectedProductId));

  // Function to handle removing a video
  const handleRemoveVideo = async (index: number) => {
    const videoToRemove = uploadedVideos[index];
    if (!videoToRemove) return;
    setRemovingIndex(index);
    const idToken = await shopify.idToken();
    try {
      await makeRequest({
        path: '/react/merchant_delete_social_video_asset?id_token=' + idToken,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ asset_url: videoToRemove.asset_url }),
      });
      const updatedVideos = [...uploadedVideos];
      updatedVideos.splice(index, 1);
      setUploadedVideos(updatedVideos);
      onSave({
        ...data,
        uploadedVideos: updatedVideos,
      });
    } catch (err: any) {
      alert('Failed to delete video: ' + (err?.message || err));
    } finally {
      setRemovingIndex(null);
    }
  };

  // Helper to format file size
  const formatFileSize = (size: number) => {
    if (size < 1024) return `${size} bytes`;
    const kb = size / 1024;
    if (kb < 1024) return `${kb.toFixed(1)} KB`;
    const mb = kb / 1024;
    return `${mb.toFixed(1)} MB`;
  };

  return (
    <div className="w-full">
      {/* Hidden file input */}
      <input
        type="file"
        accept="video/mp4,video/quicktime,video/x-msvideo,video/x-matroska,video/webm"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileInputChange}
      />
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-4">How It Works</h2>
        <p className="text-sm text-gray-600 mb-4">
          Upload a product-specific or general video. We strongly recommend that you have at least
          one general video.
        </p>

        <div className="mb-4">
          <p className="font-semibold mb-1">Product:</p>
          <p className="text-sm text-gray-600 mb-3">
            These videos show on the product description page.
          </p>

          <p className="font-semibold mb-1">General:</p>
          <p className="text-sm text-gray-600 mb-3">
            These videos show on product pages that you haven&apos;t specified a video for.
            They&apos;ll be randomly distributed.
          </p>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-4">Upload Videos</h2>

        <RadioGroup
          value={videoType}
          onChange={handleVideoTypeChange}
          name="videoType"
          layout="horizontal"
          className="mb-6"
        >
          <RadioOption value="product" label="Product" />
          <RadioOption value="general" label="General" />
        </RadioGroup>

        {videoType === 'product' && (
          <div className="mb-6">
            <Dropdown
              options={filteredProducts}
              value={selectedProductId}
              onChange={handleProductSelect}
              placeholder="Search for a product..."
              className="w-full"
              onInputChange={handleProductSearchInputChange}
              onDebouncedInputChange={wrappedOnProductSearch}
              inputValue={productSearchInput}
              isLoading={isLoadingProducts}
              debounceTime={300}
              inputRef={dropdownInputRef}
              onOpenChange={handleDropdownOpenChange}
              serverSideFiltering={true}
            />
          </div>
        )}

        <div
          className={`border-dashed border-2 rounded-md p-6 mb-6 flex flex-col items-center justify-center cursor-pointer transition-colors duration-150 ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
          }`}
          onClick={loading ? undefined : handleFileSelect}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          style={loading ? { pointerEvents: 'none', opacity: 0.6 } : {}}
        >
          <div className="flex flex-col items-center">
            <div className="mb-4">
              <VideoIcon size={24} />
            </div>
            <p className="text-center font-medium mb-1 flex items-center justify-center gap-2">
              {selectedFile ? (
                <span className="inline-flex items-center gap-2">
                  <span className="font-semibold text-gray-800" title={selectedFile.name}>
                    {selectedFile.name}
                  </span>
                  <span
                    className="ml-2 px-2 py-0.5 rounded bg-gray-100 text-xs text-gray-500 border border-gray-200"
                    title="File size"
                  >
                    {formatFileSize(selectedFile.size)}
                  </span>
                </span>
              ) : (
                'Choose a file or drag & drop it here'
              )}
            </p>
            {!selectedFile && (
              <p className="text-base text-secondary mt-1">
                MP4, MOV, AVI, MKV, and WebM formats, up to 100 MB
              </p>
            )}
            {uploadProgress !== null && (
              <div className="w-full bg-gray-200 rounded h-3 mb-4 mt-2">
                <div
                  className="bg-blue-500 h-3 rounded"
                  style={{ width: `${uploadProgress}%`, transition: 'width 0.2s' }}
                />
                <div className="text-xs text-gray-600 mt-1 text-center">{uploadProgress}%</div>
              </div>
            )}
            <Button
              onClick={e => {
                e.stopPropagation();
                if (!loading) handleFileSelect();
              }}
              className="mt-4"
              disabled={loading}
            >
              Browse Files
            </Button>
          </div>
        </div>

        <Button
          primary
          className="w-24 mb-10 flex items-center justify-center"
          onClick={handleAddClick}
          disabled={!isAddButtonEnabled || loading}
        >
          {loading ? <Loader style={{ width: '1.5rem', height: '1.5rem' }} /> : 'Add'}
        </Button>
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">Uploaded Videos</h2>

        {uploadedVideos.length > 0 ? (
          <div className="overflow-x-auto max-w-full">
            {/* Group videos by type */}
            {(() => {
              const productVideos = uploadedVideos.filter(video => video.asset_key !== 'fallback');
              const generalVideos = uploadedVideos.filter(video => video.asset_key === 'fallback');

              return (
                <table className="w-full">
                  <thead className="border-b border-secondary">
                    <tr>
                      <th className="py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider max-w-[200px] truncate">
                        Product
                      </th>
                      <th className="py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider max-w-[200px] truncate">
                        Video
                      </th>
                      <th className="py-3 text-left"></th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Product Videos */}
                    {productVideos.map((video, index) => (
                      <VideoRow
                        key={`product-${index}`}
                        video={video}
                        videoType="Product"
                        productName={
                          video.product_name && video.product_name !== '—' ? (
                            <Tooltip content={video.product_name} preferredPosition="above">
                              <span className="block truncate max-w-[200px] cursor-pointer">
                                {video.product_name}
                              </span>
                            </Tooltip>
                          ) : (
                            ''
                          )
                        }
                        previewVideoUrl={previewVideoUrl}
                        previewVideoFilename={previewVideoFilename}
                        handleSetPreviewVideo={handleSetPreviewVideo}
                        defaultPreviewVideo={data.defaultPreviewVideo}
                        removingIndex={removingIndex === uploadedVideos.indexOf(video)}
                        onDeleteClick={() => setConfirmDeleteIndex(uploadedVideos.indexOf(video))}
                      />
                    ))}

                    {/* Divider row between product and general videos */}
                    {productVideos.length > 0 && generalVideos.length > 0 && (
                      <tr>
                        <td colSpan={4} className="py-2">
                          <hr className="border-t border-dashed border-secondary" />
                        </td>
                      </tr>
                    )}

                    {/* General Videos */}
                    {generalVideos.map((video, index) => (
                      <VideoRow
                        key={`general-${index}`}
                        video={video}
                        videoType="General"
                        productName="—"
                        previewVideoUrl={previewVideoUrl}
                        previewVideoFilename={previewVideoFilename}
                        handleSetPreviewVideo={handleSetPreviewVideo}
                        defaultPreviewVideo={data.defaultPreviewVideo}
                        removingIndex={removingIndex === uploadedVideos.indexOf(video)}
                        onDeleteClick={() => setConfirmDeleteIndex(uploadedVideos.indexOf(video))}
                      />
                    ))}
                  </tbody>
                </table>
              );
            })()}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="p-4 mb-4">
              <UploadEmptyIcon size={76} />
            </div>
            <h3 className="text-lg font-medium mb-2">Upload your first video</h3>
            <p className="text-gray-500 max-w-md">
              Increase delight (and conversions) by showing your products in action!
            </p>
          </div>
        )}
      </div>

      {/* Confirmation Modal for Delete */}
      <ConfirmationModal
        open={confirmDeleteIndex !== null}
        onClose={() => setConfirmDeleteIndex(null)}
        onConfirm={() => {
          if (confirmDeleteIndex !== null) {
            handleRemoveVideo(confirmDeleteIndex);
            setConfirmDeleteIndex(null);
          }
        }}
        message="Are you sure you want to delete this video?"
        confirmText="Delete"
        cancelText="Cancel"
        destructive
      />
    </div>
  );
};
