import React, { ReactNode, useEffect } from 'react';

interface NudgeConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  showEmulationBanner?: boolean;
}

const NudgeConfigModal: React.FC<NudgeConfigModalProps> = ({
  isOpen,
  onClose,
  children,
  showEmulationBanner = false,
}) => {
  // Handle body scroll locking when modal is open
  useEffect(() => {
    const originalStyle = window.getComputedStyle(document.body).overflow;
    const originalPaddingRight = window.getComputedStyle(document.body).paddingRight;

    // Function to disable body scroll
    const disableBodyScroll = () => {
      // Get the scrollbar width to prevent content shift
      const scrollBarWidth = window.innerWidth - document.documentElement.clientWidth;

      // Save original scroll position
      const scrollY = window.scrollY;

      // Apply fixed positioning to body with current scroll offset
      document.body.style.top = `-${scrollY}px`;
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.overflow = 'hidden';

      // Add padding to prevent content shift
      if (scrollBarWidth > 0) {
        document.body.style.paddingRight = `${scrollBarWidth}px`;
      }
    };

    // Function to enable body scroll
    const enableBodyScroll = () => {
      // Restore original scroll position
      const scrollY = document.body.style.top;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflow = originalStyle;
      document.body.style.paddingRight = originalPaddingRight;

      // Scroll to the original position
      window.scrollTo(0, parseInt(scrollY || '0') * -1);
    };

    if (isOpen) {
      disableBodyScroll();
    }

    // Cleanup function to restore scroll when component unmounts
    return () => {
      if (isOpen) {
        enableBodyScroll();
      }
    };
  }, [isOpen]);

  if (!isOpen) return null;

  // Helper to check if children is a valid element before cloning
  const renderChildren = () => {
    if (React.isValidElement(children)) {
      return React.cloneElement(children as React.ReactElement<any>, {
        onClose,
        showEmulationBanner,
      });
    }
    return children; // Return children directly if not a valid element or multiple children
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white w-full h-full flex flex-col">
        {/* Content area with children (takes most of the space) */}
        <div className="flex-1 overflow-hidden">{renderChildren()}</div>

        {/* Footer removed */}
      </div>
    </div>
  );
};

export default NudgeConfigModal;
