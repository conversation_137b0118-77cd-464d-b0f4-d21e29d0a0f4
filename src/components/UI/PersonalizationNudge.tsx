import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { CartAbandonmentNudge } from './personalizationNudges/CartAbandonmentNudge/index';
import { NudgeHeader } from './common/NudgeHeader';
import { Button } from './common/Button';
import { NudgePreview } from './common/NudgePreview';
import { ConfirmationModal } from './common/modals/ConfirmationModal';
import { DiscountNudge } from './personalizationNudges/DiscountNudge/index';
import { PickUpWhereYouLeftOffNudge } from './personalizationNudges/PickUpWhereYouLeftOffNudge/index';
import { SocialMediaContentNudge } from './personalizationNudges/SocialMediaContentNudge/index';
import { NavigationNudge } from './personalizationNudges/NavigationNudge/index';
import { SavingsNudge } from './personalizationNudges/SavingsNudge/index';
import { AlertModal } from './common/modals/AlertModal';
import { LinkButton } from './common/LinkButton';
import { CheckIcon } from './common/icons/CheckIcon';
import { BackIcon } from './common/icons/BackIcon';
import { CartAbandonmentNudgeData } from '../../types/CartAbandonmentTypes';
import { DiscountNudgeData } from '../../types/DiscountNudgeTypes';
import { PickUpWhereYouLeftOffNudgeData } from '../../types/PickUpWhereYouLeftOffTypes';
import { SocialMediaContentNudgeData } from '../../types/SocialMediaContentTypes';
import { NavigationNudgeData } from '../../types/NavigationNudgeTypes';
import { SavingsNudgeData } from '../../types/SavingsNudgeTypes';

const Loader = require('./Loader').default;

// Helper function to compare nudge data with specific field exclusions
const compareNudgeData = (nudgeType: NudgeType, newData: any, initialData: any): boolean => {
  // Map of nudge types to fields that should be excluded from comparison
  const fieldsToExclude: Record<NudgeType, string[]> = {
    cartAbandonment: [],
    inSessionCartAbandonment: [],
    discount: [],
    pickUpWhereYouLeftOff: [],
    socialMediaContent: [
      'uploadedVideos',
      'defaultPreviewVideo',
      'videoUrl',
      'videoSelectionMethod',
    ],
    navigation: [],
    savings: [],
  };

  // Get fields to exclude for this nudge type
  const excludedFields = fieldsToExclude[nudgeType] || [];

  // If no fields to exclude, do a simple comparison
  if (excludedFields.length === 0) {
    return JSON.stringify(newData) !== JSON.stringify(initialData);
  }

  // Create deep copies to avoid modifying the original objects
  const newDataCopy = JSON.parse(JSON.stringify(newData));
  const initialDataCopy = JSON.parse(JSON.stringify(initialData));

  // Remove excluded fields from both objects
  excludedFields.forEach(field => {
    delete newDataCopy[field];
    delete initialDataCopy[field];
  });

  // Compare the sanitized objects
  return JSON.stringify(newDataCopy) !== JSON.stringify(initialDataCopy);
};

export type NudgeType =
  | 'cartAbandonment'
  | 'inSessionCartAbandonment'
  | 'discount'
  | 'pickUpWhereYouLeftOff'
  | 'socialMediaContent'
  | 'navigation'
  | 'savings';

interface CommonNudgeProps {
  nudgeType: NudgeType;
  paramOptions: Record<string, string>;
  dropdownOptions?: { label: string; value: string }[];
  onSave: (
    data:
      | CartAbandonmentNudgeData
      | DiscountNudgeData
      | PickUpWhereYouLeftOffNudgeData
      | SocialMediaContentNudgeData
      | NavigationNudgeData
      | SavingsNudgeData
  ) => Promise<void>;
  isPaused?: boolean;
  isSubmitting?: boolean;
  onPauseToggle?: (
    isPaused: boolean,
    data:
      | CartAbandonmentNudgeData
      | DiscountNudgeData
      | PickUpWhereYouLeftOffNudgeData
      | SocialMediaContentNudgeData
      | NavigationNudgeData
      | SavingsNudgeData
  ) => Promise<void>;
  onClose?: () => void;
  productOptions?: { label: string; value: string }[];
  onProductSearch?: (searchTerm: string) => Promise<void>;
  isLoadingProducts?: boolean;
  collectionOptions?: { label: string; value: string }[];
  onCollectionSearch?: (searchTerm: string) => Promise<void>;
  isLoadingCollections?: boolean;
  defaultData?:
    | CartAbandonmentNudgeData
    | DiscountNudgeData
    | PickUpWhereYouLeftOffNudgeData
    | SocialMediaContentNudgeData
    | NavigationNudgeData
    | SavingsNudgeData;
  showEmulationBanner?: boolean;
  isSuperAdmin?: boolean;
}

interface CartAbandonmentProps extends CommonNudgeProps {
  nudgeType: 'cartAbandonment';
  data: CartAbandonmentNudgeData;
  defaultData?: CartAbandonmentNudgeData;
}

interface InSessionCartAbandonmentProps extends CommonNudgeProps {
  nudgeType: 'inSessionCartAbandonment';
  data: CartAbandonmentNudgeData;
  defaultData?: CartAbandonmentNudgeData;
}

interface DiscountProps extends CommonNudgeProps {
  nudgeType: 'discount';
  data: DiscountNudgeData;
  defaultData?: DiscountNudgeData;
}

interface PickUpWhereYouLeftOffProps extends CommonNudgeProps {
  nudgeType: 'pickUpWhereYouLeftOff';
  data: PickUpWhereYouLeftOffNudgeData;
  defaultData?: PickUpWhereYouLeftOffNudgeData;
}

interface SocialMediaContentProps extends CommonNudgeProps {
  nudgeType: 'socialMediaContent';
  data: SocialMediaContentNudgeData;
  defaultData?: SocialMediaContentNudgeData;
}

interface NavigationProps extends CommonNudgeProps {
  nudgeType: 'navigation';
  data: NavigationNudgeData;
  defaultData?: NavigationNudgeData;
}

interface SavingsProps extends CommonNudgeProps {
  nudgeType: 'savings';
  data: SavingsNudgeData;
  defaultData?: SavingsNudgeData;
}

export type PersonalizationNudgeProps =
  | CartAbandonmentProps
  | InSessionCartAbandonmentProps
  | DiscountProps
  | PickUpWhereYouLeftOffProps
  | SocialMediaContentProps
  | NavigationProps
  | SavingsProps;

export const PersonalizationNudge = (props: PersonalizationNudgeProps) => {
  const {
    nudgeType,
    paramOptions,
    dropdownOptions,
    onSave,
    data: initialData,
    defaultData,
    isPaused: initialIsPaused = false,
    isSubmitting = false,
    onPauseToggle,
    onClose,
    productOptions = [],
    onProductSearch,
    isLoadingProducts = false,
    onCollectionSearch,
    isLoadingCollections = false,
    showEmulationBanner = false,
  } = props;

  const [loading, setLoading] = useState(false);
  const [isPaused, setIsPaused] = useState(initialIsPaused);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [justSaved, setJustSaved] = useState(false);
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);
  const [currentData, setCurrentData] = useState<
    | CartAbandonmentNudgeData
    | DiscountNudgeData
    | PickUpWhereYouLeftOffNudgeData
    | SocialMediaContentNudgeData
    | NavigationNudgeData
    | SavingsNudgeData
  >(initialData);
  const [previewState, setPreviewState] = useState<'no-savings' | 'savings'>('no-savings');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [previewVideoUrl, setPreviewVideoUrl] = useState<string>('');
  const [previewVideoFilename, setPreviewVideoFilename] = useState<string>('');
  const [showBackConfirmModal, setShowBackConfirmModal] = useState(false);

  // Emulation banner state - simplified to avoid API calls
  const [emulatingStore, setEmulatingStore] = useState<{ id: string; name: string } | null>(null);

  useEffect(() => {
    setCurrentData(initialData);
  }, [initialData]);

  // Check for existing emulation in sessionStorage when showEmulationBanner is true
  useEffect(() => {
    if (showEmulationBanner) {
      const storedEmulation = sessionStorage.getItem('emulating_store');
      if (storedEmulation) {
        setEmulatingStore(JSON.parse(storedEmulation));
      }
    }
  }, [showEmulationBanner]);

  const handleResetToCurrentStore = () => {
    setEmulatingStore(null);
    sessionStorage.removeItem('emulating_store');
    window.location.reload();
  };

  // Handler for form data saves from CartAbandonmentNudge
  const handleFormSave = useCallback(
    (
      newData:
        | CartAbandonmentNudgeData
        | DiscountNudgeData
        | PickUpWhereYouLeftOffNudgeData
        | SocialMediaContentNudgeData
        | NavigationNudgeData
        | SavingsNudgeData
    ) => {
      setCurrentData(newData);

      // Compare new data with initial data to determine if there are changes
      // excluding specific fields based on nudge type
      const hasChanges = compareNudgeData(nudgeType, newData, initialData);

      setHasUnsavedChanges(hasChanges);
    },
    [initialData, nudgeType]
  );

  // Handler to set preview video with both URL and filename
  const handleSetPreviewVideo = useCallback((url: string, filename?: string) => {
    setPreviewVideoUrl(url);
    setPreviewVideoFilename(filename || '');
  }, []);

  const handleReset = () => {
    setHasUnsavedChanges(false);
    // If defaultData is provided, use it for reset
    if (
      defaultData &&
      (nudgeType === 'cartAbandonment' ||
        nudgeType === 'inSessionCartAbandonment' ||
        nudgeType === 'discount' ||
        nudgeType === 'pickUpWhereYouLeftOff' ||
        nudgeType === 'socialMediaContent' ||
        nudgeType === 'navigation' ||
        nudgeType === 'savings')
    ) {
      handleFormSave(defaultData);
    } else if (
      nudgeType === 'cartAbandonment' ||
      nudgeType === 'inSessionCartAbandonment' ||
      nudgeType === 'discount' ||
      nudgeType === 'pickUpWhereYouLeftOff' ||
      nudgeType === 'socialMediaContent' ||
      nudgeType === 'navigation' ||
      nudgeType === 'savings'
    ) {
      // Fallback to initialData if no defaultData is provided
      handleFormSave(initialData);
    }
  };

  // Handler for preview state change
  const handlePreviewStateChange = useCallback((state: string) => {
    setPreviewState(state as 'no-savings' | 'savings');
  }, []);

  // This will render the appropriate nudge component based on nudgeType
  const renderNudge = () => {
    switch (nudgeType) {
      case 'cartAbandonment':
        return (
          <div>
            <CartAbandonmentNudge
              paramOptions={paramOptions}
              onSave={handleFormSave}
              data={currentData as CartAbandonmentNudgeData}
              productOptions={productOptions}
              onProductSearch={onProductSearch}
              isLoadingProducts={isLoadingProducts}
              isSuperAdmin={props.isSuperAdmin}
            />
          </div>
        );
      case 'inSessionCartAbandonment':
        return (
          <div>
            <CartAbandonmentNudge
              paramOptions={paramOptions}
              onSave={handleFormSave}
              data={currentData as CartAbandonmentNudgeData}
              productOptions={productOptions}
              onProductSearch={onProductSearch}
              isLoadingProducts={isLoadingProducts}
              isSuperAdmin={props.isSuperAdmin}
            />
          </div>
        );
      case 'discount':
        return (
          <div>
            <DiscountNudge
              paramOptions={paramOptions}
              onSave={handleFormSave}
              data={currentData as DiscountNudgeData}
              onCollectionSearch={onCollectionSearch}
              isLoadingCollections={isLoadingCollections}
              isSuperAdmin={props.isSuperAdmin}
            />
          </div>
        );
      case 'pickUpWhereYouLeftOff':
        return (
          <div>
            <PickUpWhereYouLeftOffNudge
              paramOptions={paramOptions}
              onSave={handleFormSave}
              data={currentData as PickUpWhereYouLeftOffNudgeData}
            />
          </div>
        );
      case 'socialMediaContent':
        return (
          <div>
            <SocialMediaContentNudge
              paramOptions={paramOptions}
              onSave={handleFormSave}
              onUpdate={handleUpdate}
              data={currentData as SocialMediaContentNudgeData}
              onProductSearch={onProductSearch}
              isLoadingProducts={isLoadingProducts}
              productOptions={productOptions}
              handleSetPreviewVideo={handleSetPreviewVideo}
              previewVideoUrl={previewVideoUrl}
              previewVideoFilename={previewVideoFilename}
              isSuperAdmin={props.isSuperAdmin}
            />
          </div>
        );
      case 'navigation':
        return (
          <div>
            <NavigationNudge
              paramOptions={paramOptions}
              onSave={handleFormSave}
              data={currentData as NavigationNudgeData}
            />
          </div>
        );
      case 'savings':
        return (
          <div>
            <SavingsNudge
              paramOptions={paramOptions}
              onSave={handleFormSave}
              data={currentData as SavingsNudgeData}
            />
          </div>
        );
      default:
        return <div>Unsupported nudge type</div>;
    }
  };

  const getNudgeHeaderProps = () => {
    switch (nudgeType) {
      case 'cartAbandonment': {
        return {
          title: 'Return Visitor Cart Abandonment Nudge',
          iconSrc: '/icons/cart-abandonment.svg',
          iconAlt: 'Cart Abandonment',
          description: 'Encourage visitors to check out if they have savings in their cart',
        };
      }
      case 'inSessionCartAbandonment': {
        return {
          title: 'In-Session Cart Abandonment Nudge',
          iconSrc: '/icons/cart-abandonment.svg',
          iconAlt: 'Cart Abandonment',
          description: 'Encourage visitors to check out if they have savings in their cart',
        };
      }
      case 'discount':
        return {
          title: 'On-the-Fence Discount Nudge',
          iconSrc: '/icons/discount.svg',
          iconAlt: 'Discount',
          description: 'Encourage visitors to check out if they have savings in their cart',
        };
      case 'pickUpWhereYouLeftOff':
        return {
          title: 'Pick-Up Where You Left Off (PUWYLO) Nudge',
          iconSrc: '/icons/pickup.svg',
          iconAlt: 'Pick Up Where You Left Off',
          description: 'Help visitors continue their shopping experience where they left off',
        };
      case 'socialMediaContent':
        return {
          title: 'Social Media Video Nudge',
          iconSrc: '/icons/social-media.svg',
          iconAlt: 'Social Media Content',
          description: 'Display social media content to engage visitors',
        };
      case 'navigation':
        return {
          title: 'New Visitor Data Collection / Navigational Nudge',
          iconSrc: '/icons/navigation.svg',
          iconAlt: 'Navigation',
          description: 'Help visitors navigate through your site with personalized options',
        };
      case 'savings':
        return {
          title: 'Savings Tab Nudge',
          iconSrc: '/icons/savings.svg',
          iconAlt: 'Savings',
          description: 'Encourage visitors to check out if they have savings in their cart',
        };
      default:
        return {
          title: 'Personalization Nudge',
          iconSrc: '',
          iconAlt: '',
          description: 'Configure your personalization nudge',
        };
    }
  };

  const handlePauseClick = () => {
    setShowStatusModal(true);
  };

  const handleStatusConfirm = async () => {
    const newPausedState = !isPaused;
    setShowStatusModal(false);

    if (onPauseToggle) {
      setIsTogglingStatus(true);
      try {
        await onPauseToggle(newPausedState, currentData);
        // Since we've reached this point without an error, consider the operation successful
        setIsPaused(newPausedState);
        setShowAlertModal(true);
      } catch (error) {
        // Error is already handled in the onPauseToggle implementation (toast shown there)
        console.error('Error toggling nudge status:', error);
      } finally {
        setIsTogglingStatus(false);
        //reflect the fact we saved the data
        setHasUnsavedChanges(false);
        setJustSaved(true);

        // Reset to "Save Draft" after 2 seconds
        setTimeout(() => {
          setJustSaved(false);
        }, 2000);
      }
    } else {
      // If no onPauseToggle handler is provided, just update the local state
      setIsPaused(newPausedState);
      setShowAlertModal(true);
    }
  };

  const handleUpdate = async () => {
    // Logic to update the nudge
    setLoading(true);
    try {
      await onSave(currentData);
      setHasUnsavedChanges(false);
      setJustSaved(true);

      // Reset to "Save Draft" after 2 seconds
      setTimeout(() => {
        setJustSaved(false);
      }, 2000);
    } catch (error) {
      // Error is already handled in the onSave implementation
      console.error('Error saving nudge:', error);
    } finally {
      setLoading(false);
    }
  };

  const getModalConfig = () => {
    if (isPaused) {
      return {
        message: 'Are you sure you want to activate this nudge?',
        confirmText: 'Yes',
      };
    }
    return {
      message: 'Are you sure you want to pause this nudge?',
      confirmText: 'Yes',
    };
  };

  const getAlertModalConfig = () => {
    if (isPaused) {
      return {
        message: "You've successfully paused this nudge.",
        buttonText: 'Close',
      };
    }

    return {
      message: "You've successfully activated this nudge.",
      buttonText: 'Close',
    };
  };

  const handleBackClick = () => {
    if (hasUnsavedChanges) {
      setShowBackConfirmModal(true);
    } else if (onClose) {
      onClose();
    }
  };

  const handleNoSave = () => {
    setShowBackConfirmModal(false);
    if (onClose) {
      onClose();
    }
  };

  const handleSaveAndBack = async () => {
    setShowBackConfirmModal(false);
    // Save changes first
    await handleUpdate();
    // Then close
    if (onClose) {
      onClose();
    }
  };

  const saveButtonText = useMemo(() => {
    return isPaused ? 'Save Draft' : 'Update';
  }, [isPaused]);

  const saveActionText = useMemo(() => {
    return isPaused ? 'save' : 'update';
  }, [isPaused]);

  return (
    <div className="flex flex-col w-full h-full min-h-0 overflow-hidden relative">
      {/* Loading Overlay */}
      {(isSubmitting || isTogglingStatus) && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50 rounded-lg">
          <Loader />
        </div>
      )}

      {/* Emulation Banner - Only show when prop is true and conditions are met */}
      {showEmulationBanner && emulatingStore && (
        <div className="bg-orange-100 border-l-4 border-orange-500 p-3 mx-8 mt-4 rounded-r-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <span className="text-orange-600 font-semibold">🔄 EMULATION MODE</span>
                <span className="text-orange-800">
                  Currently viewing: <strong>{emulatingStore.name}</strong>
                </span>
              </div>
            </div>
            <button
              onClick={handleResetToCurrentStore}
              className="px-4 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 transition-colors"
            >
              Exit Emulation
            </button>
          </div>
        </div>
      )}

      <ConfirmationModal
        open={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        onConfirm={handleStatusConfirm}
        size="small"
        {...getModalConfig()}
      />

      <ConfirmationModal
        open={showBackConfirmModal}
        onClose={handleNoSave}
        onConfirm={handleSaveAndBack}
        message={`You have unsaved changes. Do you want to ${saveActionText} this nudge?`}
        confirmText={saveButtonText}
        cancelText="No"
        size="small"
      />

      <AlertModal
        open={showAlertModal}
        onClose={() => setShowAlertModal(false)}
        size="small"
        {...getAlertModalConfig()}
      />

      {/* Header section */}
      <div className="px-8 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            {onClose && (
              <button
                onClick={handleBackClick}
                className="text-gray-600 hover:text-gray-900 p-1 rounded-full hover:bg-gray-100"
                aria-label="Back"
              >
                <BackIcon size={24} />
              </button>
            )}
            <NudgeHeader {...getNudgeHeaderProps()} />
          </div>
          <div className="flex items-center gap-3">
            {isPaused ? (
              <>
                <Button
                  onClick={handleUpdate}
                  loading={loading}
                  disabled={!hasUnsavedChanges || isSubmitting || isTogglingStatus}
                >
                  {justSaved ? (
                    <div className="flex items-center gap-1">
                      <CheckIcon size={16} />
                      <span>Saved</span>
                    </div>
                  ) : (
                    'Save Draft'
                  )}
                </Button>
                <Button
                  onClick={handlePauseClick}
                  disabled={isSubmitting || isTogglingStatus}
                  primary
                >
                  Activate
                </Button>
              </>
            ) : (
              <>
                <Button onClick={handlePauseClick} disabled={isSubmitting || isTogglingStatus}>
                  Pause
                </Button>
                <Button
                  onClick={handleUpdate}
                  loading={loading}
                  disabled={!hasUnsavedChanges || isSubmitting || isTogglingStatus}
                  primary
                >
                  {justSaved ? (
                    <div className="flex items-center gap-1">
                      <CheckIcon size={16} />
                      <span>Saved</span>
                    </div>
                  ) : (
                    'Update'
                  )}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Horizontal divider */}
      <div className="border-b border-gray-200 w-full"></div>

      {/* Content section with vertical split */}
      <div className="flex flex-1 h-full min-h-0">
        {/* Left side: Form */}
        <div className="flex-1 pl-8 py-4 border-r border-gray-200 flex flex-col min-h-0">
          <div className="flex flex-col flex-1 overflow-y-auto custom-scrollbar">
            {renderNudge()}
          </div>

          {/* Reset link at bottom of form - sticky */}
          <div className="sticky bottom-0 bg-white py-4 mt-4 border-t border-gray-200">
            <LinkButton
              onClick={handleReset}
              color="blue"
              size="md"
              className="underline font-medium"
            >
              Reset to Default
            </LinkButton>
          </div>
        </div>

        {/* Right side: Preview */}
        <div className="w-2/5 px-8 py-4 h-full overflow-y-auto custom-scrollbar min-w-[550px] min-h-[500px]">
          <NudgePreview
            nudgeType={nudgeType}
            data={currentData}
            dropdownOptions={dropdownOptions}
            selectedOption={previewState}
            onSelectChange={handlePreviewStateChange}
            paramOptions={paramOptions}
            previewVideoUrl={previewVideoUrl}
          />
        </div>
      </div>
    </div>
  );
};
