import React, { useMemo } from 'react';
import { PersonalizationNudge, NudgeType, PersonalizationNudgeProps } from './PersonalizationNudge';
import { CartAbandonmentNudgeData } from '../../types/CartAbandonmentTypes';
import { PickUpWhereYouLeftOffNudgeData } from '../../types/PickUpWhereYouLeftOffTypes';
import { SocialMediaContentNudgeData } from '../../types/SocialMediaContentTypes';
import { NavigationNudgeData } from '../../types/NavigationNudgeTypes';
import { DiscountNudgeData } from '../../types/DiscountNudgeTypes';
import { SavingsNudgeData } from '../../types/SavingsNudgeTypes';
import { DropdownOption } from './common/Dropdown';
import { removeUndefined } from '../../utils/helpers';

interface NudgeConfigManagerProps {
  nudgeId: string;
  rawParameters: any;
  onSave: (data: any) => Promise<void>;
  onClose: () => void;
  isPaused: boolean;
  isSubmitting: boolean;
  onPauseToggle: (isPaused: boolean, data: any) => Promise<void>;
  productOptions?: DropdownOption<string>[];
  isLoadingProducts?: boolean;
  onProductSearch?: (searchTerm: string) => Promise<void>;
  showEmulationBanner?: boolean;
  isSuperAdmin?: boolean;
}

const NudgeConfigManager: React.FC<NudgeConfigManagerProps> = ({
  nudgeId,
  rawParameters,
  onSave,
  onClose,
  isPaused,
  isSubmitting,
  onPauseToggle,
  productOptions = [],
  isLoadingProducts = false,
  onProductSearch,
  showEmulationBanner,
  isSuperAdmin = false,
}) => {
  // Map from API nudge IDs to PersonalizationNudge nudgeType
  const getNudgeType = (id: string): NudgeType => {
    switch (id) {
      case 'cart_abandonment_in_session':
        return 'inSessionCartAbandonment';
      case 'cart_abandonment_returning':
        return 'cartAbandonment';
      case 'pick_up_where_you_left_off':
        return 'pickUpWhereYouLeftOff';
      case 'social_media_content':
        return 'socialMediaContent';
      case 'navigational_nudge':
        return 'navigation';
      case 'intent_based_discount':
        return 'discount';
      case 'savings_nudge':
        return 'savings';
      default:
        return 'cartAbandonment'; // Fallback
    }
  };

  const getNudgeDataFromParameters = (interventionType: string, parameters: any) => {
    switch (interventionType) {
      case 'cart_abandonment_returning':
        return removeUndefined({
          group1Line1: parameters?.in_session_cart_abandonment_heading1,
          group1Line2: parameters?.in_session_cart_abandonment_heading2,
          group2Line1: parameters?.savings_cart_abandonment_heading1,
          group2Line2: parameters?.savings_cart_abandonment_heading2,
          buttonAndTextColor: parameters?.primary_background_color,
          fontName: parameters?.primary_store_font,
          excludedProductIds: (parameters?.excluded_product_ids || []).map(String),
          holdoutPercentage: parameters?.holdout_percentage * 100,
        });
      case 'cart_abandonment_in_session':
        return removeUndefined({
          group1Line1: parameters?.in_session_cart_abandonment_heading1,
          group1Line2: parameters?.in_session_cart_abandonment_heading2,
          group2Line1: parameters?.savings_cart_abandonment_heading1,
          group2Line2: parameters?.savings_cart_abandonment_heading2,
          buttonAndTextColor: parameters?.primary_background_color,
          fontName: parameters?.primary_store_font,
          excludedProductIds: (parameters?.excluded_product_ids || []).map(String),
          holdoutPercentage: parameters?.holdout_percentage * 100,
        });
      case 'pick_up_where_you_left_off':
        return removeUndefined({
          headlineLine1: parameters?.headline_line_1,
          headlineLine2: parameters?.headline_line_2,
          primaryColor: parameters?.primary_color,
          fontName: parameters?.font,
          holdoutPercentage: parameters?.holdout_percentage * 100,
        });
      case 'social_media_content':
        return removeUndefined({
          minDwellTime: parameters?.min_dwell_time,
          maxDwellTime: parameters?.max_dwell_time,
          interval: parameters?.interval,
          holdoutPercentage: parameters?.holdout_percentage * 100,
          uploadedVideos: parameters?.assets || [],
          defaultPreviewVideo: parameters?.default_preview_video,
          productOptions: parameters?.product_options,
        });
      case 'savings_nudge':
        return removeUndefined({
          // First try to get parameters in camelCase, fall back to snake_case for backward compatibility
          primaryColor: parameters?.primaryColor || parameters?.primary_color,
          font: parameters?.font,
          holdoutPercentage: parameters?.holdout_percentage * 100,
          buttonDestination: parameters?.buttonDestination || parameters?.button_destination,
          totalSavings: parameters?.totalSavings || parameters?.total_savings,
          tabText: parameters?.tabText || parameters?.tab_text,
          expandedHeadline: parameters?.expandedHeadline || parameters?.expanded_headline,
          expandedBody: parameters?.expandedBody || parameters?.expanded_body,
          tabColor: parameters?.tabColor || parameters?.tab_color,
          tabFontColor: parameters?.tabFontColor || parameters?.tab_font_color,
          primaryFontColor: parameters?.primaryFontColor || parameters?.primary_font_color,
          backgroundColor: parameters?.backgroundColor || parameters?.background_color,
          buttonText: parameters?.buttonText || parameters?.button_text,
          positioning: parameters?.positioning,
          anchor: parameters?.anchor,
          distanceFromAnchor: parameters?.distanceFromAnchor || parameters?.distance_from_anchor,
          threshold: parameters?.threshold,
          eligible: parameters?.eligible,
        });
      case 'navigational_nudge':
        return removeUndefined({
          headlineColor: parameters?.headlineColor,
          bodyTextColor: parameters?.bodyTextColor,
          backgroundColor: parameters?.backgroundColor,
          itemBackgroundColor: parameters?.itemBackgroundColor,
          fontName: parameters?.font,
          headline: parameters?.headline,
          subheader: parameters?.subheader,
          answerOptions: parameters?.answerOptions,
          timeDelay: parameters?.timeDelay,
          holdoutPercentage: parameters?.holdout_percentage * 100,
        });
      case 'intent_based_discount':
        return removeUndefined({
          headerText: parameters.popup_text_header,
          bodyText: parameters.popup_text_body,
          buttonText: parameters.popup_text_button,
          successButtonText: parameters.popup_text_success,
          collections: parameters.collections,
          minimizedTabText: parameters.minimized_text_header,
          primaryColor: '#' + parameters.popup_primary_color,
          backgroundColor: '#' + parameters.popup_bg_color,
          fontName: parameters.popup_font,
          discountPercentage: parameters.max_discount,
          discountPrefix: parameters.popup_discount_code_prefix,
          discountVariants: parameters.discount_variants.map((variant: any) => ({
            discountPercentage: variant.discount_value,
            discountPrefix: variant.discount_prefix,
          })),
          expiryDays: parameters.discount_ends_at_time,
          productApplicability: parameters.discount_collections.length === 0 ? 'all' : 'collection',
          selectedCollections: parameters.discount_collections,
          combinableDiscounts: {
            product: parameters.discount_combines_with.productDiscounts,
            order: parameters.discount_combines_with.orderDiscounts,
            shipping: parameters.discount_combines_with.shippingDiscounts,
          },
          eligibility: {
            singleItem: parameters.discount_one_time,
            subscription: parameters.discount_subscription,
          },
          // Advanced tab fields
          holdoutPercentage: 100 * parameters.holdout,
          anti_holdout_percentage: 100 * (parameters.anti_holdout_percentage || 0),
          tabDisabled: parameters.hide_minimized_popup,
          excludeLoggedInVisitors: !parameters.show_discount_when_logged_in,
          excludeVisitorsWhoBought: !parameters.show_discount_to_previous_customers,
          exclusionURLs: parameters.exclude_urls,
        });
      default:
        return {};
    }
  };

  const nudgeData = useMemo(() => {
    if (!rawParameters) return null;
    return getNudgeDataFromParameters(nudgeId, rawParameters);
  }, [nudgeId, rawParameters]);

  // Default cart abandonment data - based on the stories
  const defaultCartAbandonmentData: CartAbandonmentNudgeData = {
    group1Line1: "Don't leave your",
    group1Line2: 'cart hanging!',
    // eslint-disable-next-line no-template-curly-in-string
    group2Line1: 'You have ${savings} in',
    group2Line2: 'savings in your cart!',
    buttonAndTextColor: '#8F0067',
    fontName: 'Arial',
    storeAdImage: 'https://placehold.co/200x200?text=Product',
    holdoutPercentage: 20,
    excludedProductIds: [],
  };

  // Default pick up where you left off data - based on the stories
  const defaultPickUpWhereYouLeftOffData: PickUpWhereYouLeftOffNudgeData = {
    headlineLine1: 'Welcome back!',
    headlineLine2: 'Pick up where you left off:',
    primaryColor: '#8F0067',
    fontName: 'Arial',
    holdoutPercentage: 20,
    items: [
      {
        productId: '1',
        variantId: '1',
        url: 'https://placehold.co/200x200?text=Product%201',
        title: 'Product 1',
        price: 100,
        priceCurrency: 'USD',
        image: 'https://placehold.co/200x200?text=Product%201',
      },
      {
        productId: '2',
        variantId: '2',
        url: 'https://placehold.co/200x200?text=Product%202',
        title: 'Product 2',
        price: 200,
        priceCurrency: 'USD',
        image: 'https://placehold.co/200x200?text=Product%202',
      },
      {
        productId: '3',
        variantId: '3',
        url: 'https://placehold.co/200x200?text=Product%203',
        title: 'Product 3',
        price: 300,
        priceCurrency: 'USD',
        image: 'https://placehold.co/200x200?text=Product%203',
      },
    ],
  };

  // Default social media content data - based on the stories
  const defaultSocialMediaContentData: SocialMediaContentNudgeData = {
    holdoutPercentage: 20,
    minDwellTime: 240,
    maxDwellTime: 360,
    interval: 60,
    videoUrl: '',
    defaultPreviewVideo: '',
    productOptions: [],
  };

  // Default navigation nudge data - based on the stories
  const defaultNavigationNudgeData: NavigationNudgeData = {
    headline: 'What are you shopping for?',
    subheader: "We'll take you to the right spot!",
    headlineColor: '#358E7F',
    bodyTextColor: '#000000',
    backgroundColor: '#FFFFFF',
    itemBackgroundColor: '#EDEEF0',
    fontName: 'Arial',
    answerOptions: [
      {
        label: 'Option 1',
        url: '/option-1',
      },
      {
        label: 'Option 2',
        url: '/option-2',
      },
      {
        label: 'Option 3',
        url: '/option-3',
      },
    ],
    timeDelay: 15,
    holdoutPercentage: 20,
  };

  // Add default savings nudge data
  const defaultSavingsNudgeData: SavingsNudgeData = {
    tabColor: '#2B3336',
    tabFontColor: '#FFFFFF',
    tabText: 'Savings: {total_savings}',
    primaryColor: '#232323',
    primaryFontColor: '#232323',
    backgroundColor: '#FFFFFF',
    font: 'Inter',
    expandedHeadline: 'Lucky you… {total_savings} in savings!',
    expandedBody: "When you're ready, head to checkout and lock in these savings.",
    buttonText: 'Checkout',
    buttonDestination: '/checkout',
    positioning: 'Right',
    anchor: 'Top',
    distanceFromAnchor: 30,
    holdoutPercentage: 20,
  };
  const defaultDiscountNudgeData: DiscountNudgeData = {
    discountCode: 'VANDRA-12345',
    headerText: 'GET {discount}% OFF NOW!',
    bodyText: 'Exclusions apply.',
    buttonText: 'Apply',
    successButtonText: 'Applied',
    minimizedTabText: 'GET {discount}% OFF NOW!',
    primaryColor: '#8F0067',
    backgroundColor: '#FFFFFF',
    fontName: 'Arial',
    collections: [],
    discountPercentage: 20,
    discountPrefix: 'VANDRA',
    discountVariants: [
      {
        discountPercentage: 20,
        discountPrefix: 'VANDRA',
      },
    ],
    expiryDays: 0,
    productApplicability: 'all',
    selectedCollections: [],
    combinableDiscounts: {
      product: false,
      order: false,
      shipping: true,
    },
    eligibility: {
      singleItem: true,
      subscription: true,
    },
    tabDisabled: false,
    holdoutPercentage: 20,
    anti_holdout_percentage: 0,
    excludeLoggedInVisitors: false,
    excludeVisitorsWhoBought: false,
    exclusionURLs: [],
  };

  // Get default data based on nudge type
  const getDefaultData = (id: string) => {
    switch (id) {
      case 'discount_nudge':
        return defaultDiscountNudgeData;
      case 'cart_abandonment_in_session':
      case 'cart_abandonment_returning':
        return defaultCartAbandonmentData;
      case 'pick_up_where_you_left_off':
        return defaultPickUpWhereYouLeftOffData;
      case 'social_media_content':
        return defaultSocialMediaContentData;
      case 'navigational_nudge':
        return defaultNavigationNudgeData;
      case 'intent_based_discount':
        return defaultDiscountNudgeData;
      case 'savings_nudge':
        return defaultSavingsNudgeData;
      default:
        return defaultCartAbandonmentData;
    }
  };

  // Determine if the nudge type should have dropdown options
  const getDropdownOptions = (id: string) => {
    switch (id) {
      case 'cart_abandonment_in_session':
      case 'cart_abandonment_returning':
        return [
          { label: 'No Savings in Cart', value: 'no-savings' },
          { label: 'Savings in Cart', value: 'savings' },
        ];
      default:
        return undefined;
    }
  };

  // Create merged data with defaults and actual parameters
  const getMergedData = (id: string) => {
    const defaultData = getDefaultData(id);

    if (!nudgeData) {
      return defaultData;
    }

    switch (id) {
      case 'cart_abandonment_in_session':
      case 'cart_abandonment_returning':
        return {
          ...defaultData,
          ...nudgeData,
          holdoutPercentage: nudgeData.holdoutPercentage || 20,
        } as CartAbandonmentNudgeData;

      case 'pick_up_where_you_left_off':
        return {
          ...defaultData,
          ...nudgeData,
          holdoutPercentage: nudgeData.holdoutPercentage || 20,
        } as PickUpWhereYouLeftOffNudgeData;

      case 'social_media_content': {
        return {
          ...defaultData,
          ...(nudgeData as SocialMediaContentNudgeData),
          holdoutPercentage: nudgeData.holdoutPercentage || 20,
        } as SocialMediaContentNudgeData;
      }

      case 'navigational_nudge':
        return {
          ...defaultData,
          ...nudgeData,
          holdoutPercentage: nudgeData.holdoutPercentage || 20,
        } as NavigationNudgeData;
      case 'intent_based_discount':
        return {
          ...defaultData,
          ...nudgeData,
        } as DiscountNudgeData;
      case 'savings_nudge':
        return {
          ...defaultData,
          ...nudgeData,
          holdoutPercentage: nudgeData.holdoutPercentage || 20,
        } as SavingsNudgeData;
      default:
        return defaultData;
    }
  };

  const nudgeType = getNudgeType(nudgeId);
  const data = getMergedData(nudgeId);
  const dropdownOptions = getDropdownOptions(nudgeId);

  // Wrap the save function to handle any data transformation needed
  const handleSave = async (updatedData: any): Promise<void> => {
    await onSave(updatedData);
  };

  const handlePauseToggle = async (isPaused: boolean, data: any): Promise<void> => {
    await onPauseToggle(isPaused, data);
  };

  // Create the appropriate props based on nudge type
  const createNudgeProps = (): PersonalizationNudgeProps => {
    const baseProps = {
      paramOptions: {},
      onSave: handleSave,
      isPaused: isPaused,
      onPauseToggle: handlePauseToggle,
      isSubmitting: isSubmitting,
      productOptions,
      isLoadingProducts,
      onProductSearch,
      showEmulationBanner,
      isSuperAdmin,
    };

    switch (nudgeType) {
      case 'cartAbandonment':
      case 'inSessionCartAbandonment':
        return {
          ...baseProps,
          nudgeType,
          data: data as CartAbandonmentNudgeData,
          defaultData: getDefaultData(nudgeId) as CartAbandonmentNudgeData,
          dropdownOptions,
          paramOptions: {
            savings: '14',
          },
        };

      case 'pickUpWhereYouLeftOff':
        return {
          ...baseProps,
          nudgeType,
          data: data as PickUpWhereYouLeftOffNudgeData,
          defaultData: getDefaultData(nudgeId) as PickUpWhereYouLeftOffNudgeData,
          paramOptions: {},
        };

      case 'socialMediaContent':
        return {
          ...baseProps,
          nudgeType,
          data: data as SocialMediaContentNudgeData,
          defaultData: getDefaultData(nudgeId) as SocialMediaContentNudgeData,
          productOptions: (data as SocialMediaContentNudgeData).productOptions,
        };

      case 'discount':
        return {
          ...baseProps,
          nudgeType,
          data: data as DiscountNudgeData,
          defaultData: getDefaultData(nudgeId) as DiscountNudgeData,
          dropdownOptions,
        };

      case 'navigation':
        return {
          ...baseProps,
          nudgeType,
          data: data as NavigationNudgeData,
          defaultData: getDefaultData(nudgeId) as NavigationNudgeData,
        };

      case 'savings':
        return {
          ...baseProps,
          nudgeType,
          data: data as SavingsNudgeData,
          defaultData: getDefaultData(nudgeId) as SavingsNudgeData,
          paramOptions: {
            total_savings: '$45.00',
          },
        };

      default:
        return {
          ...baseProps,
          nudgeType: 'cartAbandonment',
          data: data as CartAbandonmentNudgeData,
          defaultData: getDefaultData(nudgeId) as CartAbandonmentNudgeData,
          dropdownOptions,
        };
    }
  };

  return <PersonalizationNudge {...createNudgeProps()} onClose={onClose} />;
};

export default NudgeConfigManager;
