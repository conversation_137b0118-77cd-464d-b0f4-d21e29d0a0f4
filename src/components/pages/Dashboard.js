import React, { useState, useEffect, useCallback } from 'react';
import Nudges from '../dashboard/nudges';
import StoreSelector from '../admin/StoreSelector';
import { makeRequest } from '../../utils/makeRequest';
import { useAppBridge } from '@shopify/app-bridge-react';

export default function Dashboard() {
  const shopify = useAppBridge();
  const [isAdmin, setIsAdmin] = useState(false);
  const [emulatingStore, setEmulatingStore] = useState(null);
  const [adminLoading, setAdminLoading] = useState(true);

  const checkAdminStatus = useCallback(async () => {
    setAdminLoading(true);
    try {
      const response = await makeRequest({
        path: `/react/admin_check_status?id_token=${await shopify.idToken()}`,
        method: 'GET',
      });
      setIsAdmin(response.is_admin || false);

      // Handle emulation session expiration
      if (response.error === 'Emulation session expired') {
        setEmulatingStore(null);
        sessionStorage.removeItem('emulating_store');
        // Show a toast or alert about session expiration
        console.warn('Emulation session has expired. Returning to normal mode.');
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);

      // If error is related to session expiration, clear emulation state
      if (error.message && error.message.includes('expired')) {
        setEmulatingStore(null);
        sessionStorage.removeItem('emulating_store');
      }
    } finally {
      setAdminLoading(false);
    }
  }, [shopify]);

  useEffect(() => {
    checkAdminStatus();
    // Check for existing emulation in sessionStorage
    const storedEmulation = sessionStorage.getItem('emulating_store');
    if (storedEmulation) {
      setEmulatingStore(JSON.parse(storedEmulation));
    }
  }, [checkAdminStatus]);

  const handleStoreEmulation = store => {
    setEmulatingStore(store);
    sessionStorage.setItem('emulating_store', JSON.stringify(store));
    window.location.reload();
  };

  const handleResetToCurrentStore = () => {
    console.log('Exiting admin emulation mode');
    setEmulatingStore(null);
    sessionStorage.removeItem('emulating_store');
    window.location.reload();
  };

  return (
    <div className="flex flex-col gap-[10px] pb-[10px]">
      {/* Compact Admin Controls */}
      {!adminLoading && isAdmin && (
        <div className="flex-col-center">
          <div className="content border rounded-lg p-4 full">
            <div className="flex items-center gap-4 flex-wrap">
              <h2 className="text-lg font-semibold text-gray-700">Admin Store Emulation</h2>
              {emulatingStore && (
                <div className="bg-orange-50 border border-orange-200 rounded-md px-3 py-2">
                  <span className="text-orange-800 text-sm font-medium">
                    ⚠️ Security Notice: You are viewing data for &quot;{emulatingStore.name}&quot;.
                    All actions will be performed in the context of this store.
                  </span>
                </div>
              )}
              <div className="flex items-center gap-4 flex-wrap">
                <div className="min-w-[400px]">
                  <StoreSelector
                    onStoreSelect={handleStoreEmulation}
                    currentStore={emulatingStore?.id}
                  />
                </div>
                {emulatingStore && (
                  <button
                    onClick={handleResetToCurrentStore}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors whitespace-nowrap"
                  >
                    Return to Current Store
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Dashboard Content */}
      <Nudges isSuperAdmin={isAdmin} />
    </div>
  );
}
