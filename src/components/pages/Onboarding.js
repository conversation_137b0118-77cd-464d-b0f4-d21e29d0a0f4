import React, { useEffect, useState } from 'react';
import FeatureCarousel from '../onboarding/featureCarousel';
import IncrementSteps from '../onboarding/incrementSteps';
import PropTypes from 'prop-types';

const renderStep = (currentStep, setCurrentStep, onBoardingDetails) => {
  switch (currentStep) {
    case 0:
      return (
        <FeatureCarousel setCurrentStep={setCurrentStep} onBoardingDetails={onBoardingDetails} />
      );
    case 1:
    case 2:
    case 3:
    case 4:
      return (
        <IncrementSteps
          currentStep={currentStep}
          setCurrentStep={setCurrentStep}
          onBoardingDetails={onBoardingDetails}
        />
      );
    default:
      return (
        <FeatureCarousel setCurrentStep={setCurrentStep} onBoardingDetails={onBoardingDetails} />
      );
  }
};

const Onboarding = props => {
  const { onBoardingDetails } = props;
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!onBoardingDetails.terms_start) {
      setLoading(false);
    } else if (!onBoardingDetails.terms_signed) {
      setCurrentStep(1);
      setLoading(false);
    } else if (!onBoardingDetails.script_activated) {
      setCurrentStep(2);
      setLoading(false);
    } else if (!onBoardingDetails.details_confirmed) {
      setCurrentStep(3);
      setLoading(false);
    } else if (!onBoardingDetails.billing_confirmed || !onBoardingDetails.confirmed_charge) {
      setCurrentStep(4);
      setLoading(false);
    }
  }, [
    onBoardingDetails.terms_start,
    onBoardingDetails.terms_signed,
    onBoardingDetails.script_activated,
    onBoardingDetails.details_confirmed,
    onBoardingDetails.billing_confirmed,
    onBoardingDetails.confirmed_charge,
    setCurrentStep,
  ]);

  if (loading) return <></>;

  return renderStep(currentStep, setCurrentStep, onBoardingDetails);
};

Onboarding.propTypes = {
  onBoardingDetails: PropTypes.shape({
    terms_start: PropTypes.bool,
    terms_signed: PropTypes.bool,
    script_activated: PropTypes.bool,
    details_confirmed: PropTypes.bool,
    billing_confirmed: PropTypes.bool,
    confirmed_charge: PropTypes.bool,
  }).isRequired,
};

export default Onboarding;
