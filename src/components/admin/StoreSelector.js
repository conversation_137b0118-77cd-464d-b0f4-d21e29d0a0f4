import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { makeRequest } from '../../utils/makeRequest';
import { useAppBridge } from '@shopify/app-bridge-react';
import { Dropdown } from '../UI/common/Dropdown';

const StoreSelector = ({ onStoreSelect, currentStore }) => {
  const shopify = useAppBridge();
  const [stores, setStores] = useState([]);
  const [, setCurrentStoreInfo] = useState(null);
  const [selectedStore, setSelectedStore] = useState(currentStore);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchStores();
  }, []);

  useEffect(() => {
    setSelectedStore(currentStore);
  }, [currentStore]);

  const fetchStores = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await makeRequest({
        path: `/react/admin_available_stores?id_token=${await shopify.idToken()}`,
        method: 'GET',
      });

      if (response.stores) {
        setStores(response.stores);
        setCurrentStoreInfo(response.current_store);
      } else {
        setError('No stores available');
      }
    } catch (error) {
      console.error('Error fetching stores:', error);
      setError('Failed to fetch stores');
    } finally {
      setLoading(false);
    }
  };

  const handleStoreChange = storeId => {
    const selectedStore = stores.find(store => store.uuid === storeId);
    if (selectedStore) {
      onStoreSelect({
        id: selectedStore.uuid,
        name: selectedStore.name,
      });
    }
  };

  // Transform stores to dropdown options
  const storeOptions = stores.map(store => ({
    value: store.uuid,
    label: `${store.name} (${store.url}) ${store.test_store ? '[TEST]' : ''}`,
  }));

  if (error) {
    return <div className="text-red-600 text-sm">Error: {error}</div>;
  }

  return (
    <div className="w-full">
      <Dropdown
        options={storeOptions}
        value={selectedStore || ''}
        onChange={handleStoreChange}
        placeholder={loading ? 'Loading stores...' : 'Select Store to Emulate'}
        disabled={loading}
        isLoading={loading}
        className="w-full"
      />
    </div>
  );
};

StoreSelector.propTypes = {
  onStoreSelect: PropTypes.func.isRequired,
  currentStore: PropTypes.string,
};

StoreSelector.defaultProps = {
  currentStore: null,
};

export default StoreSelector;
