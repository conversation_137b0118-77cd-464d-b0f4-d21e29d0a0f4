import React, { useState } from 'react';
import ConfigureIcon from '../UI/common/icons/ConfigureIcon';
// @ts-ignore
import AnalyticsModal from '../UI/AnalyticsModal.js';

interface Nudge {
  id?: string;
  active: boolean;
  description: string;
  parameters: any;
  title: string;
}

interface NudgesProps {
  [title: string]: Nudge;
}

interface NudgeRowProps {
  nudge: Nudge;
}

interface NudgeSectionProps {
  title: string;
  count: number;
  nudgeList: Nudge[];
}

const iconMap: { [x: string]: string } = {
  intent_based_discount: 'img/dashboard/upper_middle_funnel.png',
  cart_abandonment_returning: 'img/dashboard/lower_funnel.png',
  cart_abandonment_in_session: 'img/dashboard/lower_funnel.png',
  social_media_content: 'img/dashboard/lower_middle_funnel.png',
  navigational_nudge: 'img/dashboard/upper_funnel.png',
  pick_up_where_you_left_off: 'img/dashboard/lower_middle_funnel.png',
  savings_nudge: 'img/dashboard/lower_middle_funnel.png',
};

export default function MainDashboard(props: {
  nudgeRequests: NudgesProps;
  handleConfigureNudge: (x: string) => void;
  setOpenStarter: (x: boolean) => void;
  setSupportOpen: (x: boolean) => void;
}): JSX.Element {
  const [analyticsModalOpen, setAnalyticsModalOpen] = useState(false);
  const [selectedNudgeForAnalytics, setSelectedNudgeForAnalytics] = useState<{
    id: string;
    title: string;
  } | null>(null);

  let activeNudges: Nudge[] = [];
  let inactiveNudges: Nudge[] = [];
  for (const nr of Object.keys(props.nudgeRequests)) {
    if (props.nudgeRequests[nr].active) {
      activeNudges.push({ ...props.nudgeRequests[nr], id: nr });
    } else {
      inactiveNudges.push({ ...props.nudgeRequests[nr], id: nr });
    }
  }

  const nudges = {
    active: activeNudges,
    inactive: inactiveNudges,
  };

  const TableHeader = (): JSX.Element => (
    <div className="flex justify-between gap-4 py-4 border-b border-gray-200">
      <div className="text-gray-500">Personalized Nudge</div>
    </div>
  );

  const NudgeRow = ({ nudge }: NudgeRowProps): JSX.Element => (
    <div className="flex justify-between gap-4 py-4 border-b border-gray-200 items-center">
      <div className="flex items-center gap-3">
        {nudge.id && <img className="w-[40px]" alt="icon" src={iconMap[nudge.id]} />}
        <span
          className="font-medium underline cursor-pointer hover:text-[#856CF8] transition-colors"
          onClick={() => openAnalyticsModal(nudge)}
        >
          {nudge.title}
        </span>
      </div>
      <button
        // disabled={isSubmitting}
        onClick={() => props.handleConfigureNudge(nudge.id ?? '')}
        className={`flex items-center justify-center text-white px-6 py-2 rounded bg-[#856CF8] h-fit w-[190px] transition-all hover:bg-[#7258e0] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#856CF8]`}
      >
        <ConfigureIcon size={18} className="mr-2" color="white" />
        <span>Configure</span>
      </button>
    </div>
  );

  const NudgeSection = ({ title, count, nudgeList }: NudgeSectionProps): JSX.Element => (
    <div className="mt-6">
      <div className="flex items-center gap-2 mb-2">
        <h2 className="text-lg font-bold">{title}</h2>
        <span className="text-lg">{count}</span>
      </div>
      <div className="h-1 bg-gradient-to-r from-orange-400 to-orange-100 w-full mb-4"></div>

      <TableHeader />

      {nudgeList.map(nudge => (
        <NudgeRow key={nudge.id} nudge={nudge} />
      ))}
    </div>
  );

  const openAnalyticsModal = (nudge: Nudge) => {
    setSelectedNudgeForAnalytics({
      id: nudge.id || '',
      title: nudge.title || 'Nudge',
    });
    setAnalyticsModalOpen(true);
  };

  const closeAnalyticsModal = () => {
    setAnalyticsModalOpen(false);
    setSelectedNudgeForAnalytics(null);
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-white">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-8">
            <img
              className="w-[11%] min-w-[100px] cursor-pointer"
              src="/img/vandra_purple_logo.png"
              alt="Vandra logo"
            />
            <nav className="hidden md:flex space-x-6">
              <div
                onClick={() => {
                  props.setOpenStarter(true);
                  props.setSupportOpen(false);
                }}
                className="text-gray-500 cursor-pointer"
              >
                Discover
              </div>
              <div
                onClick={() => {
                  props.setSupportOpen(true);
                  props.setOpenStarter(false);
                }}
                className="text-gray-500 cursor-pointer"
              >
                Support
              </div>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto rounded-lg shadow p-6 bg-gray-50 mt-8">
        <h1 className="text-2xl font-bold mb-1">Control Center</h1>
        <p className="text-gray-500 mb-6">Manage the personalized nudges on your e-commerce site</p>

        <NudgeSection
          title="Active Nudges"
          count={nudges.active.length}
          nudgeList={nudges.active}
        />

        <NudgeSection
          title="Inactive Nudges"
          count={nudges.inactive.length}
          nudgeList={nudges.inactive}
        />

        <div className="flex justify-end mt-8 text-sm text-gray-600">
          <p>
            Vandra{' '}
            <a
              href="https://www.vandra.ai/terms-of-service"
              target="_blank"
              rel="noreferrer"
              className="underline"
            >
              Terms & Conditions
            </a>{' '}
            and{' '}
            <a
              href="https://www.vandra.ai/privacy-policy"
              target="_blank"
              rel="noreferrer"
              className="underline"
            >
              Privacy Policy
            </a>
          </p>
        </div>
      </div>

      {selectedNudgeForAnalytics && (
        <AnalyticsModal
          isOpen={analyticsModalOpen}
          onClose={closeAnalyticsModal}
          interventionName={selectedNudgeForAnalytics.id}
          title={selectedNudgeForAnalytics.title}
        />
      )}
    </div>
  );
}
