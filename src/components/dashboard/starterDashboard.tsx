import React, { useState } from 'react';
import { useAppBridge } from '@shopify/app-bridge-react';
import { X } from 'lucide-react';
import { makeRequest } from '../../utils/makeRequest';

// Main App Component
export default function StarterDashboard(props: {
  widgetColor: string;
  font: string;
  popupDiscountCodePrefix: string;
  popupBackgroundColor: string;
  handleConfigureNudge: (x: string) => void;
  setOpenStarter: (x: boolean) => void;
  setSupportOpen: (x: boolean) => void;
}) {
  const shopify = useAppBridge();
  const [popupColor, setPopupColor] = useState(
    props.widgetColor?.[0] === '#' ? props.widgetColor : `#${props.widgetColor}`
  );
  const [popupFont, setPopupFont] = useState(props.font);
  const [showNudgeModal, setShowNudgeModal] = useState(false);
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [selectedNudge, setSelectedNudge] = useState<any>(null);

  const handleSaveAllChangesButtonClick = async () => {
    const form = new FormData();
    form.append('popup_discount_code_prefix', props.popupDiscountCodePrefix);
    form.append('popup_bg_color', props.popupBackgroundColor);
    form.append('popup_font', popupFont);
    form.append('popup_primary_color', popupColor);

    await makeRequest({
      path: `/react/merchant_widget_settings_save?id_token=${await shopify.idToken()}`,
      method: 'POST',
      body: form,
    });
  };

  const handleNudgeClick = (nudge: any) => {
    setSelectedNudge(nudge);
    setShowNudgeModal(true);
  };

  const handleCloseModals = () => {
    setShowNudgeModal(false);
    setShowBrandModal(false);
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-8">
            <img
              onClick={() => {
                props.setOpenStarter(false);
                props.setSupportOpen(false);
              }}
              className="w-[11%] min-w-[100px] cursor-pointer"
              src="/img/vandra_purple_logo.png"
              alt="Vandra logo"
            />
            <nav className="hidden md:flex space-x-6">
              <div
                onClick={() => {
                  props.setSupportOpen(false);
                  props.setOpenStarter(true);
                }}
                className="text-gray-500 cursor-pointer"
              >
                Discover
              </div>
              <div
                onClick={() => {
                  props.setSupportOpen(true);
                  props.setOpenStarter(false);
                }}
                className="text-gray-500 cursor-pointer"
              >
                Support
              </div>
            </nav>
          </div>
          <div>
            <a
              href="https://landing.vandra.ai/schedule-merchant"
              target="_blank"
              rel="noreferrer"
              className="text-black font-medium underline"
            >
              New here? Chat with us!
            </a>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto py-8">
        <div className="mb-6 rounded-md inline-block">
          <h1 className="text-xl font-bold">Discover more personalized nudges</h1>
        </div>

        {/* Funnel Types */}
        <div className="flex flex-wrap gap-4 mb-8">
          <FunnelType image="img/dashboard/upper_funnel.png" title="Upper Funnel" />
          <FunnelType image="img/dashboard/upper_middle_funnel.png" title="Upper-Middle Funnel" />
          <FunnelType image="img/dashboard/lower_middle_funnel.png" title="Lower-Middle Funnel" />
          <FunnelType image="img/dashboard/lower_funnel.png" title="Lower Funnel" />
        </div>

        {/* Top Performing Nudges */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-2">Our top-performing nudges</h2>
          <p className="text-gray-500 mb-6">
            Click on any of the blocks below to learn more about that nudge and how it can benefit
            your business.
          </p>

          <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-6">
            <NudgeCard
              title="On-the-Fence Discount Nudge"
              border
              tagColor="#E7E2FF"
              image="img/dashboard/upper_middle_funnel.png"
              tag="Conversion Rate"
              description="Surface discounts only to unlikely buyers"
              onClick={() =>
                handleNudgeClick({
                  id: 'discount_nudge',
                  title: 'On-the-Fence Discount Nudge',
                  type: 'Conversion Rate',
                  detailedDescription:
                    'Nudge shoppers that our AI models determined are unlikely to make a purchase without a little extra motivation… and avoid offers for shoppers already headed to checkout.',
                  benefits: [
                    `you don't want to give out unnecessary discounts to customers who would buy from you anyway`,
                    'you want to protect your margins',
                  ],
                  images: ['img/dashboard/discount_nudge.png'],
                })
              }
            />
            <NudgeCard
              title="Cart Abandonment Return Visitor Nudge"
              border
              tagColor="#FFBAD5"
              image="img/dashboard/lower_funnel.png"
              tag="Conversion Rate"
              description="Encourage return visitors to check out if they have an active cart"
              onClick={() =>
                handleNudgeClick({
                  id: 'cart_abandonment_returning',
                  title: 'Cart Abandonment Return Visitor Nudge',
                  type: 'Conversion Rate',
                  detailedDescription:
                    'Nudge shoppers that come back to your site and have an active cart from a previous session. Foregrounds the most recently added item and, if applicable, any savings',
                  benefits: [
                    'you have a sizeable population of returning customers who abandon their cart',
                    'you currently have little to no personalized experiences for your returning customers',
                  ],
                  images: ['img/dashboard/cart_1.png', 'img/dashboard/cart_2.png'],
                })
              }
            />
            <NudgeCard
              title="In-Session Cart Abandonment Nudge"
              border
              tagColor="#FFBAD5"
              image="img/dashboard/lower_funnel.png"
              tag="Conversion Rate"
              description="Encourage visitors in-session to check out if they have an active cart"
              onClick={() =>
                handleNudgeClick({
                  id: 'cart_abandonment_in_session',
                  title: 'In-Session Cart Abandonment Nudge',
                  type: 'Conversion Rate',
                  detailedDescription:
                    'Nudge shoppers that are "stalling out" after adding items to their cart. Foregrounds the most recently added item and, if applicable, any savings',
                  benefits: [
                    `you have a sizeable amount of customers who add to their cart and don't check out`,
                    'you currently have little to no personalized experiences for customers with an active cart',
                  ],
                  images: ['img/dashboard/cart_1.png', 'img/dashboard/cart_2.png'],
                })
              }
            />
          </div>
        </section>

        {/* Additional Nudges */}
        <section>
          <h2 className="text-2xl font-bold mb-2">
            Additional nudges to <span className="text-purple-500">personalize your site</span>
          </h2>

          <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-6 mt-6">
            <NudgeCard
              title="Social Media Video Nudge"
              tagColor="#FFC7B6"
              image="img/dashboard/lower_middle_funnel.png"
              tag="Add-to-Cart Rate"
              description="Encourage visitors to convert through a social media video"
              onClick={() =>
                handleNudgeClick({
                  id: 'social_media_content',
                  title: 'Social Media Video Nudge',
                  type: 'Conversion Rate',
                  detailedDescription:
                    'Nudge shoppers that are "stalling out" on a product page by showing them product-specific social media content right on the page',
                  benefits: [
                    'you have an active social media account with original content about your products and/or brand',
                    'you want to include an interactive, visually-appealing element to your product pages',
                    'you believe there is power in showing how your product is used/worn',
                  ],
                  images: ['img/dashboard/social.png'],
                })
              }
            />
            <NudgeCard
              title="New Visitor Data Collection / Navigational Nudge"
              tagColor="#FFDEBF"
              image="img/dashboard/upper_funnel.png"
              tag="Viewed Product Rate"
              description="Get visitors to convert by asking them what they're looking for"
              onClick={() =>
                handleNudgeClick({
                  id: 'navigational_nudge',
                  title: 'New Visitor Data Collection / Navigational Nudge',
                  type: 'Conversion Rate',
                  detailedDescription:
                    'Nudge first-time visitors with a question that can help you direct them to a good next step in their journey. Surfaces a question and up to 4 options. Each option is a link to a relevant page on your site (e.g., a collection, a landing page)',
                  benefits: [
                    "you don't know what your customers are looking to buy",
                    'you want to learn a specific piece of information about your customers (i.e. Who are they shopping for? or What is their skin type?)',
                    "your website doesn't have a clear menu or is hard to navigate",
                  ],
                  images: ['img/dashboard/navigational.png'],
                })
              }
            />
            <NudgeCard
              title="Pick-Up Where You Left Off (PUWYLO) Nudge"
              tagColor="#FFC7B6"
              image="img/dashboard/lower_middle_funnel.png"
              tag="Add-to-Cart Rate"
              description="Foreground products visitors views on their last visit"
              onClick={() =>
                handleNudgeClick({
                  id: 'pick_up_where_you_left_off',
                  title: 'Pick-Up Where You Left Off (PUWYLO) Nudge',
                  type: 'Conversion Rate',
                  benefits: [
                    'you want to see up to a 20% lift in conversation rates',
                    'you want to see up to $X increase in sales',
                    "you want to only target customers who aren't very likely to buy",
                  ],
                  images: ['img/dashboard/puwylo.png'],
                })
              }
            />
            <NudgeCard
              title="Savings Tab Nudge"
              tagColor="#FFC7B6"
              image="img/dashboard/lower_middle_funnel.png"
              tag="Add-to-Cart Rate"
              description="Show shoppers how much money they've saved as they shop"
              onClick={() =>
                handleNudgeClick({
                  id: 'savings_nudge',
                  title: 'Savings Tab Nudge',
                  type: 'Conversion Rate',
                  detailedDescription:
                    'For shoppers with savings in their cart, provide ongoing reinforcement of these savings as they continue to shop. Updates in real-time as changes are made to the cart.',
                  benefits: [
                    'there are a lot of ways customers can collect savings on your products',
                    "you don't have a way of tracking customer savings",
                  ],
                  images: ['img/dashboard/savings_1.png', 'img/dashboard/savings_2.png'],
                })
              }
            />
          </div>
        </section>

        {/* Footer */}
        <footer className="mt-12 text-right text-sm text-gray-600">
          <span>Vandra </span>
          <a
            href="https://www.vandra.ai/terms-of-service"
            target="_blank"
            rel="noreferrer"
            className="underline"
          >
            Terms & Conditions
          </a>
          <span> and </span>
          <a
            href="https://www.vandra.ai/privacy-policy"
            target="_blank"
            rel="noreferrer"
            className="underline"
          >
            Privacy Policy
          </a>
        </footer>
      </main>

      {/* Nudge Modal */}
      {showNudgeModal && selectedNudge && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
          onClick={handleCloseModals}
        >
          <div
            className="bg-white rounded-lg max-w-4xl w-full overflow-hidden px-[60px] py-[40px]"
            onClick={e => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            <div className="p-6 relative">
              <button
                onClick={handleCloseModals}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>

              <div className="flex justify-center mb-6">
                <Logo />
              </div>

              <div className="flex items-center">
                <div>
                  <h2 className="text-xl font-bold mb-2">{selectedNudge.title}</h2>
                  <p className="text-gray-600 mb-8">{selectedNudge.detailedDescription}</p>

                  <div className="flex gap-6">
                    <div className="flex-1">
                      <h3 className="font-bold mb-4">You should activate this nudge if...</h3>
                      <ul className="list-disc pl-6 space-y-2">
                        {selectedNudge.benefits.map((benefit: any, index: number) => (
                          <li key={index}>{benefit}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="flex w-full h-full ml-[50px]">
                  {selectedNudge.images.map((image: string) => (
                    <div className="w-full flex items-center justify-center" key={image}>
                      <div className="border border-gray-200 rounded-lg p-2 relative">
                        <img
                          src={image}
                          alt="Phone mockup"
                          className="w-full h-auto max-h-[300px]"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="mt-8 flex justify-center">
                <button
                  onClick={() => {
                    props.handleConfigureNudge(selectedNudge.id);
                  }}
                  className="w-[50%] min-w-[385px] bg-[#856CF8] hover:bg-purple-600 text-white py-3 rounded-lg font-medium"
                >
                  Set it up
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Logo Component
function Logo() {
  return (
    <div className="flex items-center justify-center">
      <img
        className="w-[11%] min-w-[100px] pb-[5%]"
        src="/img/vandra_purple_logo.png"
        alt="Vandra logo"
      />
    </div>
  );
}

// Funnel Type Component
function FunnelType({ image, title }: { image: string; title: string }) {
  return (
    <div className="flex items-center space-x-2">
      <span className="font-bold">{title} =</span>
      <div className="w-10 h-10 rounded-full bg-opacity-20 flex items-center justify-center">
        <img src={image} alt="icon" />
      </div>
    </div>
  );
}

// Nudge Card Component
function NudgeCard({
  title,
  image,
  tag,
  description,
  onClick,
  border,
  tagColor,
}: {
  title: string;
  image: string;
  tag: string;
  description: string;
  onClick?: () => void;
  border?: boolean;
  tagColor: string;
}) {
  return (
    <div
      className={`${border && 'border-[2px] border-[#856CF8]'} rounded-xl p-6 hover:shadow-md transition-shadow cursor-pointer relative bg-[#F9FAFB]`}
      onClick={onClick}
    >
      <div className="absolute top-6 right-6 w-10 h-10 rounded-full bg-opacity-20 flex items-center justify-center">
        <img src={image} alt="icon nudge" />
      </div>

      <h3 className="font-bold mb-4 pr-12 min-h-[65px] w-[75%]">{title}</h3>
      <div
        className="inline-block px-2 py-1 rounded text-sm mb-4"
        style={{ backgroundColor: tagColor }}
      >
        {tag}
      </div>
      <p className="text-gray-700">{description}</p>
    </div>
  );
}
