import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import Loader from '../UI/Loader';
import { makeRequest } from '../../utils/makeRequest';
import NudgeConfigModal from '../UI/NudgeConfigModal';
import NudgeConfigManager from '../UI/NudgeConfigManager';
import StarterDashboard from './starterDashboard';

import { useAppBridge } from '@shopify/app-bridge-react';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import MainDashboard from './mainDashboard';

const Nudges = ({ isSuperAdmin = false }) => {
  const shopify = useAppBridge();
  const [nudgeRequests, setNudgeRequests] = useState([]);
  const [anyNudgeActive, setAnyNudgeActive] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [selectedNudge, setSelectedNudge] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [widgetSettings] = useState({});
  const [openStarter, setOpenStarter] = useState(false);
  const [supportOpen, setSupportOpen] = useState(false);
  const [isSupportLoading, setIsSupportLoading] = useState(false);
  const [supportText, setSupportText] = useState('');

  const submitSupportRequest = useCallback(async () => {
    try {
      setIsSupportLoading(true);
      const form = new FormData();
      form.append('support_message', supportText);
      await makeRequest({
        path: `/react/merchant_send_support_email?id_token=${await shopify.idToken()}`,
        method: 'POST',
        body: form,
      });
      toast.success('Your message has been received!\nA Vandra team member will respond shortly.');
      setSupportText('');
    } catch (err) {
      toast.error('Error submitting request.');
    } finally {
      setIsSupportLoading(false);
    }
  }, [shopify, supportText]);

  // State for Shopify product search
  const [productOptions, setProductOptions] = useState([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);

  const getNudgeRequests = useCallback(async () => {
    setIsPageLoading(true);
    const nudges = await makeRequest({
      path: `/react/merchant_nudge_requests?id_token=${await shopify.idToken()}`,
    });
    const getDiscountNudgeParameters = async () => {
      const discount_nudge = await makeRequest({
        path: `/react/merchant_settings?id_token=${await shopify.idToken()}`,
      });
      return discount_nudge;
    };
    const discountParams = await getDiscountNudgeParameters();
    const requests = {
      intent_based_discount: {
        active: discountParams.show_discount,
        title: discountParams.title,
        description: discountParams.description,
        parameters: discountParams,
      },
      ...nudges.requests,
    };
    setNudgeRequests(requests);
  }, [shopify]);

  const lastSavedConfigRef = useRef(null);
  const lastSavedIsActiveRef = useRef(null);
  const submitReasonRef = useRef(null);

  useEffect(() => {
    const active = Object.keys(nudgeRequests).filter(req => nudgeRequests[req].active).length > 0;
    setAnyNudgeActive(active);
    if (nudgeRequests && isPageLoading) {
      setIsPageLoading(false);
    }
  }, [nudgeRequests, isPageLoading]);

  useEffect(() => {
    if (
      !isSubmitting &&
      lastSavedConfigRef.current &&
      lastSavedIsActiveRef.current &&
      selectedNudge &&
      nudgeRequests[selectedNudge]?.parameters
    ) {
      lastSavedConfigRef.current = null;
      lastSavedIsActiveRef.current = null;
      submitReasonRef.current = null;
    }
  }, [isSubmitting, nudgeRequests, selectedNudge]);

  useEffect(() => {
    getNudgeRequests();
  }, [getNudgeRequests]);

  const handleConfigureNudge = nudgeId => {
    setSelectedNudge(nudgeId);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    if (isSubmitting) return;
    setIsModalOpen(false);
    setSelectedNudge(null);
  };

  // Helper function to normalize product IDs consistently (reusable across functions)
  const normalizeProductId = id => {
    const idStr = String(id);
    if (idStr.startsWith('gid://shopify/Product/')) {
      return idStr.split('/').pop();
    }
    return idStr;
  };

  // Track the last search term and timestamp to prevent duplicate API calls
  const searchCounter = useRef(0);
  const lastSearchTerm = useRef('');
  const lastSearchTime = useRef(0);
  const searchTimeout = useRef(null);

  const handleProductSearch = useCallback(
    async searchTerm => {
      // Increment counter to track calls
      searchCounter.current += 1;
      const currentCounter = searchCounter.current;

      // Don't make duplicate API calls for the same search term within 300ms
      const now = Date.now();
      if (searchTerm === lastSearchTerm.current && now - lastSearchTime.current < 300) {
        return;
      }

      // Clear any pending search timeouts
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }

      // Set a small delay to debounce rapid calls
      searchTimeout.current = setTimeout(async () => {
        // Only proceed if this is still the most recent call
        if (currentCounter !== searchCounter.current) {
          return;
        }

        lastSearchTerm.current = searchTerm;
        lastSearchTime.current = Date.now();

        setIsLoadingProducts(true);
        try {
          // Get a session token
          const token = await shopify.idToken();

          // Use our own backend endpoint instead of calling Shopify directly
          const response = await makeRequest({
            path: `/react/search_products?id_token=${token}&query=${encodeURIComponent(searchTerm)}`,
            method: 'GET',
          });

          if (response?.error) {
            console.error('Error:', response.error);
            toast.error('Error fetching products.');
            setProductOptions([]);
          } else {
            // Format for dropdown options with normalized IDs
            const options = response.products.map(product => ({
              value: normalizeProductId(product.id),
              label: product.title,
            }));

            // Merge with existing options, avoiding duplicates
            setProductOptions(prevOptions => {
              // Create a map of existing products by normalized ID for lookup
              const existingProductMap = new Map();
              prevOptions.forEach(opt => {
                existingProductMap.set(normalizeProductId(opt.value), opt);
              });

              // Filter out duplicates
              const newOptions = options.filter(opt => !existingProductMap.has(opt.value));

              return [...prevOptions, ...newOptions];
            });
          }
        } catch (error) {
          console.error('Error searching products:', error);
          toast.error('Error fetching products.');
          setProductOptions([]);
        } finally {
          setIsLoadingProducts(false);
        }
      }, 50); // Small delay to batch rapid events
    },
    [shopify, setIsLoadingProducts, setProductOptions]
  );

  // Fetch product details by IDs and add to productOptions
  const fetchProductsByIds = useCallback(
    async ids => {
      if (!ids || ids.length === 0) return;
      try {
        const token = await shopify.idToken();

        // Ensure all IDs are normalized strings (no GIDs, consistent format)
        const normalizeId = id => {
          const idStr = String(id);
          if (idStr.startsWith('gid://shopify/Product/')) {
            return idStr.split('/').pop();
          }
          return idStr;
        };

        const normalizedIds = ids.map(normalizeId);

        const response = await makeRequest({
          path: `/react/get_products_by_ids?id_token=${token}&ids=${normalizedIds.join(',')}`,
          method: 'GET',
        });

        if (response?.products && response.products.length > 0) {
          // Update options with new products
          setProductOptions(prevOptions => {
            // Create a map of existing products by normalized ID for efficient lookup
            const existingProductMap = new Map();
            prevOptions.forEach(opt => {
              existingProductMap.set(normalizeId(opt.value), opt);
            });

            // Create new options from the API response
            const newOptions = response.products
              .filter(p => p.id && p.title)
              .map(p => {
                const normalizedId = normalizeId(p.id);
                return {
                  value: normalizedId, // Always store normalized ID
                  label: p.title,
                };
              })
              .filter(opt => !existingProductMap.has(opt.value)); // Skip if already exists

            // Combine existing and new options
            return [...prevOptions, ...newOptions];
          });
        }
      } catch (error) {
        console.error('Error fetching products by IDs:', error);
        toast.error('Error fetching product details.');
      }
    },
    [shopify, setProductOptions]
  );

  // When modal opens, fetch product details for excludedProductIds if needed
  useEffect(() => {
    if (!isModalOpen || !selectedNudge) return;
    const params = nudgeRequests[selectedNudge]?.parameters;
    if (!params) return;

    // Helper function to normalize product IDs consistently
    const normalizeProductId = id => {
      const idStr = String(id);
      if (idStr.startsWith('gid://shopify/Product/')) {
        return idStr.split('/').pop();
      }
      return idStr;
    };

    let excludedIds = params.excluded_product_ids || [];

    // Convert all IDs to normalized strings for comparison
    excludedIds = excludedIds.map(normalizeProductId);

    // Check which excluded IDs are missing from productOptions
    const missingIds = excludedIds.filter(
      id => !productOptions.some(opt => normalizeProductId(opt.value) === id)
    );

    if (missingIds.length > 0) {
      fetchProductsByIds(missingIds);
    }
  }, [isModalOpen, selectedNudge, nudgeRequests, productOptions, fetchProductsByIds]);

  const getParametersFromNudgeData = (interventionType, nudgeData) => {
    switch (interventionType) {
      case 'cart_abandonment_returning':
        return {
          in_session_cart_abandonment_heading1: nudgeData.group1Line1,
          in_session_cart_abandonment_heading2: nudgeData.group1Line2,
          savings_cart_abandonment_heading1: nudgeData.group2Line1,
          savings_cart_abandonment_heading2: nudgeData.group2Line2,
          primary_background_color: nudgeData.buttonAndTextColor,
          primary_store_font: nudgeData.fontName,
          excluded_product_ids: (nudgeData.excludedProductIds || []).map(id => {
            if (typeof id === 'string' && id.startsWith('gid://shopify/Product/')) {
              return Number(id.split('/').pop());
            } else if (typeof id === 'string') {
              return Number(id);
            } else {
              return id;
            }
          }),
          holdout_percentage: nudgeData.holdoutPercentage / 100,
        };
      case 'cart_abandonment_in_session':
        return {
          in_session_cart_abandonment_heading1: nudgeData.group1Line1,
          in_session_cart_abandonment_heading2: nudgeData.group1Line2,
          savings_cart_abandonment_heading1: nudgeData.group2Line1,
          savings_cart_abandonment_heading2: nudgeData.group2Line2,
          primary_background_color: nudgeData.buttonAndTextColor,
          primary_store_font: nudgeData.fontName,
          excluded_product_ids: (nudgeData.excludedProductIds || []).map(id => {
            if (typeof id === 'string' && id.startsWith('gid://shopify/Product/')) {
              return Number(id.split('/').pop());
            } else if (typeof id === 'string') {
              return Number(id);
            } else {
              return id;
            }
          }),
          holdout_percentage: nudgeData.holdoutPercentage / 100,
        };
      case 'pick_up_where_you_left_off':
        return {
          headline_line_1: nudgeData.headlineLine1,
          headline_line_2: nudgeData.headlineLine2,
          primary_color: nudgeData.primaryColor,
          font: nudgeData.fontName,
          holdout_percentage: nudgeData.holdoutPercentage / 100,
        };
      case 'social_media_content':
        return {
          min_dwell_time: nudgeData.minDwellTime,
          max_dwell_time: nudgeData.maxDwellTime,
          interval: nudgeData.interval,
          holdout_percentage: nudgeData.holdoutPercentage / 100,
          product_videos: nudgeData.uploadedVideos,
        };
      case 'savings_nudge':
        return {
          // Keep parameters in camelCase to match what vandra-main.js expects
          primaryColor: nudgeData.primaryColor,
          font: nudgeData.font,
          holdout_percentage: nudgeData.holdoutPercentage / 100,
          buttonDestination: nudgeData.buttonDestination,
          totalSavings: nudgeData.totalSavings,
          tabText: nudgeData.tabText,
          expandedHeadline: nudgeData.expandedHeadline,
          expandedBody: nudgeData.expandedBody,
          tabColor: nudgeData.tabColor,
          tabFontColor: nudgeData.tabFontColor,
          primaryFontColor: nudgeData.primaryFontColor,
          backgroundColor: nudgeData.backgroundColor,
          buttonText: nudgeData.buttonText,
          positioning: nudgeData.positioning,
          anchor: nudgeData.anchor,
          distanceFromAnchor: nudgeData.distanceFromAnchor,
          threshold: nudgeData.threshold,
          eligible: nudgeData.eligible,
        };
      case 'navigational_nudge':
        return {
          headlineColor: nudgeData.headlineColor,
          bodyTextColor: nudgeData.bodyTextColor,
          backgroundColor: nudgeData.backgroundColor,
          itemBackgroundColor: nudgeData.itemBackgroundColor,
          font: nudgeData.fontName,
          headline: nudgeData.headline,
          subheader: nudgeData.subheader,
          answerOptions: nudgeData.answerOptions,
          timeDelay: nudgeData.timeDelay,
          holdout_percentage: nudgeData.holdoutPercentage / 100,
        };
      case 'intent_based_discount':
        return {
          discount_one_time: nudgeData.eligibility.singleItem,
          discount_subscription: nudgeData.eligibility.subscription,
          show_discount_when_logged_in: !nudgeData.excludeLoggedInVisitors,
          show_discount_to_previous_customers: !nudgeData.excludeVisitorsWhoBought,
          popup_primary_color: nudgeData.primaryColor.replace('#', ''),
          popup_bg_color: nudgeData.backgroundColor.replace('#', ''),
          popup_font: nudgeData.fontName,
          popup_text_header: nudgeData.headerText,
          popup_text_body: nudgeData.bodyText,
          popup_text_button: nudgeData.buttonText,
          popup_text_success: nudgeData.successButtonText,
          auto_apply_text_body: nudgeData.bodyText,
          minimized_text_header: nudgeData.minimizedTabText,
          hide_minimized_popup: nudgeData.tabDisabled,
          max_discount: nudgeData.discountPercentage,
          discount_prefix: nudgeData.discountPrefix,
          discount_combines_with: {
            orderDiscounts: nudgeData.combinableDiscounts.order,
            productDiscounts: nudgeData.combinableDiscounts.product,
            shippingDiscounts: nudgeData.combinableDiscounts.shipping,
          },
          discount_variants: nudgeData.discountVariants.map(variant => ({
            discount_value: variant.discountPercentage,
            discount_prefix: nudgeData.discountPrefix + '-' + variant.discountPercentage,
          })),
          holdout: nudgeData.holdoutPercentage / 100,
          anti_holdout_percentage: (nudgeData.anti_holdout_percentage || 0) / 100,
          discount_collections:
            nudgeData.productApplicability === 'all' ? [] : nudgeData.selectedCollections,
          exclude_urls: nudgeData.exclusionURLs,
        };
      default:
        return {};
    }
  };

  const rawParameters = useMemo(() => {
    if (
      isSubmitting &&
      lastSavedConfigRef.current &&
      ['save', 'pause-toggle'].includes(submitReasonRef.current)
    ) {
      return lastSavedConfigRef.current;
    }
    return nudgeRequests[selectedNudge]?.parameters;
  }, [isSubmitting, nudgeRequests, selectedNudge]);

  const isActive = useMemo(() => {
    if (
      isSubmitting &&
      submitReasonRef.current &&
      lastSavedIsActiveRef.current &&
      ['save', 'pause-toggle'].includes(submitReasonRef.current)
    ) {
      return lastSavedIsActiveRef.current;
    }
    return nudgeRequests[selectedNudge]?.active;
  }, [isSubmitting, nudgeRequests, selectedNudge]);

  const handlePauseToggle = async (nudgeId, isPaused, configData) => {
    const payload = {
      intervention_type_name: nudgeId,
      active: !isPaused,
      parameters: getParametersFromNudgeData(nudgeId, configData),
    };

    try {
      setIsSubmitting(true);
      // Cache the just-saved data to avoid stale re-render
      submitReasonRef.current = 'pause-toggle';
      lastSavedConfigRef.current = payload.parameters;
      lastSavedIsActiveRef.current = payload.active;
      let response;
      if (nudgeId === 'intent_based_discount') {
        response = await makeRequest({
          path: `/react/merchant_discount_nudge_activate?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: JSON.stringify({ show_discount: !isPaused }),
          headers: {
            'Content-Type': 'application/json',
          },
        });
        if (!response?.data?.success) {
          toast.error(`Failed to ${!isPaused ? 'enable' : 'disable'} nudge`);
          return false;
        }
        response = await makeRequest({
          path: `/react/merchant_discount_nudge_save_settings?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: JSON.stringify(payload.parameters),
          headers: {
            'Content-Type': 'application/json',
          },
        });
      } else {
        response = await makeRequest({
          path: `/react/merchant_nudge_parameters_save?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: JSON.stringify(payload),
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }

      if (response?.data?.success) {
        // Update the nudges list after successful save
        await getNudgeRequests();
        return true;
      } else {
        toast.error(`Failed to ${!isPaused ? 'enable' : 'disable'} nudge`);
        return false;
      }
    } catch (error) {
      console.error(`Error toggling nudge status for ${nudgeId}:`, error);
      toast.error('An error occurred while updating status');
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveConfig = async configData => {
    try {
      if (!selectedNudge) return;

      const payload = {
        intervention_type_name: selectedNudge,
        parameters: getParametersFromNudgeData(selectedNudge, configData),
      };

      setIsSubmitting(true);
      // Cache the just-saved data to avoid stale re-render
      submitReasonRef.current = 'save';
      lastSavedConfigRef.current = payload.parameters;

      let response;
      if (selectedNudge === 'intent_based_discount') {
        response = await makeRequest({
          path: `/react/merchant_discount_nudge_save_settings?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: JSON.stringify(payload.parameters),
          headers: {
            'Content-Type': 'application/json',
          },
        });
      } else {
        response = await makeRequest({
          path: `/react/merchant_nudge_parameters_save?id_token=${await shopify.idToken()}`,
          method: 'POST',
          body: JSON.stringify(payload),
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }

      if (response?.data?.success) {
        // Update the nudges list after successful save
        await getNudgeRequests();
        return true;
      } else {
        toast.error('Failed to save configuration');
        throw new Error('Failed to save configuration');
      }
    } catch (error) {
      console.error(`Error preparing configuration for ${selectedNudge}:`, error);
      toast.error('An error occurred while preparing configuration');
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Status badge component for nudges
  const StatusBadge = ({ active }) => {
    return (
      <div
        className={`flex items-center rounded-full px-3 py-1 mr-3 text-sm font-medium ${
          active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
        }`}
      >
        <div
          className={`w-2 h-2 rounded-full mr-2 ${active ? 'bg-green-500' : 'bg-gray-400'}`}
        ></div>
        {active ? 'Enabled' : 'Disabled'}
      </div>
    );
  };

  StatusBadge.propTypes = {
    active: PropTypes.bool.isRequired,
  };

  if (isPageLoading) {
    return (
      <div className="flex-col-center">
        <div className="content flex-col-center gap full">
          <Loader />
        </div>
      </div>
    );
  }

  if (selectedNudge) {
    return (
      <NudgeConfigModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={nudgeRequests[selectedNudge]?.title || 'Configure Nudge'}
        showEmulationBanner={true}
      >
        <NudgeConfigManager
          nudgeId={selectedNudge}
          rawParameters={rawParameters}
          onSave={async configData => {
            try {
              await handleSaveConfig(configData);
            } catch (error) {
              toast.error('Error saving configuration');
              throw error;
            }
          }}
          onClose={closeModal}
          isSubmitting={isSubmitting}
          isPaused={!isActive}
          isSuperAdmin={isSuperAdmin}
          onPauseToggle={async (isPaused, data) => {
            try {
              const success = await handlePauseToggle(selectedNudge, isPaused, data);
              if (!success) {
                throw new Error('Failed to toggle nudge status');
              }
            } catch (error) {
              toast.error('Error in pause toggle:', error);
              throw error;
            }
          }}
          productOptions={productOptions}
          isLoadingProducts={isLoadingProducts}
          onProductSearch={handleProductSearch}
        />
      </NudgeConfigModal>
    );
  }

  if (supportOpen) {
    return (
      <div className="min-h-screen">
        {/* Header */}
        <header className="bg-white p-4">
          <div className="container mx-auto flex justify-between items-center">
            <div className="flex items-center space-x-8">
              <img
                onClick={() => {
                  setOpenStarter(false);
                  setSupportOpen(false);
                }}
                className="w-[11%] min-w-[100px] cursor-pointer"
                src="/img/vandra_purple_logo.png"
                alt="Vandra logo"
              />
              <nav className="hidden md:flex space-x-6">
                <div
                  onClick={() => {
                    setOpenStarter(true);
                    setSupportOpen(false);
                  }}
                  className="text-gray-500 cursor-pointer"
                >
                  Discover
                </div>
                <div
                  onClick={() => {
                    setSupportOpen(true);
                    setOpenStarter(false);
                  }}
                  className="text-gray-500 cursor-pointer"
                >
                  Support
                </div>
              </nav>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="container mx-auto py-8">
          {/* Funnel Types */}
          <div className="flex flex-col items-center justify-center gap-4 mb-8 bg-gray-50 p-6">
            <textarea
              value={supportText}
              onChange={e => setSupportText(e.currentTarget.value)}
              type="text"
              placeholder="Let us know how we can help, and a member of our team will contact you!"
              className="w-full max-w-[600px] h-[150px] p-2 resize-y"
            />
            <button
              onClick={submitSupportRequest}
              className="flex justify-center w-[20%] min-w-[385px] bg-[#856CF8] hover:bg-purple-600 text-white py-3 rounded-lg font-medium"
            >
              {!isSupportLoading ? 'Submit' : <Loader className="w-6 h-6" />}
            </button>
          </div>
        </main>
      </div>
    );
  }

  if (!anyNudgeActive || openStarter) {
    return (
      <StarterDashboard
        widgetColor={widgetSettings.popup_primary_color}
        font={widgetSettings.popup_font}
        handleConfigureNudge={handleConfigureNudge}
        popupDiscountCodePrefix={widgetSettings.popupDiscountCodePrefix}
        popupBackgroundColor={widgetSettings.popupBackgroundColor}
        setOpenStarter={setOpenStarter}
        setSupportOpen={setSupportOpen}
      />
    );
  }

  return (
    <MainDashboard
      nudgeRequests={nudgeRequests}
      handleConfigureNudge={handleConfigureNudge}
      setOpenStarter={setOpenStarter}
      setSupportOpen={setSupportOpen}
    />
  );
};

Nudges.propTypes = {
  isSuperAdmin: PropTypes.bool,
};

export default Nudges;
