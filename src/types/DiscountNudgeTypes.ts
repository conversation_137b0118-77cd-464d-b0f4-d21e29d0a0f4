export interface DiscountVariant {
  discountPercentage: number;
  discountPrefix: string;
  discountCode?: string;
}

export interface DiscountNudgeData {
  headerText: string;
  bodyText: string;
  buttonText: string;
  successButtonText: string;
  minimizedTabText: string;
  primaryColor: string;
  backgroundColor: string;
  fontName: string;
  collections: Array<{ label: string; value: string }>;
  discountPercentage: number;
  discountPrefix: string;
  discountCode?: string;
  discountVariants?: DiscountVariant[];
  expiryDays?: number;
  productApplicability: 'all' | 'collection';
  selectedCollections?: string[];
  combinableDiscounts: {
    product: boolean;
    order: boolean;
    shipping: boolean;
  };
  eligibility: {
    singleItem: boolean;
    subscription: boolean;
  };
  // Advanced tab fields
  tabDisabled?: boolean;
  holdoutPercentage?: number;
  anti_holdout_percentage?: number;
  excludeLoggedInVisitors?: boolean;
  excludeVisitorsWhoBought?: boolean;
  exclusionURLs?: string | string[];
}
