export interface SavingsNudgeData {
  tabColor?: string;
  tabFontColor?: string;
  tabText?: string;
  primaryColor?: string;
  primaryFontColor?: string;
  backgroundColor?: string;
  font?: string;
  expandedHeadline?: string;
  expandedBody?: string;
  buttonText?: string;
  buttonDestination?: string;
  positioning?: 'Left' | 'Right';
  anchor?: 'Top' | 'Bottom';
  distanceFromAnchor?: number;
  total_savings?: number;
  holdoutPercentage?: number;
  threshold?: number;
}
