export interface UploadedVideo {
  product_name: string;
  asset_type: string;
  asset_key: string;
  asset_url: string;
  filename: string;
  product_id: string;
  variant_id: string;
}

export interface SocialMediaContentNudgeData {
  // Advanced tab fields
  holdoutPercentage?: number;
  minDwellTime?: number;
  maxDwellTime?: number;
  interval?: number;
  defaultPreviewVideo?: string;
  videoUrl?: string;
  videoSelectionMethod?: string;
  uploadedVideos?: UploadedVideo[];
  productOptions: Array<{ label: string; value: string }>;
}
