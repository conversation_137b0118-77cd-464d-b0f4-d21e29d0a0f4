export interface NavigationNudgeData {
  // Basic tab fields
  headline?: string;
  subheader?: string;
  headlineColor?: string;
  bodyTextColor?: string;
  backgroundColor?: string;
  itemBackgroundColor?: string;
  fontName?: string;
  answerOptions?: Array<AnswerOption>;
  timeDelay?: number;
  // Advanced tab fields
  holdoutPercentage?: number;
}

export interface AnswerOption {
  label: string;
  url: string;
}
