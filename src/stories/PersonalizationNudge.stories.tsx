import React, { useState, useEffect } from 'react';
import {
  NudgeType,
  PersonalizationNudge,
  PersonalizationNudgeProps,
} from '../components/UI/PersonalizationNudge';
import { CartAbandonmentNudgeData } from '../types/CartAbandonmentTypes';
import { DiscountNudgeData } from '../types/DiscountNudgeTypes';
import {
  PickUpWhereYouLeftOffItem,
  PickUpWhereYouLeftOffNudgeData,
} from '../types/PickUpWhereYouLeftOffTypes';
import { SocialMediaContentNudgeData } from '../types/SocialMediaContentTypes';
import { NavigationNudgeData } from '../types/NavigationNudgeTypes';
import { SavingsNudgeData } from '../types/SavingsNudgeTypes';
import { useDebounce } from '../hooks/useDebounce';
import product1 from '../../public/assets/PickupWhereYouLeftOff/product1.png';
import product2 from '../../public/assets/PickupWhereYouLeftOff/product2.png';
import product3 from '../../public/assets/PickupWhereYouLeftOff/product3.png';

// Create 50 realistic Shopify products
const generateShopifyProducts = () => {
  const products: Array<{ label: string; value: string }> = [
    { label: 'Vintage Denim Jacket', value: 'vintage-denim-jacket' },
    { label: 'Organic Cotton T-Shirt', value: 'organic-cotton-t-shirt' },
    {
      label: 'Smart Home Security Camera',
      value: 'smart-home-security-camera',
    },
    { label: 'Bamboo Cutting Board Set', value: 'bamboo-cutting-board-set' },
    {
      label: 'Wireless Bluetooth Earbuds',
      value: 'wireless-bluetooth-earbuds',
    },
    { label: 'Natural Face Serum', value: 'natural-face-serum' },
    {
      label: 'Handcrafted Leather Wallet',
      value: 'handcrafted-leather-wallet',
    },
    {
      label: 'Stainless Steel Water Bottle',
      value: 'stainless-steel-water-bottle',
    },
    {
      label: 'Aromatherapy Essential Oil Diffuser',
      value: 'aromatherapy-essential-oil-diffuser',
    },
    {
      label: 'Yoga Mat with Alignment Lines',
      value: 'yoga-mat-with-alignment-lines',
    },
    { label: 'Artisan Ceramic Mug Set', value: 'artisan-ceramic-mug-set' },
    { label: 'Adjustable Standing Desk', value: 'adjustable-standing-desk' },
    {
      label: 'Noise Cancelling Headphones',
      value: 'noise-cancelling-headphones',
    },
    {
      label: 'Glass Food Storage Container Set',
      value: 'glass-food-storage-container-set',
    },
    { label: 'Digital Fitness Tracker', value: 'digital-fitness-tracker' },
    { label: 'Silk Pillowcase', value: 'silk-pillowcase' },
    { label: 'Minimalist Wall Clock', value: 'minimalist-wall-clock' },
    { label: 'Cast Iron Skillet', value: 'cast-iron-skillet' },
    {
      label: 'Organic Herbal Tea Collection',
      value: 'organic-herbal-tea-collection',
    },
    {
      label: 'Portable Bluetooth Speaker',
      value: 'portable-bluetooth-speaker',
    },
    { label: 'Handwoven Basket Set', value: 'handwoven-basket-set' },
    {
      label: 'Sustainable Bamboo Toothbrush',
      value: 'sustainable-bamboo-toothbrush',
    },
    { label: 'Ergonomic Office Chair', value: 'ergonomic-office-chair' },
    { label: 'Linen Bedding Set', value: 'linen-bedding-set' },
    { label: 'LED Plant Grow Light', value: 'led-plant-grow-light' },
    { label: 'Handmade Ceramic Planter', value: 'handmade-ceramic-planter' },
    { label: 'Merino Wool Scarf', value: 'merino-wool-scarf' },
    { label: 'Smart Wi-Fi Thermostat', value: 'smart-wifi-thermostat' },
    { label: 'Natural Beeswax Candles', value: 'natural-beeswax-candles' },
    {
      label: 'Recycled Glass Drinking Set',
      value: 'recycled-glass-drinking-set',
    },
    { label: 'Leather Crossbody Bag', value: 'leather-crossbody-bag' },
    {
      label: 'Lightweight Hiking Backpack',
      value: 'lightweight-hiking-backpack',
    },
    { label: 'Memory Foam Pillow', value: 'memory-foam-pillow' },
    { label: 'Cold Brew Coffee Maker', value: 'cold-brew-coffee-maker' },
    {
      label: 'Reusable Silicone Food Bags',
      value: 'reusable-silicone-food-bags',
    },
    { label: 'Bamboo Drawer Organizers', value: 'bamboo-drawer-organizers' },
    {
      label: 'Woven Cotton Throw Blanket',
      value: 'woven-cotton-throw-blanket',
    },
    { label: 'Modern Ceramic Vase', value: 'modern-ceramic-vase' },
    {
      label: 'Organic Cotton Bath Towels',
      value: 'organic-cotton-bath-towels',
    },
    { label: 'Wooden Desktop Organizer', value: 'wooden-desktop-organizer' },
    {
      label: 'Air Purifier with HEPA Filter',
      value: 'air-purifier-with-hepa-filter',
    },
    {
      label: 'Stainless Steel Compost Bin',
      value: 'stainless-steel-compost-bin',
    },
    { label: 'Portable Solar Charger', value: 'portable-solar-charger' },
    { label: 'Macrame Wall Hanging', value: 'macrame-wall-hanging' },
    { label: 'Facial Cleansing Brush', value: 'facial-cleansing-brush' },
    { label: 'Lightweight Running Shoes', value: 'lightweight-running-shoes' },
    {
      label: 'Collapsible Silicone Lunch Box',
      value: 'collapsible-silicone-lunch-box',
    },
    { label: 'Linen Kitchen Apron', value: 'linen-kitchen-apron' },
    { label: 'Wooden Board Game Set', value: 'wooden-board-game-set' },
    { label: 'Natural Wool Dryer Balls', value: 'natural-wool-dryer-balls' },
  ];

  return products;
};

// Generate all products once
const allProducts = generateShopifyProducts();

// Create realistic Shopify collections
const generateShopifyCollections = () => {
  const collections: Array<{ label: string; value: string }> = [
    { label: 'Summer Collection', value: 'summer-collection' },
    { label: 'Winter Collection', value: 'winter-collection' },
    { label: 'Spring Collection', value: 'spring-collection' },
    { label: 'Fall Collection', value: 'fall-collection' },
    { label: 'Holiday Collection', value: 'holiday-collection' },
    { label: 'New Arrivals', value: 'new-arrivals' },
    { label: 'Bestsellers', value: 'bestsellers' },
    { label: 'Clearance', value: 'clearance' },
    { label: 'Organic Products', value: 'organic-products' },
    { label: 'Eco-Friendly', value: 'eco-friendly' },
    { label: 'Limited Edition', value: 'limited-edition' },
    { label: 'Handmade Items', value: 'handmade-items' },
    { label: 'Kitchen Essentials', value: 'kitchen-essentials' },
    { label: 'Bathroom Products', value: 'bathroom-products' },
    { label: 'Outdoor Living', value: 'outdoor-living' },
    { label: 'Home Decor', value: 'home-decor' },
    { label: 'Electronics', value: 'electronics' },
    { label: 'Beauty & Personal Care', value: 'beauty-personal-care' },
    { label: 'Sports & Fitness', value: 'sports-fitness' },
    { label: 'Books & Media', value: 'books-media' },
  ];

  return collections;
};

// Generate all collections once
const allCollections = generateShopifyCollections();

// Simulated API call to fetch filtered products
const fetchFilteredProducts = async (searchTerm = '') => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // Simulate server-side filtering
  const filtered = allProducts.filter(
    product => searchTerm === '' || product.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Return max 20 items
  return filtered.slice(0, 20);
};

// Simulated API call to fetch filtered collections
const fetchFilteredCollections = async (searchTerm = '') => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // Simulate server-side filtering
  const filtered = allCollections.filter(
    collection =>
      searchTerm === '' || collection.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Return max 10 items
  return filtered.slice(0, 10);
};

const meta = {
  title: 'Nudge Personalization',
  component: PersonalizationNudge,
  parameters: {
    layout: 'fullscreen',
    backgrounds: {
      default: 'white',
      values: [
        {
          name: 'white',
          value: '#ffffff',
        },
      ],
    },
    docs: {
      story: {
        inline: false,
        iframeHeight: '100%',
      },
    },
  },
  decorators: [
    (Story: React.ComponentType) => (
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          height: '100vh',
          width: '100vw',
          overflow: 'hidden',
        }}
      >
        <Story />
      </div>
    ),
  ],
  tags: ['autodocs'],
  argTypes: {
    nudgeType: {
      control: { type: 'select' },
      options: [
        'cartAbandonment',
        'inSessionCartAbandonment',
        'discount',
        'pickUpWhereYouLeftOff',
        'socialMediaContent',
        'navigation',
        'savings',
      ],
      description: 'Type of nudge to display',
    },
    paramOptions: {
      control: 'array',
      description: 'Parameter options for dynamic content',
    },
  },
};

export default meta;

// Wrapper component to handle state
const PersonalizationNudgeWrapper = ({
  args,
}: {
  args: {
    nudgeType: NudgeType;
    paramOptions: Record<string, string>;
    data:
      | CartAbandonmentNudgeData
      | DiscountNudgeData
      | PickUpWhereYouLeftOffNudgeData
      | SocialMediaContentNudgeData
      | NavigationNudgeData
      | SavingsNudgeData;
    isPaused: boolean;
    productOptions?: { label: string; value: string }[];
  };
}) => {
  const [savedData, setSavedData] = useState<
    | CartAbandonmentNudgeData
    | DiscountNudgeData
    | PickUpWhereYouLeftOffNudgeData
    | SocialMediaContentNudgeData
    | NavigationNudgeData
    | SavingsNudgeData
  >(args.data);
  const [isPaused, setIsPaused] = useState(args.isPaused || false);
  const [filteredProducts, setFilteredProducts] = useState<{ label: string; value: string }[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [filteredCollections, setFilteredCollections] = useState<
    { label: string; value: string }[]
  >([]);
  const [isLoadingCollections, setIsLoadingCollections] = useState(false);

  // Product search state
  const [searchTerm, setSearchTerm] = useState('');

  // Collection search state
  const [collectionSearchTerm, setCollectionSearchTerm] = useState('');

  // Apply debounce to searchTerm
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const debouncedCollectionSearchTerm = useDebounce(collectionSearchTerm, 300);

  // Load initial products when component mounts
  useEffect(() => {
    const loadInitialProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const initialProducts = await fetchFilteredProducts();
        setFilteredProducts(initialProducts);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    loadInitialProducts();
  }, []);

  // Load initial collections when component mounts
  useEffect(() => {
    const loadInitialCollections = async () => {
      setIsLoadingCollections(true);
      try {
        const initialCollections = await fetchFilteredCollections();
        setFilteredCollections(initialCollections);
      } finally {
        setIsLoadingCollections(false);
      }
    };

    loadInitialCollections();
  }, []);

  // Effect to handle product search when debounced term changes
  useEffect(() => {
    const performSearch = async () => {
      setIsLoadingProducts(true);
      try {
        const results = await fetchFilteredProducts(debouncedSearchTerm);
        setFilteredProducts(results);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    performSearch();
  }, [debouncedSearchTerm]);

  // Effect to handle collection search when debounced term changes
  useEffect(() => {
    const performCollectionSearch = async () => {
      setIsLoadingCollections(true);
      try {
        const results = await fetchFilteredCollections(debouncedCollectionSearchTerm);
        setFilteredCollections(results);
      } finally {
        setIsLoadingCollections(false);
      }
    };

    performCollectionSearch();
  }, [debouncedCollectionSearchTerm]);

  const handleSave = async (data: any) => {
    await new Promise(r => setTimeout(r, 1000)); // simulate saving delay
    console.info(`Saving data: ${JSON.stringify(data)}`);
    setSavedData(data);
  };

  const handlePauseToggle = async (newPausedState: boolean, data: any): Promise<void> => {
    setIsPaused(newPausedState);
    console.info(`Pausing data: ${JSON.stringify(data)}`);
    await new Promise(r => setTimeout(r, 1000)); // simulate API call
  };

  // Handler for product search - just updates the searchTerm state
  const handleProductSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Handler for collection search - just updates the collectionSearchTerm state
  const handleCollectionSearch = (term: string) => {
    setCollectionSearchTerm(term);
  };

  // Create properly typed props based on the nudge type
  const nudgeProps = {
    ...args,
    data: savedData,
    onSave: handleSave,
    isPaused: isPaused,
    onPauseToggle: handlePauseToggle,
    productOptions: filteredProducts,
    onProductSearch: handleProductSearch,
    isLoadingProducts: isLoadingProducts,
    collectionOptions: filteredCollections,
    onCollectionSearch: handleCollectionSearch,
    isLoadingCollections: isLoadingCollections,
  };

  return <PersonalizationNudge {...(nudgeProps as PersonalizationNudgeProps)} />;
};

export const ReturnVisitorCartAbandonmentNudge = {
  render: (args: any) => <PersonalizationNudgeWrapper args={args} />,
  name: 'Return Visitor Cart Abandonment Nudge',
  args: {
    nudgeType: 'cartAbandonment',
    dropdownOptions: [
      { label: 'No Savings in Cart', value: 'no-savings' },
      { label: 'Savings in Cart', value: 'savings' },
    ],
    paramOptions: {
      savings: 14,
    },
    isPaused: false,
    data: {
      group1Line1: "Don't leave your",
      group1Line2: 'cart hanging!',
      // eslint-disable-next-line no-template-curly-in-string
      group2Line1: 'You have ${savings} in',
      group2Line2: 'savings in your cart!',
      buttonAndTextColor: '#8F0067',
      fontName: 'Arial',
      storeAdImage: 'assets/CartAbandoned/product.png',
      holdoutPercentage: 20,
      productExclusions: '',
    },
  },
};

export const InSessionCartAbandonmentNudge = {
  render: (args: any) => <PersonalizationNudgeWrapper args={args} />,
  name: 'In-Session Cart Abandonment Nudge',
  args: {
    nudgeType: 'inSessionCartAbandonment',
    dropdownOptions: [
      { label: 'No Savings in Cart', value: 'no-savings' },
      { label: 'Savings in Cart', value: 'savings' },
    ],
    paramOptions: {
      savings: 14,
    },
    isPaused: false,
    data: {
      group1Line1: "Don't leave your",
      group1Line2: 'cart hanging!',
      // eslint-disable-next-line no-template-curly-in-string
      group2Line1: 'You have ${savings} in',
      group2Line2: 'savings in your cart!',
      buttonAndTextColor: '#0067A3',
      fontName: 'Arial',
      storeAdImage: 'assets/CartAbandoned/product.png',
      holdoutPercentage: 20,
      productExclusions: '',
    },
  },
};

export const DiscountNudge = {
  render: (args: any) => <PersonalizationNudgeWrapper args={args} />,
  name: 'On-the-Fence Discount Nudge',
  args: {
    nudgeType: 'discount',
    paramOptions: { discount: '20' },
    isPaused: false,
    data: {
      discountPrefix: 'VANDRA',
      discountAmount: '20%',
      headerText: 'GET {discount}% OFF NOW!',
      bodyText: 'Exclusions apply.',
      buttonText: 'Apply',
      successButtonText: 'Applied',
      minimizedTabText: 'GET {discount}% OFF NOW!',
      primaryColor: '#8F0067',
      backgroundColor: '#FFFFFF',
      fontName: 'Arial',
      discountPercentage: 20,
    },
  },
};

export const PickUpWhereYouLeftOffNudge = {
  render: (args: any) => <PersonalizationNudgeWrapper args={args} />,
  name: 'Pick-Up Where You Left Off (PUWYLO) Nudge',
  args: {
    nudgeType: 'pickUpWhereYouLeftOff',
    paramOptions: {},
    isPaused: false,
    data: {
      headlineLine1: 'Welcome back!',
      headlineLine2: 'Pick up where you left off:',
      primaryColor: '#8F0067',
      backgroundColor: '#FFFFFF',
      fontName: 'Arial',
      holdoutPercentage: 20,
      items: [
        {
          productId: '10001',
          variantId: '20001',
          url: 'https://www.google.com',
          image: product1,
          title: "Women's Multi",
          price: 13.99,
          priceCurrency: 'USD',
        },
        {
          productId: '10002',
          variantId: '20002',
          url: 'https://www.google.com',
          image: product2,
          title: "Men's Multi",
          price: 13.99,
          priceCurrency: 'USD',
        },
        {
          productId: '10003',
          variantId: '20003',
          url: 'https://www.google.com',
          image: product3,
          title: 'Probiotic - Mango',
          price: 13.99,
          priceCurrency: 'USD',
        },
      ] as PickUpWhereYouLeftOffItem[],
    },
  },
};

export const SocialMediaContentNudge = {
  render: (args: any) => <PersonalizationNudgeWrapper args={args} />,
  name: 'Social Media Video Nudge',
  args: {
    nudgeType: 'socialMediaContent',
    paramOptions: {},
    isPaused: false,
    data: {
      videoUrl: 'assets/SocialMediaContentNudge/goodbye-stress.mp4',
      holdoutPercentage: 20,
      uploadedVideos: [], // Start with empty videos list
    },
    socialMediaProductOptions: [
      {
        productId: 'goodbye-stress',
        productTitle: 'Goodbye Stress',
        videoUrl: 'assets/SocialMediaContentNudge/goodbye-stress.mp4',
      },
      {
        productId: 'generic-fallback',
        productTitle: 'Generic Fallback',
        videoUrl: 'assets/SocialMediaContentNudge/fallback1.mp4',
      },
      {
        productId: 'vintage-denim-jacket',
        productTitle: 'Vintage Denim Jacket',
        videoUrl: 'assets/SocialMediaContentNudge/vintage-denim.mp4',
      },
      {
        productId: 'organic-cotton-tshirt',
        productTitle: 'Organic Cotton T-Shirt',
        videoUrl: 'assets/SocialMediaContentNudge/cotton-tshirt.mp4',
      },
      {
        productId: 'smart-home-security',
        productTitle: 'Smart Home Security Camera',
        videoUrl: 'assets/SocialMediaContentNudge/security-camera.mp4',
      },
      {
        productId: 'bamboo-cutting-board',
        productTitle: 'Bamboo Cutting Board Set',
        videoUrl: 'assets/SocialMediaContentNudge/cutting-board.mp4',
      },
      {
        productId: 'wireless-earbuds',
        productTitle: 'Wireless Bluetooth Earbuds',
        videoUrl: 'assets/SocialMediaContentNudge/earbuds.mp4',
      },
      {
        productId: 'natural-face-serum',
        productTitle: 'Natural Face Serum',
        videoUrl: 'assets/SocialMediaContentNudge/face-serum.mp4',
      },
      {
        productId: 'leather-wallet',
        productTitle: 'Handcrafted Leather Wallet',
        videoUrl: 'assets/SocialMediaContentNudge/leather-wallet.mp4',
      },
      {
        productId: 'stainless-water-bottle',
        productTitle: 'Stainless Steel Water Bottle',
        videoUrl: 'assets/SocialMediaContentNudge/water-bottle.mp4',
      },
    ],
  },
};

export const NavigationNudge = {
  render: (args: any) => <PersonalizationNudgeWrapper args={args} />,
  name: 'New Visitor Data Collection / Navigational Nudge',
  args: {
    nudgeType: 'navigation',
    paramOptions: {},
    isPaused: false,
    data: {
      headline: 'What are you shopping for?',
      subheader: "We'll take you to the right spot!",
      headlineColor: '#358E7F',
      bodyTextColor: '#000000',
      backgroundColor: '#FFFFFF',
      itemBackgroundColor: '#EDEEF0',
      font: 'Arial',
      timeDelay: 15,
      answerOptions: [
        {
          label: 'Option 1',
          url: '/option-1',
        },
        {
          label: 'Option 2',
          url: '/option-2',
        },
        {
          label: 'Option 3',
          url: '/option-3',
        },
      ],
      holdoutPercentage: 20,
    },
  },
};

export const SavingsNudge = {
  render: (args: any) => <PersonalizationNudgeWrapper args={args} />,
  name: 'Savings Nudge',
  args: {
    nudgeType: 'savings',
    paramOptions: { total_savings: '$45.00' },
    isPaused: false,
    data: {
      tabColor: '#2B3336',
      tabFontColor: '#FFFFFF',
      tabText: 'Savings: {total_savings}',
      primaryColor: '#232323',
      primaryFontColor: '#232323',
      backgroundColor: '#FFFFFF',
      font: 'Inter',
      expandedHeadline: 'Lucky you… {total_savings} in savings!',
      expandedBody: "When you're ready, head to checkout and lock in these savings.",
      buttonText: 'Checkout',
      buttonDestination: '/checkout',
      positioning: 'Right',
      anchor: 'Top',
      distanceFromAnchor: 30,
      holdoutPercentage: 20,
    },
  },
};
