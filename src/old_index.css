body {
  background-color: #f6f8f9;
}

.flex-col-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.flex-col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.flex-col-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

.flex-row-left {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.flex-row-center-top {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
}

.flex-row-center {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.flex-row-center-stretch {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: stretch;
}

.flex-row-between-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
}

.flex-row-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.flex-row-right {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.gap-lg {
  gap: 4rem;
}

.gap-md {
  gap: 4rem;
}

.gap {
  gap: 1rem;
}

.gap-sm {
  gap: 0.25rem;
}

.gap-xs {
  gap: 0.1rem;
}

.full {
  width: 100%;
}

.one-half {
  width: 50%;
}

.one-third {
  width: 33%;
}

.two-thirds {
  width: 66%;
}

.hidden {
  display: none;
}

.invisible {
  visibility: hidden;
}

.content {
  max-width: 1000px;
}

.card {
  padding: 2rem;
}

.card-sm {
  padding: 1rem;
}

.card-xs {
  padding: 0.5rem 1rem 0.5rem 1rem;
}

.link {
  text-decoration: none;
  color: #856cf8;
}

/* Overview */

#selectSales fieldset {
  border: none !important;
  outline: none !important;
}

/* Widget */

.slide-center-to-right {
  animation: center-to-right 0.5s;
  -webkit-animation: center-to-right 0.5s;
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
}

@keyframes center-to-right {
  0% {
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    transform: translateX(0%);
  }

  100% {
    -webkit-transform: translateX(200%);
    -ms-transform: translateX(200%);
    transform: translateX(200%);
  }
}

@-webkit-keyframes center-to-right {
  0% {
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    transform: translateX(0%);
  }

  100% {
    -webkit-transform: translateX(200%);
    -ms-transform: translateX(200%);
    transform: translateX(200%);
  }
}

.slide-left-to-center {
  animation: left-to-center 0.5s;
  -webkit-animation: left-to-center 0.5s;
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
}

@keyframes left-to-center {
  0% {
    -webkit-transform: translateX(-200%);
    -ms-transform: translateX(-200%);
    transform: translateX(-200%);
  }

  100% {
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    transform: translateX(0%);
  }
}

@-webkit-keyframes left-to-center {
  0% {
    -webkit-transform: translateX(-200%);
    -ms-transform: translateX(-200%);
    transform: translateX(-200%);
  }

  100% {
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    transform: translateX(0%);
  }
}

/* Loader */
.spinner {
  animation: rotator 1.4s linear infinite;
}

.path {
  stroke-dasharray: 187;
  stroke-dashoffset: 0;
  transform-origin: center;
  animation:
    dash 1.4s ease-in-out infinite,
    colors 5.6s ease-in-out infinite;
}

@keyframes rotator {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(270deg);
  }
}

@keyframes colors {
  0% {
    stroke: #4285f4;
  }

  25% {
    stroke: #de3e35;
  }

  50% {
    stroke: #f7c223;
  }

  75% {
    stroke: #1b9a59;
  }

  100% {
    stroke: #4285f4;
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 187;
  }

  50% {
    stroke-dashoffset: 46.75;
    transform: rotate(135deg);
  }

  100% {
    stroke-dashoffset: 187;
    transform: rotate(450deg);
  }
}

@media only screen and (max-width: 600px) {
  .flex-col-left-mobile {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 1rem;
  }

  .flex-col-center-mobile {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
  }

  .full-mobile {
    width: 100%;
  }

  .one-half-mobile {
    width: 50%;
  }

  .one-third-mobile {
    width: 33%;
  }

  .two-thirds-mobile {
    width: 66%;
  }

  .show-mobile {
    display: block;
  }

  .hidden-mobile {
    display: none;
  }

  .charts-mobile {
    width: 500px;
  }
}
