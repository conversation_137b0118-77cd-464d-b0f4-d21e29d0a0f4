@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Custom scrollbar styles with improved transition */
  .custom-scrollbar {
    /* Firefox scrollbar */
    scrollbar-width: thin;
    scrollbar-color: rgba(152, 162, 179, 0.3) transparent;
  }

  /* For Webkit browsers like Chrome/Safari */
  .custom-scrollbar::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(152, 162, 179, 0.3);
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  /* Only darken scrollbar when hovering the scrollbar itself */
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(152, 162, 179, 0.7);
  }

  /* For Firefox - can't directly target scrollbar hover in Firefox, so we use a fallback */
  @supports (scrollbar-color: auto) {
    .custom-scrollbar:hover {
      scrollbar-color: rgba(152, 162, 179, 0.7) transparent;
    }
  }
}
