import 'react-responsive-carousel/lib/styles/carousel.min.css';
import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import { toast, ToastContainer } from 'react-toastify';
import { useAppBridge } from '@shopify/app-bridge-react';
import Onboarding from './components/pages/Onboarding';
import Dashboard from './components/pages/Dashboard';
import Loader from './components/UI/Loader';
import { makeRequest } from './utils/makeRequest';
import './old_index.css';
import './index.css';

const PARAMS = window.location.search;

const DRAWER_WIDTH = 240;

const Main = styled('main', { shouldForwardProp: prop => prop !== 'open' })(({ theme, open }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: `-${DRAWER_WIDTH}px`,
  ...(open && {
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
    marginLeft: 0,
  }),
}));

const App = () => {
  const shopify = useAppBridge();
  const [storeLoading, setStoreLoading] = useState(true);
  const [onBoardingDetails, setOnBoardingDetails] = useState({});
  const [initError, setInitError] = useState(null);

  const getStore = async (retryCount = 0) => {
    try {
      setInitError(null);

      // Try to get a fresh token from Shopify App Bridge
      let initPath = '/react/initialize_store' + PARAMS;
      try {
        const freshToken = await shopify.idToken();
        // Add fresh token to the request if we got one
        if (freshToken) {
          const url = new URL(initPath, window.location.origin);
          url.searchParams.set('id_token', freshToken);
          initPath = url.pathname + url.search;
        }
      } catch (tokenError) {
        console.warn('Could not get fresh token from App Bridge, using URL params:', tokenError);
        // Fall back to using URL parameters as before
      }

      const storeDetails = await makeRequest({
        path: initPath,
        method: 'GET',
      });

      if (storeDetails.details) {
        setOnBoardingDetails(storeDetails.details);
        setStoreLoading(false);
        setInitError(null);
      } else {
        throw new Error('No store details received');
      }
    } catch (error) {
      console.error('Error initializing store:', error);

      // Check if this is a session expiration error
      if (error.message.includes('Session token expired') && retryCount < 2) {
        console.log(`Session token expired, retrying... (attempt ${retryCount + 1})`);
        // Wait a moment and retry
        setTimeout(() => {
          getStore(retryCount + 1);
        }, 1000);
        return;
      }

      setInitError(error.message);
      toast.error('Failed to initialize store. Please refresh the page.');
      setStoreLoading(false);
    }
  };

  useEffect(() => {
    getStore();
  }, []);

  if (storeLoading) {
    return (
      <div className="w-full flex-row-center" style={{ marginTop: '4rem' }}>
        <div className="content flex-col-center gap full">
          <Loader />
          {initError && (
            <div className="text-red-600 text-center mt-4">
              <p>Initialization failed: {initError}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Refresh Page
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <Box sx={{ display: 'flex', height: '100%' }}>
      <Main open={true} style={{ height: 'fit-content', backgroundColor: 'white' }}>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={true}
          newestOnTop={true}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable={false}
          pauseOnHover
          theme="light"
        />
        {!onBoardingDetails.confirmed_charge ? (
          <Onboarding onBoardingDetails={onBoardingDetails} />
        ) : (
          <Dashboard />
        )}
      </Main>
    </Box>
  );
};

export default App;
