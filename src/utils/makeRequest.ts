import { toast } from 'react-toastify';

// Set default baseURL if not defined in environment
let baseURL = process.env.REACT_APP_API_URL || '';

// If baseURL is still empty, try to use window.location.origin
if (!baseURL && typeof window !== 'undefined') {
  baseURL = window.location.origin;
}

// Extend the Window interface to include Shopify App Bridge properties
declare global {
  interface Window {
    app?: {
      idToken: () => Promise<string>;
    };
    shopify?: {
      idToken: () => Promise<string>;
    };
  }
}

// Helper function to detect session token expiration errors
const isSessionExpiredError = (statusCode: number, responseText: string, error?: any): boolean => {
  const expiredPatterns = [
    'Session token has expired',
    'Signature has expired',
    'ExpiredSignatureError',
    'Token has expired',
    'JWT expired',
  ];

  return (
    statusCode === 401 &&
    expiredPatterns.some(
      pattern => responseText.includes(pattern) || error?.message?.includes(pattern)
    )
  );
};

// Helper function to get fresh token from Shopify App Bridge
const getFreshToken = async (): Promise<string | null> => {
  try {
    // Try to get shopify app bridge instance
    if (typeof window !== 'undefined' && window.app && window.app.idToken) {
      return await window.app.idToken();
    }

    // Fallback: try to get from global shopify object
    if (typeof window !== 'undefined' && window.shopify && window.shopify.idToken) {
      return await window.shopify.idToken();
    }

    // If App Bridge is not available, return null
    return null;
  } catch (error) {
    console.warn('Failed to get fresh token from App Bridge:', error);
    return null;
  }
};

// Helper function to refresh token in URL path
const refreshTokenInPath = async (path: string): Promise<string> => {
  try {
    const freshToken = await getFreshToken();
    if (!freshToken) {
      return path; // Return original path if we can't get a fresh token
    }

    // Replace existing id_token parameter with fresh one
    const url = new URL(path, baseURL);
    url.searchParams.set('id_token', freshToken);

    // Return just the path + search params
    return url.pathname + url.search;
  } catch (error) {
    console.warn('Failed to refresh token in path:', error);
    return path;
  }
};

// Shopify App Bridge requires use of fetch to include built-in authorization headers
export const makeRequest = async ({
  path,
  method = 'GET',
  body,
  headers,
  _isRetry = false, // Internal flag to prevent infinite retries
}: {
  path: string;
  method: string;
  body: any;
  headers?: any;
  _isRetry?: boolean;
}): Promise<any> => {
  try {
    // Check for admin store emulation
    const emulatingStoreData = sessionStorage.getItem('emulating_store');
    const emulatingStore = emulatingStoreData ? JSON.parse(emulatingStoreData) : null;
    const emulatingStoreId = emulatingStore?.id;

    // Add emulation parameter if admin is emulating a store
    let finalPath = path;
    if (
      emulatingStoreId &&
      !path.includes('admin_check_status') &&
      !path.includes('admin_available_stores')
    ) {
      const separator = path.includes('?') ? '&' : '?';
      finalPath = `${path}${separator}emulate_store=${emulatingStoreId}`;
    }

    // Prepare headers with admin emulation indicator
    const finalHeaders = {
      ...headers,
      'X-Admin-Emulation': emulatingStoreId ? 'true' : 'false',
    };

    const response = await fetch(`${baseURL}${finalPath}`, {
      method,
      body,
      headers: finalHeaders,
    });

    if (!response.ok) {
      console.error(`Request failed with status: ${response.status}`);
      const text = await response.text();
      console.error(`Response body: ${text}`);

      // Handle specific session token expiration errors
      if (isSessionExpiredError(response.status, text)) {
        // If this is not a retry attempt, try to refresh the token
        if (!_isRetry && finalPath.includes('id_token=')) {
          console.log('Token expired, attempting to refresh...');
          const refreshedPath = await refreshTokenInPath(finalPath);

          if (refreshedPath !== finalPath) {
            console.log('Retrying request with fresh token...');
            // Retry the request with the fresh token
            return makeRequest({
              path: refreshedPath,
              method,
              body,
              headers,
              _isRetry: true, // Mark as retry to prevent infinite loop
            });
          }
        }

        // If retry failed or no token refresh available, show error and reload
        toast.error('Session expired. Please refresh the page and try again.');
        // Clear emulation state if it was active
        if (emulatingStoreId) {
          sessionStorage.removeItem('emulating_store');
          toast.info('Emulation mode has been reset due to session expiration.');
        }
        // Optionally refresh the page after a delay
        setTimeout(() => {
          window.location.reload();
        }, 2000);
        throw new Error('Session token expired');
      }

      // Handle admin emulation errors specifically
      if (response.status === 403 && text.includes('emulation')) {
        toast.error('Admin emulation session invalid. Returning to normal mode.');
        sessionStorage.removeItem('emulating_store');
        setTimeout(() => {
          window.location.reload();
        }, 1500);
        throw new Error('Admin emulation session invalid');
      }

      throw new Error(`Request failed with status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (e: any) {
    console.error('Error in makeRequest:', e);

    // Check for session expiration in network errors too
    if (isSessionExpiredError(0, '', e)) {
      // If this is not a retry attempt and we have a token in the path, try to refresh
      if (!_isRetry && path.includes('id_token=')) {
        console.log('Network error with token expiration, attempting to refresh...');
        const refreshedPath = await refreshTokenInPath(path);

        if (refreshedPath !== path) {
          console.log('Retrying request with fresh token...');
          // Retry the request with the fresh token
          return makeRequest({
            path: refreshedPath,
            method,
            body,
            headers,
            _isRetry: true, // Mark as retry to prevent infinite loop
          });
        }
      }

      toast.error('Session expired. Please refresh the page and try again.');
      // Clear emulation state if it was active
      const emulatingStoreData = sessionStorage.getItem('emulating_store');
      if (emulatingStoreData) {
        sessionStorage.removeItem('emulating_store');
        toast.info('Emulation mode has been reset due to session expiration.');
      }
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      throw new Error('Session token expired');
    }

    // Don't show duplicate toast for session expiration (already handled above)
    if (!e.message.includes('Session token expired')) {
      toast.error(`API Error: ${e.message || 'Unknown error'}`);
    }

    throw e; // Re-throw to allow handling in the component
  }
};
