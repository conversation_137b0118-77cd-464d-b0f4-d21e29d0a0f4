# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "f06a2bb326fc8db69d9376d09f0b0ae0"
name = "vandra_test"
handle = "vandra_test"
application_url = "https://vandra-merchant-staging-4ec574ae8b4e.herokuapp.com/dashboard"
embedded = true

[build]
dev_store_url = "vandra-test.myshopify.com"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_checkouts,read_discounts,read_orders,read_price_rules,read_products,read_themes,write_checkouts,write_discounts,write_orders,write_price_rules,write_products"

[auth]
redirect_urls = [ ]

[webhooks]
api_version = "2022-07"

  [[webhooks.subscriptions]]
  uri = "https://staging.vandra.ai/shop_delete_request"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://staging.vandra.ai/user_data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://staging.vandra.ai/user_delete_request"
  compliance_topics = [ "customers/redact" ]
