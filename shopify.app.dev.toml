# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "f06a2bb326fc8db69d9376d09f0b0ae0"
name = "vandra_test"
handle = "vandra_test"
application_url = "https://staging.vandra.ai/frontend/merchant_dashboard"
embedded = true

[build]
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
use_legacy_install_flow = true

[auth]
redirect_urls = [
  "https://staging.vandra.ai/app_install",
  "https://staging.vandra.ai/install_finalize",
  "https://staging.vandra.ai/app_uninstalled"
]

[webhooks]
api_version = "2022-07"

  [[webhooks.subscriptions]]
  uri = "https://staging.vandra.ai/shop_delete_request"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://staging.vandra.ai/user_data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://staging.vandra.ai/user_delete_request"
  compliance_topics = [ "customers/redact" ]

[pos]
embedded = false
