# Merchant App

React Frontend for the merchant Shopify app

# Environment

Node 20.x

# Specification

The frontend for this Shopify App utilizes React App Bridge V4. In V4, all requests made with Fetch include an authorization token that can be used to verify the request came from within the Shopify dashboard.

# Component Architecture

All UI components in this application are wrapped in the `src/components/UI/common` folder. These wrapper components encapsulate third-party UI libraries and provide a consistent interface for our application.

## Best Practices

- **Always use wrapped components** from `@common` instead of importing UI library components directly
- If you need a new component, first wrap it in the common folder before using it in your feature
- Never import UI library components directly in feature code
- This approach ensures we can easily switch UI libraries in the future without changing component usage throughout the app

# Install Flows

Use the toml files to deploy the application.

Once a store selects the app for install, they will be prompted to approve the necessary permissions within the Shopify dashboard.

If they accept, they will be redirected to this embeded app, where the flow will then check if they have subscribed to our payment plan.

If not - they will be redirected within Shopify to approve the subscription, and once approved they will be able to access the rest of the application.

# Test Environments

## Build One

- [ ] Create heroku app under the `vandra-merchant-app` pipeline
- [ ] deploy branch to heroku so we can create the resources based on the Procfile
  - [ ] `heroku git:remote --remote vandra-merchant-orange -a vandra-merchant-orange`
  - [ ] `git push vandra-merchant-orange <local-branch-name>:main --force`
- [ ] Setup the vars in doppler and sync environment with heroku app just created
  - `REACT_APP_CONFIG_API_KEY` -> shopify api key
  - `REACT_APP_EXTENSION_DEEP_LINK_ID` -> shopify theme extension id
  - others are self explanatory
- [ ] Push the code:
  - `git push vandra-merchant-orange <branch>:main --force`
- [ ] Grab the app url, you'll need it for the toml file on the other repo

## Deploy to one

`git push <heroku-repo> <branch>:main --force`
