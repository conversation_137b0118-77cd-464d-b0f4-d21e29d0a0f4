# Vandra

# Run the project
In order to make easy to run the project (after everything is setup) we suggest to install the extension `fabiospampinato.vscode-terminals`. This extension will allow to run multiple terminal commands in a single step.

To run it just press on the keyboard from VSCode CTRL/CMD + Shift + P and search for `Terminals: Run`.

Check this Loom video for more details: https://www.loom.com/share/7259af0d6d0a488fa99e481d2ccf3c84?sid=854b427b-9faa-477a-9d28-52f5259bc859

# A note about the data model and using the ORM effectively

## Partitioned tables


### References

Note that the partition key has to be part of the primary key. Take a look at the `session_intervention` table as an example

### Vandra Components

#### Deployment

When deploying after merge we need to rebuild the vandra components. We run `npm run build:components`. The output will now be in:

extensions/vandra-js/assets/vandra-components.css
extensions/vandra-js/assets/vandra-components.iife.js

### Migrations

Certain tables are structured to be partitioned (e.g. session_intervention), while Flask Migrate is usually pretty good about it, it falls short in three areas:

1. Auto-generating operations to attach the partitions to the parent table
2. Auto-generating operations to detach before dropping (as part of down revisions/rollbacks)
3. Only performing column add/delete operations in parent tables, not the partitioned ones
4. The foreign key constraints should be added only to the individual partions, and not the parent table, otherwise they take forever to create

For all these, we have to add the migration files code directly. Take a look at the `adding_partitions` or `new_partition` migrations as examples


# Testing the App

In order to support multiple developers working on features simultaneously we maintain a number of test store/app pairs we can use to test features in isolatation.

## Test Environments

### Vandra Green

- Test Store Url: https://vandra-green.myshopify.com/
- Test App Installed: Vandra Green
- Store password: vandra-green

### Vandra Blue

- Test Store Url: https://vandra-blue.myshopify.com/
- Test App Installed: Vandra Blue
- Store password: vandra-blue

### Vandra Red

- Test Store Url: https://vandra-red.myshopify.com/
- Test App Installed: Vandra Red
- Store password: vandra-red

### Vandra Orange

- Test Store Url: https://vandra-orange.myshopify.com/
- Test App Installed: Vandra Orange
- Store password: vandra-orange

## Deploying to Test Environment

There are three things that need to be deployed from each test environment

From this repo:

- The extension
- The backend

From the merchant-admin-app repo:

- The embedded shopify admin app, built in React


### How to deploy backend via heroku cli
Since multiple heroku apps and environments are tied to the repo, we'll be adding a lot of different remotes to our local git

You can check linked remotes like so:
`git remote -v`

And link new ones with:

`heroku git:remote --remote <git name for local use> -a <heroku app name>`

You can then push like so:

`git push <git name for local use> <local branch>:main --force`

### How to deploy extension via Shopify CLI

Run:

`npm run deploy -- --reset`

Make sure you choose the right app and store combination as prompted:

e.g. Vandra Green (app) and Vandra Green (store)

### Recapping: Deployment Steps

From your desired branch, run:

- `git push <git name for local use> <local branch>:main --force`
- `npm run deploy -- --reset`

### Database and ENV changes for lower environments

- You can grab the db connection details from Doppler
- If you need to make changes to the env configs, edit the corresponding env in Doppler (e.g. backend -> stg_green)

## Creating New Test Env

- [ ]  create new app in shopify via cli
    - [ ]  create via cli
    - [ ]  enable network access in shopify admin dash (api access section of app settings)
    - [ ]  keep an eye on the new .env file created, don’t delete it until you copy the relevant things to doppler
    - [ ]  doppler
        - [ ]  `shopify-app-env-file-backup`
            - [ ]  create new environment
            - [ ]  import the auto-generated `.env` file from shopify cli from the above step
        - [ ]  `backend` repo
            - [ ]  create new environment or clone it from existing one (e.g. `stg_env_name`)
            - [ ]  `SHOPIFY_API_KEY` →grab it from the .env file, same as the key from the admin panel in shopify
            - [ ]  `SHOPIFY_SHARED_SECRET` → grab it from shopify Admin , not in the .env file
            - [ ]  `SHOPIFY_VANDRA_THEME_EXTENSION_ID` from .env file
            - [ ]  `SHOPIFY_VANDRA_CHECKOUT_ID` from .env file
    
- [ ]  create heroku app, provision cheap redis and postgres
    - [ ]  create resources
    - [ ]  `APP_ENVIRONMENT` (E.g. `staging_green`)
    - [ ]  `DATABASE_URL` from heroku
    - [ ]  `REDIS_URL` from heroku
- [ ]  Publish the merchant app frontend repo (React) to a new heroku app
- [ ]  Run db migrations on heroku db
    - [ ]  `doppler run -- python -m flask db upgrade`
- [ ]  deploy branch to heroku so we can create the resources based on the Procfile
    - [ ]  `heroku git:remote --remote vandra-green -a vandra-green`
    - [ ]  `git push vandra-green <local-branch-name>:main --force`
    - [ ]  Turn on all the resources defined in the Procfile in the heroku admin dash (e.g. worker)
- [ ]  [optional] set custom domain for environment in AWS route 53
- [ ]  set relevant configs in doppler
    - [ ]  `backend`
        - [ ]  `API_BASE_URL`
- [ ]  add logic to extension code to point to backend url depending on the host
    - [ ]  search for `staging-blue` in the codebase and make add a similar block of code to support your new env
- [ ]  Deploy the app again
    - [ ]  build frontend merchant app
    - [ ]  deploy to heroku
    - [ ]  deploy extension code
- [ ]  Sync the `backend` doppler environment with the heroku app
- [ ] Deploy a merchant app via the `merchant-admin-app` repo (similar process)
- [ ]  edit the .toml file to mirror that of an existing staging environment (e.g. `staging-blue`), edit the secret key, etc

- [ ]  install into relevant test-store
- [ ]  seed db as appropriate
