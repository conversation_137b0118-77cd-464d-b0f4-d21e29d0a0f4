# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "9ccc65d023e77ba3270cd0f6cf64c5a1"
name = "Van<PERSON> Green"
handle = "vandra-green"
application_url = "https://vandra-merchant-green-45df2740e647.herokuapp.com/dashboard"
embedded = true

[build]
dev_store_url = "vandra-green.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_checkouts,read_discounts,read_orders,read_price_rules,read_products,read_themes,write_checkouts,write_discounts,write_orders,write_price_rules,write_products"

[auth]
redirect_urls = [ ]

[webhooks]
api_version = "2022-07"

  [[webhooks.subscriptions]]
  uri = "https://staging-green.vandra.ai/shop_delete_request"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://staging-green.vandra.ai/user_data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://staging-green.vandra.ai/user_delete_request"
  compliance_topics = [ "customers/redact" ]
