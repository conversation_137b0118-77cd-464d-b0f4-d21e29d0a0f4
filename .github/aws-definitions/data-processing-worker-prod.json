{"taskDefinitionArn": "arn:aws:ecs:us-east-2:************:task-definition/ecs-rq-worker-data-processing:1", "containerDefinitions": [{"name": "vandra-flask-app", "image": "************.dkr.ecr.us-east-2.amazonaws.com/vandra-flask-app", "cpu": 4096, "memory": 5120, "portMappings": [], "essential": true, "entryPoint": ["doppler", "run", "--"], "command": ["python", "task_queue_workers/async_data_processing_worker.py", "--action", "run-worker"], "environment": [], "secrets": [{"name": "DOPPLER_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-2:************:secret:prod/VandraApp/Doppler-11vhrr"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "workingDirectory": "/web", "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ecs-rq-worker-data-processing", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "ecs-rq-worker-data-processing", "taskRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.17"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "4096", "memory": "9216", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2025-02-26T18:23:05.942Z", "registeredBy": "arn:aws:sts::************:assumed-role/AWSReservedSSO_AWSAdministratorAccess_6f07f5e50ba2f835/rom<PERSON><PERSON>@1848ventures.com", "tags": [{"key": "accounting", "value": "production"}]}