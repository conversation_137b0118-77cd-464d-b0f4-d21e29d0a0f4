{"taskDefinitionArn": "arn:aws:ecs:us-east-2:************:task-definition/ecs-rq-worker-background-scoring:1", "containerDefinitions": [{"name": "vandra-flask-app", "image": "************.dkr.ecr.us-east-2.amazonaws.com/vandra-flask-app:31369103c311df75b2bd4ba9688eef94d906e6f6", "cpu": 0, "essential": true, "entryPoint": ["doppler", "run", "--"], "command": ["python", "model_utilities/run_prediction_utility.py", "--action", "run-worker", "--queue-name", "background-scoring-layer"], "environment": [], "environmentFiles": [], "secrets": [{"name": "DOPPLER_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-2:************:secret:prod/VandraApp/Doppler-11vhrr"}], "mountPoints": [], "volumesFrom": [], "workingDirectory": "/web", "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ecs-rq-worker-background-scoring", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "ecs-rq-worker-background-scoring", "taskRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.17"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "5120", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-12-19T23:01:37.064Z", "registeredBy": "arn:aws:sts::************:assumed-role/AWSReservedSSO_AWSAdministratorAccess_6f07f5e50ba2f835/rom<PERSON><PERSON>@1848ventures.com", "tags": [{"key": "accounting", "value": "production"}]}