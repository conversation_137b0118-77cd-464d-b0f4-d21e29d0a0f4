{"taskDefinitionArn": "arn:aws:ecs:us-east-2:311870172621:task-definition/flask-webapp-task:16", "containerDefinitions": [{"name": "vandra-flask-app", "image": "311870172621.dkr.ecr.us-east-2.amazonaws.com/vandra-flask-app:latest", "cpu": 4096, "memory": 8192, "memoryReservation": 4096, "portMappings": [{"name": "vandra-flask-app-80-tcp", "containerPort": 80, "hostPort": 80, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "entryPoint": ["doppler", "run", "--"], "command": ["gunicorn", "-w", "9", "--worker-class", "gevent", "-b", "0.0.0.0:80", "-t", "600", "--keep-alive", "3600", "app:app", "--log-file=-"], "environment": [], "secrets": [{"name": "DOPPLER_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-2:311870172621:secret:prod/VandraApp/Doppler-11vhrr"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "workingDirectory": "/web", "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/flask-webapp-task", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}, {"name": "aws-otel-collector", "image": "public.ecr.aws/aws-observability/aws-otel-collector:v0.40.0", "cpu": 0, "portMappings": [], "essential": true, "command": ["--config=/etc/ecs/ecs-cloudwatch-xray.yaml"], "environment": [], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/ecs-aws-otel-sidecar-collector", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "flask-webapp-task", "taskRoleArn": "arn:aws:iam::311870172621:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::311870172621:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 16, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.17"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.21"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}, {"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.extensible-ephemeral-storage"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "4096", "memory": "8192", "ephemeralStorage": {"sizeInGiB": 21}, "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-08-22T23:23:26.042Z", "registeredBy": "arn:aws:sts::311870172621:assumed-role/AWSReservedSSO_AWSAdministratorAccess_6f07f5e50ba2f835/rom<PERSON><PERSON>@1848ventures.com", "tags": []}