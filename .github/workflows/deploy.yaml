name: Deploy to CloudFront

on:
  push:
    branches:
      - main # Change this if deploying from another branch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: Build React App
        env:
          REACT_APP_CONFIG_API_KEY: ${{ secrets.REACT_APP_CONFIG_API_KEY }}
          REACT_APP_EXTENSION_DEEP_LINK_ID: ${{ secrets.REACT_APP_EXTENSION_DEEP_LINK_ID }}
          REACT_APP_ENV: ${{ secrets.REACT_APP_ENV }}
          REACT_APP_API_URL: ${{ secrets.REACT_APP_API_URL }}
          REACT_APP_VANDRA_COMPONENTS_URL: ${{ secrets.REACT_APP_VANDRA_COMPONENTS_URL }}
        run: |
          npm install
          CI="" npm run build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_V2_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_V2_SECRET_ACCESS_KEY }}
          aws-region: us-east-1 # Change to your AWS region

      - name: Sync files to S3
        run: aws s3 sync ./build s3://${{ secrets.AWS_S3_BUCKET_NAME }} --delete

      - name: Invalidate CloudFront Cache
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.AWS_CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*"
