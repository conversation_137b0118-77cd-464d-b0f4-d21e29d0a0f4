# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

# GitHub recommends pinning actions to a commit SHA.
# To get a newer version, you will need to update the SHA.
# You can also reference a tag or branch, but the action may change without warning.

name: Deploy to Amazon ECS

on:
  push:
    branches:
      - main

env:
  AWS_REGION: us-east-2                   # set this to your preferred AWS region, e.g. us-west-1
  ECR_REPOSITORY: vandra-flask-app           # set this to your Amazon ECR repository name
  ECS_CLUSTER: Flask-Core-App                 # set this to your Amazon ECS cluster name
                                               # file, e.g. .aws/task-definition.json
  CONTAINER_NAME: vandra-flask-app           # set this to the name of the container in the
                                               # containerDefinitions section of your task definition

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@0e613a0980cbf65ed5b322eb7a1e075d28913a83
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@62f4f872db3836360b72999f4b87f1ff13310f3a

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          # Build a docker container and
          # push it to ECR so that it can
          # be deployed to ECS.
          docker build -f Dockerfile-Backend -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Update Sync-orders Scheduled Job
        uses: appleboy/lambda-action@v0.2.0
        with:
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: ${{ env.AWS_REGION }}
          function_name: scheduled-jobs-sync-orders 
          image_uri: ${{ steps.build-image.outputs.image }}
    
      - name: Update Generate Usage Charges Scheduled Job
        uses: appleboy/lambda-action@v0.2.0
        with:
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: ${{ env.AWS_REGION }}
          function_name: generate-usage-charges 
          image_uri: ${{ steps.build-image.outputs.image }}  

      - name: Update Calibrate Intent Thresholds Scheduled Job
        uses: appleboy/lambda-action@v0.2.0
        with:
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: ${{ env.AWS_REGION }}
          function_name: calibrate-intent-thresholds
          image_uri: ${{ steps.build-image.outputs.image }}    

      - name: Update Cache Bestsellers Scheduled Job
        uses: appleboy/lambda-action@v0.2.0
        with:
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: ${{ env.AWS_REGION }}
          function_name: cache-bestsellers
          image_uri: ${{ steps.build-image.outputs.image }}
  
      - name: Task Definition Flask
        id: flask-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@c804dfbdd57f713b6c079302a4c01db7017a36fc
        with:
          task-definition: .github/aws-definitions/flask-app-prod.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ steps.build-image.outputs.image }}
          environment-variables: |
            APP_NAME=Flask-App
      

      - name: Task Definition Background Scoring Worker
        id: background-scoring-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@c804dfbdd57f713b6c079302a4c01db7017a36fc
        with:
          task-definition: .github/aws-definitions/background-worker-scoring-prod.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ steps.build-image.outputs.image }}
          environment-variables: |
            APP_NAME=Task-Worker-Background-Scoring

      - name: Task Definition Live Scoring Worker
        id: live-scoring-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@c804dfbdd57f713b6c079302a4c01db7017a36fc
        with:
          task-definition: .github/aws-definitions/live-worker-scoring-prod.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ steps.build-image.outputs.image }}
          environment-variables: |
            APP_NAME=Task-Worker-Live-Scoring

      - name: Task Definition Data Processing Worker
        id: data-processing-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@c804dfbdd57f713b6c079302a4c01db7017a36fc
        with:
          task-definition: .github/aws-definitions/data-processing-worker-prod.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ steps.build-image.outputs.image }}
          environment-variables: |
            APP_NAME=Task-Worker-Data-Processing
      
      
      - name: Deploy Background Scoring Worker ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@df9643053eda01f169e64a0e60233aacca83799a
        with:
          task-definition: ${{ steps.background-scoring-task-def.outputs.task-definition }}
          service: ecs-rq-worker-background-scoring-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
      
      - name: Deploy Live Scoring Worker ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@df9643053eda01f169e64a0e60233aacca83799a
        with:
          task-definition: ${{ steps.live-scoring-task-def.outputs.task-definition }}
          service: ecs-rq-worker-live-scoring-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Deploy Data Processing Worker ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@df9643053eda01f169e64a0e60233aacca83799a
        with:
          task-definition: ${{ steps.data-processing-task-def.outputs.task-definition }}
          service: ecs-rq-worker-data-processing-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Deploy Flask ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@df9643053eda01f169e64a0e60233aacca83799a
        with:
          task-definition: ${{ steps.flask-task-def.outputs.task-definition }}
          service: ecs-flask-webapp-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
      