# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "0571d25d771722163b60e5ed574c6087"
name = "vandra-local-dev"
handle = "vandra-local-dev"
application_url = "https://vandra-merchant-app-a118de1e9dbb.herokuapp.com/dashboard"
embedded = true

[build]
include_config_on_deploy = true
dev_store_url = "vandra-blue.myshopify.com"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_products,write_products,read_orders,write_orders,read_price_rules,write_price_rules,read_discounts,write_discounts,read_themes,read_checkouts,write_checkouts"

[auth]
redirect_urls = [ ]

[webhooks]
api_version = "2022-07"

  [[webhooks.subscriptions]]
  uri = "https://cab679fcc131.ngrok.app/shop_delete_request"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://cab679fcc131.ngrok.app/user_data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://cab679fcc131.ngrok.app/user_delete_request"
  compliance_topics = [ "customers/redact" ]
