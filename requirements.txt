#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile requirements.in
#
alembic==1.13.1
    # via flask-migrate
anyio==4.4.0
    # via httpx
argparse==1.4.0
    # via -r requirements.in
asn1crypto==1.5.1
    # via
    #   -r requirements.in
    #   oscrypto
async-timeout==5.0.1
    # via redis
awslambdaric==2.2.1
    # via -r requirements.in
bcrypt==4.0.1
    # via -r requirements.in
blinker==1.8.2
    # via sentry-sdk
boto3==1.34.106
    # via -r requirements.in
botocore==1.34.106
    # via
    #   boto3
    #   s3transfer
cachelib==0.9.0
    # via -r requirements.in
certifi==2022.9.24
    # via
    #   -r requirements.in
    #   httpcore
    #   httpx
    #   requests
    #   sentry-sdk
cffi==1.15.1
    # via
    #   -r requirements.in
    #   cryptography
charset-normalizer==2.1.1
    # via
    #   -r requirements.in
    #   requests
click==8.1.3
    # via
    #   -r requirements.in
    #   flask
    #   rq
cryptography==38.0.3
    # via
    #   -r requirements.in
    #   pyopenssl
datatable==1.1.0
    # via -r requirements.in
exceptiongroup==1.2.2
    # via anyio
ffmpeg-python==0.2.0
    # via -r requirements.in
filelock==3.8.0
    # via -r requirements.in
flask==2.2.2
    # via
    #   -r requirements.in
    #   flask-cors
    #   flask-migrate
    #   flask-sqlalchemy
    #   sentry-sdk
flask-cors==4.0.1
    # via -r requirements.in
flask-migrate==4.0.7
    # via -r requirements.in
flask-sqlalchemy==3.0.3
    # via
    #   -r requirements.in
    #   flask-migrate
future==1.0.0
    # via ffmpeg-python
gevent==24.2.1
    # via -r requirements.in
greenlet==3.0.3
    # via gevent
gunicorn==22.0.0
    # via -r requirements.in
h11==0.14.0
    # via httpcore
httpcore==1.0.5
    # via httpx
httpx==0.27.0
    # via trycourier
idna==3.4
    # via
    #   -r requirements.in
    #   anyio
    #   httpx
    #   requests
itsdangerous==2.1.2
    # via
    #   -r requirements.in
    #   flask
jinja2==3.1.2
    # via
    #   -r requirements.in
    #   flask
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
joblib==1.5.1
    # via scikit-learn
mako==1.3.5
    # via alembic
markupsafe==2.1.1
    # via
    #   -r requirements.in
    #   jinja2
    #   mako
    #   sentry-sdk
    #   werkzeug
numpy==1.26.4
    # via
    #   -r requirements.in
    #   pandas
    #   scikit-learn
    #   scipy
oscrypto==1.3.0
    # via -r requirements.in
packaging==24.1
    # via gunicorn
pandas==2.2.0
    # via -r requirements.in
psycopg2-binary==2.9.9
    # via -r requirements.in
pyactiveresource==2.2.2
    # via
    #   -r requirements.in
    #   shopifyapi
pycparser==2.21
    # via
    #   -r requirements.in
    #   cffi
pycryptodomex==3.15.0
    # via -r requirements.in
pydantic==1.10.17
    # via trycourier
pyjwt==2.6.0
    # via
    #   -r requirements.in
    #   shopifyapi
pyopenssl==22.1.0
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   pandas
python-dotenv==1.0.1
    # via -r requirements.in
pytz==2022.6
    # via
    #   -r requirements.in
    #   pandas
pyyaml==6.0
    # via
    #   -r requirements.in
    #   shopifyapi
redis==5.0.6
    # via rq
requests==2.28.1
    # via -r requirements.in
rq==1.16.2
    # via -r requirements.in
s3transfer==0.10.1
    # via boto3
scikit-learn==1.6.1
    # via -r requirements.in
scipy==1.15.3
    # via scikit-learn
sentry-sdk[flask]==2.3.1
    # via -r requirements.in
shopifyapi==12.4.0
    # via -r requirements.in
simplejson==3.19.3
    # via awslambdaric
six==1.16.0
    # via
    #   -r requirements.in
    #   pyactiveresource
    #   python-dateutil
    #   shopifyapi
sniffio==1.3.1
    # via
    #   anyio
    #   httpx
sqlalchemy==2.0.20
    # via
    #   -r requirements.in
    #   alembic
    #   flask-sqlalchemy
    #   sqlalchemy-json
sqlalchemy-json==0.7.0
    # via -r requirements.in
threadpoolctl==3.6.0
    # via scikit-learn
trycourier==6.1.0
    # via -r requirements.in
typing-extensions==4.4.0
    # via
    #   -r requirements.in
    #   alembic
    #   anyio
    #   pydantic
    #   sqlalchemy
    #   trycourier
tzdata==2024.1
    # via pandas
ua-parser==0.18.0
    # via user-agents
urllib3==1.26.12
    # via
    #   -r requirements.in
    #   botocore
    #   requests
    #   sentry-sdk
user-agents==2.2.0
    # via -r requirements.in
uwsgi==2.0.21
    # via -r requirements.in
werkzeug==2.2.2
    # via
    #   -r requirements.in
    #   flask
zope-event==5.0
    # via gevent
zope-interface==7.0.1
    # via gevent

# The following packages are considered to be unsafe in a requirements file:
# setuptools
