# syntax=docker/dockerfile:1

FROM python:3.10-slim-buster

RUN apt-get update && apt-get install -y \
    python3 python3-dev gcc libpq-dev

# Install Doppler CLI
RUN apt-get update && apt-get install -y apt-transport-https ca-certificates curl gnupg && \
    curl -sLf --retry 3 --tlsv1.2 --proto "=https" 'https://packages.doppler.com/public/cli/gpg.DE2A7741A397C129.key' | gpg --dearmor -o /usr/share/keyrings/doppler-archive-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/doppler-archive-keyring.gpg] https://packages.doppler.com/public/cli/deb/debian any-version main" | tee /etc/apt/sources.list.d/doppler-cli.list && \
    apt-get update && \
    apt-get -y install doppler
    
RUN pip install psycopg2

RUN pip install --upgrade pip


COPY dsreqs.txt dsreqs.txt
COPY ./data_science/prepare_data.py ./data_science/prepare_data.py
COPY ./data_science/ds_constants.py ./data_science/ds_constants.py
COPY ./data_science/ds_helpers.py ./data_science/ds_helpers.py
COPY ./data_science/train_model_sm.py ./data_science/train_model_sm.py
COPY ./data_science/models/2024122001.py ./data_science/models/2024122001.py
COPY ./data_science/isotonic_postprocess.py ./data_science/isotonic_postprocess.py
COPY ./web/db.py ./web/db.py
COPY ./web/app.py ./web/app.py
COPY ./web/config.py ./web/config.py
COPY ./web/constants.py ./web/constants.py
COPY ./web/utils.py ./web/utils.py
COPY ./web/helpers.py ./web/helpers.py
COPY ./web/database_models ./web/database_models
COPY ./web/blueprints ./web/blueprints

RUN pip3 install -r dsreqs.txt

#read this https://docs.doppler.com/docs/dockerfile
ENTRYPOINT ["doppler", "run", "--"]
CMD ["python3", "data_science/train_model_sm.py"]