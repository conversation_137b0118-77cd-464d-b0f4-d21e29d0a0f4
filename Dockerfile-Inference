# syntax=docker/dockerfile:1

FROM python:3.10-slim-buster

RUN apt-get update && apt-get install -y \
    python3 python3-dev gcc libpq-dev make g++

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y git
    
RUN apt-get update && apt-get install -y apt-transport-https ca-certificates curl gnupg && \
    curl -sLf --retry 3 --tlsv1.2 --proto "=https" 'https://packages.doppler.com/public/cli/gpg.DE2A7741A397C129.key' | gpg --dearmor -o /usr/share/keyrings/doppler-archive-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/doppler-archive-keyring.gpg] https://packages.doppler.com/public/cli/deb/debian any-version main" | tee /etc/apt/sources.list.d/doppler-cli.list && \
    apt-get update && \
    apt-get -y install doppler

RUN pip install psycopg2

RUN pip install --upgrade pip


COPY requirements.txt requirements.txt
COPY ./web ./web
COPY ./data_science ./data_science

RUN pip install -r requirements.txt

WORKDIR /web
#read this https://docs.doppler.com/docs/dockerfile
ENTRYPOINT ["doppler", "run", "--"]
CMD ["python", "model_utilities/run_prediction_utility.py", "--action", "run-worker", "--queue-name", "live"]
#CMD ["/bin/bash"]