from flask import Blueprint
from flask_cors import CORS

from constants import (
    INTERVENTION_EVENT_TYPE_CTA_CLICKED, 
    INTERVENTION_EVENT_TYPE_DISMISSED, 
    INTERVENTION_EVENT_TYPE_MISSING_ASSET, 
    INTERVENTION_EVENT_TYPE_SHOWN,
    INTERVENTION_EVENT_TYPE_EXPANDED,
    INTERVENTION_EVENT_TYPE_MINIMIZED,
    INTERVENTION_EVENT_TYPE_PLAYED,
    INTERVENTION_EVENT_TYPE_PAUSED,
    INTERVENTION_EVENT_TYPE_UNMUTED,
    INTERVENTION_EVENT_TYPE_MUTED,
    INTERVENTION_STATUS_ACTIONED, 
    INTERVENTION_STATUS_DISMISSED, 
    INTERVENTION_STATUS_MISSING_ASSET, 
    INTERVENTION_STATUS_SHOWN,
    INTERVENTION_STATUS_ENGAGED
)

base_nudge_api = Blueprint("base_nudge", __name__)

cors = CORS(base_nudge_api)

@base_nudge_api.route("/types", methods=["GET"])
def get_store_intervention_association_types():
    from modules.base_nudge import get_store_intervention_types
    return get_store_intervention_types()


@base_nudge_api.route("/parameters", methods=["GET"])
def get_store_intervention_association_parameters():
    from modules.base_nudge import get_store_intervention_parameters
    return get_store_intervention_parameters()

@base_nudge_api.route("/parameters", methods=["POST"]) #for backward compatibility, can remove after 06/25
@base_nudge_api.route("/session/parameters", methods=["POST"])
def update_session_intervention_parameters():
    from modules.base_nudge import update_parameters_handler
    return update_parameters_handler()

@base_nudge_api.route("/holdout", methods=["POST"])
def update_session_intervention_holdout():
    from modules.base_nudge import update_session_intervention_holdout_handler
    return update_session_intervention_holdout_handler()

@base_nudge_api.route("/get_or_create", methods=["POST"])
def record_get_or_create_action():
    from modules.base_nudge import get_or_create_intervention
    return get_or_create_intervention()

@base_nudge_api.route("/create", methods=["POST"])
def record_create_action():
    from modules.base_nudge import create_intervention
    return create_intervention()

@base_nudge_api.route("/record/<action>", methods=["POST"])
def record_intervention_action_unified(action):
    """
    A single endpoint that handles cta, dismissed, and shown actions.
    The `action` parameter in the URL should be one of: "cta", "dismissed", or "shown".
    """
    from flask import request
    import json
    
    mapping = {
        "cta": (INTERVENTION_STATUS_ACTIONED, INTERVENTION_EVENT_TYPE_CTA_CLICKED),
        "dismissed": (INTERVENTION_STATUS_DISMISSED, INTERVENTION_EVENT_TYPE_DISMISSED),
        "shown": (INTERVENTION_STATUS_SHOWN, INTERVENTION_EVENT_TYPE_SHOWN),
        "missing_asset": (INTERVENTION_STATUS_MISSING_ASSET, INTERVENTION_EVENT_TYPE_MISSING_ASSET),
        "expanded": (INTERVENTION_STATUS_ENGAGED, INTERVENTION_EVENT_TYPE_EXPANDED),
        "minimized": (INTERVENTION_STATUS_ENGAGED, INTERVENTION_EVENT_TYPE_MINIMIZED),
        "played": (INTERVENTION_STATUS_ENGAGED, INTERVENTION_EVENT_TYPE_PLAYED),
        "paused": (INTERVENTION_STATUS_ENGAGED, INTERVENTION_EVENT_TYPE_PAUSED),
        "unmuted": (INTERVENTION_STATUS_ENGAGED, INTERVENTION_EVENT_TYPE_UNMUTED),
        "muted": (INTERVENTION_STATUS_ENGAGED, INTERVENTION_EVENT_TYPE_MUTED),
    }
    
    action_values = mapping.get(action)
    if action_values is None:
        return {"error": "Invalid action supplied."}, 400

    status, event_type = action_values
    
    # For automatic events (like autoplay), we don't want to mark them as user engagement
    # We'll check the metadata for is_automatic flag
    try:
        metadata_str = request.form.get("metadata", "{}")
        metadata = json.loads(metadata_str)
        if metadata.get("is_automatic", False) and status == INTERVENTION_STATUS_ENGAGED:
            # Use SHOWN status instead of ENGAGED for automatic events
            status = INTERVENTION_STATUS_SHOWN
    except Exception:
        # If there's any error processing the metadata, proceed with the original status
        pass
        
    from modules.base_nudge import record_intervention_action
    return record_intervention_action(status, event_type)

@base_nudge_api.route("/assets", methods=["GET"])
def get_asset_route():
    from modules.base_nudge import get_assets
    return get_assets()