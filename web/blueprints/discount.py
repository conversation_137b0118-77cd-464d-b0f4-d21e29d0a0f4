import time
from flask import Blueprint, request, jsonify
from db import user_session
from utils import get_generic_logger

logger = get_generic_logger(__name__)
discount_blueprint = Blueprint('discount', __name__)

@discount_blueprint.route('/discount/session/<session_cookie>/record_discount_code', methods=['POST'])
def record_discount_code(session_cookie):
    """
    Records or updates a discount code application for a user session.
    
    If the discount code hasn't been seen before for this session, creates a new record.
    If it has been seen, updates the last_detected_in field.
    """
    from modules.discount_code_use_cases import handle_record_discount_code

    discount_code = request.form.get('discount_code')
    page_view_id = request.form.get('page_view_id')
    detection_source = request.form.get('detection_source', 'cookie')

    success, error, status_code = handle_record_discount_code(
        session_cookie,
        discount_code,
        page_view_id,
        detection_source
    )

    if not success:
        return jsonify({"error": error}), status_code

    return jsonify({"success": True})

@discount_blueprint.route('/discount/session/<session_cookie>/check_competitive_discount', methods=['POST'])
def check_competitive_discount(session_cookie):
    """Check if there's a competitive discount active for the session"""
    from modules.discount_code_use_cases import has_competitive_active_discount
    try:
        if not session_cookie:
            return jsonify({
                'success': False,
                'error': 'Missing session cookie'
            }), 400

        user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
        if not user_session_record:
            return jsonify({
                'success': False,
                'error': 'Session not found'
            }), 404

        has_competitive = has_competitive_active_discount(user_session_record)
        
        return jsonify({
            'success': True,
            'has_competitive_discount': has_competitive
        })

    except Exception as e:
        logger.exception(f"Error checking competitive discount: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

