"""
API to interact with email services (via courier)
"""
from config import COURIER_API_TOKEN, SKIP_NOTIFICATIONS
from courier.client import Courier
import courier
from courier.core import ApiError
import requests
from utils import logged
#templates from Courier
EMAIL_TEMPLATE_USAGE_CHARGE = "usage_charge"

TEMPLATE_TO_ID_MAP = {
    EMAIL_TEMPLATE_USAGE_CHARGE: "ENF8Z4TVCKMQP9JV349PA5JCRPDR"
}

@logged
class Notifier:
    """
    this is meant to replace the `send_mail` function in the helpers module over time
    """
    client = Courier(authorization_token=COURIER_API_TOKEN)
    skip_notifications = SKIP_NOTIFICATIONS

    @classmethod
    def send_raw(cls, subject, body, to, attachment=None):
        """Send communications via courier"""
        if cls.skip_notifications:
            return None
        try:
            # TODO: run async
            attachments=[attachment] if attachment else []
            resp = cls.client.send(
                message=courier.ContentMessage(
                    to=[courier.UserRecipient(email=x) for x in to],
                    content=courier.ElementalContentSugar(
                        title=subject,
                        body=body,
                    ),

                    routing=courier.Routing(method="all", channels=["email"]),
                    providers = {
                        "aws-ses": {
                            "override": {
                                "attachments": attachments,
                                 "Destination": {
                                    "CcAddresses": ["<EMAIL>"],
                                    "ToAddresses": [x for x in to],
                                },
                                
                          }
                        }
                    }
                    ),
                    
                    
                )
            
            
            
            
            return resp
        except requests.exceptions.HTTPError as err:
            cls.logger.error(err.response.text)
            return None
        except ApiError as err:
            cls.logger.error(err)
            return None

    @classmethod
    def send_from_template(cls, template_name, to, data, attachment=None):
        """Send communications via courier"""
        if cls.skip_notifications:
            return None
        try:
            template_id = TEMPLATE_TO_ID_MAP[template_name]
            # TODO: run async
            attachments=[attachment] if attachment else []
            resp = cls.client.send(
                message=courier.TemplateMessage(
                    template=template_id,
                    to=[courier.UserRecipient(email=x["email"]) for x in to],
                    data=data,
                    providers = {
                        "aws-ses": {
                            "override": {
                                "attachments": attachments
                            }
                        }
                    }   
                ),
            )
            return resp
        except requests.exceptions.HTTPError as err:
            cls.logger.error(err.response.text)
            return None
        except ApiError as err:
            cls.logger.error(err)
            return None
