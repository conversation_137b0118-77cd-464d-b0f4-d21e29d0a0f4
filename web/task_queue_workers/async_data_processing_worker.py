import os
import sys
import inspect
import tempfile
import boto3
import ffmpeg
from urllib.parse import urlparse
import signal
import resource

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

from rq import Worker, SimpleWorker
from redis.exceptions import ConnectionError as RedisConnectionError
from task_queue import (
    ASYNC_DATA_PROCESSING_QUEUE,
    REDIS_CONNECTION
)
import argparse
from utils import get_generic_logger
import config

logger = get_generic_logger(__name__)

def handle_job_failure(job, exc_type, exc_value, traceback):  # skipcq: PYL-W0613
    if exc_type is RedisConnectionError:
        logger.exception(f"Connection error for job {job.id}")
    return True

def validate_video_file(file_path):
    """Validate if the video file is readable and not corrupted."""
    try:
        probe = ffmpeg.probe(file_path)
        if not probe or 'format' not in probe:
            return False, "Invalid video format"
        if 'duration' not in probe['format']:
            return False, "Cannot determine video duration"
        return True, probe
    except ffmpeg.Error as e:
        error_message = e.stderr.decode() if hasattr(e, 'stderr') and e.stderr else str(e)
        return False, f"Error probing video: {error_message}"
    except Exception as e:
        return False, f"Unexpected error validating video: {str(e)}"

def process_social_video_asset(asset_url, asset_uuid):
    """
    Downloads a video from S3, converts it to mp4, 720p, 1MB per 10 seconds using ffmpeg-python (two-pass), and uploads it back to S3.
    """
    import math
    
    logger.info(f"Starting video processing for asset {asset_uuid}: {asset_url}")
    
    try:
        s3 = boto3.client(
            "s3",
            aws_access_key_id=config.SES_ACCESS_KEY,
            aws_secret_access_key=config.SES_SECRET,
            region_name="eu-north-1"
        )
        
        parsed = urlparse(asset_url)
        if not parsed.netloc or not parsed.path:
            logger.error(f"Invalid URL format for asset {asset_uuid}: {asset_url}")
            return
            
        bucket = parsed.netloc.split('.')[0]
        key = parsed.path.lstrip('/')
        
        if not bucket or not key:
            logger.error(f"Could not extract bucket or key from URL for asset {asset_uuid}: {asset_url}")
            return

        with tempfile.TemporaryDirectory() as tmpdir:
            local_input = f"{tmpdir}/input.mp4"
            local_output = f"{tmpdir}/output.mp4"
            pass_log_file = f"{tmpdir}/ffmpeg2pass"  # Path for the pass log file
            
            # Download from S3
            try:
                logger.info(f"Downloading video from S3 bucket: {bucket}, key: {key}")
                s3.download_file(bucket, key, local_input)
                logger.info(f"Downloaded file exists: {os.path.exists(local_input)}, size: {os.path.getsize(local_input) if os.path.exists(local_input) else 'N/A'} bytes for asset {asset_uuid}")
            except Exception as e:
                logger.error(f"Failed to download video from S3: {e}")
                return
                
            # Check if file exists and has content
            if not os.path.exists(local_input) or os.path.getsize(local_input) == 0:
                logger.error(f"Downloaded file is empty or does not exist for asset {asset_uuid}")
                return
                
            # Log original file size
            try:
                original_size = os.path.getsize(local_input)
                logger.info(f"Original video size for asset {asset_uuid}: {original_size} bytes")
            except Exception as e:
                logger.warning(f"Could not determine original video size for asset {asset_uuid}: {e}")
                original_size = None
                
            # Validate the video file
            valid, result = validate_video_file(local_input)
            logger.info(f"ffmpeg probe result for asset {asset_uuid}: {result if valid else 'Invalid file'}")
            if not valid:
                logger.error(f"Invalid video file for asset {asset_uuid}: {result}")
                return
                
            probe = result
            duration = float(probe['format']['duration'])
            
            # Calculate target bitrate for 1MB per 10 seconds
            # 1MB = 8,388,608 bits (1 * 1024 * 1024 * 8)
            # Target size = (duration in seconds / 10) * 1MB
            target_size_bits = (duration / 10) * 1024 * 1024 * 8
            audio_bitrate = 128000  # 128k in bits
            video_bitrate = int((target_size_bits / duration) - audio_bitrate)
            video_bitrate_k = max(video_bitrate // 1000, 100)  # at least 100k
            
            logger.info(f"Video duration: {duration:.2f} seconds")
            logger.info(f"Target size: {(target_size_bits / 8 / 1024 / 1024):.2f}MB based on 1MB per 10 seconds")
            logger.info(f"Calculated video bitrate for asset {asset_uuid}: {video_bitrate_k}k")
            
            # First pass - with timeout handling
            try:
                logger.info(f"Running ffmpeg first pass for asset {asset_uuid} with bitrate {video_bitrate_k}k, input: {local_input}, passlogfile: {pass_log_file}")
                # Set a reasonable timeout for the ffmpeg process
                (
                    ffmpeg
                    .input(local_input)
                    .output(
                        os.devnull,
                        vcodec='libx264',
                        video_bitrate=f'{video_bitrate_k}k',
                        vf='scale=-2:720',
                        preset='medium',  # Using medium instead of slow for better performance
                        maxrate=f'{math.ceil(video_bitrate_k * 1.5)}k',  # Allow some flexibility for complex scenes
                        bufsize='2M',
                        an=None,
                        f='mp4',
                        loglevel='info',  # Change from quiet to info to get more diagnostic information
                        passlogfile=pass_log_file,  # Specify the pass log file location
                        **{'pass': 1}
                    )
                    .global_args('-y')  # Force overwrite
                    .overwrite_output()
                    .run(quiet=False, capture_stdout=True, capture_stderr=True)
                )
                logger.info(f"First pass completed for asset {asset_uuid}, pass log file exists: {os.path.exists(f'{pass_log_file}-0.log')}")
            except ffmpeg.Error as e:
                stderr = e.stderr.decode() if hasattr(e, 'stderr') and e.stderr else "No error details"
                logger.error(f"First pass ffmpeg-python failed for asset {asset_uuid}: {stderr}")
                logger.error(f"ffmpeg command: {e.cmd if hasattr(e, 'cmd') else 'N/A'}")
                return
            except Exception as e:
                logger.error(f"Unexpected error in first pass for asset {asset_uuid}: {str(e)}")
                return
                
            # Check if pass log file was created
            if not os.path.exists(f"{pass_log_file}-0.log"):
                logger.error(f"Pass log file not created for asset {asset_uuid}, aborting second pass")
                return
                
            # Second pass
            try:
                logger.info(f"Running ffmpeg second pass for asset {asset_uuid} with bitrate {video_bitrate_k}k, input: {local_input}, output: {local_output}, passlogfile: {pass_log_file}")
                (
                    ffmpeg
                    .input(local_input)
                    .output(
                        local_output,
                        vcodec='libx264',
                        video_bitrate=f'{video_bitrate_k}k',
                        vf='scale=-2:720',
                        preset='medium',  # Using medium instead of slow for better performance
                        maxrate=f'{math.ceil(video_bitrate_k * 1.5)}k',
                        bufsize='2M',
                        acodec='aac',
                        audio_bitrate='128k',
                        movflags='+faststart',
                        loglevel='info',  # Change from quiet to info
                        passlogfile=pass_log_file,
                        **{'pass': 2}
                    )
                    .global_args('-y')  # Force overwrite
                    .overwrite_output()
                    .run(quiet=False, capture_stdout=True, capture_stderr=True)
                )
                logger.info(f"Second pass completed for asset {asset_uuid}, output file exists: {os.path.exists(local_output)}, size: {os.path.getsize(local_output) if os.path.exists(local_output) else 'N/A'} bytes")
            except ffmpeg.Error as e:
                stderr = e.stderr.decode() if hasattr(e, 'stderr') and e.stderr else "No error details"
                logger.error(f"Second pass ffmpeg-python failed for asset {asset_uuid}: {stderr}")
                logger.error(f"ffmpeg command: {e.cmd if hasattr(e, 'cmd') else 'N/A'}")
                return
            except Exception as e:
                logger.error(f"Unexpected error in second pass for asset {asset_uuid}: {str(e)}")
                return
                
            # Check if output file was created and is not empty
            if not os.path.exists(local_output) or os.path.getsize(local_output) == 0:
                logger.error(f"Output file is empty or does not exist for asset {asset_uuid}")
                return
                
            # Check output file size
            try:
                out_size = os.path.getsize(local_output)
                target_size = (duration / 10) * 1024 * 1024  # Target size in bytes
                logger.info(f"Compressed video size for asset {asset_uuid}: {out_size} bytes")
                logger.info(f"Target size was: {target_size:.0f} bytes")
                if original_size:
                    ratio = out_size / original_size if original_size > 0 else 0
                    logger.info(f"Compression ratio for asset {asset_uuid}: {ratio:.2f}")
                if out_size > target_size * 1.1:  # Allow 10% over target
                    logger.warning(f"Output video exceeds target size by more than 10%: {out_size} bytes vs target {target_size:.0f} bytes")
            except Exception as e:
                logger.warning(f"Could not determine compressed video size for asset {asset_uuid}: {e}")
                out_size = None
                
            # Upload back to S3 (overwrite original)
            try:
                logger.info(f"Uploading processed video back to S3 for asset {asset_uuid}")
                s3.upload_file(local_output, bucket, key, ExtraArgs={"ContentType": "video/mp4"})
            except Exception as e:
                logger.error(f"Failed to upload processed video to S3 for asset {asset_uuid}: {e}")
                return
                
            logger.info(f"Successfully processed and uploaded video for asset {asset_uuid}")
            
    except Exception as e:
        logger.error(f"Unexpected error processing video asset {asset_uuid}: {str(e)}")

if __name__ == "__main__":
    from app import app
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Define what you want to do.",
        choices=["run-worker"],
        required=True,
    )

    args = parser.parse_args()
    if args.action == "run-worker":
        with app.app_context():
            queues = [ASYNC_DATA_PROCESSING_QUEUE]
            # Setup worker based on config
            if config.PYTHON_RQ_SIMPLE_WORKER:
                worker_type = "SimpleWorker"
                worker = SimpleWorker(
                    queues=queues,
                    connection=REDIS_CONNECTION,
                    exception_handlers=[handle_job_failure],
                )
            else:
                worker_type = "Worker"
                worker = Worker(
                    queues=queues,
                    connection=REDIS_CONNECTION,
                    exception_handlers=[handle_job_failure],
                )
            logger.info(
                f"{worker_type} for async data processing is now RUNNING. "
                f"Queue: {ASYNC_DATA_PROCESSING_QUEUE.name}, "
                f"Environment: {getattr(config, 'ENV', 'unknown')}"
            )
            # Run worker
            worker.work(with_scheduler=True) 