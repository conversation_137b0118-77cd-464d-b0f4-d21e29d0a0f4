from rq import Queue
from redis import Redis
from config import REDIS_URL, REDIS_SSL
from urllib.parse import urlparse
import redis

if not REDIS_SSL:
    # Use minimal connection for local/dev
    REDIS_CONNECTION = redis.from_url(REDIS_URL)
else:
    _URL = urlparse(REDIS_URL)
    REDIS_CONNECTION = Redis(
        host=_URL.hostname,
        port=_URL.port,
        password=_URL.password,
        health_check_interval=10,
        socket_connect_timeout=5,
        retry_on_timeout=True,
        socket_keepalive=True,
        ssl=REDIS_SSL,
        ssl_cert_reqs=None,
    )

LIVE_SCORING_LAYER_QUEUE = Queue(
    "live_scoring_layer_queue", connection=REDIS_CONNECTION, default_timeout=120
)

BACKGROUND_SCORING_LAYER_QUEUE = Queue(
    "background_scoring_layer_queue", connection=REDIS_CONNECTION, default_timeout=120
)
ASYNC_DATA_PROCESSING_QUEUE = Queue(
    "async_data_processing_queue", connection=REDIS_CONNECTION, default_timeout=120
)

LOADED_MODELS = {}