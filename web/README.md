# Secret Management

We use [<PERSON><PERSON><PERSON>](https://doppler.com) to manage secrets, make sure you preceed the commands by `doppler run -- <command>`


# Backend 

Runs on `Python 3.10`

## Keeping Dependencies Clean

If you need to install new libraries for the python code to work, you need to add it to the `requirements.in` file and generate `requirements.txt` using pip-compile. 

Install `pip-tools` and then run `pip-compile requirements.in`.

## Keeping Codebase Syntax standard [TODO]

Please use the `python-black` `pre-commit` webhook.

To install just make sure you have `pre-commit` installed in your local Python 3.10 virtual environment and run the below:

`pre-commit install`

## Migrations

### Creating the migration
> `flask db migrate -m "migration message"`

### Running the migration
> `flask db upgrade`

### Notes
If running into any issues with the above, just add the prefix `python -m ...` before the commands

Also, another issue you may encounter if you're running all the mgirations at once is this:

```
ERROR [flask_migrate] Error: Online migration expected to match one row when updating '79234ca16668' to 'a21b82767542' in 'alembic_version'; 0 found
```

This is because migration a21b82767542 uses a slightly different approach to creating indexes, namely that it doesn't create a transaction so we can create the indexes concurrently.

One way to do this is to stagger the upgrades like so:
1. `flask db upgrade 79234ca16668` #this runs the migration up to the point before the concurrent index creation
2. `flask db upgrade a21b82767542` #this runs the migration with the concurrent index creation
3. `flask db upgrade` #this runs the rest of the migration

### Data migrations for important configs

Data migrations can be manually added to the `migrations/versions/` folder. You can use ORM functionality in both the upgrade, and downgrade functions. Make sure you conform with the id, and timestamps that Alembic uses to determine the dependency graph of migrations.

# Seeding a local environment for the first time

- Run `flask db upgrade` to set up initial DB
- Add test store: `python seed_utilities/test_store_utilities.py --action add-test-store --store-url vandra-test.myshopify.com`
- `python seed_utilities/discount_utilities.py --action add-discount --code <test code from shopify test store> --store-id <storeid from step 1>`
- Add default UI `python seed_utilities/frontend_ui_utilities.py --action add-new-ui --name DEFAULT_RIGHT --filename vandra-default.js --default`
- Add super admin`python seed_utilities/admin_user_utilities.py --action add-admin --email <email> --password <password> --super-admin`
- Add intervention type: `python intervention_type_utilities.py --action add-new-intervention-type --intervention-type-name cart_abandonment_returning`
- Add store intervention association `python intervention_type_utilities.py --action seed-intervention-store-associations --intervention-type-name cart_abandonment_returning`
- Add a simple baseline scoring layer `python seed_utilities/scoring_layer_utilities.py --action create-test-intent-scoring-layer --intent-deployment-reference 554a9c1a-80a8-4832-b6df-218420c853a5 --filename 2024091901`
- Add placeholder thresholds for your test store (grab the layer id and the store id from above steps) `python seed_utilities/scoring_layer_utilities.py --action seed-intent-scoring-layer-thresholds-for-store --store-id <store-id-from-above> --scoring-layer-id <scoring-id-from-above`
- Add default Subscriptions: `python seed_utilities/subscription_plan_utilities.py --action add-plans`
- For existing environments, you might need to backfill onboarding details: `python seed_utilities/backfill_onboarding_details.py`
- For existing environments, you might need to backfill intervention types: `python seed_utilities/backfill_intervention_types.py --action backfill`

# How to manage deployments via heroku cli
Since multiple heroku apps and environments are tied to the repo, we'll be adding a lot of different remotes to our local git

You can check linked remotes like so:
`git remote -v`

And link new ones with:

`heroku git:remote --remote <git name for local use> -a <heroku app name>`

You can then push like so:

`git push <git name for local use> <local branch>:main`
