from sqlalchemy import <PERSON>umn, BigInteger, Integer, String, Numeric, Boolean, LargeBinary, JSON, DateTime
from sqlalchemy.orm import backref
import uuid 
from database_models import (db, base, 
                    intervention_type, 
                    session_intervention, 
                    store_intervention_association,
                    scoring_artifact,
                    scoring_layer,
                    scoring_layer_determination,
                    scoring_layer_thresholds,
                    store_intervention_event,
                    session_consent_event,
                    session_intervention_event,
                    session_discount_code)

# Base model with to_dict()
class BaseModel(db.Model):
    __abstract__ = True 

    def to_dict(self):
        """Convert model instance to dictionary"""
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}

# A table manually populated with a few rows, each of which represent
# a <PERSON><PERSON> employee (<PERSON>, <PERSON>, <PERSON> as of 3/24/23)
# Manages access to the vandra_admin pages
class admin_user(BaseModel):
    __tablename__ = "admin_user"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    email = Column(String)
    password = Column(LargeBinary)
    super_admin = Column(Boolean, default=False)

# Authentication token associated to an admin_user so they don't have to log in each time
class admin_user_login_token(BaseModel):
    __tablename__ = "admin_user_login_token"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    admin_user_id = Column(String, db.ForeignKey('admin_user.uuid', ondelete='CASCADE'))
    token = Column(String)

    parent = db.relationship('admin_user', backref=backref('login_tokens', passive_deletes=True))

# Data collected about end users' interactions with their Shopify carts
# Our back-end receives data (via our js) from the Shopify cart API
# Can be used to drive intelligent decisioning, though the data is
# considered somewhat unreliable as of 3/24/23 due to inconsistent capture
# across merchants and types of actions
class cart(BaseModel):
    __tablename__ = 'cart'
    uuid = Column(String, primary_key=True)
    cart_token = Column(String)
    time = Column(Integer)
    cart_change_type = Column(String)
    cart_items = Column(JSON)
    checkout_charge_amount = Column(Numeric)
    currency = Column(String)
    item_count = Column(Integer)
    items_subtotal_price = Column(Numeric)
    note = Column(String)
    original_total_price = Column(Numeric)
    requires_shipping = Column(Boolean)
    taxes_included = Column(Boolean)
    total_discount = Column(Numeric)
    total_price = Column(Numeric)
    total_weight = Column(Numeric)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'))

    parent = db.relationship('user_session', backref=backref('carts', passive_deletes=True))

# Data collected about end users' cart tokens, each associated
# with a user_session in a one (user_session) to many (cart_token)
# relationship
class cart_token(BaseModel):
    __tablename__ = 'cart_token'
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    cart_token = Column(String)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'))

    parent = db.relationship('user_session', backref=backref('cart_tokens', passive_deletes=True))

class checkout_discount(BaseModel):
    __tablename__ = 'checkout_discount'
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    discount_code = Column(String)
    discount_amount = Column(Numeric)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'))

    parent = db.relationship('user_session', backref=backref('checkout_discounts', passive_deletes=True))

# Data collected about end users' clicks on merchants' websites
# Events are recorded by our javascript embedded on the merchants' sites
# A primary feature for our intelligent decisioning model
class click(BaseModel):
    __tablename__ = 'click'
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    base_uri = Column(String)
    children_count = Column(Integer)
    class_name = Column(String)
    client_width = Column(Integer)
    client_height = Column(Integer)
    client_top = Column(Integer)
    client_left = Column(Integer)
    element_id = Column(String)
    first_child_name = Column(String)
    inner_text = Column(String)
    last_child_name = Column(String)
    next_sibling_name = Column(String)
    offset_width = Column(Integer)
    offset_height = Column(Integer)
    page_view_id = Column(String, db.ForeignKey('page_view.uuid', ondelete='CASCADE'))
    parent_node_name = Column(String)
    previous_sibling_name = Column(String)
    style = Column(String)
    tag_name = Column(String)

    parent = db.relationship('page_view', backref=backref('clicks', passive_deletes=True))

# Lambda function populates these rows for each store every night for the previous day (midnight to midnight ET)
class daily_stat(BaseModel):
    __tablename__ = "daily_stat"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    analytics_actionable_audience_conversions = Column(Integer)
    analytics_actionable_audience_revenue = Column(Numeric)
    analytics_actionable_audience_sessions = Column(Integer)
    analytics_protected_audience_conversions = Column(Integer)
    analytics_protected_audience_revenue = Column(Numeric)
    analytics_protected_audience_sessions = Column(Integer)
    analytics_total_audience_conversions = Column(Integer)
    analytics_total_audience_revenue = Column(Numeric)
    analytics_total_audience_sessions = Column(Integer)
    conversions = Column(Integer)
    conversions_before_predictions = Column(Integer)
    conversions_before_predictions_dollars = Column(Numeric)
    conversions_dollars = Column(Numeric)
    conversions_vandra_show = Column(Integer)
    conversions_vandra_show_dollars = Column(Numeric)
    end_time = Column(Integer)
    holdout_sessions = Column(Integer)
    holdout_sessions_with_show_predictions = Column(Integer)
    holdout_session_conversions = Column(Integer)
    holdout_session_conversions_dollars = Column(Numeric)
    holdout_session_conversions_vandra_show = Column(Integer)
    holdout_session_conversions_vandra_show_dollars = Column(Numeric)
    merchant_admin_engagements = Column(Integer)
    opportunity_session_conversions = Column(Integer)
    opportunity_session_conversions_dollars = Column(Numeric)
    opportunity_session_conversions_before_predictions = Column(Integer)
    opportunity_session_conversions_before_predictions_dollars = Column(Numeric)
    opportunity_session_conversions_vandra_show = Column(Integer)
    opportunity_session_conversions_vandra_show_dollars = Column(Numeric)
    opportunity_sessions = Column(Integer)
    opportunity_sessions_with_predictions = Column(Integer)
    opportunity_sessions_with_show_predictions = Column(Integer)
    popups_applied = Column(Integer)
    popups_dismissed = Column(Integer)
    popups_shown = Column(Integer)
    sessions = Column(Integer)
    sessions_with_predictions = Column(Integer)
    sessions_with_show_predictions = Column(Integer)
    start_time = Column(Integer)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    vandra_conversions = Column(Integer)
    vandra_conversions_dollars = Column(Numeric)
    vandra_conversions_not_shown_that_session = Column(Integer)
    vandra_conversions_not_shown_that_session_dollars = Column(Numeric)
    vandra_conversions_not_shown_any_session = Column(Integer)
    vandra_conversions_not_shown_any_session_dollars = Column(Numeric)

# A record of Shopify DiscountCode and/or PriceRule objects that we create via the API
# These records hold the discount amount and the actual code applied to end users' carts
class discount_code(BaseModel):
    __tablename__ = "discount_code"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    discount_code_code = Column(String)
    ends_at_time = Column(Integer)
    expired = Column(Boolean, default=False)
    price_rule_id = Column(String)
    price_rule_title = Column(String)
    price_rule_value = Column(Numeric)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))

    parent = db.relationship('store', backref=backref('discount_codes', passive_deletes=True))

# When these are present, the merchant's discount code should ONLY apply to the collection(s)
# As of 5/1/23 this table is managed manually (no UI)
class discount_collection(BaseModel):
    __tablename__ = "discount_collection"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    title = Column(String)
    collection_id = Column(String)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))

    parent = db.relationship('store', backref=backref('discount_collections', passive_deletes=True))

# A merchant-specified tag applied to products that they do not want to have
# vandra discounts applied to
# These tags are used by a microservice to periodically keep product collections
# up to date, so that we can just include the collection in the discount
# Ties to include_collection, where all products that DO NOT have the exclude_tag
# are added to the include_collection
# As of 3/31/23 these are maintained manually by Vandra (no UI)
class exclude_tag(BaseModel):
    __tablename__ = "exclude_tag"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    tag_name = Column(String)

    parent = db.relationship('store', backref=backref('exclude_tags', passive_deletes=True))

class exclude_url(BaseModel):
    __tablename__ = "exclude_url"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    url = Column(String)

    parent = db.relationship('store', backref=backref('exclude_urls', passive_deletes=True))

# Represents a unique set of HTML/CSS/JS that changes the end-user's experience
# Associated to a user_session via front_end_experiment
class front_end_ui(BaseModel):
    __tablename__ = "front_end_ui"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    default = Column(Boolean, default=False)
    filename = Column(String)
    name = Column(String)

# A combination of front_end_ui and store with weightings to determine
# which UIs end users of a specific store can receive
class front_end_experiment(BaseModel):
    __tablename__ = "front_end_experiment"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    active = Column(Boolean, default=False)
    description = Column(String)
    name = Column(String)
    store_specific = Column(Boolean, default=False)

# Associate front_end_experiment to front_end_ui with a weighting informing how often to show each UI
class front_end_experiment_ui_allocation(BaseModel):
    __tablename__ = "front_end_experiment_ui_allocation"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    front_end_ui_id = Column(String, db.ForeignKey('front_end_ui.uuid', ondelete='CASCADE'))
    front_end_experiment_id = Column(String, db.ForeignKey('front_end_experiment.uuid', ondelete='CASCADE'))
    weight = Column(Numeric)

# Keep track of when experiments are turned on and off
class front_end_experiment_log(BaseModel):
    __tablename__ = "front_end_experiment_log"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    front_end_experiment_id = Column(String, db.ForeignKey('front_end_experiment.uuid', ondelete='CASCADE'))
    start_time = Column(Integer)
    end_time = Column(Integer)

# Determine which stores are associated to an experiment when store_specific is True
class front_end_experiment_store_association(BaseModel):
    __tablename__ = "front_end_experiment_store_association"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    front_end_experiment_id = Column(String, db.ForeignKey('front_end_experiment.uuid', ondelete='CASCADE'))
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    
    parent = db.relationship('front_end_experiment', backref=backref('store_associations', passive_deletes=True))

# A record of the Shopify collection object that we create and maintain
# Used if the merchant wants to exclude some of their products from our discounting
# This collection is made of all of the products that do NOT have the exclude_tag applied
class include_collection(BaseModel):
    __tablename__ = "include_collection"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    title = Column(String)
    shopify_id = Column(String)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))

    parent = db.relationship('store', backref=backref('include_collections', passive_deletes=True))

# Keep track of when we've send a programtic command to the inference VM to restart
# Used to make sure we don't keep restarting it over and over again
class inference_vm_restart(BaseModel):
    __tablename__ = "inference_vm_restart"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)

# IP2Location open source database
# Provided an IP address, it turns location
# Used to augment data from the user_session table
class ip2location(BaseModel):
    __tablename__ = "ip2location"
    id = Column(Integer, primary_key=True, autoincrement=True)
    ip_from = Column(BigInteger)
    ip_to = Column(BigInteger)
    country_code = Column(String)
    country_name = Column(String)
    region_name = Column(String)
    city_name = Column(String)
    latitude = Column(Numeric)
    longitude = Column(Numeric)
    zip_code = Column(String)
    time_zone = Column(String)

# Records all Python exceptions thrown anywhere in the application
class logged_error(BaseModel):
    __tablename__ = "logged_error"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    email_sent = Column(Boolean, default=False)
    error_text = Column(String)

# Track the merchants' actions in the merchant admin panel
class merchant_engagement(BaseModel):
    __tablename__ = "merchant_engagement"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    engagement = Column(String)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))

# A record of a decision by our intelligent model as to whether or not we should show
# the discount on a given page load
# We record these even if the discount popup is not being actively shown on the merchant site
class model_decision(BaseModel):
    __tablename__ = "model_decision"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    conversion_1_probability = Column(Numeric)
    decided_to_show_discount = Column(Boolean)
    discount_code_id = Column(String)
    live_version = Column(Boolean) # False if this is a test model, not to be used to show discounts; True otherwise
    model_version_id = Column(String, db.ForeignKey('model_version.uuid', ondelete='CASCADE'))
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE')) 
    decision_criterion = Column(String) # e.g `low-intent`, `anti-holdout`, etc.

    parent = db.relationship('user_session', backref=backref('model_decisions', passive_deletes=True))

# An entry is created every time the popup is enabled/disabled for a merchant either at
# the merchant level or the admin level
# Can be used to determine if we were showing popups for a given time period
class model_state(BaseModel):
    __tablename__ = "model_state"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    merchant_enabled = Column(Boolean)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    vandra_admin_enabled = Column(Boolean)

class model_store_holdout(BaseModel):
    __tablename__ = "model_store_holdout"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    holdout_percent = Column(Numeric)
    model_version_id = Column(String, db.ForeignKey('model_version.uuid', ondelete='CASCADE'))
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    active = Column(Boolean, default=False)
    holdout_type = Column(String) # e.g. low-intent, anti-holdout, etc.


class model_store_threshold(BaseModel):
    __tablename__ = "model_store_threshold"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    cannibalization_rate_target = Column(Numeric(precision=5, scale=2))
    decision_threshold = Column(Numeric)
    expected_cannibalization_rate = Column(Numeric) # Test-set expectation at the decision threshold set
    expected_discount_offer_rate = Column(Numeric) # Test-set expectation at the decision threshold set
    min_decision_threshold = Column(Numeric)
    model_version_id = Column(String, db.ForeignKey('model_version.uuid', ondelete='CASCADE'))
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    test_more_restrictive = Column(Boolean) # Currently use the more conservative of two possible thresholds: one calculated according to validation sets and one according to test set. This is True if test set was the more conservative.
    total_test_set_count = Column(Integer) # Number of sessions in the train/validation set if test_split_method is 'no_test' or 'time_based', or the test set if test_split_method is 'randomized'
    active = Column(Boolean, default=False)
    threshold_type = Column(String) # e.g. low-intent, anti-holdout, etc.


    parent = db.relationship('model_version', backref=backref('model_store_thresholds', passive_deletes=True))

class model_version(BaseModel):
    __tablename__ = "model_version"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    decision_threshold = Column(Numeric)
    description = Column(String)
    deployment_id = Column(String)
    filename = Column(String)
    h2o_experiment_id = Column(String)
    holdout_percent = Column(Numeric, default=0)
    live_version = Column(Boolean) # False if this is a test model, not to be used to show discounts; True otherwise
    min_decision_threshold = Column(Numeric)
    min_user_session_time_processed = Column(Integer) # The timestamp of the earliest user_session that has been processed for feature engineering
    platform = Column(String) # 'h2o' or 'sagemaker'
    status = Column(String) # How far along is the model in training/deployment
    store_specific_model = Column(Boolean) # True if this model is not used for all merchants; relies on child entries in model_store_threshold
    run_predictions = Column(Boolean) # True if we should record predictions, False for disabled
    test_split_method = Column(String) # One of ['no_test', 'time_based', 'randomized']
    time_cutoff = Column(Integer)
    train_end_time = Column(Integer)
    train_start_time = Column(Integer)

# A representation of a Shopify Order instance
# These are generated by our microservice as our "source of truth" about
# merchant orders and our attribution for orders we were responsible for
# This table is used to generate UsageCharges
class order(BaseModel):
    __tablename__ = "order"
    uuid = Column(String, primary_key=True)
    cart_token = Column(String)
    time = Column(Integer)
    created_at = Column(Integer)
    currency = Column(String)
    discount_code = Column(String)
    session_id = Column(String)
    shopify_id = Column(String)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    subtotal_price = Column(Numeric)

    parent = db.relationship('store', backref=backref('orders', passive_deletes=True))

# Lookup table to translate user_session.os_version into a time for use in machine learning as a linear feature
# Manually updated from https://en.wikipedia.org/wiki/Timeline_of_operating_systems
class os_version_time(BaseModel):
    __tablename__ = "os_version_time"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    os = Column(String)
    os_version = Column(String)
    release_time = Column(Integer)

# Child of page_view
# Collect activity about dwell time, mouse movement, and scrolling every 1/3/5/10 seconds
class page_activity(BaseModel):
    __tablename__ = "page_activity"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    dwell_time = Column(Numeric)
    focused_dwell_time = Column(Numeric)
    page_view_id = Column(String, db.ForeignKey('page_view.uuid', ondelete='CASCADE'))
    mouse_move = Column(Integer)
    scroll = Column(Integer)

    parent = db.relationship('page_view', backref=backref('page_activities', passive_deletes=True))

# Child of page_view
# Collect activity about the keys the user has presed
class page_keystrokes(BaseModel):
    __tablename__ = "page_keystrokes"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    keys = Column(String)
    page_view_id = Column(String, db.ForeignKey('page_view.uuid', ondelete='CASCADE'))
    
    parent = db.relationship('page_view', backref=backref('page_keystrokes', passive_deletes=True))

# A collection of data about a user's interaction with a merchant's site
# Has a many to one relationship to the user_session
# Contains much of the data that drives the intelligent decisioning
class page_view(BaseModel):
    __tablename__ = "page_view"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    document_has_focus = Column(Boolean)
    dwell_time = Column(Numeric)
    focused_dwell_time = Column(Numeric)
    front_end_id = Column(String)
    local_time = Column(DateTime(timezone=True))
    local_timezone = Column(String)
    page = Column(String)
    total_mouse_move = Column(Integer)
    total_scroll = Column(Integer)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'))

    parent = db.relationship('user_session', backref=backref('page_views', passive_deletes=True))

# Data collected about end users' focus or blur (lack of focus) events on merchants' websites
# Events are recorded by our javascript embedded on the merchants' sites
class page_focus_change(BaseModel):
    __tablename__ = 'page_focus_change'
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    page_view_id = Column(String, db.ForeignKey('page_view.uuid', ondelete='CASCADE'))
    coming_into_focus = Column(Boolean)

    parent = db.relationship('page_view', backref=backref('page_focus_changes', passive_deletes=True))

# Stores product eligibility for Vandra discount
# Products in this table are eligible for discounts
# Products not in the table are not eligible
class product(BaseModel):
    __tablename__ = 'product'
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    product_id = Column(String)

    parent = db.relationship('store', backref=backref('products', passive_deletes=True))

# A record of when an end-user does a search on a merchant's website
# Theoretically can be used as an input to our intelligent decisioning model
# As of 3/24/23 this data is not reliable due to inconsistent capture across merchants
class search(BaseModel):
    __tablename__ = 'search'
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    page_view_id = Column(String, db.ForeignKey('page_view.uuid', ondelete='CASCADE'))
    search_query = Column(String)

    parent = db.relationship('page_view', backref=backref('searches', passive_deletes=True))

# Represents a merchant / our customer
# Mostly associates to Shopify's equivalent records on the "url" field
class store(BaseModel):
    __tablename__ = "store"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    access_token = Column(String)
    active_store = Column(Boolean, default=False) # Manual toggle we use just to put active stores at the top of lists
    address1 = Column(String)
    address2 = Column(String)
    auto_apply_text_body = Column(String, default="A discount is waiting in your cart. It's only good for today so don't miss out.")
    charge_id = Column(String)
    city = Column(String)
    commission_rate = Column(Numeric, default=0.05)
    discount_prefix = Column(String, default="VANDRA")
    discount_one_time = Column(Boolean, default=True)
    discount_subscription = Column(Boolean, default=True)
    email = Column(String)
    had_first_visit = Column(Boolean, default=False)
    popup_image_url = Column(String)
    last_exclusion_sync = Column(Integer, default=0)
    last_exclusion_sync_end_cursor = Column(String)
    last_order_sync = Column(Integer, default=0)
    last_webhook_check = Column(Integer)
    max_discount = Column(Numeric, default=20) # Positive integer between ~5-30
    minimized_text_header = Column(String, default="GET {discount}% OFF NOW")
    name = Column(String)
    popup_bg_color = Column(String, default="FFFFFF")
    popup_font = Column(String, default="Source Sans Pro")
    popup_primary_color = Column(String, default="0000AA")
    popup_text_header = Column(String, default="GET {discount}% OFF NOW")
    popup_text_body = Column(String, default="Valid for one day only. Don't miss out!")
    popup_text_button = Column(String, default="Apply Discount")
    popup_text_button_close = Column(String, default="No, Thanks!")
    popup_text_success = Column(String, default="Discount successfully applied!")
    shop_owner = Column(String)
    show_discount = Column(Boolean, default=False)
    show_discount_when_logged_in = Column(Boolean, default=True)
    show_discount_only_to_paid = Column(Boolean, default=False)
    show_discount_to_previous_customers = Column(Boolean, default=True)
    hide_minimized_popup = Column(Boolean, default=False)
    state = Column(String)
    test_store = Column(Boolean, default=False)
    url = Column(String)
    use_in_datascience = Column(Boolean, default=True)
    use_meta_ad_pixel = Column(Boolean, default=False)
    vandra_admin_show_discount = Column(Boolean, default=False)
    shop_owner_role = Column(String)
    subscription_plan = Column(String, db.ForeignKey('subscription_plans.uuid', ondelete='SET NULL'))
    onboarding_details = Column(String, db.ForeignKey('onboarding_details.uuid', ondelete='SET NULL'))
    status = Column(String)
    zip = Column(String)

    #add a relationship to the onboarding_details table
    onboarding_details_record = db.relationship('onboarding_details', backref=backref('stores', uselist=False, passive_deletes=True))

# URLs for stores
class store_url(BaseModel):
    __tablename__ = "store_url"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    default = Column(Boolean, default=False)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    url = Column(String)

    parent = db.relationship('store', backref=backref('store_urls', passive_deletes=True))

# A record of a discount amount that's actively being offered by the store
# Used primarily to allow us to offer multiple discount amounts at the same time
# for a store
class store_discount(BaseModel):
    __tablename__ = "store_discount"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    active = Column(Boolean, default=True)
    discount_value = Column(Numeric) # Should be a positive integer between ~5-30
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    discount_prefix = Column(String, nullable=True)

    parent = db.relationship('store', backref=backref('store_discounts', passive_deletes=True))

# Child of the store table
# Used by the update_exclusions table
# Keeps track of which collections have already been synced by a given iteration
class store_synced_exclusion_collection(BaseModel):
    __tablename__ = "store_synced_exclusion_collection"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    collection_id = Column(String)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))

    parent = db.relationship('store', backref=backref('store_synced_exclusion_collections', passive_deletes=True))

# A representation of a Shopify UsageCharge object
# This is how we charge our customers for the orders we were responsible for
# Based on the information we get and store in the Order table
# Created and submitted to Shopify from a microservice
class usage_charge(BaseModel):
    __tablename__ = "usage_charge"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    description = Column(String)
    end_time = Column(Integer)
    price = Column(Numeric)
    shopify_id = Column(String)
    start_time = Column(Integer)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    submitted_time = Column(Integer)
    submitted_to_shopify = Column(Boolean, default=False)
    total_sales = Column(Numeric)

    parent = db.relationship('store', backref=backref('usage_charges', passive_deletes=True))

# Represents an end user's session on a merchant website
# Can represent numerous page views, but resets after an hour
# Contains much of the data used to drive the intelligent decisioning model
class user_session(BaseModel):
    __tablename__ = "user_session"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    ad_bing = Column(Boolean)
    ad_doubleclick = Column(Boolean)
    ad_facebook = Column(Boolean)
    ad_google = Column(Boolean)
    ad_tiktok = Column(Boolean)
    browser = Column(String)
    browser_version = Column(String)
    cart_size = Column(Numeric)
    cart_token = Column(String) # 5/4/23 Feel free to delete in a month or so if we don't need this data
    conversion = Column(Boolean, default=False)
    conversion_discount_code = Column(String)
    conversion_discount_amount = Column(Numeric) # In dollar/currency, not percent
    countdown_deadline = Column(Numeric)
    customer_cookie = Column(String)
    device = Column(String)
    device_memory = Column(Numeric)
    domain = Column(String)
    front_end_ui_id = Column(String, db.ForeignKey("front_end_ui.uuid", ondelete="SET NULL"))
    front_end_experiment_id = Column(String, db.ForeignKey("front_end_experiment.uuid", ondelete="SET NULL"))
    holdout = Column(Boolean, default=None) #changed from False to None to prevent reclassifications
    ip_address = Column(String)
    country_name = Column(String)
    region_name = Column(String)
    zip_code = Column(String)
    language = Column(String)
    latitude = Column(Numeric)
    local_time = Column(DateTime(timezone=True))
    local_timezone = Column(String)
    longitude = Column(Numeric)
    mobile = Column(Boolean)
    no_vandra_cookie = Column(Boolean, default=False)
    order_id = Column(String)
    os = Column(String)
    os_version = Column(String)
    page = Column(String)
    price = Column(String)
    referrer = Column(String)
    screen_height = Column(Integer)
    screen_width = Column(Integer)
    session_cookie = Column(String)
    shopify_customer_id = Column(Integer)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    utm_campaign = Column(String)
    utm_content = Column(String)
    utm_medium = Column(String)
    utm_source = Column(String)
    utm_term = Column(String)
    vandra_conversion = Column(Boolean, default=False) # A session that resulted in an order that had a VANDRA discount code
    vandra_discount_code_assigned = Column(String)
    vandra_discount_rate_assigned = Column(Numeric)
    vandra_dismissed = Column(Boolean, default=False) # The user dismissed the Vandra popup
    vandra_reopened = Column(Boolean, default=False) # The user reopened the Vandra popup
    vandra_discount_applied = Column(Boolean, default=False) # The user clicked the "Apply" button on the discount popup
    vandra_discount_toasted = Column(Boolean, default=False) # We let the user know that they successfully applied the discount
    vandra_opportunity = Column(Boolean, default=False) # Did we have a chance to show the popup? False if merchant or vandra admin chose not to show
    vandra_shown = Column(Boolean, default=False) # We showed the popup

    parent = db.relationship("store", backref=backref("user_sessions", passive_deletes=True))
    front_end_ui = db.relationship("front_end_ui", backref=backref("user_sessions", passive_deletes=True))

class user_session_event(BaseModel):
    __tablename__ = "user_session_event"
    uuid = Column(String, primary_key=True)
    time = Column(Integer)
    action = Column(String)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'))
    dwell_time = Column(Numeric)
    focused_dwell_time = Column(Numeric)
    page_view_id = Column(String, db.ForeignKey('page_view.uuid', ondelete='CASCADE'))

    parent = db.relationship('user_session', backref=backref('user_session_events', passive_deletes=True))
    page_view = db.relationship('page_view', backref=backref('user_session_events', passive_deletes=True))

class user_session_nudge_parameters(BaseModel):
    __tablename__ = "user_session_nudge_parameters"
    uuid = Column(String, primary_key=True, index=True,default=uuid.uuid4)
    time = Column(Integer)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'), index=True)
    trigger_type = Column(String)
    trigger_delay = Column(Integer)
    trigger_state = Column(String)
    trigger_action = Column(String)
    
    user_session = db.relationship('user_session', backref=backref('user_session_nudge_parameters', uselist=False,passive_deletes=True))
    
class onboarding_details(BaseModel):
    __tablename__ = "onboarding_details"
    uuid = Column(String, primary_key=True, index=True,default=uuid.uuid4)
    terms_start = Column(Integer)
    terms_signed = Column(Integer)
    script_start = Column(Integer)
    script_activated = Column(Integer)
    details_start = Column(Integer)
    installer_first_name = Column(String)
    installer_last_name = Column(String)
    installer_email = Column(String)
    installer_role = Column(String)
    details_confirmed = Column(Integer)
    billing_start = Column(Integer)
    billing_confirmed = Column(Integer)
    billing_trial_end = Column(Integer)
    billing_orders_selected = Column(Integer)
    billing_plan_selected = Column(String)
    deleted = Column(Boolean, default=False)
    store_url = Column(String)

class metabase_analytics(BaseModel):
    __tablename__ = "metabase_analytics"
    uuid = Column(String, primary_key=True, index=True,default=uuid.uuid4)
    intervention_type_id = Column(String, db.ForeignKey('intervention_type.uuid', ondelete='CASCADE'), index=True)
    dashboard_id = Column(Integer)

class subscription_plans(BaseModel):
    __tablename__ = "subscription_plans"
    uuid = Column(String, primary_key=True, index=True, default=uuid.uuid4)
    created_at = Column(Integer)
    pricing_details = Column(JSON)

# Used to store the data from webhook requests we get from Shopify
# Mostly orders that we can use to tie outcomes to our session data
# Also important for store deletions and other GDPR-related data requests
class webhook_request(BaseModel):
    __tablename__ = "webhook_request"
    uuid = Column(String(36), primary_key=True)
    time = Column(BigInteger)
    data = Column(String)
    event = Column(String(200))
    shop_url = Column(String(200))
