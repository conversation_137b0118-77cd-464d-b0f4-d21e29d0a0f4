import unittest
 
from app import app, db, db_session
from db import admin_user, cart_token, page_activity, page_focus_change, store, store_url

import bcrypt
import json
import time
import uuid

class route_tests(unittest.TestCase):
    # Reserved method name in the unittest framework run before each test
    def setUp(self):
        app.config['TESTING'] = True
        app.config['DEBUG'] = False
        
        app.config["SQLALCHEMY_DATABASE_URI"] = app.config["SQLALCHEMY_TEST_DATABASE_URI"]

        self.app = app.test_client()
        with app.app_context():
            db.drop_all()
            db.create_all()
    
    # Reserved method name in the unittest framework run after each test
    def tearDown(self):
        with app.app_context():
            db_session.remove()
            db.drop_all()
    
    def add_store(self):
        with app.app_context():
            new_store = store()
            new_store.uuid = str(uuid.uuid4())
            new_store.time = time.time()
            db_session.add(new_store)

            new_store_url = store_url()
            new_store_url.uuid = str(uuid.uuid4())
            new_store_url.time = time.time()
            new_store_url.default = True
            new_store_url.store_id = new_store.uuid
            new_store_url.url = "test.myshopify.com"
            db_session.add(new_store_url)

            db_session.commit()
    
    def record_page_view(self):
        return self.app.post("/record_page_view",
            data={ "session_cookie": "aaaa", "shopify_url": "test.myshopify.com", "page_view_id": "bbbb" },
            content_type="application/json",
            follow_redirects=True)
    
    # Test loading and logging in to the Vandra admin dashboard
    def test_admin_dashboard(self):
        with app.app_context():
            new_admin = admin_user()
            new_admin.uuid = str(uuid.uuid4())
            new_admin.time = time.time()
            new_admin.email = "<EMAIL>"
            new_admin.password = bcrypt.hashpw("test".encode("utf-8"), bcrypt.gensalt())
            db_session.add(new_admin)
            db_session.commit()
        
        # Make sure login screen works
        response = self.app.get("/vandra_admin_login")
        self.assertEqual(response.status_code, 200)
        self.assertIn("Email address", response.data.decode("utf-8"))
        
        # Make sure fake login doesn't work - email
        response = self.app.post("/vandra_admin_login_process",
            data={ "email": "<EMAIL>", "password": "test" }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn("Email address", response.data.decode("utf-8"))
        
        # Make sure fake login doesn't work - password
        response = self.app.post("/vandra_admin_login_process",
            data={ "email": "<EMAIL>", "password": "test2" }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn("Email address", response.data.decode("utf-8"))
        
        # Try to log in
        response = self.app.post("/vandra_admin_login_process",
            data={ "email": "<EMAIL>", "password": "test" }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn("Welcome to Vandra admin", response.data.decode("utf-8"))
    
    def test_record_page_view(self):
        self.add_store()
        
        response = self.record_page_view()
        self.assertEqual(response.status_code, 200)
        json_response = json.loads(response.get_data(as_text=True))
        self.assertNotEqual(json_response.get("status"), "error")
    
    def test_get_model_decision(self):
        self.add_store()
        self.record_page_view()
        
        response = self.app.post("/get_model_decision",
            data={ "session_cookie": "aaaa", "shopify_url": "test.myshopify.com", "page": "https://test.myshopify.com/" },
            content_type="application/json",
            follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        json_response = json.loads(response.get_data(as_text=True))
        self.assertNotEqual(json_response.get("status"), None)
    
    def test_record_click(self):
        self.add_store()
        self.record_page_view()
        
        response = self.app.post("/record_click",
            data={ "session_cookie": "aaaa", "page_view_id": "bbbb" },
            follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertEqual("okay", response.data.decode("utf-8"))
    
    def test_record_cart_token(self):
        self.add_store()
        self.record_page_view()
        
        response = self.app.post("/record_cart_token",
            data={ "session_cookie": "aaaa", "cart_token": "cccc" },
            follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertEqual("okay", response.data.decode("utf-8"))
        
        cart_token_record = cart_token.query.filter_by(user_session_id="aaaa", cart_token="cccc").first()
        self.assertNotEqual(cart_token_record, None)
    
    def test_record_dwell_time(self):
        self.add_store()
        self.record_page_view()
        
        response = self.app.post("/record_dwell_time",
            data={ "session_cookie": "aaaa", "page_view_id": "bbbb", "dwell_time": 100,
                "total_scroll": 101, "total_mouse_move": 102 },
            follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertEqual("okay", response.data.decode("utf-8"))
        
        dwell_time_record = page_activity.query.\
            filter_by(page_view_id="bbbb", dwell_time=100, scroll=101, mouse_move=102).first()
        self.assertNotEqual(dwell_time_record, None)
    
    def test_record_page_focus_change(self):
        self.add_store()
        self.record_page_view()
        
        response = self.app.post("/record_focus_change",
            data={ "session_cookie": "aaaa", "page_view_id": "bbbb", "focus_change_type": "focus" },
            follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertEqual("okay", response.data.decode("utf-8"))
        
        page_focus_change_record = page_focus_change.query.\
            filter_by(page_view_id="bbbb", coming_into_focus=True).first()
        self.assertNotEqual(page_focus_change_record, None)

if __name__ == "__main__":
	unittest.main()