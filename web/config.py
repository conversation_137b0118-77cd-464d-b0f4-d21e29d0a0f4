import os
from dotenv import load_dotenv
import json

load_dotenv()

SESSION_TYPE=os.getenv("SESSION_TYPE")
SECRET_KEY=os.getenv("SECRET_KEY")
APP_NAME=f"{os.getenv('APP_NAME','Python')}-{os.getpid()}"
SQLALCHEMY_DATABASE_URI=(os.getenv("DATABASE_URL") or os.getenv("SQLALCHEMY_DATABASE_URI")).replace("postgres://", "postgresql://")
SHOPIFY_SHARED_SECRET=os.getenv("SHOPIFY_SHARED_SECRET")
SHOPIFY_LEGACY_API_VERSION=os.getenv("SHOPIFY_LEGACY_API_VERSION")
SHOPIFY_API_VERSION=os.getenv("SHOPIFY_API_VERSION")
SHOPIFY_GRAPHQL_API_VERSION=os.getenv("SHOPIFY_GRAPHQL_API_VERSION")
SHOPIFY_API_KEY=os.getenv("SHOPIFY_API_KEY")
SHOPIFY_VANDRA_CHECKOUT_ID=os.getenv("SHOPIFY_VANDRA_CHECKOUT_ID")
SHOPIFY_VANDRA_THEME_EXTENSION_ID=os.getenv("SHOPIFY_VANDRA_THEME_EXTENSION_ID")
SENTRY_DSN=os.getenv("SENTRY_DSN")
APP_ENVIRONMENT=os.getenv("APP_ENVIRONMENT")
API_BASE_URL=os.getenv("API_BASE_URL")
SFD_API_BASE_URL=os.getenv("SFD_API_BASE_URL")
SFD_API_USER_TOKEN=os.getenv("SFD_API_USER_TOKEN")
SFD_API_USER_EMAIL=os.getenv("SFD_API_USER_EMAIL")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
SES_ACCESS_KEY = os.getenv("SES_ACCESS_KEY")
SES_SECRET = os.getenv("SES_SECRET")
if APP_ENVIRONMENT == "local":
    REDIS_SSL = False
else:
    REDIS_SSL = True
REDIS_SSL = os.getenv("REDIS_SSL") == "true" if os.getenv("REDIS_SSL") else REDIS_SSL
SKIP_NOTIFICATIONS=False
COURIER_API_TOKEN=os.getenv("COURIER_API_TOKEN")
if os.getenv("SQLALCHEMY_BINDS"):
    SQLALCHEMY_BINDS=json.loads(os.getenv("SQLALCHEMY_BINDS"))
SHOPIFY_VANDRA_THEME_EXTENSION_BLOCK_ID=os.getenv("SHOPIFY_VANDRA_THEME_EXTENSION_BLOCK_ID")
NEPTUNE_API_TOKEN=os.getenv("NEPTUNE_API_TOKEN")
PYTHON_RQ_SIMPLE_WORKER=os.getenv("PYTHON_RQ_SIMPLE_WORKER") == "True" if os.getenv("PYTHON_RQ_SIMPLE_WORKER") else False
METABASE_API_KEY=os.getenv("METABASE_API_KEY")
METABASE_EMBEDDING_SECRET_KEY=os.getenv("METABASE_EMBEDDING_SECRET_KEY")
DEFAULT_PREVIEW_VIDEO_URL=os.getenv("DEFAULT_PREVIEW_VIDEO_URL")