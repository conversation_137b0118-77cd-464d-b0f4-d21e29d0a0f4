<link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">

<h1>ML model thresholds</h1>

<h2>{{ model_dict["creation_date"] }} - {{ model_dict["description"] }}</h2>

<h2>Thresholds</h2>
<form action="vandra_admin_ml_model_save_thresholds" method="post">
    <input type="hidden" name="model_id" value="{{model_dict["uuid"]}}" />
    <table>
        <tr>
            <th>Store</th>
            <th>Threshold</th>
        </tr>
        <tr class="hover_tr">
            <td>DEFAULT HOLDOUT (0.00-1.00)</td>
            <td><input type="text" name="holdout_0" value="{{model_dict["default_holdout"]}}" /></td>
        </tr>
        <tr class="hover_tr">
            <td>DEFAULT THRESHOLD</td>
            <td><input type="text" name="threshold_0" value="{{model_dict["default_threshold"]}}" /></td>
        </tr>
        {% for store in store_query %}
            <tr class="hover_tr">
                <td>{{store.name or store.url}}</td>
                <td><input type="text" name="threshold_{{store.uuid}}" value="{{threshold_dict[store.uuid]}}" /></td>
            </tr>
        {% endfor %}
    </table>
    <input type="submit" value="Save" />
</form>