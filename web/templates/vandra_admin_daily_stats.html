<style>
    table {
        overflow: visible;
        z-index: 1;
    }
    
    tr:hover {
        background-color: rgb(227, 226, 255);
    }

    td, th {
        position: relative;
    }

    .hover_cell:hover::after {
        background-color: rgb(227, 226, 255);
        content: '\00a0';
        height: 10000px;
        left: 0;
        position: absolute;
        top: -5000px;
        width: 100%;
        z-index: -1;
    }
    
    .sticky_col {
        background-color: #FFF;
        left: 0;
        position: sticky;
        z-index: 9999;
    }
</style>

<a href="vandra_admin">Back home</a>
<h1>Daily stats - {{ date_string }}</h1>
<table>
    <thead>
        <tr>
            <th class="sticky_col" style="vertical-align: top;">Merchant</th>
            {% for stat in stat_list %}
                <th class="hover_cell" style="vertical-align: top; padding-right: 10px;">{{ stat }}</th>
            {% endfor %}
        </tr>
    </thead>
    <tbody>
        {% for merchant in merchant_list %}
            <tr>
                <td class="sticky_col"><a href="vandra_admin_merchant_stats_over_time?id={{merchant["id"]}}">{{ merchant["name"] }}</a></td>
                {% for stat in stat_list %}
                    <td class="hover_cell">{{ merchant[stat] }}</td>
                {% endfor %}
            </tr>
        {% endfor %}
    </tbody>
</table>