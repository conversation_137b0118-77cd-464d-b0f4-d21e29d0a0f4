<a href="vandra_admin_store?id={{store_display.id}}">Back to store</a>
<h1>{{ store_display.url }}</h1>
<form action="" method="post">
    Start date/time: <input name="min_time" type="text" value="{{ min_time }}" /><br />
    End date/time: <input name="max_time" type="text" value="{{ max_time }}" /><br />
    <input type="submit" value="Update" />
</form>
<div style="height: 1em;"></div>
<div style="display: inline-block; margin-right: 20px; vertical-align: top;">
    <h3>Analytics</h3>
    Session count: {{ store_display.session_count }}<br />
    Conversion count: {{ store_display.conversion_count }} ({{ "${:,.2f}".format(store_display.conversion_value) }})<br />
    Conversion rate: {{ store_display.conversion_rate }}%<br />
    # of opportunity sessions: {{ store_display.opportunity_count }}<br />
    # of opportunities that reached model decision: {{ store_display.number_of_decisions_on_opportunities }} <span title="On all sessions">({{ store_display.number_of_decisions }})</span><br />
    # of opportunities that reached model decision (HO): {{ store_display.number_of_decisions_on_opportunities_ho }}<br />
    # of opportunities with "show" from model: {{ store_display.number_of_discounts_on_opportunities }} <span title="On all sessions">({{ store_display.number_of_discounts }})</span><br />
    # of opportunities with "show" from model (HO): {{ store_display.number_of_discounts_on_opportunities_ho }}<br />
    # of times discount applied: {{ store_display.number_of_discounts_applied }}<br />
    # of times popup dismissed: {{ store_display.number_of_discounts_dismissed }}<br />
    # of Vandra conversions: {{ store_display.vandra_conversion_count }} ({{ "${:,.2f}".format(store_display.vandra_conversion_value) }})<br />
    Vandra conversion rate: {{ store_display.vandra_conversion_rate }}%
    <div style="height: 0.6em;"></div>
    (all sessions)<br />
    # of conversions before Vandra decision: {{ store_display.pre_vandra_conversion_count_all }} ({{ "${:,.2f}".format(store_display.pre_vandra_conversion_value_all) }})<br />
    # of conversions Vandra decided not to discount: {{ store_display.no_conversion_count_all }} ({{ "${:,.2f}".format(store_display.no_conversion_value_all) }})<br />
    # of conversions Vandra decided to discount: {{ store_display.yes_conversion_count_all }} ({{ "${:,.2f}".format(store_display.yes_conversion_value_all) }})<br />
    <div style="height: 0.6em;"></div>
    (opportunity sessions only)<br />
    # of conversions before Vandra decision: {{ store_display.pre_vandra_conversion_count }} ({{ "${:,.2f}".format(store_display.pre_vandra_conversion_value) }})<br />
    # of conversions Vandra decided not to discount: {{ store_display.no_conversion_count }} ({{ "${:,.2f}".format(store_display.no_conversion_value) }})<br />
    # of conversions Vandra decided not to discount (HO): {{ store_display.no_conversion_count_ho }} ({{ "${:,.2f}".format(store_display.no_conversion_value_ho) }})<br />
    # of conversions Vandra decided to discount: {{ store_display.yes_conversion_count }} ({{ "${:,.2f}".format(store_display.yes_conversion_value) }})<br />
    # of conversions Vandra decided to discount (HO): {{ store_display.yes_conversion_count_ho }} ({{ "${:,.2f}".format(store_display.yes_conversion_value_ho) }})<br />
    # of Vandra conversions with discount shown: {{ store_display.vandra_conversion_count_shown }} ({{ "${:,.2f}".format(store_display.vandra_conversion_value_shown) }})<br />
    # of Vandra conversions with discount not shown that session: {{ store_display.vandra_conversion_count_not_shown }} ({{ "${:,.2f}".format(store_display.vandra_conversion_value_not_shown) }})<br />
    # of Vandra conversions with discount not shown any session: {{ store_display.vandra_conversion_count_not_shown_any_session }} ({{ "${:,.2f}".format(store_display.vandra_conversion_value_not_shown_any_session) }})
</div>
<div style="display: inline-block; margin-right: 20px; vertical-align: top;">
    <h3>Engagements</h3>
    {% for name, count in engagement_dict.items() %}
        {{name}}: {{count}}<br />
    {% endfor %}
</div>