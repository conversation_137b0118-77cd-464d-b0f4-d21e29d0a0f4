<a href="vandra_admin">Back home</a>
<h1>{{ store_url }}</h1>
<a href="vandra_admin_store_analytics?id={{store_id}}">Analytics</a>
<div style="height: 1em;"></div>


<div style="display: inline-block; margin-right: 20px; vertical-align: top;">
    <h3>Settings</h3>
    <form method="post" action="vandra_admin_store_save_settings?id={{ store_id }}">
        <table>
            <tr>
                <td>Maximum discount (%)</td>
                <td><input type="text" name="maximum_discount" value="{{ store_settings.maximum_discount }}" /></td>
            </tr>
            <tr>
                <td>Commission rate (%)</td>
                <td><input type="text" name="commission_rate" value="{{ store_settings.commission_rate }}" /></td>
            </tr>
            <tr>
                <td>Show discounts to users who are logged in?</td>
                <td><input type="checkbox" name="show_discount_when_logged_in" value="true" {{ "checked" if store_settings.show_discount_when_logged_in }} /></td>
            </tr>
            <tr>
                <td>Show discounts to previous customers?</td>
                <td><input type="checkbox" name="show_discount_to_previous_customers" value="true" {{ "checked" if store_settings.show_discount_to_previous_customers }} /></td>
            </tr>
            <tr>
                <td>Popup primary color (hex value)</td>
                <td><input type="text" name="popup_primary_color" value="{{ store_settings.popup_primary_color }}" /></td>
            </tr>
            <tr>
                <td>Popup background color (hex value)</td>
                <td><input type="text" name="popup_bg_color" value="{{ store_settings.popup_bg_color }}" /></td>
            </tr>
            <tr>
                <td>Popup font</td>
                <td><input type="text" name="popup_font" value="{{ store_settings.popup_font }}" /></td>
            </tr>
            <tr>
                <td>Popup text - header</td>
                <td><input type="text" name="popup_text_header" value="{{ store_settings.popup_text_header }}" style="width: 300px;" /></td>
            </tr>
            <tr>
                <td>Popup text - body</td>
                <td><input type="text" name="popup_text_body" value="{{ store_settings.popup_text_body }}" style="width: 300px;" /></td>
            </tr>
            <tr>
                <td>Popup text - button</td>
                <td><input type="text" name="popup_text_button" value="{{ store_settings.popup_text_button }}" /></td>
            </tr>
            <tr>
                <td>Popup text - close button</td>
                <td><input type="text" name="popup_text_button_close" value="{{ store_settings.popup_text_button_close }}" /></td>
            </tr>
            <tr>
                <td>Popup text - success</td>
                <td><input type="text" name="popup_text_success" value="{{ store_settings.popup_text_success }}" style="width: 300px;" /></td>
            </tr>
            <tr>
                <td>Minimized text - header</td>
                <td><input type="text" name="minimized_text_header" value="{{ store_settings.minimized_text_header }}" style="width: 300px;" /></td>
            </tr>
            <tr>
                <td>AUTO_APPLY text - body</td>
                <td><input type="text" name="auto_apply_text_body" value="{{ store_settings.auto_apply_text_body }}" style="width: 300px;" /></td>
            </tr>
            <tr>
                <td>IMAGE and MODAL image URL</td>
                <td><input type="text" name="popup_image_url" value="{{ store_settings.popup_image_url }}" style="width: 300px;" /></td>
            </tr>
            <tr>
                <td>Discount code prefix</td>
                <td><input type="text" name="discount_prefix" value="{{ store_settings.discount_prefix }}" /></td>
            </tr>
            <tr>
                <td>Collection ids to include in discounting (excludes all others) - 1 per line</td>
                <td>
                    <textarea name="discount_collections" style="width: 500px;" rows="4">{{ store_settings.discount_collections }}</textarea>
                </td>
            </tr>
            <tr>
                <td>URLs to exclude from showing the popup (1 per line)</td>
                <td>
                    <textarea name="exclude_urls" style="width: 500px;" rows="4">{{ store_settings.exclude_urls }}</textarea>
                </td>
            </tr>
            <tr>
                <td>Default Store URL</td>
                <td><input type="text" name="store_url_default" value="{{ store_settings.store_url_default }}" style="width: 300px;" /></td>
            </tr>
            <tr>
                <td>Non-Default Store URLs (ex: headless URLs)</td>
                <td>
                    <textarea name="store_urls_non_default" style="width: 500px;" rows="4">{{ store_settings.store_urls_non_default }}</textarea>
                </td>
            </tr>
            <tr>
                <td>Apply discounts to products purchased as single items?</td>
                <td><input type="checkbox" name="discount_one_time" value="true" {{ "checked" if store_settings.discount_one_time }} /></td>
            </tr>
            <tr>
                <td>Apply discounts to products purchased as a subscription?</td>
                <td><input type="checkbox" name="discount_subscription" value="true" {{ "checked" if store_settings.discount_subscription }} /></td>
            </tr>
            <tr>
                <td colspan="2">
                    <div style="height: 1em;"></div>
                    <input type="submit" value="Save" />
                </td>
            </tr>
        </table>
    </form>
</div>
<!-- Add new form for intent calibration -->
<div style="display: inline-block; margin-right: 20px; vertical-align: top; margin-bottom: 20px;">
    <h3>Intent Calibration</h3>
    <form id="calibrationForm" onsubmit="triggerCalibration(event)">
        <input type="submit" value="Trigger Intent Calibration" />
    </form>
</div>

<script>
function triggerCalibration(event) {
    event.preventDefault();
    
    fetch('/vandra_admin_store_trigger_calibration?id={{ store_id }}', {
        method: 'POST',
    })
    .then(response => response.json())
    .then(data => {
        // Replace newlines with HTML line breaks for alert
        const formattedMessage = data.message.replace(/\n/g, '\n');
        alert(formattedMessage);
        
        if (data.success && data.details.updates) {
            // Optionally refresh the page to show updated state
            // window.location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error triggering calibration');
    });
}
</script>
