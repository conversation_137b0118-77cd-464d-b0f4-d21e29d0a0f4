<link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">

<h1>Welcome to Vandra admin</h1>
<a href="vandra_admin_manage_admins">Add new admin</a><br />
<a href="vandra_admin_daily_stats">View yesterday's stats</a><br />
<a href="vandra_admin_ui_experiments">UI Experiments</a><br />
<a href="vandra_admin_ml_models">ML Models</a><br />
<a href="vandra_admin_error_log">Error Log</a><br />
<a href="vandra_admin_webhooks">View webhook requests</a><br />
<a href="vandra_admin_os_version_time">Manage OS version table</a><br />
<div style="height: 1em;"></div>
{% if usage_charge_list|length > 0 %}
    <div style="height: 1em;"></div>
    <h2>Charges:</h2>
    <table>
        <thead>
            <tr><th>Store</th><th>Date range</th><th>Charge</th><th>Total Sales</th><th>Vandra Sales</th><th></th><th></th></tr>
        </thead>
        <tbody>
            {% for charge in usage_charge_list %}
                <tr>
                    <td>{{ charge.store }}</td>
                    <td>{{ charge.description }}</td>
                    <td>{{ charge.date_range }}</td>
                    <td>{{ charge.charge }}</td>
                    <td>{{ charge.total_orders }}</td>
                    <td>
                        <form action="vandra_admin_submit_charge" method="post" onsubmit="return confirm('Submit charge?');">
                            <input type="hidden" name="usage_charge_id" value="{{ charge.uuid }}" />
                            <input type="submit" value="Submit" />
                        </form>
                    </td>
                    <td><a href="vandra_admin_delete_usage_charge?id={{charge.uuid}}" onclick="return confirm('Delete charge? Will be recreated within an hour.');">Delete</a></td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
{% endif %}
<div style="height: 1em;"></div>

<h2>Live Merchants:</h2>
<table>
    <tr><th>Name</th><th>URL</th><th>Max Discount %</th><th>Show Discount (merchant)</th><th>Show Discount (admin)</th><th></th></tr>
    {% for store in live_store_list %}
        <tr class="hover_tr">
            <td>{{store.name}}</td>
            <td>{{store.url}}</td>
            <td>{{store.max_discount}}</td>
            <td>
                <form action="vandra_admin_toggle_merchant_show_discount" method="post">
                    <input type="hidden" name="store_uuid" value="{{store.uuid}}" />
                    {{store.show_discount}} 
                    {% if store.show_discount %}
                        <input type="submit" value="Turn off" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Turn on" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <td>
                <form action="vandra_admin_toggle_admin_show_discount" method="post">
                    <input type="hidden" name="store_uuid" value="{{store.uuid}}" />
                    {{store.vandra_admin_show_discount}} 
                    {% if store.vandra_admin_show_discount %}
                        <input type="submit" value="Turn off" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Turn on" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <td>
                <a href="vandra_admin_store?id={{store.uuid}}">View store</a>
            </td>
        </tr>
    {% endfor %}
</table>

<h2>Inactive Merchants:</h2>
<table>
    <tr><th>Name</th><th>URL</th><th>Max Discount %</th><th>Show Discount (merchant)</th><th>Show Discount (admin)</th><th></th></tr>
    {% for store in inactive_store_list %}
        <tr class="hover_tr">
            <td>{{store.name}}</td>
            <td>{{store.url}}</td>
            <td>{{store.max_discount}}</td>
            <td>
                <form action="vandra_admin_toggle_merchant_show_discount" method="post">
                    <input type="hidden" name="store_uuid" value="{{store.uuid}}" />
                    {{store.show_discount}} 
                    {% if store.show_discount %}
                        <input type="submit" value="Turn off" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Turn on" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <td>
                <form action="vandra_admin_toggle_admin_show_discount" method="post">
                    <input type="hidden" name="store_uuid" value="{{store.uuid}}" />
                    {{store.vandra_admin_show_discount}} 
                    {% if store.vandra_admin_show_discount %}
                        <input type="submit" value="Turn off" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Turn on" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <td>
                <a href="vandra_admin_store?id={{store.uuid}}">View store</a>
            </td>
        </tr>
    {% endfor %}
</table>

<h2>Test stores:</h2>
<table>
    <tr><th>Name</th><th>URL</th><th>Max Discount %</th><th>Show Discount (merchant)</th><th>Show Discount (admin)</th><th></th></tr>
    {% for store in test_store_list %}
        <tr class="hover_tr">
            <td>{{store.name}}</td>
            <td>{{store.url}}</td>
            <td>{{store.max_discount}}</td>
            <td>
                <form action="vandra_admin_toggle_merchant_show_discount" method="post">
                    <input type="hidden" name="store_uuid" value="{{store.uuid}}" />
                    {{store.show_discount}} 
                    {% if store.show_discount %}
                        <input type="submit" value="Turn off" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Turn on" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <td>
                <form action="vandra_admin_toggle_admin_show_discount" method="post">
                    <input type="hidden" name="store_uuid" value="{{store.uuid}}" />
                    {{store.vandra_admin_show_discount}} 
                    {% if store.vandra_admin_show_discount %}
                        <input type="submit" value="Turn off" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Turn on" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <td>
                <a href="vandra_admin_store?id={{store.uuid}}">View analytics</a>
            </td>
        </tr>
    {% endfor %}
</table>