<style>
    table {
        overflow: visible;
        z-index: 1;
    }
    
    tr:hover {
        background-color: rgb(227, 226, 255);
    }

    td, th {
        position: relative;
    }

    .hover_cell:hover::after {
        background-color: rgb(227, 226, 255);
        content: '\00a0';
        height: 10000px;
        left: 0;
        position: absolute;
        top: -5000px;
        width: 100%;
        z-index: -1;
    }
</style>

<a href="vandra_admin">Back home</a>
<h1>Dashboard</h1>
<table>
    <thead>
        <tr>
            <th></th>
            {% for day in day_list %}
                <th class="hover_cell" style="vertical-align: top; padding-right: 10px; font-size: 0.9em;">{{ day }}</th>
            {% endfor %}
        </tr>
    </thead>
    <tbody>
        {% for label in stat_labels %}
            <tr>
                <td>{{label}}</td>
                {% for stat in stat_list %}
                    <td class="hover_cell" style="{{'font-weight: bold;' if stat["summary"] == True}}">{{ stat[label] }}</td>
                {% endfor %}
            </tr>
        {% endfor %}
    </tbody>
</table>