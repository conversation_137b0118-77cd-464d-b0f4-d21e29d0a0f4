<a href="vandra_admin">Back home</a>
<h1>User sessions</h1>
<table>
    <tr>
        <th>uuid</th>
        <th>time</th>
    </tr>
    {% for user_session in user_sessions %}
        <tr>
            <td>{{user_session.uuid}}</td>
            <td>{{user_session.time}}</td>
        </tr>
    {% endfor %}
</table>
<form method="get" action="vandra_admin_user_session">
    <input type="text" name="user_session_uuid" value="" style="width: 300px;" />
    <input type="submit" value="Query by user_session uuid" />
</form>
{% if events %}
    <h1>User session: {{user_session_uuid}}</h1>
    <table>
        <tr>
            <th>time</th>
            <th>session timestamp</th>
            <th>action</th>
        </tr>
        {% for event in events %}
            <tr>
                <td>{{event.time}}</td>
                <td>{{event.session_timestamp}}</td>
                <td>{{event.action}}</td>
            </tr>
        {% endfor %}
    </table>
{% endif %}