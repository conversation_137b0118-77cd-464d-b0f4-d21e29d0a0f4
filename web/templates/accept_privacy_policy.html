{% block head %}
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Vandra - Privacy Policy</title>
    <style>
        #download_button:hover {
            background-color: #111144;
        }
    </style>
    <script type="text/javascript">
        function update_acceptance() {
            if (document.getElementById("accept_checkbox").checked) {
                document.getElementById("accept_button").disabled = false;
            } else {
                document.getElementById("accept_button").disabled = true;
            }
        }
    </script>
{% endblock %}

{% block content %}
    <div style="width: 800px; margin-left: auto; margin-right: auto;">
        <img id="merchant_menu_logo" src="/static/img/logo_big.png" style="width: 120px" alt="Merchant Menu Logo" />
        <h1>Our Privacy Policy</h1>
        Here at Vandra we take the privacy and security of data very seriously, whether the data belongs to our merchants or to your customers. Please take some time to read our privacy policy for more information about how we handle data. You must agree with the policy in order to complete the installation fo the Vandra Shopify app.
        <div style="height: 1em;"></div>
        <a href="/static/privacy_policy.pdf" style="display: block; margin-left: auto; margin-right: auto; width: 250px; text-decoration: none; text-align: center; padding: 10px; background-color: #0a0a73; color: #FFF;">Download Privacy Policy</a>
        <div style="height: 1em;"></div>
        <form action="post_install_account_setup" method="get">
            {% for key in get_variables %}
                <input type="hidden" name="{{ key }}" value="{{ get_variables[key] }}" />
            {% endfor %}
            <input id="accept_checkbox" type="checkbox" onchange="update_acceptance();"> I agree to the Privacy Policy
            <div style="height: 0.5em;"></div>
            <input id="accept_button" type="submit" value="Complete installation" disabled />
        </form>
    </div>
{% endblock %}