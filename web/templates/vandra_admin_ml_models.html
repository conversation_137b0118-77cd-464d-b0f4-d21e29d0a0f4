<link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">

<h1>ML Models</h1>

<h2>Models</h2>
<table>
    <tr>
        <th>UUID</th>
        <th>Creation date</th>
        <th>Time Cutoff</th>
        <th>Filename</th>
        <th>Store Specific Model?</th>
        <th>Test Split Method</th>
        <th>Description</th>
        <th>Run predictions</th>
        <th>Make decisions (live)</th>
        <th></th>
    </tr>
    {% for ml_model in model_version_list %}
        <tr class="hover_tr">
            <td>{{ml_model.uuid}}</td>
            <td>{{ml_model.creation_date}}</td>
            <td>{{ml_model.time_cutoff}}</td>
            <td>{{ml_model.filename}}</td>
            <td>{{ml_model.store_specific_model}}</td>
            <td>{{ml_model.test_split_method}}</td>
            <td>{{ml_model.description}}</td>
            <td>
                <form action="vandra_admin_toggle_run_predictions" method="post">
                    <input type="hidden" name="model_id" value="{{ml_model.uuid}}" />
                    {{ml_model.run_predictions}}
                    {% if ml_model.run_predictions %}
                        <input type="submit" value="Deactivate" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Activate" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <td>
                <form action="vandra_admin_toggle_live_version" method="post">
                    <input type="hidden" name="model_id" value="{{ml_model.uuid}}" />
                    {{ml_model.live_version}}
                    {% if ml_model.live_version %}
                        <input type="submit" value="Deactivate" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Activate" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <th><a href="vandra_admin_ml_model_thresholds?model_id={{ml_model.uuid}}">View thresholds</a></th>
        </tr>
    {% endfor %}
</table>
<h3>Add Model:</h3>
<form action="vandra_admin_ml_models_add" method="post">
    <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 0.5rem;"></div>
        Filename: <input name="filename" type="text" /><br />
        Deployment ID: <input name="deployment_id" type="text" /><br />
        Description: <input name="description" type="text" /><br />
        <input type="submit" value="Submit" />
    </div>
</form>