<link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">

<h1>UI Experiments</h1>

<h2>Exeriments</h2>
<table>
    <tr>
        <th>Name</th>
        <th>Description</th>
        <th>Active Status</th>
        <th>UIs</th>
        <th></th>
    </tr>
    {% for experiment in all_front_end_experiments %}
        <tr class="hover_tr">
            <td>{{experiment.name}}</td>
            <td>{{experiment.description}}</td>
            <td>
                <form action="vandra_admin_experiment_toggle_active" method="post">
                    <input type="hidden" name="toggle_active_experiment_uuid" value="{{experiment.uuid}}" />
                    {{experiment.active}}
                    {% if experiment.active %}
                        <input type="submit" value="Deactivate" style="margin-left: 9px;" />
                    {% else %}
                        <input type="submit" value="Activate" style="margin-left: 9px;" />
                    {% endif %}
                </form>
            </td>
            <td>{{experiment_ui_allocations_dict[experiment.uuid]}}</td>
            <td>
                <form action="vandra_admin_add_ui_to_experiment" method="post">
                    <input type="hidden" name="experiment_id" value="{{experiment.uuid}}" />
                    <select name="ui_id">
                        {% for front_end_ui in all_front_end_uis %}
                            {% if front_end_ui.uuid not in allocation_uis_dict[experiment.uuid] %}
                                <option value="{{front_end_ui.uuid}}">{{front_end_ui.name}}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                    <input type="submit" value="Add UI" style="margin-left: 9px;" />
                </form>
            </td>
            <td><a href="vandra_admin_ui_experiment_analytics?id={{experiment.uuid}}">Analytics</a></td>
        </tr>
    {% endfor %}
</table>
<h3>Add Experiment:</h3>
<form action="vandra_admin_front_end_experiment_add" method="post">
    <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 0.5rem;"></div>
        Name: <input name="add_front_end_ui_name" type="text" value=""/><br />
        Description: <input name="add_front_end_ui_description" type="text" value=""/><br />
        <input type="submit" value="Submit" />
    </div>
</form>

<h2>Front End UIs</h2>
<table>
    <tr>
        <th>Name</th>
        <th>File Name</th>
        <th>Default</th>
    </tr>
    {% for front_end_ui in all_front_end_uis %}
        <tr class="hover_tr">
            <td>{{front_end_ui.name}}</td>
            <td>{{front_end_ui.filename}}</td>
            <td>{{front_end_ui.default}}</td>
        </tr>
    {% endfor %}
</table>
<h3>Add Front End UI:</h3>
<form action="vandra_admin_front_end_ui_add" method="post">
    <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 0.5rem;"></div>
        Name: <input name="add_front_end_ui_name" type="text" value=""/><br />
        Filename: <input name="add_front_end_ui_filename" type="text" value=""/><br />
        <input type="submit" value="Submit" />
    </div>
</form>