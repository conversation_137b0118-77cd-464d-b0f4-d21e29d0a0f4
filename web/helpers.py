from flask import request, session, g
from app import app, db_session
from db import admin_user, admin_user_login_token, discount_code, discount_collection, \
    front_end_experiment_ui_allocation, inference_vm_restart, store_discount, store_url, store_intervention_association, intervention_type
from constants import INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
import base64
import bcrypt
import boto3
import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import hashlib
import hmac
import json
import random
import requests
import shopify
from sqlalchemy import func
import time
import traceback
from urllib.parse import urlencode
import uuid
from communications import Notifier

def send_mail(subject, body, to=["<EMAIL>"], attachment=None, force_send=False):
    """
    Deprecated, please refrain from using this, all future comms should be 
    sent via the Notifier class in the communications module
    """
    if not force_send and app.config.get("FLASK_ENV") != "production":
        return False

    try:
        Notifier.send_raw(subject,body,to,attachment)
    except Exception as e2:
        print(f"error {e2}")
    
    return True

def validate_vandra_admin_credentials(check_email, check_password):
    potential_user = admin_user.query.filter(func.lower(admin_user.email) == check_email.lower()).first()
    if potential_user is None:
        return False
    
    if not bcrypt.checkpw(check_password.encode('utf8'), potential_user.password):
        return False
    
    return True

def check_admin():
    if session.get('vandra_admin_email') is None:
        if request.path != '/' and request.path != '/vandra_admin_login':
            session['vandra_admin_post_login_path'] = request.full_path
        return False
    
    check_email = session.get('vandra_admin_email').lower()
    token_id = session.get('vandra_admin_token_id')

    potential_user = admin_user.query.filter(func.lower(admin_user.email) == check_email).first()
    if potential_user is None:
        session['post_login_path'] = request.full_path
        return False
    
    user_id = potential_user.uuid

    check_token = admin_user_login_token.query.filter_by(admin_user_id=user_id, token=token_id).first()
    if check_token is None:
        session['post_login_path'] = request.full_path
        return False
    
    return True

def check_super_admin():
    if not check_admin():
        return False
    
    check_email = session.get('vandra_admin_email').lower()
    admin_user_record = admin_user.query.filter(func.lower(admin_user.email) == check_email).first()
    if not admin_user_record.super_admin:
        return False
    
    return True

def create_webhook(shopify_object, address, topic):
    new_webhook = shopify_object.Webhook()
    new_webhook.address = address
    new_webhook.format = 'json'
    new_webhook.topic = topic
    new_webhook.save()
    return True

def process_webhook_request():
    data = request.get_data()
    verified = verify_webhook(data, request.headers.get('X-SHOPIFY_HMAC_SHA256', request.headers.get('X-Shopify-Hmac-Sha256')))
    if not verified:
        return False, False
    
    event = request.headers.get('X_SHOPIFY_TOPIC')
    shop_url = request.headers.get('X-SHOPIFY_SHOP_DOMAIN')
    return event, shop_url

def hmac_is_valid(query_dict):
    if "hmac" in query_dict:
        try:
            hmac_from_query_string = query_dict.pop("hmac")
            url_encoded = urlencode(query_dict)
            secret = app.config['SHOPIFY_SHARED_SECRET'].encode('utf-8')
            signature = hmac.new(secret, url_encoded.encode('utf-8'), hashlib.sha256).hexdigest()
            g.shop = query_dict["shop"]
            return hmac.compare_digest(hmac_from_query_string, signature)
        except KeyError as e:
            return False

    elif "hmac_from_query_string" in session:
        return hmac.compare_digest(session["hmac_from_query_string"], session["signature"])

    else:
        return False

def verify_webhook(data, hmac_header):
    digest = hmac.new(app.config['SHOPIFY_SHARED_SECRET'].encode('utf-8'), data, hashlib.sha256).digest()
    computed_hmac = base64.b64encode(digest)
    return hmac.compare_digest(computed_hmac, hmac_header.encode('utf-8'))

# Send AWS a restart request to the inference server when it's not working
def restart_inference_vm():
    import boto3
    
    # Don't send more than 1 restart every 5 minutes
    recent_restart = inference_vm_restart.query.filter(inference_vm_restart.time > time.time() - 300).first()
    if recent_restart is not None:
        return False
    
    client = boto3.client("ec2",
        aws_access_key_id=app.config["EC2_ACCESS_KEY"],
        aws_secret_access_key=app.config["EC2_SECRET"],
        region_name="us-east-2")
    client.reboot_instances(InstanceIds=["i-0a6ea89e5b0dc223c"])
    
    new_restart = inference_vm_restart()
    new_restart.uuid = str(uuid.uuid4())
    new_restart.time = time.time()
    db_session.add(new_restart)
    db_session.commit()
    
    return True

# Called when a discount code is needed and the store does not have
# any discount codes that aren't expired and were created less than
# 24 hours ago
def create_discount(store_record):
    if store_record is None:
        return False

    # Make sure the store is live
    if store_record.access_token in [None, ""]:
        return False
    
    store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
        order_by(store_url.default.desc()).first()
    if store_url_record is None:
        return False
    
    # Expire any existing discounts
    codes_to_expire = discount_code.query.filter_by(store_id=store_record.uuid, expired=False).all()
    for code in codes_to_expire:
        code.expired = True
        db_session.commit()
    
    discount_rates = []
    # Get all the values we need to create a new discount for
    discount_rate_query = store_discount.query.filter_by(store_id=store_record.uuid, active=True).all()
    for discount_rate in discount_rate_query:
        # add discount rate and prefix to the list, default to the store record prefix
        discount_rates.append((discount_rate.discount_value,discount_rate.discount_prefix or store_record.discount_prefix))
    
    if len(discount_rates) == 0:
        discount_rates.append((store_record.max_discount, store_record.discount_prefix))
    
    return_discounts = []
    
    for discount_rate, discount_rate_prefix in discount_rates:
        random_number_string = str(random.randint(0,100000))
        
        #TODO: MOVE ALL THESE SETTINGS TO STORE_INTERVENTION_ASSOCIATION parameters
        customer_gets_dict = {
            "items": {"all": True},
            "appliesOnOneTimePurchase": store_record.discount_one_time,
            "appliesOnSubscription": store_record.discount_subscription,
            "value": {"percentage": float(round(discount_rate/100, 2))}
        }

        #define combinesWith
        combines_with_dict = {
            "shippingDiscounts": True
        }
        #update if there is a store intervention association param for combinesWith
        store_intervention_association_record = store_intervention_association.query.join(
            intervention_type, store_intervention_association.intervention_type_id == intervention_type.uuid
        ).filter(store_intervention_association.store_id == store_record.uuid, intervention_type.name == INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, store_intervention_association.active == True).first()

        if store_intervention_association_record is not None and store_intervention_association_record.parameters is not None:
            #see https://shopify.dev/docs/api/admin-graphql/latest/objects/DiscountCombinesWith for the possible values
            combines_with_dict.update(store_intervention_association_record.parameters.get("combinesWith", {})) 

            # Read Purchase Type Eligibility from parameters if available (for emulation mode compatibility)
            discount_one_time = store_intervention_association_record.parameters.get("discount_one_time", store_record.discount_one_time)
            discount_subscription = store_intervention_association_record.parameters.get("discount_subscription", store_record.discount_subscription)
            
            # Update customer_gets_dict with parameter values
            customer_gets_dict["appliesOnOneTimePurchase"] = discount_one_time
            customer_gets_dict["appliesOnSubscription"] = discount_subscription

        # let's check for explicit filters on discount collections
        collection_records = discount_collection.query.filter_by(store_id=store_record.uuid).all()
        if len(collection_records) > 0:
            collection_id_set = {x.collection_id.lower() for x in collection_records}
            customer_gets_dict["items"]["all"] = False
            customer_gets_dict["items"]["collections"] = {"add": ["gid://shopify/Collection/" + str(x) for x in collection_id_set]}

        ends_at_time = (datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=2)).strftime("%Y-%m-%dT%H:%M:%S")
        ends_at_datetime = datetime.datetime.strptime(ends_at_time, "%Y-%m-%dT%H:%M:%S")
        ends_at_timestamp = int(ends_at_datetime.timestamp())
        graphql_url = "https://" + store_url_record.url + "/admin/api/" + app.config['SHOPIFY_LEGACY_API_VERSION'] + "/graphql.json"
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": store_record.access_token
        }
        query = '''mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {
            discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
                codeDiscountNode {
                id
                }
                userErrors {
                field
                message
                }
            }
        }'''
        variables = {
            "basicCodeDiscount": {
                "appliesOncePerCustomer": True,
                "code": discount_rate_prefix + "-" + random_number_string,
                "combinesWith": combines_with_dict,
                "customerGets": customer_gets_dict,
                "customerSelection": {
                    "all": True
                },
                "endsAt": ends_at_time,
                "recurringCycleLimit": 1,
                "startsAt": (datetime.datetime.now(datetime.timezone.utc)).strftime("%Y-%m-%dT%H:%M:%S"),
                "title": discount_rate_prefix + "-CODE-" + random_number_string,
                "usageLimit": None
            }
        }

        resp = requests.post(graphql_url, headers=headers, json={'query': query, 'variables': variables})

        if "is not permitted without the shop using subscriptions" in resp.text:
            del variables["basicCodeDiscount"]["customerGets"]["appliesOnOneTimePurchase"]
            del variables["basicCodeDiscount"]["customerGets"]["appliesOnSubscription"]
            del variables["basicCodeDiscount"]["recurringCycleLimit"]
            resp = requests.post(graphql_url, headers=headers, json={'query': query, 'variables': variables})
        
        try:
            new_discount_id = resp.json()["data"]["discountCodeBasicCreate"]["codeDiscountNode"]["id"].split("/")[-1]
        except (KeyError, TypeError, json.decoder.JSONDecodeError) as e:
            new_discount_id = None
        
        if new_discount_id is None:
            continue
        
        discount_code_code = ""
        discount_title = ""
        discount_value = None
        if new_discount_id is not None:
            discount_code_code = discount_rate_prefix + "-" + random_number_string
            discount_title = discount_rate_prefix + "-CODE-" + random_number_string
            discount_value = -1 * discount_rate

        new_discount_record = discount_code()
        new_discount_record.uuid = str(uuid.uuid4())
        new_discount_record.time = time.time()
        new_discount_record.store_id = store_record.uuid
        new_discount_record.discount_code_code = discount_code_code
        new_discount_record.ends_at_time = ends_at_timestamp
        new_discount_record.price_rule_id = new_discount_id
        new_discount_record.price_rule_title = discount_title
        new_discount_record.price_rule_value = discount_value
        db_session.add(new_discount_record)
        db_session.commit()
        
        return_discounts.append(new_discount_record)
    
    return return_discounts

def balance_front_end_experiment_allocations(front_end_experiment_id):
    allocations = front_end_experiment_ui_allocation.query.filter_by(front_end_experiment_id=front_end_experiment_id).all()
    num_uis = len(allocations)
    weight_per_ui = 100 / num_uis
    for allocation_record in allocations:
        allocation_record.weight = weight_per_ui
    db_session.commit()
    
    return True