from sqlalchemy import Column, Inte<PERSON>, <PERSON>, Boolean
from sqlalchemy.orm import backref
from database_models.shared import db, default_uuid4
from sqlalchemy_json import NestedMutableJson

class store_intervention_association(db.Model):
    __tablename__ = 'store_intervention_association'
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer)
    intervention_type_id = Column(String, db.ForeignKey('intervention_type.uuid', ondelete='CASCADE'))
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    active = Column(Boolean, default=True)
    parameters = Column(NestedMutableJson)
    #relationships
    store = db.relationship('store', backref=backref('store_intervention_associations', lazy='dynamic', passive_deletes=True))
    intervention_type = db.relationship('intervention_type', backref=backref('store_intervention_associations', lazy='dynamic', passive_deletes=True))
