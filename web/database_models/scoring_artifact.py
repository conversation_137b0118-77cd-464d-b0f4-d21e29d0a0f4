from sqlalchemy import <PERSON>umn, Integer, String
from sqlalchemy_json import NestedMutableJson
from database_models.shared import db, default_uuid4


class scoring_artifact(db.Model):
    """
    Represents a scoring artifact in the database.

    Scoring artifacts are statistical/machine learning models that can produce 
    individual scores, classifications, etc.
    
    Individual modeling artifacts trained for a specific purpose, and likely part of a multi-model decision concept (E.g. uplift)

    Attributes:
        uuid (str): Unique identifier for the scoring artifact.
        time (int): Timestamp or time-related information for the scoring artifact.
        description (str): Description of the scoring artifact.
        component_type (str): Type of the component associated with the scoring artifact. 
            Individual modeling artifacts trained for a specific purpose, and likely part of a multi-model decision concept (E.g. uplift)
        deployment_external_reference (str): External reference for deployment.
        metadata (JSON): Additional metadata related to the scoring artifact.
    """
    __tablename__ = 'scoring_artifact'
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer)
    description = Column(String)
    component_type = Column(String)
    deployment_external_reference = Column(String)
    artifact_metadata = Column(NestedMutableJson)