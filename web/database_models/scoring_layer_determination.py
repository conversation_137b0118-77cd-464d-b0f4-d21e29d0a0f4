from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy_json import NestedMutableJson
from database_models.shared import db, default_uuid4


class ScoringLayerDeterminationMixin:
    """
    A mixin class for the Scoring Layer Determination model.

    Attributes:
        uuid (str): The unique identifier for the scoring layer determination.
        time (int): The timestamp of the scoring layer determination.
        user_session_id (str): The foreign key referencing the user session.
        scoring_layer_id (str): The foreign key referencing the scoring layer.
        scoring_layer_thresholds_id (str): The foreign key referencing the scoring layer thresholds.
        scoring_algorithm_version (str): The version of the scoring algorithm used.
        scoring_layer_scores (JSON): The scores for the scoring layer in JSON format.
        determination (str): The determination result.

    Note:
        This is a partitioned table. Migrations need to be handled manually.
    """
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer, primary_key=True)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'), index=True)
    scoring_layer_id = Column(String, db.ForeignKey('scoring_layer.uuid', ondelete='CASCADE'), index=True)
    scoring_layer_thresholds_id = Column(String, db.ForeignKey('scoring_layer_thresholds.uuid', ondelete='CASCADE'), index=True)
    scoring_algorithm_version = Column(String)
    scoring_layer_scores = Column(NestedMutableJson)
    determination = Column(String)

    

class scoring_layer_determination(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination'
    __table_args__ = {
        "postgresql_partition_by": 'RANGE (time)'
    }

    #relationships
    user_session = db.relationship("user_session", backref=db.backref('scoring_layer_determinations', lazy='dynamic', passive_deletes=True))
    scoring_layer = db.relationship("scoring_layer", backref=db.backref('scoring_layer_determinations', lazy='dynamic', passive_deletes=True))
    scoring_layer_thresholds = db.relationship("scoring_layer_thresholds", backref=db.backref('scoring_layer_determinations', lazy='dynamic', passive_deletes=True))

class scoring_layer_determination_112024(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_112024'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                      db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_122024(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_122024'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                      db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_012025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_012025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_022025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_022025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_032025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_032025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_042025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_042025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_052025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_052025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_062025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_062025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_072025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_072025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_082025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_082025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_092025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_092025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_102025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_102025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_112025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_112025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_122025(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_122025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_012026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_012026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_022026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_022026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_032026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_032026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_042026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_042026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_052026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_052026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_062026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_062026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_072026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_072026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_082026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_082026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_092026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_092026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_102026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_102026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_112026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_112026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )

class scoring_layer_determination_122026(ScoringLayerDeterminationMixin, db.Model):
    __tablename__ = 'scoring_layer_determination_122026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "scoring_layer_id"),
                        db.UniqueConstraint("user_session_id", "scoring_layer_id","time")
    )