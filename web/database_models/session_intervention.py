from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy_json import NestedMutableJson

from sqlalchemy.orm import backref
from database_models.shared import db, default_uuid4
from sqlalchemy import event


class SessionInterventionMixin:
    """
    SessionInterventionMixin is a SQLAlchemy mixin class that defines the schema for a session intervention table.
    This mixin is used to provide a reusable set of columns and constraints for tables that need to store session intervention data.
    Attributes:
        uuid (str): A unique identifier for the session intervention, used as a primary key.
        time (int): The time associated with the session intervention, used as a primary key.
        intervention_type_id (str): A foreign key referencing the intervention type.
        user_session_id (str): A foreign key referencing the user session.
        status (str): The status of the intervention, which can be 'holdout', 'assigned', 'shown', 'dismissed', or 'actioned'.
        holdout (bool): A boolean flag indicating whether the intervention is on holdout, with a default value of False.
    """


    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer, primary_key=True)
    intervention_type_id = Column(String, db.ForeignKey('intervention_type.uuid', ondelete='CASCADE'), index=True)
    store_intervention_association_id = Column(String, db.ForeignKey('store_intervention_association.uuid', ondelete='CASCADE'), index=True)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'), index=True)
    status = Column(String) # holdout/assigned, shown, dismissed, actioned
    holdout = Column(Boolean, default=False)
    opportunity = Column(Boolean, default=False)
    opportunity_label = Column(String) # no_paid_ads, etc.
    decision = Column(String) # show, no show, etc.
    decision_criterion = Column(String) # no_paid_ads, etc.
    parameters = Column(NestedMutableJson)
    scoring_layer_determinations = Column(NestedMutableJson)


    
class session_intervention(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention'
    __table_args__ = {
        "postgresql_partition_by": 'RANGE (time)'
    }

    store = db.relationship('user_session', backref=backref('session_interventions', lazy='dynamic', passive_deletes=True))

class session_intervention_112024(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_112024'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                      db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )
class session_intervention_122024(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_122024'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                      db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )
    
class session_intervention_012025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_012025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_022025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_022025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_032025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_032025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_042025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_042025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_052025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_052025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_062025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_062025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_072025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_072025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_082025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_082025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_092025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_092025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_102025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_102025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_112025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_112025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_122025(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_122025'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_012026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_012026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_022026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_022026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_032026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_032026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_042026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_042026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_052026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_052026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_062026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_062026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_072026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_072026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_082026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_082026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_092026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_092026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_102026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_102026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_112026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_112026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )

class session_intervention_122026(SessionInterventionMixin, db.Model):
    __tablename__ = 'session_intervention_122026'
    __table_args__ = (db.UniqueConstraint("user_session_id", "intervention_type_id"),
                        db.UniqueConstraint("user_session_id", "intervention_type_id","time")
    )