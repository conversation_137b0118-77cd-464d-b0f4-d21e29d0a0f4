from sqlalchemy import Column, Integer, String, Numeric
from database_models.shared import db, default_uuid4
from sqlalchemy.orm import backref
from sqlalchemy_json import NestedMutableJson

class session_intervention_event(db.Model):
    __tablename__ = 'session_intervention_event'
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer)
    session_intervention_id = Column(String, db.ForeignKey('session_intervention.uuid', ondelete='CASCADE'))
    event = Column(String)
    page_view_id = Column(String, db.ForeignKey('page_view.uuid', ondelete='CASCADE'))
    dwell_time = Column(Numeric)
    focused_dwell_time = Column(Numeric)
    event_metadata = Column(NestedMutableJson)
    
    session_intervention = db.relationship('session_intervention', backref=backref('session_intervention_events', passive_deletes=True))
    page_view = db.relationship('page_view', backref=backref('session_intervention_events', passive_deletes=True))
