from sqlalchemy import Column, Inte<PERSON>, String, Bo<PERSON>an, Float
from database_models.shared import db, default_uuid4
from sqlalchemy.orm import backref
from sqlalchemy_json import NestedMutableJson

class session_discount_code(db.Model):
    __tablename__ = 'session_discount_code'
    uuid = Column(String, primary_key=True, index=True, default=default_uuid4)
    time = Column(Integer)
    user_session_id = Column(String, db.ForeignKey('user_session.uuid', ondelete='CASCADE'))
    discount_code_value = Column(String)
    first_detected_in = Column(String)  # cookie, cart, checkout
    last_detected_in = Column(String)   # cookie, cart, checkout
    first_detected_time = Column(Integer)
    last_detected_time = Column(Integer)
    
    # New discount details fields
    discount_method = Column(String)  # percentage, amount, bxgy, free_shipping
    discount_value = Column(Float)    # percentage or amount value
    restrictions_apply = Column(Boolean, default=False)  # True if any restrictions exist
    starts_at = Column(Integer)  # Unix timestamp for when discount becomes valid
    ends_at = Column(Integer)    # Unix timestamp for when discount expires
    combines_with_order_discounts = Column(Boolean)  # Whether discount combines with order discounts
    discount_code_metadata = Column(NestedMutableJson)
    
    user_session = db.relationship('user_session', backref=backref('session_discount_codes', passive_deletes=True)) 