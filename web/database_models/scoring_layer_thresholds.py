from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>
from database_models.shared import db, default_uuid4
from sqlalchemy_json import NestedMutableJson


class scoring_layer_thresholds(db.Model):
    """
    A SQLAlchemy model representing the scoring layer thresholds.

    Attributes:
        uuid (str): The unique identifier for the scoring layer threshold.
        time (int): The time associated with the scoring layer threshold.
        store_id (str): The foreign key referencing the store's unique identifier.
        scoring_layer_id (str): The foreign key referencing the scoring layer's unique identifier.
        active (bool): A flag indicating whether the scoring layer threshold is active. Defaults to True.
        scoring_artifact_thresholds (dict): A JSON field containing the scoring artifact thresholds.
    """
    __tablename__ = 'scoring_layer_thresholds'
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    scoring_layer_id = Column(String, db.ForeignKey('scoring_layer.uuid', ondelete='CASCADE'))
    active = Column(Boolean, default=True)
    scoring_artifact_thresholds = Column(NestedMutableJson)
    #relationships
    store = db.relationship('store', backref=db.backref('scoring_layer_thresholds', lazy='dynamic', passive_deletes=True))
    scoring_layer = db.relationship('scoring_layer', backref=db.backref('scoring_layer_thresholds', lazy='dynamic', passive_deletes=True))
    threshold_methodology = Column(String)
