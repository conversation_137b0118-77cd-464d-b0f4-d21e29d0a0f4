from sqlalchemy import Column, String, ForeignKey
from sqlalchemy_json import NestedMutableJson
from database_models.shared import db, default_uuid4

class store_intervention_association_assets(db.Model):
    """
    Represents an asset associated with a store intervention association.
    
    Columns:
        uuid (str): Primary key.
        store_intervention_association_id (str): Foreign key to the store intervention association.
        asset_key (str): Key for the asset.
        asset_url (str): URL for the asset.
        asset_type (str): Type or category of the asset.
        asset_metadata (JSON): Additional metadata for the asset.
    """
    __tablename__ = 'store_intervention_association_assets'
    
    uuid = Column(String, primary_key=True, index=True, default=default_uuid4)
    store_intervention_association_id = Column(String, db.ForeignKey('store_intervention_association.uuid', ondelete='CASCADE'))
    asset_key = Column(String)
    asset_url = Column(String)
    asset_type = Column(String, nullable=True)
    asset_metadata = Column(NestedMutableJson)

    def to_dict(self):
        """Convert the asset record to a dictionary."""
        return {
            'uuid': self.uuid,
            'store_intervention_association_id': self.store_intervention_association_id,
            'asset_key': self.asset_key,
            'asset_url': self.asset_url,
            'asset_type': self.asset_type,
            'asset_metadata': self.asset_metadata
        } 