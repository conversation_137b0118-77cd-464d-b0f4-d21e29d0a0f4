from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Boolean
from sqlalchemy_json import NestedMutableJson
from database_models.shared import db, default_uuid4


class scoring_layer(db.Model):
    """
    Represents a scoring layer in the database, capturing the relationship of 
    individual scoring artifacts used to make certain determinations, 
    such as whether to show a discount.

    Attributes:
        uuid (str): Unique identifier for the scoring layer.
        time (int): Time associated with the scoring layer.
        description (str): Description of the scoring layer.
        active (bool): Indicates if the scoring layer is active. Defaults to True.
        store_specific (bool): Indicates if the scoring layer is store-specific. Defaults to False.
        run_predictions (bool): Indicates if predictions should be run. Defaults to False.
        live_decisioning (bool): Indicates if live decisioning is enabled. Defaults to False.
        scoring_algorithm_type (str): Describes the overall class/implementation name in the code base (e.g., uplift, intent, exit_intent, etc.). Can be used by the code when fetching the relevant "scoring layer" for any given session and intervention (e.g., uplift).
        scoring_artifacts (JSON): JSON field to store scoring artifacts and its ids.
    """
    __tablename__ = 'scoring_layer'
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer)
    description = Column(String)
    active = Column(Boolean, default=True)
    store_specific = Column(Boolean, default=False)
    run_predictions = Column(Boolean, default=False)
    live_decisioning = Column(Boolean, default=False)
    scoring_algorithm_type = Column(String)
    scoring_artifacts = Column(NestedMutableJson)
    scoring_time_cutoff = Column(Integer)
