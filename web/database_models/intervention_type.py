from sqlalchemy import Column, Integer, String, Boolean
import uuid
from database_models.shared import db, default_uuid4




class intervention_type(db.Model):
    __tablename__ = 'intervention_type'
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer)
    name = Column(String)
    title = Column(String)
    description = Column(String)
    active = Column(Boolean, default=True)
