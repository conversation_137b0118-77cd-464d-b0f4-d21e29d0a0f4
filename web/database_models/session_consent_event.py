from sqlalchemy import Column, Integer, String
from database_models.shared import db, default_uuid4
from sqlalchemy.orm import backref




class session_consent_event(db.Model):
    __tablename__ = 'session_consent_event'
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer)
    session_cookie = Column(String, index=True)
    customer_cookie = Column(String, index=True)
    consent_state = Column(String)
    marketing_consent = Column(String)
    analytics_consent = Column(String)
    sale_of_data_consent = Column(String)
    preferences_consent = Column(String)
    store_id = Column(String, db.ForeignKey('store.uuid', ondelete='CASCADE'))
    #relationship    
    store = db.relationship('store', backref=backref('store_session_consent_events', passive_deletes=True))
