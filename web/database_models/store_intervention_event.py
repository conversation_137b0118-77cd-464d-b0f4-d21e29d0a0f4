from sqlalchemy import Column, Integer, String
from database_models.shared import db, default_uuid4


class store_intervention_event(db.Model):
    """
    Represents an event related to a store intervention. For example, marked active/inactive

    Attributes:
        uuid (str): Unique identifier for the event.
        time (int): Timestamp of the event.
        store_intervention_association_id (str): Foreign key referencing the associated store intervention.
        event (str): Description or type of the event.
    """
    __tablename__ = 'store_intervention_event'
    uuid = Column(String, primary_key=True, index=True,default=default_uuid4)
    time = Column(Integer)
    store_intervention_association_id = Column(String, db.<PERSON><PERSON>ey('store_intervention_association.uuid', ondelete='CASCADE'))
    event = Column(String)