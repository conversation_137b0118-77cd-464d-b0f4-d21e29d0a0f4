"""Adding consent table

Revision ID: 0a70e0ab65ea
Revises: c52b341fcf87
Create Date: 2024-12-11 12:01:56.758599

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0a70e0ab65ea'
down_revision = 'c52b341fcf87'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('session_consent_event',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('session_cookie', sa.String(), nullable=True),
    sa.Column('customer_cookie', sa.String(), nullable=True),
    sa.Column('consent_state', sa.String(), nullable=True),
    sa.Column('marketing_consent', sa.String(), nullable=True),
    sa.Column('analytics_consent', sa.String(), nullable=True),
    sa.Column('sale_of_data_consent', sa.String(), nullable=True),
    sa.Column('preferences_consent', sa.String(), nullable=True),
    sa.<PERSON><PERSON>eyConstraint('uuid')
    )
    with op.batch_alter_table('session_consent_event', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_consent_event_customer_cookie'), ['customer_cookie'], unique=False)
        batch_op.create_index(batch_op.f('ix_session_consent_event_session_cookie'), ['session_cookie'], unique=False)
        batch_op.create_index(batch_op.f('ix_session_consent_event_uuid'), ['uuid'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('session_consent_event', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_consent_event_uuid'))
        batch_op.drop_index(batch_op.f('ix_session_consent_event_session_cookie'))
        batch_op.drop_index(batch_op.f('ix_session_consent_event_customer_cookie'))

    op.drop_table('session_consent_event')
    # ### end Alembic commands ###
