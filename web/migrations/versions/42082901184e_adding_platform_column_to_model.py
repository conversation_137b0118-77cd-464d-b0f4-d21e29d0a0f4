"""Adding platform column to model

Revision ID: 42082901184e
Revises: 561609267d27
Create Date: 2024-08-20 12:37:56.941225

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '42082901184e'
down_revision = '561609267d27'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('model_version', schema=None) as batch_op:
        batch_op.add_column(sa.Column('platform', sa.String(), nullable=True))


    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('model_version', schema=None) as batch_op:
        batch_op.drop_column('platform')

    # ### end Alembic commands ###
