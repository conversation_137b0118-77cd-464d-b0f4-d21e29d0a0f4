"""use meta ad pixel for store

Revision ID: 1030b5d277c7
Revises: c84ae2b3f68f
Create Date: 2024-10-11 13:14:07.471414

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1030b5d277c7'
down_revision = 'c84ae2b3f68f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('store', schema=None) as batch_op:
        batch_op.add_column(sa.Column('use_meta_ad_pixel', sa.<PERSON>an(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('store', schema=None) as batch_op:
        batch_op.drop_column('use_meta_ad_pixel')

    # ### end Alembic commands ###
