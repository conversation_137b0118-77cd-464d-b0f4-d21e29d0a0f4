"""Column

Revision ID: 2e4b83e772b8
Revises: 6aba70b9b5e6
Create Date: 2024-12-17 20:15:10.501105

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2e4b83e772b8'
down_revision = '6aba70b9b5e6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('scoring_layer', schema=None) as batch_op:
        batch_op.add_column(sa.Column('scoring_time_cutoff', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    with op.batch_alter_table('scoring_layer', schema=None) as batch_op:
        batch_op.drop_column('scoring_time_cutoff')

    # ### end Alembic commands ###
