"""add nudge parameters columns for trigger state and action

Revision ID: b681b2bc6104
Revises: 52e092c46d3a
Create Date: 2024-08-23 11:15:41.996413

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b681b2bc6104'
down_revision = '52e092c46d3a' #this is a revision currently in prod
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_session_nudge_parameters', schema=None) as batch_op:
        batch_op.add_column(sa.Column('trigger_state', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('trigger_action', sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_session_nudge_parameters', schema=None) as batch_op:
        batch_op.drop_column('trigger_action')
        batch_op.drop_column('trigger_state')

    # ### end Alembic commands ###
