"""Initial schema

Revision ID: 361a3f17825c
Revises: 
Create Date: 2024-05-24 10:35:18.780143

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '361a3f17825c'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('admin_user',
    sa.<PERSON>umn('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('password', sa.LargeBinary(), nullable=True),
    sa.Column('super_admin', sa.<PERSON>(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('front_end_experiment',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('active', sa.<PERSON>(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('store_specific', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('front_end_ui',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('default', sa.Boolean(), nullable=True),
    sa.Column('filename', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('inference_vm_restart',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('ip2location',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('ip_from', sa.BigInteger(), nullable=True),
    sa.Column('ip_to', sa.BigInteger(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('country_name', sa.String(), nullable=True),
    sa.Column('region_name', sa.String(), nullable=True),
    sa.Column('city_name', sa.String(), nullable=True),
    sa.Column('latitude', sa.Numeric(), nullable=True),
    sa.Column('longitude', sa.Numeric(), nullable=True),
    sa.Column('zip_code', sa.String(), nullable=True),
    sa.Column('time_zone', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('logged_error',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('email_sent', sa.Boolean(), nullable=True),
    sa.Column('error_text', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('model_version',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('decision_threshold', sa.Numeric(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('deployment_id', sa.String(), nullable=True),
    sa.Column('filename', sa.String(), nullable=True),
    sa.Column('h2o_experiment_id', sa.String(), nullable=True),
    sa.Column('holdout_percent', sa.Numeric(), nullable=True),
    sa.Column('live_version', sa.Boolean(), nullable=True),
    sa.Column('min_decision_threshold', sa.Numeric(), nullable=True),
    sa.Column('min_user_session_time_processed', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('store_specific_model', sa.Boolean(), nullable=True),
    sa.Column('run_predictions', sa.Boolean(), nullable=True),
    sa.Column('test_split_method', sa.String(), nullable=True),
    sa.Column('time_cutoff', sa.Integer(), nullable=True),
    sa.Column('train_end_time', sa.Integer(), nullable=True),
    sa.Column('train_start_time', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('os_version_time',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('os', sa.String(), nullable=True),
    sa.Column('os_version', sa.String(), nullable=True),
    sa.Column('release_time', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('store',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('access_token', sa.String(), nullable=True),
    sa.Column('active_store', sa.Boolean(), nullable=True),
    sa.Column('address1', sa.String(), nullable=True),
    sa.Column('address2', sa.String(), nullable=True),
    sa.Column('auto_apply_text_body', sa.String(), nullable=True),
    sa.Column('charge_id', sa.String(), nullable=True),
    sa.Column('city', sa.String(), nullable=True),
    sa.Column('commission_rate', sa.Numeric(), nullable=True),
    sa.Column('discount_prefix', sa.String(), nullable=True),
    sa.Column('discount_one_time', sa.Boolean(), nullable=True),
    sa.Column('discount_subscription', sa.Boolean(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('had_first_visit', sa.Boolean(), nullable=True),
    sa.Column('popup_image_url', sa.String(), nullable=True),
    sa.Column('last_exclusion_sync', sa.Integer(), nullable=True),
    sa.Column('last_exclusion_sync_end_cursor', sa.String(), nullable=True),
    sa.Column('last_order_sync', sa.Integer(), nullable=True),
    sa.Column('last_webhook_check', sa.Integer(), nullable=True),
    sa.Column('max_discount', sa.Numeric(), nullable=True),
    sa.Column('minimized_text_header', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('popup_bg_color', sa.String(), nullable=True),
    sa.Column('popup_font', sa.String(), nullable=True),
    sa.Column('popup_primary_color', sa.String(), nullable=True),
    sa.Column('popup_text_header', sa.String(), nullable=True),
    sa.Column('popup_text_body', sa.String(), nullable=True),
    sa.Column('popup_text_button', sa.String(), nullable=True),
    sa.Column('popup_text_button_close', sa.String(), nullable=True),
    sa.Column('popup_text_success', sa.String(), nullable=True),
    sa.Column('shop_owner', sa.String(), nullable=True),
    sa.Column('show_discount', sa.Boolean(), nullable=True),
    sa.Column('show_discount_when_logged_in', sa.Boolean(), nullable=True),
    sa.Column('show_discount_only_to_paid', sa.Boolean(), nullable=True),
    sa.Column('show_discount_to_previous_customers', sa.Boolean(), nullable=True),
    sa.Column('state', sa.String(), nullable=True),
    sa.Column('test_store', sa.Boolean(), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.Column('use_in_datascience', sa.Boolean(), nullable=True),
    sa.Column('vandra_admin_show_discount', sa.Boolean(), nullable=True),
    sa.Column('zip', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('webhook_request',
    sa.Column('uuid', sa.String(length=36), nullable=False),
    sa.Column('time', sa.BigInteger(), nullable=True),
    sa.Column('data', sa.String(), nullable=True),
    sa.Column('event', sa.String(length=200), nullable=True),
    sa.Column('shop_url', sa.String(length=200), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('admin_user_login_token',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('admin_user_id', sa.String(), nullable=True),
    sa.Column('token', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['admin_user_id'], ['admin_user.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('daily_stat',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('analytics_actionable_audience_conversions', sa.Integer(), nullable=True),
    sa.Column('analytics_actionable_audience_revenue', sa.Numeric(), nullable=True),
    sa.Column('analytics_actionable_audience_sessions', sa.Integer(), nullable=True),
    sa.Column('analytics_protected_audience_conversions', sa.Integer(), nullable=True),
    sa.Column('analytics_protected_audience_revenue', sa.Numeric(), nullable=True),
    sa.Column('analytics_protected_audience_sessions', sa.Integer(), nullable=True),
    sa.Column('analytics_total_audience_conversions', sa.Integer(), nullable=True),
    sa.Column('analytics_total_audience_revenue', sa.Numeric(), nullable=True),
    sa.Column('analytics_total_audience_sessions', sa.Integer(), nullable=True),
    sa.Column('conversions', sa.Integer(), nullable=True),
    sa.Column('conversions_before_predictions', sa.Integer(), nullable=True),
    sa.Column('conversions_before_predictions_dollars', sa.Numeric(), nullable=True),
    sa.Column('conversions_dollars', sa.Numeric(), nullable=True),
    sa.Column('conversions_vandra_show', sa.Integer(), nullable=True),
    sa.Column('conversions_vandra_show_dollars', sa.Numeric(), nullable=True),
    sa.Column('end_time', sa.Integer(), nullable=True),
    sa.Column('holdout_sessions', sa.Integer(), nullable=True),
    sa.Column('holdout_sessions_with_show_predictions', sa.Integer(), nullable=True),
    sa.Column('holdout_session_conversions', sa.Integer(), nullable=True),
    sa.Column('holdout_session_conversions_dollars', sa.Numeric(), nullable=True),
    sa.Column('holdout_session_conversions_vandra_show', sa.Integer(), nullable=True),
    sa.Column('holdout_session_conversions_vandra_show_dollars', sa.Numeric(), nullable=True),
    sa.Column('merchant_admin_engagements', sa.Integer(), nullable=True),
    sa.Column('opportunity_session_conversions', sa.Integer(), nullable=True),
    sa.Column('opportunity_session_conversions_dollars', sa.Numeric(), nullable=True),
    sa.Column('opportunity_session_conversions_before_predictions', sa.Integer(), nullable=True),
    sa.Column('opportunity_session_conversions_before_predictions_dollars', sa.Numeric(), nullable=True),
    sa.Column('opportunity_session_conversions_vandra_show', sa.Integer(), nullable=True),
    sa.Column('opportunity_session_conversions_vandra_show_dollars', sa.Numeric(), nullable=True),
    sa.Column('opportunity_sessions', sa.Integer(), nullable=True),
    sa.Column('opportunity_sessions_with_predictions', sa.Integer(), nullable=True),
    sa.Column('opportunity_sessions_with_show_predictions', sa.Integer(), nullable=True),
    sa.Column('popups_applied', sa.Integer(), nullable=True),
    sa.Column('popups_dismissed', sa.Integer(), nullable=True),
    sa.Column('popups_shown', sa.Integer(), nullable=True),
    sa.Column('sessions', sa.Integer(), nullable=True),
    sa.Column('sessions_with_predictions', sa.Integer(), nullable=True),
    sa.Column('sessions_with_show_predictions', sa.Integer(), nullable=True),
    sa.Column('start_time', sa.Integer(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('vandra_conversions', sa.Integer(), nullable=True),
    sa.Column('vandra_conversions_dollars', sa.Numeric(), nullable=True),
    sa.Column('vandra_conversions_not_shown_that_session', sa.Integer(), nullable=True),
    sa.Column('vandra_conversions_not_shown_that_session_dollars', sa.Numeric(), nullable=True),
    sa.Column('vandra_conversions_not_shown_any_session', sa.Integer(), nullable=True),
    sa.Column('vandra_conversions_not_shown_any_session_dollars', sa.Numeric(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('discount_code',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('discount_code_code', sa.String(), nullable=True),
    sa.Column('ends_at_time', sa.Integer(), nullable=True),
    sa.Column('expired', sa.Boolean(), nullable=True),
    sa.Column('price_rule_id', sa.String(), nullable=True),
    sa.Column('price_rule_title', sa.String(), nullable=True),
    sa.Column('price_rule_value', sa.Numeric(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('discount_collection',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('exclude_tag',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('tag_name', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('exclude_url',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('front_end_experiment_log',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('front_end_experiment_id', sa.String(), nullable=True),
    sa.Column('start_time', sa.Integer(), nullable=True),
    sa.Column('end_time', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['front_end_experiment_id'], ['front_end_experiment.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('front_end_experiment_store_association',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('front_end_experiment_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['front_end_experiment_id'], ['front_end_experiment.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('front_end_experiment_ui_allocation',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('front_end_ui_id', sa.String(), nullable=True),
    sa.Column('front_end_experiment_id', sa.String(), nullable=True),
    sa.Column('weight', sa.Numeric(), nullable=True),
    sa.ForeignKeyConstraint(['front_end_experiment_id'], ['front_end_experiment.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['front_end_ui_id'], ['front_end_ui.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('include_collection',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('shopify_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('merchant_engagement',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('engagement', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('model_state',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('merchant_enabled', sa.Boolean(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('vandra_admin_enabled', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('model_store_holdout',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('holdout_percent', sa.Numeric(), nullable=True),
    sa.Column('model_version_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['model_version_id'], ['model_version.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('model_store_threshold',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('cannibalization_rate_target', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('decision_threshold', sa.Numeric(), nullable=True),
    sa.Column('expected_cannibalization_rate', sa.Numeric(), nullable=True),
    sa.Column('expected_discount_offer_rate', sa.Numeric(), nullable=True),
    sa.Column('min_decision_threshold', sa.Numeric(), nullable=True),
    sa.Column('model_version_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('test_more_restrictive', sa.Boolean(), nullable=True),
    sa.Column('total_test_set_count', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['model_version_id'], ['model_version.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('order',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('cart_token', sa.String(), nullable=True),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.Integer(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('discount_code', sa.String(), nullable=True),
    sa.Column('session_id', sa.String(), nullable=True),
    sa.Column('shopify_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('subtotal_price', sa.Numeric(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('product',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('product_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('store_discount',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('discount_value', sa.Numeric(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('store_synced_exclusion_collection',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('collection_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('store_url',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('default', sa.Boolean(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('usage_charge',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('end_time', sa.Integer(), nullable=True),
    sa.Column('price', sa.Numeric(), nullable=True),
    sa.Column('shopify_id', sa.String(), nullable=True),
    sa.Column('start_time', sa.Integer(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('submitted_time', sa.Integer(), nullable=True),
    sa.Column('submitted_to_shopify', sa.Boolean(), nullable=True),
    sa.Column('total_sales', sa.Numeric(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('user_session',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('ad_bing', sa.Boolean(), nullable=True),
    sa.Column('ad_doubleclick', sa.Boolean(), nullable=True),
    sa.Column('ad_facebook', sa.Boolean(), nullable=True),
    sa.Column('ad_google', sa.Boolean(), nullable=True),
    sa.Column('ad_tiktok', sa.Boolean(), nullable=True),
    sa.Column('browser', sa.String(), nullable=True),
    sa.Column('browser_version', sa.String(), nullable=True),
    sa.Column('cart_size', sa.Numeric(), nullable=True),
    sa.Column('cart_token', sa.String(), nullable=True),
    sa.Column('conversion', sa.Boolean(), nullable=True),
    sa.Column('conversion_discount_code', sa.String(), nullable=True),
    sa.Column('conversion_discount_amount', sa.Numeric(), nullable=True),
    sa.Column('countdown_deadline', sa.Numeric(), nullable=True),
    sa.Column('customer_cookie', sa.String(), nullable=True),
    sa.Column('device', sa.String(), nullable=True),
    sa.Column('device_memory', sa.Numeric(), nullable=True),
    sa.Column('domain', sa.String(), nullable=True),
    sa.Column('front_end_ui_id', sa.String(), nullable=True),
    sa.Column('front_end_experiment_id', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('country_name', sa.String(), nullable=True),
    sa.Column('region_name', sa.String(), nullable=True),
    sa.Column('zip_code', sa.String(), nullable=True),
    sa.Column('language', sa.String(), nullable=True),
    sa.Column('latitude', sa.Numeric(), nullable=True),
    sa.Column('local_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('local_timezone', sa.String(), nullable=True),
    sa.Column('longitude', sa.Numeric(), nullable=True),
    sa.Column('mobile', sa.Boolean(), nullable=True),
    sa.Column('no_vandra_cookie', sa.Boolean(), nullable=True),
    sa.Column('order_id', sa.String(), nullable=True),
    sa.Column('os', sa.String(), nullable=True),
    sa.Column('os_version', sa.String(), nullable=True),
    sa.Column('page', sa.String(), nullable=True),
    sa.Column('price', sa.String(), nullable=True),
    sa.Column('referrer', sa.String(), nullable=True),
    sa.Column('screen_height', sa.Integer(), nullable=True),
    sa.Column('screen_width', sa.Integer(), nullable=True),
    sa.Column('session_cookie', sa.String(), nullable=True),
    sa.Column('shopify_customer_id', sa.Integer(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('utm_campaign', sa.String(), nullable=True),
    sa.Column('utm_content', sa.String(), nullable=True),
    sa.Column('utm_medium', sa.String(), nullable=True),
    sa.Column('utm_source', sa.String(), nullable=True),
    sa.Column('utm_term', sa.String(), nullable=True),
    sa.Column('vandra_conversion', sa.Boolean(), nullable=True),
    sa.Column('vandra_discount_code_assigned', sa.String(), nullable=True),
    sa.Column('vandra_discount_rate_assigned', sa.Numeric(), nullable=True),
    sa.Column('vandra_dismissed', sa.Boolean(), nullable=True),
    sa.Column('vandra_reopened', sa.Boolean(), nullable=True),
    sa.Column('vandra_discount_applied', sa.Boolean(), nullable=True),
    sa.Column('vandra_discount_toasted', sa.Boolean(), nullable=True),
    sa.Column('vandra_opportunity', sa.Boolean(), nullable=True),
    sa.Column('vandra_shown', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['front_end_experiment_id'], ['front_end_experiment.uuid'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['front_end_ui_id'], ['front_end_ui.uuid'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('cart',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('cart_token', sa.String(), nullable=True),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('cart_change_type', sa.String(), nullable=True),
    sa.Column('cart_items', sa.JSON(), nullable=True),
    sa.Column('checkout_charge_amount', sa.Numeric(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('item_count', sa.Integer(), nullable=True),
    sa.Column('items_subtotal_price', sa.Numeric(), nullable=True),
    sa.Column('note', sa.String(), nullable=True),
    sa.Column('original_total_price', sa.Numeric(), nullable=True),
    sa.Column('requires_shipping', sa.Boolean(), nullable=True),
    sa.Column('taxes_included', sa.Boolean(), nullable=True),
    sa.Column('total_discount', sa.Numeric(), nullable=True),
    sa.Column('total_price', sa.Numeric(), nullable=True),
    sa.Column('total_weight', sa.Numeric(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('cart_token',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('cart_token', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('checkout_discount',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('discount_code', sa.String(), nullable=True),
    sa.Column('discount_amount', sa.Numeric(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('model_decision',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('conversion_1_probability', sa.Numeric(), nullable=True),
    sa.Column('decided_to_show_discount', sa.Boolean(), nullable=True),
    sa.Column('discount_code_id', sa.String(), nullable=True),
    sa.Column('live_version', sa.Boolean(), nullable=True),
    sa.Column('model_version_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['model_version_id'], ['model_version.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('page_view',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('document_has_focus', sa.Boolean(), nullable=True),
    sa.Column('dwell_time', sa.Numeric(), nullable=True),
    sa.Column('front_end_id', sa.String(), nullable=True),
    sa.Column('local_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('local_timezone', sa.String(), nullable=True),
    sa.Column('page', sa.String(), nullable=True),
    sa.Column('total_mouse_move', sa.Integer(), nullable=True),
    sa.Column('total_scroll', sa.Integer(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('user_session_event',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('action', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('click',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('base_uri', sa.String(), nullable=True),
    sa.Column('children_count', sa.Integer(), nullable=True),
    sa.Column('class_name', sa.String(), nullable=True),
    sa.Column('client_width', sa.Integer(), nullable=True),
    sa.Column('client_height', sa.Integer(), nullable=True),
    sa.Column('client_top', sa.Integer(), nullable=True),
    sa.Column('client_left', sa.Integer(), nullable=True),
    sa.Column('element_id', sa.String(), nullable=True),
    sa.Column('first_child_name', sa.String(), nullable=True),
    sa.Column('inner_text', sa.String(), nullable=True),
    sa.Column('last_child_name', sa.String(), nullable=True),
    sa.Column('next_sibling_name', sa.String(), nullable=True),
    sa.Column('offset_width', sa.Integer(), nullable=True),
    sa.Column('offset_height', sa.Integer(), nullable=True),
    sa.Column('page_view_id', sa.String(), nullable=True),
    sa.Column('parent_node_name', sa.String(), nullable=True),
    sa.Column('previous_sibling_name', sa.String(), nullable=True),
    sa.Column('style', sa.String(), nullable=True),
    sa.Column('tag_name', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['page_view_id'], ['page_view.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('page_activity',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('dwell_time', sa.Numeric(), nullable=True),
    sa.Column('page_view_id', sa.String(), nullable=True),
    sa.Column('mouse_move', sa.Integer(), nullable=True),
    sa.Column('scroll', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['page_view_id'], ['page_view.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('page_focus_change',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('page_view_id', sa.String(), nullable=True),
    sa.Column('coming_into_focus', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['page_view_id'], ['page_view.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('page_keystrokes',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('keys', sa.String(), nullable=True),
    sa.Column('page_view_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['page_view_id'], ['page_view.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    op.create_table('search',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('page_view_id', sa.String(), nullable=True),
    sa.Column('search_query', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['page_view_id'], ['page_view.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('search')
    op.drop_table('page_keystrokes')
    op.drop_table('page_focus_change')
    op.drop_table('page_activity')
    op.drop_table('click')
    op.drop_table('user_session_event')
    op.drop_table('page_view')
    op.drop_table('model_decision')
    op.drop_table('checkout_discount')
    op.drop_table('cart_token')
    op.drop_table('cart')
    op.drop_table('user_session')
    op.drop_table('usage_charge')
    op.drop_table('store_url')
    op.drop_table('store_synced_exclusion_collection')
    op.drop_table('store_discount')
    op.drop_table('product')
    op.drop_table('order')
    op.drop_table('model_store_threshold')
    op.drop_table('model_store_holdout')
    op.drop_table('model_state')
    op.drop_table('merchant_engagement')
    op.drop_table('include_collection')
    op.drop_table('front_end_experiment_ui_allocation')
    op.drop_table('front_end_experiment_store_association')
    op.drop_table('front_end_experiment_log')
    op.drop_table('exclude_url')
    op.drop_table('exclude_tag')
    op.drop_table('discount_collection')
    op.drop_table('discount_code')
    op.drop_table('daily_stat')
    op.drop_table('admin_user_login_token')
    op.drop_table('webhook_request')
    op.drop_table('store')
    op.drop_table('os_version_time')
    op.drop_table('model_version')
    op.drop_table('logged_error')
    op.drop_table('ip2location')
    op.drop_table('inference_vm_restart')
    op.drop_table('front_end_ui')
    op.drop_table('front_end_experiment')
    op.drop_table('admin_user')
    # ### end Alembic commands ###
