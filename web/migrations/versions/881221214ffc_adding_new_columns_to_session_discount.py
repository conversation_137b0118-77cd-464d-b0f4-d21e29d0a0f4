"""Adding new columns to session discount

Revision ID: 881221214ffc
Revises: 7b8dadd217ff
Create Date: 2025-02-26 11:59:22.911078

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '881221214ffc'
down_revision = '7b8dadd217ff'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('session_discount_code', schema=None) as batch_op:
        batch_op.add_column(sa.Column('discount_method', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('discount_value', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('restrictions_apply', sa.<PERSON>(), nullable=True))
        batch_op.add_column(sa.Column('starts_at', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('ends_at', sa.Integer(), nullable=True))


    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('session_discount_code', schema=None) as batch_op:
        batch_op.drop_column('ends_at')
        batch_op.drop_column('starts_at')
        batch_op.drop_column('restrictions_apply')
        batch_op.drop_column('discount_value')
        batch_op.drop_column('discount_method')

    # ### end Alembic commands ###
