"""Adding focused dwell time columns

Revision ID: 216f72a58adc
Revises: a21b82767542
Create Date: 2025-03-11 18:16:13.939827

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '216f72a58adc'
down_revision = 'a21b82767542'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('page_activity', schema=None) as batch_op:
        batch_op.add_column(sa.Column('focused_dwell_time', sa.Numeric(), nullable=True))

    with op.batch_alter_table('page_view', schema=None) as batch_op:
        batch_op.add_column(sa.Column('focused_dwell_time', sa.Numeric(), nullable=True))

    with op.batch_alter_table('session_intervention_event', schema=None) as batch_op:
        batch_op.add_column(sa.Column('focused_dwell_time', sa.Numeric(), nullable=True))

    with op.batch_alter_table('user_session_event', schema=None) as batch_op:
        batch_op.add_column(sa.Column('focused_dwell_time', sa.Numeric(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_session_event', schema=None) as batch_op:
        batch_op.drop_column('focused_dwell_time')

    with op.batch_alter_table('session_intervention_event', schema=None) as batch_op:
        batch_op.drop_column('focused_dwell_time')

    with op.batch_alter_table('page_view', schema=None) as batch_op:
        batch_op.drop_column('focused_dwell_time')

    with op.batch_alter_table('page_activity', schema=None) as batch_op:
        batch_op.drop_column('focused_dwell_time')

    # ### end Alembic commands ###
