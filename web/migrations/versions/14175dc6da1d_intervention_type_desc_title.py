"""empty message

Revision ID: 14175dc6da1d
Revises: 11a4b22f44aa
Create Date: 2025-03-05 14:19:47.980452

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '14175dc6da1d'
down_revision = '11a4b22f44aa'
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table('intervention_type', schema=None) as batch_op:
        batch_op.add_column(sa.Column('title', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('description', sa.String(), nullable=True))


def downgrade():
    with op.batch_alter_table('intervention_type', schema=None) as batch_op:
        batch_op.drop_column('description')
        batch_op.drop_column('title')
