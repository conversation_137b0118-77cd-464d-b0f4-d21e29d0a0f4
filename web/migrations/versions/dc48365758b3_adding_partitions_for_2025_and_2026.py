"""Adding partitions for 2025 and 2026

Revision ID: dc48365758b3
Revises: 4856051c0f13
Create Date: 2024-12-10 09:04:10.748020

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql.ddl import DDL


# revision identifiers, used by Alembic.
revision = 'dc48365758b3'
down_revision = '4856051c0f13'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('scoring_layer_determination_012025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_012025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_012025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_012026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_012026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_012026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_022025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_022025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_022025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_022026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_022026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_022026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_032025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_032025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_032025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_032026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_032026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_032026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_042025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_042025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_042025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_042026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_042026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_042026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_052025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_052025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_052025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_052026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_052026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_052026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_062025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_062025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_062025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_062026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_062026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_062026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_072025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_072025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_072025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_072026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_072026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_072026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_082025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_082025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_082025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_082026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_082026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_082026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_092025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_092025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_092025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_092026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_092026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_092026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_102025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_102025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_102025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_102026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_102026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_102026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_112025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_112025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_112025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_112026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_112026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_112026_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_122025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_122025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_122025_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_122026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_122026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_122026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_012025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_012025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_012025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_012026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_012026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_012026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_022025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_022025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_022025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_022026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_022026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_022026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_032025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_032025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_032025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_032026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_032026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_032026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_042025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_042025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_042025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_042026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_042026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_042026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_052025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_052025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_052025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_052026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_052026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_052026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_062025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_062025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_062025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_062026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_062026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_062026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_072025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_072025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_072025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_072026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_072026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_072026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_082025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_082025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_082025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_082026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_082026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_082026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_092025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_092025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_092025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_092026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_092026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_092026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_102025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_102025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_102025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_102026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_102026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_102026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_112025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_112025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_112025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_112026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_112026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_112026_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_122025',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_122025', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_122025_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_122026',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('decision_criterion', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_122026', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_122026_uuid'), ['uuid'], unique=False)

    # DDL TO Attach partitions
    conn = op.get_bind()
    #fixing partitions for session_intervention
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_012025 FOR VALUES FROM (1735689600) TO (1738368000);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_022025 FOR VALUES FROM (1738368000) TO (1740787200);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_032025 FOR VALUES FROM (1740787200) TO (1743465600);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_042025 FOR VALUES FROM (1743465600) TO (1746057600);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_052025 FOR VALUES FROM (1746057600) TO (1748736000);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_062025 FOR VALUES FROM (1748736000) TO (1751328000);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_072025 FOR VALUES FROM (1751328000) TO (1754006400);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_082025 FOR VALUES FROM (1754006400) TO (1756684800);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_092025 FOR VALUES FROM (1756684800) TO (1759276800);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_102025 FOR VALUES FROM (1759276800) TO (1761955200);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_112025 FOR VALUES FROM (1761955200) TO (1764547200);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_122025 FOR VALUES FROM (1764547200) TO (1767225600);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_012026 FOR VALUES FROM (1767225600) TO (1769904000);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_022026 FOR VALUES FROM (1769904000) TO (1772323200);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_032026 FOR VALUES FROM (1772323200) TO (1775001600);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_042026 FOR VALUES FROM (1775001600) TO (1777593600);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_052026 FOR VALUES FROM (1777593600) TO (1780272000);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_062026 FOR VALUES FROM (1780272000) TO (1782864000);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_072026 FOR VALUES FROM (1782864000) TO (1785542400);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_082026 FOR VALUES FROM (1785542400) TO (1788220800);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_092026 FOR VALUES FROM (1788220800) TO (1790812800);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_102026 FOR VALUES FROM (1790812800) TO (1793491200);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_112026 FOR VALUES FROM (1793491200) TO (1796083200);"""))
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_122026 FOR VALUES FROM (1796083200) TO (1798761600);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_012025 FOR VALUES FROM (1735689600) TO (1738368000);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_022025 FOR VALUES FROM (1738368000) TO (1740787200);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_032025 FOR VALUES FROM (1740787200) TO (1743465600);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_042025 FOR VALUES FROM (1743465600) TO (1746057600);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_052025 FOR VALUES FROM (1746057600) TO (1748736000);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_062025 FOR VALUES FROM (1748736000) TO (1751328000);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_072025 FOR VALUES FROM (1751328000) TO (1754006400);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_082025 FOR VALUES FROM (1754006400) TO (1756684800);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_092025 FOR VALUES FROM (1756684800) TO (1759276800);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_102025 FOR VALUES FROM (1759276800) TO (1761955200);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_112025 FOR VALUES FROM (1761955200) TO (1764547200);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_122025 FOR VALUES FROM (1764547200) TO (1767225600);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_012026 FOR VALUES FROM (1767225600) TO (1769904000);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_022026 FOR VALUES FROM (1769904000) TO (1772323200);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_032026 FOR VALUES FROM (1772323200) TO (1775001600);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_042026 FOR VALUES FROM (1775001600) TO (1777593600);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_052026 FOR VALUES FROM (1777593600) TO (1780272000);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_062026 FOR VALUES FROM (1780272000) TO (1782864000);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_072026 FOR VALUES FROM (1782864000) TO (1785542400);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_082026 FOR VALUES FROM (1785542400) TO (1788220800);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_092026 FOR VALUES FROM (1788220800) TO (1790812800);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_102026 FOR VALUES FROM (1790812800) TO (1793491200);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_112026 FOR VALUES FROM (1793491200) TO (1796083200);"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_122026 FOR VALUES FROM (1796083200) TO (1798761600);"""))
       
    # ### end Alembic commands ###


def downgrade():
    # DDL TO DETACH partitions
    conn = op.get_bind()
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_012025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_012026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_022025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_022026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_032025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_032026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_042025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_042026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_052025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_052026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_062025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_062026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_072025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_072026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_082025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_082026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_092025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_092026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_102025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_102026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_112025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_112026;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_122025;"""))
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_122026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_012025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_012026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_022025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_022026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_032025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_032026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_042025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_042026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_052025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_052026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_062025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_062026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_072025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_072026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_082025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_082026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_092025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_092026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_102025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_102026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_112025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_112026;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_122025;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_122026;"""))

    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('session_intervention_122026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_122026_uuid'))

    op.drop_table('session_intervention_122026')
    with op.batch_alter_table('session_intervention_122025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_122025_uuid'))

    op.drop_table('session_intervention_122025')
    with op.batch_alter_table('session_intervention_112026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_112026_uuid'))

    op.drop_table('session_intervention_112026')
    with op.batch_alter_table('session_intervention_112025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_112025_uuid'))

    op.drop_table('session_intervention_112025')
    with op.batch_alter_table('session_intervention_102026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_102026_uuid'))

    op.drop_table('session_intervention_102026')
    with op.batch_alter_table('session_intervention_102025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_102025_uuid'))

    op.drop_table('session_intervention_102025')
    with op.batch_alter_table('session_intervention_092026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_092026_uuid'))

    op.drop_table('session_intervention_092026')
    with op.batch_alter_table('session_intervention_092025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_092025_uuid'))

    op.drop_table('session_intervention_092025')
    with op.batch_alter_table('session_intervention_082026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_082026_uuid'))

    op.drop_table('session_intervention_082026')
    with op.batch_alter_table('session_intervention_082025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_082025_uuid'))

    op.drop_table('session_intervention_082025')
    with op.batch_alter_table('session_intervention_072026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_072026_uuid'))

    op.drop_table('session_intervention_072026')
    with op.batch_alter_table('session_intervention_072025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_072025_uuid'))

    op.drop_table('session_intervention_072025')
    with op.batch_alter_table('session_intervention_062026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_062026_uuid'))

    op.drop_table('session_intervention_062026')
    with op.batch_alter_table('session_intervention_062025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_062025_uuid'))

    op.drop_table('session_intervention_062025')
    with op.batch_alter_table('session_intervention_052026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_052026_uuid'))

    op.drop_table('session_intervention_052026')
    with op.batch_alter_table('session_intervention_052025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_052025_uuid'))

    op.drop_table('session_intervention_052025')
    with op.batch_alter_table('session_intervention_042026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_042026_uuid'))

    op.drop_table('session_intervention_042026')
    with op.batch_alter_table('session_intervention_042025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_042025_uuid'))

    op.drop_table('session_intervention_042025')
    with op.batch_alter_table('session_intervention_032026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_032026_uuid'))

    op.drop_table('session_intervention_032026')
    with op.batch_alter_table('session_intervention_032025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_032025_uuid'))

    op.drop_table('session_intervention_032025')
    with op.batch_alter_table('session_intervention_022026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_022026_uuid'))

    op.drop_table('session_intervention_022026')
    with op.batch_alter_table('session_intervention_022025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_022025_uuid'))

    op.drop_table('session_intervention_022025')
    with op.batch_alter_table('session_intervention_012026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_012026_uuid'))

    op.drop_table('session_intervention_012026')
    with op.batch_alter_table('session_intervention_012025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_012025_uuid'))

    op.drop_table('session_intervention_012025')
    with op.batch_alter_table('scoring_layer_determination_122026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_122026_uuid'))

    op.drop_table('scoring_layer_determination_122026')
    with op.batch_alter_table('scoring_layer_determination_122025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_122025_uuid'))

    op.drop_table('scoring_layer_determination_122025')
    with op.batch_alter_table('scoring_layer_determination_112026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_112026_uuid'))

    op.drop_table('scoring_layer_determination_112026')
    with op.batch_alter_table('scoring_layer_determination_112025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_112025_uuid'))

    op.drop_table('scoring_layer_determination_112025')
    with op.batch_alter_table('scoring_layer_determination_102026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_102026_uuid'))

    op.drop_table('scoring_layer_determination_102026')
    with op.batch_alter_table('scoring_layer_determination_102025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_102025_uuid'))

    op.drop_table('scoring_layer_determination_102025')
    with op.batch_alter_table('scoring_layer_determination_092026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_092026_uuid'))

    op.drop_table('scoring_layer_determination_092026')
    with op.batch_alter_table('scoring_layer_determination_092025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_092025_uuid'))

    op.drop_table('scoring_layer_determination_092025')
    with op.batch_alter_table('scoring_layer_determination_082026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_082026_uuid'))

    op.drop_table('scoring_layer_determination_082026')
    with op.batch_alter_table('scoring_layer_determination_082025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_082025_uuid'))

    op.drop_table('scoring_layer_determination_082025')
    with op.batch_alter_table('scoring_layer_determination_072026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_072026_uuid'))

    op.drop_table('scoring_layer_determination_072026')
    with op.batch_alter_table('scoring_layer_determination_072025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_072025_uuid'))

    op.drop_table('scoring_layer_determination_072025')
    with op.batch_alter_table('scoring_layer_determination_062026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_062026_uuid'))

    op.drop_table('scoring_layer_determination_062026')
    with op.batch_alter_table('scoring_layer_determination_062025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_062025_uuid'))

    op.drop_table('scoring_layer_determination_062025')
    with op.batch_alter_table('scoring_layer_determination_052026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_052026_uuid'))

    op.drop_table('scoring_layer_determination_052026')
    with op.batch_alter_table('scoring_layer_determination_052025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_052025_uuid'))

    op.drop_table('scoring_layer_determination_052025')
    with op.batch_alter_table('scoring_layer_determination_042026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_042026_uuid'))

    op.drop_table('scoring_layer_determination_042026')
    with op.batch_alter_table('scoring_layer_determination_042025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_042025_uuid'))

    op.drop_table('scoring_layer_determination_042025')
    with op.batch_alter_table('scoring_layer_determination_032026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_032026_uuid'))

    op.drop_table('scoring_layer_determination_032026')
    with op.batch_alter_table('scoring_layer_determination_032025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_032025_uuid'))

    op.drop_table('scoring_layer_determination_032025')
    with op.batch_alter_table('scoring_layer_determination_022026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_022026_uuid'))

    op.drop_table('scoring_layer_determination_022026')
    with op.batch_alter_table('scoring_layer_determination_022025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_022025_uuid'))

    op.drop_table('scoring_layer_determination_022025')
    with op.batch_alter_table('scoring_layer_determination_012026', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_012026_uuid'))

    op.drop_table('scoring_layer_determination_012026')
    with op.batch_alter_table('scoring_layer_determination_012025', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_012025_uuid'))

    op.drop_table('scoring_layer_determination_012025')
    # ### end Alembic commands ###
