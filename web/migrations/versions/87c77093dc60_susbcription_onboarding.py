"""susbcription_onboarding

Revision ID: 87c77093dc60
Revises: ed14ebae4391
Create Date: 2025-02-27 09:13:39.296526

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '87c77093dc60'
down_revision = 'ed14ebae4391'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('onboarding_details',
    sa.<PERSON>umn('uuid', sa.String(), nullable=False),
    sa.Column('terms_start', sa.Integer(), nullable=True),
    sa.Column('terms_signed', sa.Integer(), nullable=True),
    sa.Column('script_start', sa.Integer(), nullable=True),
    sa.Column('script_activated', sa.Integer(), nullable=True),
    sa.Column('details_start', sa.Integer(), nullable=True),
    sa.Column('details_confirmed', sa.Integer(), nullable=True),
    sa.Column('billing_start', sa.Integer(), nullable=True),
    sa.Column('billing_confirmed', sa.Integer(), nullable=True),
    sa.Column('billing_trial_end', sa.Integer(), nullable=True),
    sa.Column('billing_orders_selected', sa.Integer(), nullable=True),
    sa.Column('installer_first_name', sa.String(), nullable=True),
    sa.Column('installer_last_name', sa.String(), nullable=True),
    sa.Column('installer_email', sa.String(), nullable=True),
    sa.Column('installer_role', sa.String(), nullable=True),
    sa.Column('billing_plan_selected', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('onboarding_details', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_onboarding_details_uuid'), ['uuid'], unique=False)

    op.create_table('subscription_plans',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('created_at', sa.Integer(), nullable=True),
    sa.Column('pricing_details', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('subscription_plans', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_subscription_plans_uuid'), ['uuid'], unique=False)

    with op.batch_alter_table('store', schema=None) as batch_op:
        batch_op.add_column(sa.Column('shop_owner_role', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('subscription_plan', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('onboarding_details', sa.String(), nullable=True))
        batch_op.create_foreign_key(None, 'onboarding_details', ['onboarding_details'], ['uuid'], ondelete='SET NULL')
        batch_op.create_foreign_key(None, 'subscription_plans', ['subscription_plan'], ['uuid'], ondelete='SET NULL')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('store', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_column('onboarding_details')
        batch_op.drop_column('subscription_plan')
        batch_op.drop_column('shop_owner_role')

    with op.batch_alter_table('subscription_plans', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_subscription_plans_uuid'))

    op.drop_table('subscription_plans')
    with op.batch_alter_table('onboarding_details', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_onboarding_details_uuid'))

    op.drop_table('onboarding_details')
