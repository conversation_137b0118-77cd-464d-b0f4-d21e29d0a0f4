"""Add event_metadata column to session_intervention_event

Revision ID: 28a41b05c8f9
Revises: 36e77b290e29
Create Date: 2025-02-04 13:22:41.325516

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '28a41b05c8f9'
down_revision = '36e77b290e29'
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table('session_intervention_event', schema=None) as batch_op:
        batch_op.add_column(sa.Column('event_metadata', sa.JSON(), nullable=True))

def downgrade():
    with op.batch_alter_table('session_intervention_event', schema=None) as batch_op:
        batch_op.drop_column('event_metadata')
