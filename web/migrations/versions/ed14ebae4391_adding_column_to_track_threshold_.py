"""Adding column to track threshold methodology

Revision ID: ed14ebae4391
Revises: f57bfcca22d7
Create Date: 2025-02-28 17:07:38.554851

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ed14ebae4391'
down_revision = 'f57bfcca22d7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    with op.batch_alter_table('scoring_layer_thresholds', schema=None) as batch_op:
        batch_op.add_column(sa.Column('threshold_methodology', sa.String(), nullable=True))


    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('scoring_layer_thresholds', schema=None) as batch_op:
        batch_op.drop_column('threshold_methodology')

    # ### end Alembic commands ###
