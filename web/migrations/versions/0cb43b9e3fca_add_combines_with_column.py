"""Add combines with column

Revision ID: 0cb43b9e3fca
Revises: 881221214ffc
Create Date: 2025-02-26 12:12:27.440612

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0cb43b9e3fca'
down_revision = '881221214ffc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('session_discount_code', schema=None) as batch_op:
        batch_op.add_column(sa.Column('combines_with_order_discounts', sa.<PERSON>an(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('session_discount_code', schema=None) as batch_op:
        batch_op.drop_column('combines_with_order_discounts')

    # ### end Alembic commands ###
