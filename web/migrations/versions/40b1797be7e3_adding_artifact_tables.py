"""Adding artifact tables

Revision ID: 40b1797be7e3
Revises: 9a2e72fcfb4d
Create Date: 2024-12-09 13:11:31.681590

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql.ddl import DDL


# revision identifiers, used by Alembic.
revision = '40b1797be7e3'
down_revision = '9a2e72fcfb4d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('scoring_artifact',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('component_type', sa.String(), nullable=True),
    sa.Column('deployment_external_reference', sa.String(), nullable=True),
    sa.Column('artifact_metadata', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('scoring_artifact', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_artifact_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('store_specific', sa.Boolean(), nullable=True),
    sa.Column('scoring_algorithm_type', sa.String(), nullable=True),
    sa.Column('scoring_artifacts', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('scoring_layer', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_thresholds',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('scoring_artifact_thresholds', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('scoring_layer_thresholds', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_thresholds_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    #sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    #sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    #sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    postgresql_partition_by='RANGE (time)'
    )
    with op.batch_alter_table('scoring_layer_determination', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_uuid'), ['uuid'], unique=False)

    op.create_table('scoring_layer_determination_122024',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_122024', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_122024_uuid'), ['uuid'], unique=False)

    op.create_table('store_intervention_event',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('event', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('store_intervention_event', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_store_intervention_event_uuid'), ['uuid'], unique=False)

    with op.batch_alter_table('session_intervention', schema=None) as batch_op:
        #batch_op.add_column(sa.Column('store_intervention_association_id', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('anti_holdout', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('opportunity', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('opportunity_label', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('decision', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('parameters', sa.JSON(), nullable=True))
        #batch_op.create_foreign_key(None, 'store_intervention_association', ['store_intervention_association_id'], ['uuid'], ondelete='CASCADE')

    
    with op.batch_alter_table('store_intervention_association', schema=None) as batch_op:
        batch_op.add_column(sa.Column('parameters', sa.JSON(), nullable=True))

    conn = op.get_bind()
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_122024 FOR VALUES FROM (1733011200) TO (1735689600);"""))

    # ### end Alembic commands ###


def downgrade():
    conn = op.get_bind()
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination DETACH PARTITION scoring_layer_determination_122024;"""))

    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('store_intervention_association', schema=None) as batch_op:
        batch_op.drop_column('parameters')


    with op.batch_alter_table('session_intervention', schema=None) as batch_op:
        batch_op.drop_column('parameters')
        batch_op.drop_column('decision')
        batch_op.drop_column('opportunity_label')
        batch_op.drop_column('opportunity')
        batch_op.drop_column('anti_holdout')

    with op.batch_alter_table('store_intervention_event', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_store_intervention_event_uuid'))

    op.drop_table('store_intervention_event')
    with op.batch_alter_table('scoring_layer_determination_122024', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_122024_uuid'))

    op.drop_table('scoring_layer_determination_122024')
    with op.batch_alter_table('scoring_layer_determination', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_uuid'))

    op.drop_table('scoring_layer_determination')
    with op.batch_alter_table('scoring_layer_thresholds', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_thresholds_uuid'))

    op.drop_table('scoring_layer_thresholds')
    with op.batch_alter_table('scoring_layer', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_uuid'))

    op.drop_table('scoring_layer')
    with op.batch_alter_table('scoring_artifact', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_artifact_uuid'))

    op.drop_table('scoring_artifact')
    # ### end Alembic commands ###
