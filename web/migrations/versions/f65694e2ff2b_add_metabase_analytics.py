"""add_metabase_analytics

Revision ID: f65694e2ff2b
Revises: 216f72a58adc
Create Date: 2025-03-16 15:49:03.824756

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f65694e2ff2b'
down_revision = '216f72a58adc'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('metabase_analytics',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('dashboard_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('metabase_analytics', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_metabase_analytics_intervention_type_id'), ['intervention_type_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_metabase_analytics_uuid'), ['uuid'], unique=False)

    


def downgrade():
    with op.batch_alter_table('metabase_analytics', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_metabase_analytics_uuid'))
        batch_op.drop_index(batch_op.f('ix_metabase_analytics_intervention_type_id'))

    op.drop_table('metabase_analytics')
