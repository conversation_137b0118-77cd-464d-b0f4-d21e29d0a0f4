"""Add discount code tracker table

Revision ID: 7b8dadd217ff
Revises: 28a41b05c8f9
Create Date: 2025-02-20 18:52:46.863829

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7b8dadd217ff'
down_revision = '28a41b05c8f9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('session_discount_code',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('discount_code_value', sa.String(), nullable=True),
    sa.Column('first_detected_in', sa.String(), nullable=True),
    sa.Column('last_detected_in', sa.String(), nullable=True),
    sa.Column('first_detected_time', sa.Integer(), nullable=True),
    sa.Column('last_detected_time', sa.Integer(), nullable=True),
    sa.Column('discount_code_metadata', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('session_discount_code', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_discount_code_uuid'), ['uuid'], unique=False)


    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('session_discount_code', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_discount_code_uuid'))

    op.drop_table('session_discount_code')
    # ### end Alembic commands ###
