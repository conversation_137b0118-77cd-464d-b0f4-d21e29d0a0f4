"""empty message

Revision ID: 69fe77b8bcf4
Revises: 87c77093dc60
Create Date: 2025-03-02 18:01:38.308676

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '69fe77b8bcf4'
down_revision = '87c77093dc60'
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table('store', schema=None) as batch_op:
        batch_op.add_column(sa.Column('status', sa.String(), nullable=True))


def downgrade():
    with op.batch_alter_table('store', schema=None) as batch_op:
        batch_op.drop_column('status')
