"""Adding new table

Revision ID: 956ccb380e07
Revises: 2e4b83e772b8
Create Date: 2025-01-17 19:38:13.934250

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '956ccb380e07'
down_revision = '2e4b83e772b8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('session_intervention_event',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('session_intervention_id', sa.String(), nullable=True),
    sa.Column('event', sa.String(), nullable=True),
    sa.Column('page_view_id', sa.String(), nullable=True),
    sa.Column('dwell_time', sa.Numeric(), nullable=True),
    sa.ForeignKeyConstraint(['page_view_id'], ['page_view.uuid'], ondelete='CASCADE'),
    #sa.ForeignKeyConstraint(['session_intervention_id'], ['session_intervention.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('session_intervention_event', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_event_uuid'), ['uuid'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
   
    with op.batch_alter_table('session_intervention_event', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_event_uuid'))

    op.drop_table('session_intervention_event')
    # ### end Alembic commands ###
