"""Add new columns

Revision ID: 4856051c0f13
Revises: d49beb15dff2
Create Date: 2024-12-10 08:17:49.091740

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4856051c0f13'
down_revision = 'd49beb15dff2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('scoring_layer', schema=None) as batch_op:
        batch_op.add_column(sa.Column('run_predictions', sa.<PERSON>(), nullable=True))
        batch_op.add_column(sa.Column('live_decisioning', sa.<PERSON>an(), nullable=True))

    with op.batch_alter_table('session_intervention', schema=None) as batch_op:
        batch_op.add_column(sa.Column('decision_criterion', sa.String(), nullable=True))
        batch_op.drop_column('anti_holdout')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('session_intervention', schema=None) as batch_op:
        batch_op.add_column(sa.Column('anti_holdout', sa.BOOLEAN(), autoincrement=False, nullable=True))
        batch_op.drop_column('decision_criterion')

    with op.batch_alter_table('scoring_layer', schema=None) as batch_op:
        batch_op.drop_column('live_decisioning')
        batch_op.drop_column('run_predictions')

    # ### end Alembic commands ###
