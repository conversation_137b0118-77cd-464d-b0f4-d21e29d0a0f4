"""Adding discount code specific prefix

Revision ID: c84ae2b3f68f
Revises: b681b2bc6104
Create Date: 2024-10-08 06:27:00.239833

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c84ae2b3f68f'
down_revision = 'b681b2bc6104'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('store_discount', schema=None) as batch_op:
        batch_op.add_column(sa.Column('discount_prefix', sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('store_discount', schema=None) as batch_op:
        batch_op.drop_column('discount_prefix')

    # ### end Alembic commands ###
