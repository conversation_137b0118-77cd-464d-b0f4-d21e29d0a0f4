"""Adding intervention tables

Revision ID: 9a2e72fcfb4d
Revises: 1030b5d277c7
Create Date: 2024-11-27 16:55:45.747839

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql.ddl import DDL



# revision identifiers, used by Alembic.
revision = '9a2e72fcfb4d'
down_revision = '1030b5d277c7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('intervention_type',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('intervention_type', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_intervention_type_uuid'), ['uuid'], unique=False)

    op.create_table('store_intervention_association',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('store_intervention_association', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_store_intervention_association_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    #sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    #sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    postgresql_partition_by='RANGE (time)'
    )
    with op.batch_alter_table('session_intervention', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_122024',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_122024', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_122024_uuid'), ['uuid'], unique=False)

    conn = op.get_bind()
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_122024 FOR VALUES FROM (1733011200) TO (1735689600);"""))
    # ### end Alembic commands ###


def downgrade():
    #detach
    conn = op.get_bind()
    
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_122024;"""))
    
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('session_intervention', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_uuid'))

    with op.batch_alter_table('session_intervention_122024', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_122024_uuid'))

    op.drop_table('session_intervention_122024')
    
    op.drop_table('session_intervention')
    with op.batch_alter_table('store_intervention_association', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_store_intervention_association_uuid'))

    op.drop_table('store_intervention_association')
    with op.batch_alter_table('intervention_type', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_intervention_type_uuid'))

    op.drop_table('intervention_type')
    # ### end Alembic commands ###
