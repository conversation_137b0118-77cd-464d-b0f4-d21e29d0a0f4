"""New cols for session event, and new table for params

Revision ID: b12e4c9f631f
Revises: 361a3f17825c
Create Date: 2024-05-30 17:18:44.983983

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b12e4c9f631f'
down_revision = '361a3f17825c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_session_nudge_parameters',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('trigger_type', sa.String(), nullable=True),
    sa.Column('trigger_delay', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('user_session_nudge_parameters', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_session_nudge_parameters_user_session_id'), ['user_session_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_session_nudge_parameters_uuid'), ['uuid'], unique=False)

    with op.batch_alter_table('user_session_event', schema=None) as batch_op:
        batch_op.add_column(sa.Column('dwell_time', sa.Numeric(), nullable=True))
        batch_op.add_column(sa.Column('page_view_id', sa.String(), nullable=True))
        batch_op.create_foreign_key(None, 'page_view', ['page_view_id'], ['uuid'], ondelete='CASCADE')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_session_event', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_column('page_view_id')
        batch_op.drop_column('dwell_time')

    with op.batch_alter_table('user_session_nudge_parameters', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_session_nudge_parameters_uuid'))
        batch_op.drop_index(batch_op.f('ix_user_session_nudge_parameters_user_session_id'))

    op.drop_table('user_session_nudge_parameters')
    # ### end Alembic commands ###
