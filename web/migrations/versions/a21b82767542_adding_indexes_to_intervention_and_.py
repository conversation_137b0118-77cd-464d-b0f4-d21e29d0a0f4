"""Adding indexes to intervention and determination tables

Revision ID: a21b82767542
Revises: 79234ca16668
Create Date: 2025-03-07 15:04:08.281531

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a21b82767542'
down_revision = '79234ca16668'
branch_labels = None
depends_on = None


def upgrade():
    # Get raw connection and set autocommit
    connection = op.get_bind()
    old_isolation_level = connection.connection.isolation_level
    connection.connection.set_isolation_level(0)  # ISOLATION_LEVEL_AUTOCOMMIT

    try:
        # Handle 2024 separately (only November and December)
        for month in [11, 12]:
            table_name = f'scoring_layer_determination_{month:02d}2024'
            op.execute(
                f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_scoring_layer_thresholds_id '
                f'ON {table_name} (scoring_layer_thresholds_id)'
            )
            print(f"Created index scoring_layer_thresholds_id on {table_name}")
            
            op.execute(
                f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_user_session_id '
                f'ON {table_name} (user_session_id)'
            )
            print(f"Created index user_session_id on {table_name}")

        # Handle 2025 and 2026 (all months)
        for month in range(1, 13):
            for year in [2025, 2026]:
                table_name = f'scoring_layer_determination_{month:02d}{year}'
                op.execute(
                    f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_scoring_layer_thresholds_id '
                    f'ON {table_name} (scoring_layer_thresholds_id)'
                )
                print(f"Created index scoring_layer_thresholds_id on {table_name}")
                
                op.execute(
                    f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_user_session_id '
                    f'ON {table_name} (user_session_id)'
                )
                print(f"Created index user_session_id on {table_name}")

                table_name = f'session_intervention_{month:02d}{year}'
                op.execute(
                    f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_user_session_id '
                    f'ON {table_name} (user_session_id)'
                )
                print(f"Created index user_session_id on {table_name}")

        # Handle 2024 separately (only November and December)
        for month in [11, 12]:
            table_name = f'scoring_layer_determination_{month:02d}2024'
            op.execute(
                f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_scoring_layer_id '
                f'ON {table_name} (scoring_layer_id)'
            )
            print(f"Created index scoring_layer_id on {table_name}")

            table_name = f'session_intervention_{month:02d}2024'
            op.execute(
                f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_intervention_type_id '
                f'ON {table_name} (intervention_type_id)'
            )
            print(f"Created index intervention_type_id on {table_name}")
            op.execute(
                f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_store_intervention_association_id '
                f'ON {table_name} (store_intervention_association_id)'
            )
            print(f"Created index store_intervention_association_id on {table_name}")

        # Handle 2025 and 2026 (all months)
        for month in range(1, 13):
            for year in [2025, 2026]:
                table_name = f'scoring_layer_determination_{month:02d}{year}'
                op.execute(
                    f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_scoring_layer_id '
                    f'ON {table_name} (scoring_layer_id)'
                )
                print(f"Created index scoring_layer_id on {table_name}")

                table_name = f'session_intervention_{month:02d}{year}'
                op.execute(
                    f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_intervention_type_id '
                    f'ON {table_name} (intervention_type_id)'
                )
                print(f"Created index intervention_type_id on {table_name}")
                op.execute(
                    f'CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_{table_name}_store_intervention_association_id '
                    f'ON {table_name} (store_intervention_association_id)'
                )
                print(f"Created index store_intervention_association_id on {table_name}")
    finally:
        # Restore original isolation level
        connection.connection.set_isolation_level(old_isolation_level)


def downgrade():
    # Get raw connection and set autocommit
    connection = op.get_bind()
    old_isolation_level = connection.connection.isolation_level
    connection.connection.set_isolation_level(0)

    try:
        # Handle 2024 separately (only November and December)
        for month in [11, 12]:
            table_name = f'scoring_layer_determination_{month:02d}2024'
            op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_user_session_id')
            print(f"Dropped index user_session_id from {table_name}")
            
            op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_scoring_layer_thresholds_id')
            print(f"Dropped index scoring_layer_thresholds_id from {table_name}")

        # Handle 2025 and 2026 (all months)
        for month in range(1, 13):
            for year in [2025, 2026]:
                table_name = f'session_intervention_{month:02d}{year}'
                op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_user_session_id')
                print(f"Dropped index user_session_id from {table_name}")

                table_name = f'scoring_layer_determination_{month:02d}{year}'
                op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_user_session_id')
                print(f"Dropped index user_session_id from {table_name}")
                
                op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_scoring_layer_thresholds_id')
                print(f"Dropped index scoring_layer_thresholds_id from {table_name}")

        for month in [11, 12]:
            table_name = f'scoring_layer_determination_{month:02d}2024'
            op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_scoring_layer_id')
            print(f"Dropped index scoring_layer_id from {table_name}")

            table_name = f'session_intervention_{month:02d}2024'
            op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_intervention_type_id')
            print(f"Dropped index intervention_type_id from {table_name}")
            op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_store_intervention_association_id')
            print(f"Dropped index store_intervention_association_id from {table_name}")

        # Handle 2025 and 2026 (all months)
        for month in range(1, 13):
            for year in [2025, 2026]:
                table_name = f'scoring_layer_determination_{month:02d}{year}'
                op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_scoring_layer_id')
                print(f"Dropped index scoring_layer_id from {table_name}")

                table_name = f'session_intervention_{month:02d}{year}'
                op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_intervention_type_id')
                print(f"Dropped index intervention_type_id from {table_name}")
                op.execute(f'DROP INDEX CONCURRENTLY IF EXISTS ix_{table_name}_store_intervention_association_id')
                print(f"Dropped index store_intervention_association_id from {table_name}")

    finally:
        # Restore original isolation level
        connection.connection.set_isolation_level(old_isolation_level)
