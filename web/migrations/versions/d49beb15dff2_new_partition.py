"""New partition

Revision ID: d49beb15dff2
Revises: 40b1797be7e3
Create Date: 2024-12-09 20:59:46.900043

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql.ddl import DDL

# revision identifiers, used by Alembic.
revision = 'd49beb15dff2'
down_revision = '40b1797be7e3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('scoring_layer_determination_112024',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_id', sa.String(), nullable=True),
    sa.Column('scoring_layer_thresholds_id', sa.String(), nullable=True),
    sa.Column('scoring_algorithm_version', sa.String(), nullable=True),
    sa.Column('scoring_layer_scores', sa.JSON(), nullable=True),
    sa.Column('determination', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_layer_id'], ['scoring_layer.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scoring_layer_thresholds_id'], ['scoring_layer_thresholds.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id'),
    sa.UniqueConstraint('user_session_id', 'scoring_layer_id', 'time')
    )
    with op.batch_alter_table('scoring_layer_determination_112024', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_scoring_layer_determination_112024_uuid'), ['uuid'], unique=False)

    op.create_table('session_intervention_112024',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('time', sa.Integer(), nullable=False),
    sa.Column('intervention_type_id', sa.String(), nullable=True),
    sa.Column('store_intervention_association_id', sa.String(), nullable=True),
    sa.Column('user_session_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('holdout', sa.Boolean(), nullable=True),
    sa.Column('anti_holdout', sa.Boolean(), nullable=True),
    sa.Column('opportunity', sa.Boolean(), nullable=True),
    sa.Column('opportunity_label', sa.String(), nullable=True),
    sa.Column('decision', sa.String(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['intervention_type_id'], ['intervention_type.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_session_id'], ['user_session.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', 'time'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id'),
    sa.UniqueConstraint('user_session_id', 'intervention_type_id', 'time')
    )
    with op.batch_alter_table('session_intervention_112024', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_session_intervention_112024_uuid'), ['uuid'], unique=False)

    conn = op.get_bind()
    #fixing partitions for session_intervention
    conn.execute(DDL("""ALTER TABLE session_intervention ATTACH PARTITION session_intervention_112024 FOR VALUES FROM (1730419200) TO (1733011200);"""))
    #fixing partitions for scoring layer
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination ATTACH PARTITION scoring_layer_determination_112024 FOR VALUES FROM (1730419200) TO (1733011200);"""))    

    # ### end Alembic commands ###


def downgrade():
    conn = op.get_bind()
    conn.execute(DDL("""ALTER TABLE session_intervention DETACH PARTITION session_intervention_112024;"""))
    conn.execute(DDL("""ALTER TABLE scoring_layer_determination  DETACH PARTITION scoring_layer_determination_112024;"""))    
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('session_intervention_112024', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_session_intervention_112024_uuid'))

    op.drop_table('session_intervention_112024')
    with op.batch_alter_table('scoring_layer_determination_112024', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_scoring_layer_determination_112024_uuid'))

    op.drop_table('scoring_layer_determination_112024')
    # ### end Alembic commands ###
