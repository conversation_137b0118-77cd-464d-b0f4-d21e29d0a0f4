"""Adding store_url and deleted to onboarding_details

Revision ID: 79234ca16668
Revises: 14175dc6da1d
Create Date: 2025-03-06 10:37:14.033820

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '79234ca16668'
down_revision = '14175dc6da1d'
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table('onboarding_details', schema=None) as batch_op:
        batch_op.add_column(sa.Column('deleted', sa.Bo<PERSON>an(), nullable=True))
        batch_op.add_column(sa.Column('store_url', sa.String(), nullable=True))


def downgrade():
    with op.batch_alter_table('onboarding_details', schema=None) as batch_op:
        batch_op.drop_column('store_url')
        batch_op.drop_column('deleted')
