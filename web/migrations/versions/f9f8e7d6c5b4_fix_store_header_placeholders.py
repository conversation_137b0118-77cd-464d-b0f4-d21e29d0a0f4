"""Fix double curly placeholders in store header fields

Revision ID: f9f8e7d6c5b4
Revises: f65694e2ff2b
Create Date: 2025-05-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'f9f8e7d6c5b4'
down_revision = 'f65694e2ff2b'
branch_labels = None
depends_on = None


def upgrade():
    # Fix discount_rate placeholder - Replace {{discount_rate}} with {discount}
    for field in ['minimized_text_header', 'popup_text_header', 'popup_text_body', 'popup_text_button']:
        op.execute(
            f"""
            UPDATE store
            SET {field} = REPLACE({field}, '{{{{discount_rate}}}}', '{{discount}}')
            WHERE {field} LIKE '%{{{{discount_rate}}}}%';
            """
        )


def downgrade():
       
    # Revert discount_rate placeholder - Replace {discount} with {{discount_rate}}
    for field in ['minimized_text_header', 'popup_text_header', 'popup_text_body', 'popup_text_button']:
        op.execute(
            f"""
            UPDATE store
            SET {field} = REPLACE({field}, '{{discount}}', '{{{{discount_rate}}}}')
            WHERE {field} LIKE '%{{discount}}%' AND 
                  ({field} LIKE '%OFF%' OR 
                   {field} LIKE '%off%' OR 
                   {field} LIKE '%discount%' OR 
                   {field} LIKE '%Discount%' OR
                   {field} LIKE '%UNLOCKED%');
            """
        )
