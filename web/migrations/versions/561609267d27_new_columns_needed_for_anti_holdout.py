"""New columns needed for anti-holdout

Revision ID: 561609267d27
Revises: b12e4c9f631f
Create Date: 2024-07-30 08:49:05.189638

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '561609267d27'
down_revision = 'b12e4c9f631f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('model_decision', schema=None) as batch_op:
        batch_op.add_column(sa.Column('decision_criterion', sa.String(), nullable=True))

    with op.batch_alter_table('model_store_holdout', schema=None) as batch_op:
        batch_op.add_column(sa.Column('active', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('holdout_type', sa.String(), nullable=True))

    with op.batch_alter_table('model_store_threshold', schema=None) as batch_op:
        batch_op.add_column(sa.Column('active', sa.<PERSON>(), nullable=True))
        batch_op.add_column(sa.Column('threshold_type', sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('model_store_threshold', schema=None) as batch_op:
        batch_op.drop_column('threshold_type')
        batch_op.drop_column('active')

    with op.batch_alter_table('model_store_holdout', schema=None) as batch_op:
        batch_op.drop_column('holdout_type')
        batch_op.drop_column('active')

    with op.batch_alter_table('model_decision', schema=None) as batch_op:
        batch_op.drop_column('decision_criterion')

    # ### end Alembic commands ###
