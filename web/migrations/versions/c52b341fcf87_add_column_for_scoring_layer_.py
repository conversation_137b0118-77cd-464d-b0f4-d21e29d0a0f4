"""Add column for scoring layer determinations

Revision ID: c52b341fcf87
Revises: dc48365758b3
Create Date: 2024-12-10 11:39:20.287477

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c52b341fcf87'
down_revision = 'dc48365758b3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('session_intervention', schema=None) as batch_op:
        batch_op.add_column(sa.Column('scoring_layer_determinations', sa.JSON(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('session_intervention', schema=None) as batch_op:
        batch_op.drop_column('scoring_layer_determinations')

    # ### end Alembic commands ###
