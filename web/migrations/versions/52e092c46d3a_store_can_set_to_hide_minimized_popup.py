"""store can set to hide minimized popup

Revision ID: 52e092c46d3a
Revises: 42082901184e
Create Date: 2024-08-29 07:51:55.804695

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '52e092c46d3a'
down_revision = '42082901184e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('store', schema=None) as batch_op:
        batch_op.add_column(sa.Column('hide_minimized_popup', sa.<PERSON>an(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('store', schema=None) as batch_op:
        batch_op.drop_column('hide_minimized_popup')

    # ### end Alembic commands ###
