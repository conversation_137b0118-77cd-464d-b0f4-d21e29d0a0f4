"""empty message

Revision ID: 7a7885ac709d
Revises: 69fe77b8bcf4
Create Date: 2025-03-04 13:31:23.409706

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7a7885ac709d'
down_revision = '69fe77b8bcf4'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('nudge_requests',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('store_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('nudge_requests', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_nudge_requests_uuid'), ['uuid'], unique=False)


def downgrade():
    with op.batch_alter_table('nudge_requests', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_nudge_requests_uuid'))

    op.drop_table('nudge_requests')
