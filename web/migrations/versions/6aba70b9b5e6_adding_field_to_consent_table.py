"""Adding field to consent table

Revision ID: 6aba70b9b5e6
Revises: 0a70e0ab65ea
Create Date: 2024-12-11 15:29:01.740851

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6aba70b9b5e6'
down_revision = '0a70e0ab65ea'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('session_consent_event', schema=None) as batch_op:
        batch_op.add_column(sa.Column('store_id', sa.String(), nullable=True))
        batch_op.create_foreign_key(None, 'store', ['store_id'], ['uuid'], ondelete='CASCADE')


    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    with op.batch_alter_table('session_consent_event', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_column('store_id')

    # ### end Alembic commands ###
