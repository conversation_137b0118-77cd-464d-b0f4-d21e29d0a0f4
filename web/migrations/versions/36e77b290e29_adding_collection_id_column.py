"""Adding collection id column

Revision ID: 36e77b290e29
Revises: 956ccb380e07
Create Date: 2025-01-30 18:00:31.023794

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '36e77b290e29'
down_revision = '956ccb380e07'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('discount_collection', schema=None) as batch_op:
        batch_op.add_column(sa.Column('collection_id', sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('discount_collection', schema=None) as batch_op:
        batch_op.drop_column('collection_id')

    # ### end Alembic commands ###
