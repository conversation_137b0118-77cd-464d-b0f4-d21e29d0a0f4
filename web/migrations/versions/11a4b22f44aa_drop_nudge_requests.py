"""empty message

Revision ID: 11a4b22f44aa
Revises: 7a7885ac709d
Create Date: 2025-03-05 09:37:38.798500

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '11a4b22f44aa'
down_revision = '7a7885ac709d'
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table('nudge_requests', schema=None) as batch_op:
        batch_op.drop_index('ix_nudge_requests_uuid')

    op.drop_table('nudge_requests')


def downgrade():
    op.create_table('nudge_requests',
    sa.Column('uuid', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('store_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['store.uuid'], name='nudge_requests_store_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid', name='nudge_requests_pkey')
    )
    with op.batch_alter_table('nudge_requests', schema=None) as batch_op:
        batch_op.create_index('ix_nudge_requests_uuid', ['uuid'], unique=False)
