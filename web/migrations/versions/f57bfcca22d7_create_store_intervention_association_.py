"""Create store_intervention_association_assets table

Revision ID: f57bfcca22d7
Revises: 0cb43b9e3fca
Create Date: 2025-02-13 12:44:23.198079

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f57bfcca22d7'
down_revision = '0cb43b9e3fca'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('store_intervention_association_assets',
    sa.Column('uuid', sa.String(), nullable=False),
    sa.Column('store_intervention_association_id', sa.String(), nullable=False),
    sa.Column('asset_type', sa.String(), nullable=False),
    sa.Column('asset_key', sa.String(), nullable=False),
    sa.Column('asset_url', sa.String(), nullable=False),
    sa.Column('asset_metadata', sa.JSON(), nullable=False),
    sa.ForeignKeyConstraint(['store_intervention_association_id'], ['store_intervention_association.uuid'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('uuid')
    )
    with op.batch_alter_table('store_intervention_association_assets', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_store_intervention_association_assets_uuid'), ['uuid'], unique=False)


def downgrade():
    with op.batch_alter_table('store_intervention_association_assets', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_store_intervention_association_assets_uuid'))

    op.drop_table('store_intervention_association_assets')
