from redis import Redis
import config
from urllib.parse import urlparse


class EphemeralStore:
    url = urlparse(config.REDIS_URL)
    _store = Redis(
        host=url.hostname,
        port=url.port,
        password=url.password,
        health_check_interval=10,
        socket_connect_timeout=5,
        retry_on_timeout=True,
        socket_keepalive=True,
        ssl=config.REDIS_SSL,
        ssl_cert_reqs=None,
        decode_responses=True
    )

    @classmethod
    def add_to_store(cls, key, expire_timedelta, data):
        """
        Store offers
        """
        cls._store.set(key, data, ex=expire_timedelta)

    @classmethod
    def find_key(cls, key):
        return cls._store.get(key)

    @classmethod
    def remove_from_store(cls, key):
        if config.ENABLE_KEY_BLOCKLIST:
            cls._store.delete(key)
