import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 


import argparse
import datetime
import dateutil
import math
import os
import shopify
import sqlalchemy
import time
import uuid
from modules.record_order import associate_session_and_order_from_shopify_object
from app import app
import config
from utils import get_generic_logger

logger = get_generic_logger(__name__)



def sync_orders_function(store_id=None):
    # Connect to database
    engine = sqlalchemy.create_engine(config.SQLALCHEMY_DATABASE_URI)
    conn = engine.connect()

    # Get the store and order tables
    meta_data = sqlalchemy.MetaData()
    meta_data.reflect(bind=engine)
    store_table = meta_data.tables["store"]
    store_url_table = meta_data.tables["store_url"]
    order_table = meta_data.tables["order"]
    discount_code_table = meta_data.tables["discount_code"]
    usage_charge_table = meta_data.tables["usage_charge"]
    user_session_table = meta_data.tables["user_session"]

    store_results = conn.execute(
        sqlalchemy.select(
            store_table.c.uuid,
            store_table.c.access_token,
            store_table.c.test_store,
            store_table.c.time,
            store_table.c.last_order_sync,
            store_table.c.commission_rate).\
        where(store_table.c.test_store == False)
    ).fetchall()
    for result in store_results:
        #skip if a store filter was provided
        if store_id and result.uuid != store_id:
            continue

        # Get store URL
        result_url = conn.execute(
            sqlalchemy.select(store_url_table.c.url).\
            where(store_url_table.c.store_id == result.uuid).\
            order_by(sqlalchemy.desc(store_url_table.c.default))
        ).fetchone()
        if result_url is None or len(result_url) == 0:
            continue
        store_url = result_url[0]

        # Connect to Shopify API for the merchant
        session = shopify.Session(store_url, os.environ['SHOPIFY_LEGACY_API_VERSION'], result.access_token)
        shopify.ShopifyResource.activate_session(session)
        
        # Get all their orders from the last 60 days
        try:
            orders = shopify.Order.find(status='any')
        except Exception as e:
            logger.info("Shopify Orders API issue for " + store_url + " (" + result.uuid + ")")
            continue
        logger.info("Processing orders for store: " + store_url + " (" + result.uuid + ")")
        order_list = []
        for order in orders:
            order_list.append(order)
        while orders.has_next_page():
            if dateutil.parser.parse(orders[-1].created_at).timestamp() < max(result.last_order_sync, result.time):
                break
            
            orders = orders.next_page()
            for order in orders:
                order_list.append(order)
        for order in order_list:
            existing_order_record = conn.execute(
                sqlalchemy.select(order_table.c.uuid).\
                where(order_table.c.shopify_id == str(order.id))
            ).fetchone()
                        
            if existing_order_record is not None:
                continue
            
            # See if a Vandra discount code was applied
            applied_discount_code = None
            discount_code_list = [x.code for x in order.discount_codes]
            
            if discount_code_list != []:
                discount_code_results = conn.execute(
                    sqlalchemy.select(discount_code_table.c.discount_code_code).\
                    where(discount_code_table.c.discount_code_code.in_(discount_code_list))
                ).fetchall()
                if len(discount_code_results) > 0:
                    applied_discount_code = discount_code_results[0].discount_code_code
            
            # Get the user session for this order
            user_session_record = conn.execute(
                sqlalchemy.select(user_session_table.c.uuid).\
                where(user_session_table.c.order_id == str(order.id))
            ).fetchone()
            user_session_id = None
            if user_session_record is not None:
                user_session_id = user_session_record.uuid
            else:
                #try from the note attributes
                for att in  order.note_attributes:
                    if att.name == "__vandra_session":
                        session_id = att.value
                        #associate
                        associate_session_and_order_from_shopify_object(session_id,order, store_url)   
                        #try again
                        user_session_record = conn.execute(
                            sqlalchemy.select(user_session_table.c.uuid).\
                            where(user_session_table.c.order_id == str(order.id))
                        ).fetchone()
                        if user_session_record is not None:
                            user_session_id = user_session_record.uuid
                        
            # Add order to db
            try:
                
                conn.execute(sqlalchemy.insert(order_table).values(
                    uuid=str(uuid.uuid4()),
                    cart_token=order.cart_token,
                    time=time.time(),
                    created_at=dateutil.parser.parse(order.created_at).timestamp(),
                    currency=order.currency,
                    discount_code=applied_discount_code,
                    session_id=user_session_id,
                    shopify_id=str(order.id),
                    store_id=result.uuid,
                    subtotal_price=order.subtotal_price
                ))
                conn.commit()
            except sqlalchemy.exc.IntegrityError:
                conn.rollback()
                pass
                    
        del orders
        del order_list
        
        conn.execute(sqlalchemy.update(store_table).\
            where(store_table.c.uuid == result.uuid).\
            values(last_order_sync=time.time())
        )
        conn.commit()
        
        conn.commit()
    logger.info("Finished synchronizing orders")

def lambda_handler(event, context):
    with app.app_context():
        sync_orders_function()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "sync-orders"
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--store-id",
        help="Store id.",
        dest="store_id",
    )

    args = parser.parse_args()
    if args.action == "sync-orders":
        with app.app_context():
            sync_orders_function(store_id=args.store_id)
    