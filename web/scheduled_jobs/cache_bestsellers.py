import os
import sys
import inspect
import time
from collections import defaultdict


currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
import json
import sqlalchemy
from app import app
import config
from utils import get_generic_logger
from db import webhook_request
from modules.record_order import associate_session_and_order_from_shopify_object
from db import webhook_request, store_url as store_url_table
from ephemeral_store import EphemeralStore
from datetime import datetime, timedelta

logger = get_generic_logger(__name__)


def cache_bestsellers(store_url=None):
    # Get the current timestamp and the timestamp for 30 days ago
    thirty_days_ago = time.mktime((datetime.now() - timedelta(days=30)).timetuple())
    #get all webhooks data
    webhook_data = webhook_request.query.filter((webhook_request.shop_url==store_url)| (store_url is None), 
                            webhook_request.time >= thirty_days_ago,
                            webhook_request.event == 'orders/create'
    )
    store_product_counts = {}
    for row in webhook_data:
        if store_product_counts.get(row.shop_url) is None:
            store_product_counts[row.shop_url] = defaultdict(int)
        data = json.loads(row.data)
        for line_item in data["line_items"]:
            product_id = line_item["product_id"]
            quantity = line_item["quantity"]
            store_product_counts[row.shop_url][product_id] += quantity       
    
    for shop_url, product_counts in store_product_counts.items():
        store_url_record = store_url_table.query.filter_by(url=shop_url.lower()).first()
        #get top 10
        bestsellers = sorted(product_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        #get product ids only
        bestseller_ids = [product_id for product_id, sale_count in bestsellers if sale_count>=100]
        #put in cache
        delta = timedelta(hours=24)
        logger.info(f"Cache product, {shop_url}, {store_url_record.store_id}, {bestseller_ids}")
        EphemeralStore.add_to_store(key=store_url_record.store_id, expire_timedelta=delta, data=json.dumps(bestseller_ids)) 

    logger.info("Finished.")


def lambda_handler(event, context):
    with app.app_context():
        cache_bestsellers()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "cache-bestsellers"
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--store-url",
        help="Store url.",
        dest="store_url",
    )

    args = parser.parse_args()
    if args.action == "cache-bestsellers":
        with app.app_context():
            cache_bestsellers(store_url=args.store_url)