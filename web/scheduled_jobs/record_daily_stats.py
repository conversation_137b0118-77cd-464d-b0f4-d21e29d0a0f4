import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import datetime
import dateutil.tz
import os
from sqlalchemy import and_, create_engine, exists, func, insert, MetaData, not_, select, sql
import time
import uuid
from app import app
import config
from utils import get_generic_logger
import argparse

logger = get_generic_logger(__name__)


def record_daily_stats_function():
    # Connect to database
    engine = create_engine(config.SQLALCHEMY_DATABASE_URI)
    conn = engine.connect()

    # Get the tables we need
    meta_data = MetaData()
    meta_data.reflect(bind=engine)
    daily_stat_table = meta_data.tables["daily_stat"]
    merchant_engagement_table = meta_data.tables["merchant_engagement"]
    model_decision_table = meta_data.tables["model_decision"]
    store_table = meta_data.tables["store"]
    user_session_table = meta_data.tables["user_session"]
    
    current_day = (datetime.datetime
        .now(dateutil.tz.gettz('America/New_York'))
        .replace(hour=0, minute=0, second=0, microsecond=0)
        .astimezone(dateutil.tz.tzutc()))
    previous_day = current_day - datetime.timedelta(days=1)
    start_time = previous_day.timestamp()
    end_time = current_day.timestamp()

    store_results = conn.execute(
        select(store_table.c.uuid).where(store_table.c.test_store == False)
    ).fetchall()
    for store_record in store_results:
        logger.info("Starting store " + str(store_record.uuid))
        # Make sure there isn't already a stat for this day
        check_stat = conn.execute(select(daily_stat_table.c.uuid).\
            where(daily_stat_table.c.store_id == store_record.uuid).\
            where(daily_stat_table.c.start_time == start_time)).fetchone()
        if check_stat is not None:
            logger.info("Skipping store " + str(store_record.uuid) + " because there is already a stat for this day")
            continue
        
        base_query = select(func.count(), func.sum(user_session_table.c.cart_size)).\
            where(user_session_table.c.time >= start_time).\
            where(user_session_table.c.time < end_time).\
            where(user_session_table.c.store_id == store_record.uuid)
        
        # Merchant analytics
        # Total audience
        analytics_total_audience_model_decision_subquery = select(model_decision_table.c.user_session_id).where(
            and_(
                model_decision_table.c.user_session_id == user_session_table.c.uuid,
                model_decision_table.c.live_version == True
            )
        )
        analytics_total_audience_sessions = conn.execute(
            base_query.where(
                and_(
                    (user_session_table.c.holdout == False) | (user_session_table.c.holdout == None),
                    user_session_table.c.vandra_opportunity == True,
                    exists(analytics_total_audience_model_decision_subquery)
                )
            )
        ).fetchone()[0]
        analytics_total_audience_conversions_query = conn.execute(
            base_query.where(
                and_(
                    (user_session_table.c.holdout == False) | (user_session_table.c.holdout == None),
                    user_session_table.c.vandra_opportunity == True,
                    user_session_table.c.conversion == True,
                    exists(analytics_total_audience_model_decision_subquery)
                )
            )
        ).fetchone()
        analytics_total_audience_conversions = analytics_total_audience_conversions_query[0]
        analytics_total_audience_revenue = analytics_total_audience_conversions_query[1]

        # Protected audience
        analytics_protected_audience_model_decision_subquery = select(model_decision_table.c.user_session_id).where(
            and_(
                model_decision_table.c.user_session_id == user_session_table.c.uuid,
                model_decision_table.c.live_version == True,
                model_decision_table.c.decided_to_show_discount == False
            )
        )
        analytics_protected_audience_sessions = conn.execute(
            base_query.where(
                and_(
                    (user_session_table.c.holdout == False) | (user_session_table.c.holdout == None),
                    user_session_table.c.vandra_opportunity == True,
                    exists(analytics_protected_audience_model_decision_subquery)
                )
            )
        ).fetchone()[0]
        analytics_protected_audience_conversions_query = conn.execute(
            base_query.where(
                and_(
                    (user_session_table.c.holdout == False) | (user_session_table.c.holdout == None),
                    user_session_table.c.vandra_opportunity == True,
                    user_session_table.c.conversion == True,
                    exists(analytics_protected_audience_model_decision_subquery)
                )
            )
        ).fetchone()
        analytics_protected_audience_conversions = analytics_protected_audience_conversions_query[0]
        analytics_protected_audience_revenue = analytics_protected_audience_conversions_query[1]

        # Actionable audience
        analytics_actionable_audience_model_decision_subquery = select(model_decision_table.c.user_session_id).where(
            and_(
                model_decision_table.c.user_session_id == user_session_table.c.uuid,
                model_decision_table.c.live_version == True,
                model_decision_table.c.decided_to_show_discount == True
            )
        )
        analytics_actionable_audience_sessions = conn.execute(
            base_query.where(
                and_(
                    (user_session_table.c.holdout == False) | (user_session_table.c.holdout == None),
                    user_session_table.c.vandra_opportunity == True,
                    exists(analytics_actionable_audience_model_decision_subquery)
                )
            )
        ).fetchone()[0]
        analytics_actionable_audience_conversions_query = conn.execute(
            base_query.where(
                and_(
                    (user_session_table.c.holdout == False) | (user_session_table.c.holdout == None),
                    user_session_table.c.vandra_opportunity == True,
                    user_session_table.c.conversion == True,
                    exists(analytics_actionable_audience_model_decision_subquery)
                )
            )
        ).fetchone()
        analytics_actionable_audience_conversions = analytics_actionable_audience_conversions_query[0]
        analytics_actionable_audience_revenue = analytics_actionable_audience_conversions_query[1]

        model_decision_subquery_1 = select(model_decision_table.c.user_session_id).where(
            and_(
                model_decision_table.c.user_session_id == user_session_table.c.uuid,
                model_decision_table.c.live_version == True,
                model_decision_table.c.decided_to_show_discount == True
            ))
        model_decision_subquery_2 = select(model_decision_table.c.user_session_id).where(
            and_(
                model_decision_table.c.user_session_id == user_session_table.c.uuid,
                model_decision_table.c.live_version == True,
                model_decision_table.c.time > 0
            ))

        conversions_query = conn.execute(base_query.\
            where(user_session_table.c.conversion == True)
        ).fetchone()
        conversions = conversions_query[0]
        conversions_dollars = conversions_query[1]
        
        conversions_before_predictions_query = conn.execute(base_query.\
            where(user_session_table.c.conversion == True).\
            where(not_(sql.expression.exists(
                select().where(model_decision_table.c.user_session_id == user_session_table.c.uuid)
            )))
        ).fetchone()
        conversions_before_predictions = conversions_before_predictions_query[0]
        conversions_before_predictions_dollars = (conversions_before_predictions_query[1] or 0)
        
        conversions_vandra_show_query = conn.execute(
            base_query.where(
                and_(
                    user_session_table.c.conversion == True,
                    exists(model_decision_subquery_1)
                )
            )
        ).fetchone()
        conversions_vandra_show = conversions_vandra_show_query[0]
        conversions_vandra_show_dollars = conversions_vandra_show_query[1]
        
        holdout_sessions = conn.execute(base_query.\
            where(user_session_table.c.holdout == True)
        ).fetchone()[0]

        holdout_sessions_with_show_predictions = conn.execute(
            base_query.where(
                and_(
                    user_session_table.c.holdout == True,
                    exists(model_decision_subquery_1)
                )
            )
        ).fetchone()[0]
        
        holdout_sessions_with_conversions_query = conn.execute(base_query.\
            where(user_session_table.c.holdout == True).\
            where(user_session_table.c.conversion == True)
        ).fetchone()
        holdout_sessions_with_conversions = holdout_sessions_with_conversions_query[0]
        holdout_sessions_with_conversions_dollars = holdout_sessions_with_conversions_query[1]
        
        holdout_session_conversions_vandra_show_query = conn.execute(base_query.where(
            and_(
                user_session_table.c.holdout == True,
                user_session_table.c.conversion == True,
                exists(model_decision_subquery_1)
            )
        )).fetchone()
        holdout_session_conversions_vandra_show = holdout_session_conversions_vandra_show_query[0]
        holdout_session_conversions_vandra_show_dollars = holdout_session_conversions_vandra_show_query[1]
        
        merchant_admin_engagements = conn.execute(
            select(func.count()).\
            where(merchant_engagement_table.c.time >= start_time).\
            where(merchant_engagement_table.c.time < start_time).\
            where(merchant_engagement_table.c.store_id == store_record.uuid)
        ).fetchone()[0]
        
        opportunity_session_conversions_query = conn.execute(base_query.\
            where(user_session_table.c.vandra_opportunity == True).\
            where(user_session_table.c.conversion == True)
        ).fetchone()
        opportunity_session_conversions = opportunity_session_conversions_query[0]
        opportunity_session_conversions_dollars = opportunity_session_conversions_query[1]
        
        opportunity_session_conversions_before_predictions_query = conn.execute(base_query.\
            where(user_session_table.c.vandra_opportunity == True).\
            where(user_session_table.c.conversion == True).\
            where(not_(sql.expression.exists(
                select().where(model_decision_table.c.user_session_id == user_session_table.c.uuid)
            )))
        ).fetchone()
        opportunity_session_conversions_before_predictions = opportunity_session_conversions_before_predictions_query[0]
        opportunity_session_conversions_before_predictions_dollars = (opportunity_session_conversions_before_predictions_query[1] or 0)
        
        opportunity_session_conversions_vandra_show_query = conn.execute(base_query.where(
            and_(
                user_session_table.c.vandra_opportunity == True,
                user_session_table.c.conversion == True,
                exists(model_decision_subquery_1)
            )
        )).fetchone()
        opportunity_session_conversions_vandra_show = opportunity_session_conversions_vandra_show_query[0]
        opportunity_session_conversions_vandra_show_dollars = opportunity_session_conversions_vandra_show_query[1]
        
        opportunity_sessions = conn.execute(base_query.where(
            user_session_table.c.vandra_opportunity == True
        )).fetchone()[0]
        
        opportunity_sessions_with_predictions = conn.execute(base_query.where(
            and_(
                user_session_table.c.vandra_opportunity == True,
                exists(model_decision_subquery_2)
            )
        )).fetchone()[0]
        
        opportunity_sessions_with_show_predictions = conn.execute(base_query.where(
            and_(
                user_session_table.c.vandra_opportunity == True,
                exists(model_decision_subquery_1)
            )
        )).fetchone()[0]
        
        popups_applied = conn.execute(base_query.where(
            user_session_table.c.vandra_discount_applied == True
        )).fetchone()[0]
        
        popups_dismissed = conn.execute(base_query.where(
            user_session_table.c.vandra_dismissed == True
        )).fetchone()[0]
        
        popups_shown = conn.execute(base_query.where(
            user_session_table.c.vandra_shown == True
        )).fetchone()[0]
        
        sessions = conn.execute(base_query).fetchone()[0]
        
        sessions_with_predictions = conn.execute(base_query.where(
            exists(model_decision_subquery_2)
        )).fetchone()[0]
        
        sessions_with_show_predictions = conn.execute(base_query.where(
            exists(model_decision_subquery_1)
        )).fetchone()[0]
        
        vandra_conversions_query = conn.execute(base_query.where(
            user_session_table.c.vandra_conversion == True)
        ).fetchone()
        vandra_conversions = vandra_conversions_query[0]
        vandra_conversions_dollars = vandra_conversions_query[1]
        
        vandra_conversions_not_shown_that_session_query = conn.execute(base_query.\
            where(user_session_table.c.vandra_conversion == True).\
            where(user_session_table.c.vandra_shown == False)
        ).fetchone()
        vandra_conversions_not_shown_that_session = vandra_conversions_not_shown_that_session_query[0]
        vandra_conversions_not_shown_that_session_dollars = vandra_conversions_not_shown_that_session_query[1]
        
        # TODO - vandra_conversions_not_shown_any_session
        
        # Add stat to db
        conn.execute(insert(daily_stat_table).values(
            uuid=str(uuid.uuid4()),
            time=time.time(),
            store_id=store_record.uuid,
            start_time=start_time,
            end_time=end_time,
            analytics_actionable_audience_conversions=analytics_actionable_audience_conversions,
            analytics_actionable_audience_revenue=analytics_actionable_audience_revenue,
            analytics_actionable_audience_sessions=analytics_actionable_audience_sessions,
            analytics_protected_audience_conversions=analytics_protected_audience_conversions,
            analytics_protected_audience_revenue=analytics_protected_audience_revenue,
            analytics_protected_audience_sessions=analytics_protected_audience_sessions,
            analytics_total_audience_conversions=analytics_total_audience_conversions,
            analytics_total_audience_revenue=analytics_total_audience_revenue,
            analytics_total_audience_sessions=analytics_total_audience_sessions,
            conversions=conversions,
            conversions_dollars=conversions_dollars,
            conversions_before_predictions=conversions_before_predictions,
            conversions_before_predictions_dollars=conversions_before_predictions_dollars,
            conversions_vandra_show=conversions_vandra_show,
            conversions_vandra_show_dollars=conversions_vandra_show_dollars,
            holdout_sessions=holdout_sessions,
            holdout_sessions_with_show_predictions=holdout_sessions_with_show_predictions,
            holdout_session_conversions=holdout_sessions_with_conversions,
            holdout_session_conversions_dollars=holdout_sessions_with_conversions_dollars,
            holdout_session_conversions_vandra_show=holdout_session_conversions_vandra_show,
            holdout_session_conversions_vandra_show_dollars=holdout_session_conversions_vandra_show_dollars,
            merchant_admin_engagements=merchant_admin_engagements,
            opportunity_session_conversions=opportunity_session_conversions,
            opportunity_session_conversions_dollars=opportunity_session_conversions_dollars,
            opportunity_session_conversions_before_predictions=opportunity_session_conversions_before_predictions,
            opportunity_session_conversions_before_predictions_dollars=opportunity_session_conversions_before_predictions_dollars,
            opportunity_session_conversions_vandra_show=opportunity_session_conversions_vandra_show,
            opportunity_session_conversions_vandra_show_dollars=opportunity_session_conversions_vandra_show_dollars,
            opportunity_sessions=opportunity_sessions,
            opportunity_sessions_with_predictions=opportunity_sessions_with_predictions,
            opportunity_sessions_with_show_predictions=opportunity_sessions_with_show_predictions,
            popups_applied=popups_applied,
            popups_dismissed=popups_dismissed,
            popups_shown=popups_shown,
            sessions=sessions,
            sessions_with_predictions=sessions_with_predictions,
            sessions_with_show_predictions=sessions_with_show_predictions,
            vandra_conversions=vandra_conversions,
            vandra_conversions_dollars=vandra_conversions_dollars,
            vandra_conversions_not_shown_that_session=vandra_conversions_not_shown_that_session,
            vandra_conversions_not_shown_that_session_dollars=vandra_conversions_not_shown_that_session_dollars
        ))
        conn.commit()
        logger.info("Finishing store " + store_record.uuid)
    logger.info("Finished recording daily stats")


def lambda_handler(event, context):
    with app.app_context():
        record_daily_stats_function()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "record-stats"
        ],
        required=True,
        dest="action",
    )

    args = parser.parse_args()
    if args.action == "record-stats":
        with app.app_context():
            record_daily_stats_function()
    