import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 


import argparse
import datetime
import dateutil
import math
import os
import shopify
import sqlalchemy
import time
import uuid
from constants import INTERVENTION_STATUS_SHOWN
from db import store, order, usage_charge, onboarding_details, session_intervention, store_intervention_association
from app import app, db_session
import config
from utils import get_generic_logger
from sqlalchemy import func

logger = get_generic_logger(__name__)



def get_tier_pricing(order_count):
    """Calculate monthly charge based on order count tiers"""
    if order_count <= 500:
        return 39, "Starter"
    elif order_count <= 1000:
        return 99, "Pro"
    elif order_count <= 2500:
        return 249, "Advanced"
    elif order_count <= 5000:
        return 499, "Growth"
    elif order_count <= 10000:
        return 999, "Enterprise"
    else:
        # Enterprise + overage ($50 per 1k orders over 10k)
        overage_orders = order_count - 10000
        overage_charge = (overage_orders // 1000) * 50  # Integer division
        return 999 + overage_charge, "Enterprise+"

def generate_usage_charges(store_id=None, billing_date=None):
    """Generate monthly usage charges based on order volume tiers"""
    try:
        # Connect to database
        current_time = int(time.time())
        # Get first and last day of previous month in UTC
        today = billing_date if billing_date else datetime.datetime.now(datetime.timezone.utc)
        first_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
        last_of_prev_month = first_of_this_month - datetime.timedelta(days=1)
        first_of_prev_month = last_of_prev_month.replace(day=1)
        
        # Convert to unix timestamps (already in UTC)
        month_start = int(first_of_prev_month.timestamp())
        month_end = int(first_of_this_month.timestamp())

        logger.info(f"Generating charges for period: {first_of_prev_month.strftime('%B %Y')}")

        # First get stores that have had nudges
        stores_with_nudges = session_intervention.query.join(
            store_intervention_association,
            session_intervention.store_intervention_association_id == store_intervention_association.uuid
        ).join(
            store,
            store_intervention_association.store_id == store.uuid
        ).join(
            onboarding_details,
            store.onboarding_details == onboarding_details.uuid
        ).with_entities(
            store.uuid,
            func.min(session_intervention.time).label('first_nudge_time')
        ).filter(
            session_intervention.opportunity == True,
            session_intervention.holdout == False,
            session_intervention.time <= month_end,
            session_intervention.time >= month_start,
            session_intervention.status == INTERVENTION_STATUS_SHOWN,
            session_intervention.time >= onboarding_details.billing_start,
            ((store.status == 'Active') | (store.status == None)),
            store.test_store == False,
            ((onboarding_details.billing_trial_end == None) | 
             (onboarding_details.billing_trial_end <= month_end)),
            onboarding_details.billing_start <= month_end,
            store.last_order_sync >= month_start
        ).group_by(
            store.uuid
        ).subquery()

        stores_query = store.query.join(
            onboarding_details,
            store.onboarding_details == onboarding_details.uuid
        ).join(
            stores_with_nudges,
            store.uuid == stores_with_nudges.c.uuid
        )

        #count how many stores we're charging
        store_count = stores_query.count()
        logger.info(f"{store_count} stores to consider for charges")

        if store_id:
            stores_query = stores_query.filter(store.uuid == store_id)
        
        for store_record in stores_query.all():
            try:
                # Get trial end time for this store
                trial_end = store_record.onboarding_details_record.billing_trial_end
                billing_start = store_record.onboarding_details_record.billing_start
                
                # Use the later of count_start or first nudge time
                count_start = max(month_start, trial_end or billing_start)
                
                
                # Check if we already charged for this month
                existing_charge = usage_charge.query.filter(
                    usage_charge.store_id == store_record.uuid,
                    usage_charge.start_time >= count_start,
                    usage_charge.end_time <= month_end
                ).first()
                
                if existing_charge:
                    logger.info(f"Skipping store {store_record.uuid} because we already charged for this period")
                    continue

                # Count eligible orders in the last period
                order_count = order.query.filter(
                    order.store_id == store_record.uuid,
                    order.created_at >= count_start,
                    order.created_at < month_end,
                    order.cart_token != None
                ).count()

                if order_count == 0:
                    continue

                # Calculate charge based on tier
                charge_amount, tier_name = get_tier_pricing(order_count)
                
                # Format dates for description
                month_name = first_of_prev_month.strftime('%B %Y')
                description = f"Vandra {tier_name} Plan - {month_name}"

                # Create usage charge record
                new_charge = usage_charge(
                    uuid=str(uuid.uuid4()),
                    time=current_time,
                    description=description,
                    price=charge_amount,
                    store_id=store_record.uuid,
                    total_sales=order_count,  # Using order count instead of sales
                    start_time=count_start, # Use the later of month_start or trial_end as the counting start
                    end_time=month_end
                )
                db_session.add(new_charge)
                db_session.commit()

                logger.info(f"Created {tier_name} tier charge for store {store_record.uuid}: ${charge_amount}")

            except Exception as e:
                logger.exception(f"Error processing store {store_record.uuid}: {str(e)}")
                db_session.rollback()
                continue

        logger.info("Finished generating usage charges")

    except Exception as e:
        logger.exception(f"Error in generate_usage_charges: {str(e)}")
        db_session.rollback()

def lambda_handler(event, context):
    with app.app_context():
        generate_usage_charges()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "generate-usage-charges"
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--store-id",
        help="Store id.",
        dest="store_id",
    )

    parser.add_argument(
        "--billing-date",
        help="Override billing date (YYYY-MM-DD format)",
        dest="billing_date",
        type=lambda s: datetime.datetime.strptime(s, '%Y-%m-%d')
    )

    args = parser.parse_args()
    if args.action == "generate-usage-charges":
        with app.app_context():
            generate_usage_charges(
                store_id=args.store_id,
                billing_date=args.billing_date
            )
    