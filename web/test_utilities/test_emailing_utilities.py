import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
from app import app
from db import order, webhook_request, user_session, store_url, store, discount_code, db, usage_charge
from helpers import send_mail
from utils import get_generic_logger
from communications import EMAIL_TEMPLATE_USAGE_CHARGE, Notifier
logger = get_generic_logger(__name__)
from modules.vandra_admin import vandra_send_email_from_usage_charge
import base64

def send_usage_charge_email(usage_charge_id,email_override=None):
    usage_charge_record = usage_charge.query.filter_by(uuid=usage_charge_id).first()
    vandra_send_email_from_usage_charge(usage_charge_record,email_override=email_override)



def test_sns_email():
    message_body = "We've just submitted a charge for our commission on Vandra-nudged sales for the period \n"
    message_body += "\n"
    message_body += "Total Vandra-Nudged Sales during Period: $\n"
    message_body += "Commission Rate: %\n"
    message_body += "Vandra Commissions during Period: $\n"
    message_body += "\n"
    message_body += "We have also attached a detailed list of the underlying orders for the Vandra-nudged sales figure above.\n"
    message_body += "\n"
    message_body += "We value being a trusted partner of your brand and supporting your efforts to squeeze more sales out of your existing traffic. If you have any questions about this charge, please don't hesitate to contact <NAME_EMAIL>.\n"
    message_body += "\n"
    message_body += "Have a great day!\n"
    message_body += "\n"
    message_body += "Sincerely,\n"
    message_body += "The Vandra Team\n"
    message_body += "https://www.vandra.ai"
    
    csv = [",".join([
                "Date",
                "Revenue",
                "Vandra Orders",
                "Actionable Sessions",
                "Show Rate",
                "Show-to-Apply Rate",
                "Apply-to-Convert Rate",
                "Commission"
        ])]
    csv = "\n".join(csv)
    encoded_csv = base64.b64encode(csv.encode("ascii"))


    csv_attachment = { "filename": "vandra_transactions.csv",
                            "contentType": "text/csv",
                            "data": encoded_csv }
    

    send_mail("hello", message_body,to=["<EMAIL>"], attachment=csv_attachment, force_send=True)
            


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "test-sns-email","send-charge-template", "send-usage-charge-email"
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--usage-charge-id",
        help="Charge id.",
        dest="usage_charge_id",
    )

    parser.add_argument(
        "--email-override",
        help="Email override.",
        dest="email_override",
        required=True,
    )

    args = parser.parse_args()
    if args.action == "test-sns-email":
        with app.app_context():
            test_sns_email()
    elif args.action == "send-usage-charge-email":
        with app.app_context():
            send_usage_charge_email(args.usage_charge_id, args.email_override)
    elif args.action == "send-charge-template":
        with app.app_context():
            Notifier.send_from_template(EMAIL_TEMPLATE_USAGE_CHARGE,[{"email": "<EMAIL>"}, {"email": "<EMAIL>"}],data={"start_date" : "ok"})