import argparse
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app, db_session
from db import session_discount_code
from modules.discount_code_use_cases import get_discount_code_details_from_shopify
from utils import get_generic_logger

logger = get_generic_logger(__name__)

def fetch_discount_metadata(discount_code_id):
    """
    Fetches Shopify metadata for a specific discount code record
    """
    try:
        success, error = get_discount_code_details_from_shopify(discount_code_id)
        if not success:
            logger.error(f"Failed to fetch discount metadata: {error}")
            return False
        
        # Get updated record to show metadata
        discount_record = session_discount_code.query.get(discount_code_id)
        if not discount_record:
            logger.error("Discount code record not found")
            return False
            
        print("\nDiscount Code Details:")
        print(f"Code: {discount_record.discount_code_value}")
        print(f"First detected in: {discount_record.first_detected_in}")
        print(f"Last detected in: {discount_record.last_detected_in}")
        print("\nShopify Metadata:")
        if discount_record.discount_code_metadata:
            import json
            print(json.dumps(discount_record.discount_code_metadata, indent=2))
        else:
            print("No metadata available")
            
        return True

    except Exception as e:
        logger.error(f"Error in fetch_discount_metadata: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Discount Code Utility')
    parser.add_argument('action', choices=['fetch_metadata'], 
                      help='Action to perform')
    parser.add_argument('--discount_code_id', type=str,
                      help='UUID of the discount code record')

    args = parser.parse_args()

    if args.action == 'fetch_metadata':
        if not args.discount_code_id:
            logger.error("discount_code_id is required for fetch_metadata action")
            return False
        return fetch_discount_metadata(args.discount_code_id)

if __name__ == "__main__":
    with app.app_context():
        success = main()
        sys.exit(0 if success else 1) 