import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
from app import app
from utils import get_generic_logger
import shopify
from modules.record_order import associate_session_and_order_from_shopify_object
from db import user_session
from db import order as order_table, db

logger = get_generic_logger(__name__)

def associate_order_and_session(store_url, access_token, order_id):
    session = shopify.Session(store_url, os.environ['SHOPIFY_LEGACY_API_VERSION'], access_token)
    shopify.ShopifyResource.activate_session(session)
    #get order
    if order_id:
        orders = [shopify.Order.find(order_id)]
    else:
        order_list = shopify.Order.find(status="any",  created_at_min="2024-08-11", created_at_max="2024-08-12")
        orders = []
        for order in order_list:
            orders.append(order)
        while order_list.has_next_page():
            print("pulling more")
            order_list = order_list.next_page()
            for order in order_list:
                print(order.created_at)
                orders.append(order)
    for order in orders:
        for att in  order.note_attributes:
            if att.name == "__vandra_session":
                session_id = att.value
                existing_session = user_session.query.filter(user_session.order_id==str(order.id)).first()
                order_object = order_table.query.filter_by(shopify_id=str(order.id)).first()
                if existing_session:
                    logger.info(f"Order {order.id} already linked to session, skipping")
                    continue
                logger.info(f"Attempting to link {order.id}")
                associate_session_and_order_from_shopify_object(session_id,order, store_url)
                #try again and attach session id to order this time
                order_object = order_table.query.filter_by(shopify_id=str(order.id)).first()
                existing_session = user_session.query.filter(user_session.order_id==str(order.id)).first()
                if existing_session and order_object and order_object.session_id is None:
                    logger.info(f"Order {order.id} object did not have the session id, adding it")
                    order_object.session_id = session_id
                    db.session.commit() 

            
                


def fetch_order_details(store_url, access_token, order_id):
    session = shopify.Session(store_url, os.environ['SHOPIFY_LEGACY_API_VERSION'], access_token)
    shopify.ShopifyResource.activate_session(session)
    #get order
    order = shopify.Order.find(order_id)
    for att in  order.note_attributes:
        if att.name == "__vandra_session":
            print(att.value)
    #print(order.__dict__)
    print({
        "created_at" : order.created_at,
        "subtotal_price": order.subtotal_price,
        "id" : order.id,
        "discount_codes" : [{"code": x.code, "amount" : x.amount} for x in order.discount_codes]
    })
        
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "fetch-order-details","associate-order-session"
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--access-token",
        help="Access token.",
        dest="access_token",
    )

    parser.add_argument(
        "--order-id",
        help="Order id.",
        dest="order_id",
    )

    parser.add_argument(
        "--store-url",
        help="Store url.",
        dest="store_url",
    )

    args = parser.parse_args()
    if args.action == "fetch-order-details":
        with app.app_context():
            fetch_order_details(args.store_url, args.access_token, args.order_id)
    elif args.action == "associate-order-session":
        with app.app_context():
            associate_order_and_session(args.store_url, args.access_token, args.order_id)
    