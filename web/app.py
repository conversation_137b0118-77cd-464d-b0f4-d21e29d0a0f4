from flask import Flask, render_template, redirect, request, g, abort, send_from_directory
from db import db, order, user_session, webhook_request
from blueprints.base_nudge import base_nudge_api
from blueprints.pick_up_where_you_left_off import pick_up_where_you_left_off_api
from blueprints.discount import discount_blueprint

import json
import os
import time
import uuid
from flask_migrate import Migrate
import sentry_sdk
from sentry_sdk.integrations.flask import FlaskIntegration
from sentry_sdk.integrations.rq import RqIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from flask_cors import CORS, cross_origin
from shopify import session_token
import logging

# Set up logging for better debugging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_pyfile('config.py')

CORS(
    app,
    origins="*",
    methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    supports_credentials=True
)


if app.config.get("SENTRY_DSN") not in [None, ""]:
    APP_ENVIRONMENT = app.config.get("APP_ENVIRONMENT", "local")
    if APP_ENVIRONMENT == "production":
        traces_sample_rate = 0.001
    else:
        traces_sample_rate = 1.0
    sentry_sdk.init(
        dsn=app.config["SENTRY_DSN"],
        environment=APP_ENVIRONMENT,
        integrations=[FlaskIntegration(), RqIntegration(), RedisIntegration()],
        attach_stacktrace=True,
        send_default_pii=True,
        traces_sample_rate=traces_sample_rate,
    )

# Used by the data science code to run in containers
if os.environ.get("SQLALCHEMY_DATABASE_URI") not in [None, ""]:
    app.config["SQLALCHEMY_DATABASE_URI"] = os.environ["SQLALCHEMY_DATABASE_URI"]
app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
    "pool_pre_ping": True,
    "connect_args": {
        "keepalives": 1,
        "keepalives_idle": 30,
        "keepalives_interval": 10,
        "keepalives_count": 5,
        "application_name" : app.config.get("APP_NAME")
    },
}
app.config["ALEMBIC_CONTEXT"] = {
    "environment_context": {"config": {"include_schemas": True}}
}

db.init_app(app)
db_session = db.session
# add ability to track db migrations

def check_shopify_auth():
    """Middleware to check for Authorization header on /react/* routes."""
    if request.path.startswith("/react/"):
        auth_header = request.headers.get('Authorization')
        token = request.args.get('id_token')
        
        # Check for admin store emulation
        emulate_store_id = request.args.get('emulate_store')
        
        if emulate_store_id:
            # This is an admin emulation request - validate admin permissions
            if token:
                # First decode the session normally to get admin user info
                try:
                    request.shopify_session = session_token._decode_session_token(
                        token,
                        app.config['SHOPIFY_API_KEY'],
                        app.config['SHOPIFY_SHARED_SECRET']
                    )
                    
                    # Check if there's an existing emulation session and validate timeout
                    existing_session = getattr(request, 'shopify_session', {})
                    if existing_session.get('emulated') and existing_session.get('emulation_start_time'):
                        # Check if emulation session has expired (2 hours)
                        if time.time() - existing_session.get('emulation_start_time', 0) > 7200:
                            return "Emulation session expired. Please refresh and start a new emulation session.", 401
                    
                    # Validate that this user is a super admin
                    from modules.merchant_admin import admin_check_status_handler
                    
                    # Temporarily store the original session
                    original_session = request.shopify_session
                    
                    # Check admin status
                    admin_check = admin_check_status_handler()
                    admin_data = admin_check.get_json() if hasattr(admin_check, 'get_json') else {}
                    
                    if not admin_data.get('is_admin', False):
                        return "Unauthorized - Super admin required for store emulation", 403
                    
                    # Create emulated session for the target store
                    from db import store, store_url
                    target_store = store.query.filter_by(uuid=emulate_store_id).first()
                    if not target_store:
                        return "Invalid store ID for emulation", 400
                    
                    target_store_url = store_url.query.filter_by(
                        store_id=target_store.uuid,
                        default=True
                    ).first()
                    
                    if not target_store_url:
                        return "Store URL not found for emulation", 400
                    
                    # Override the session with emulated store context
                    request.shopify_session = {
                        'dest': f'https://{target_store_url.url}',
                        'emulated': True,
                        'original_session': original_session,
                        'admin_email': admin_data.get('email'),
                        'original_admin_email': admin_data.get('email'),  # Preserve original admin email
                        'emulation_start_time': time.time()  # Add timestamp for session timeout
                    }
                    
                    # Log admin emulation for security audit
                    logger.info(f"Admin emulation started: {admin_data.get('email')} emulating store {target_store.name} ({emulate_store_id})")
                    
                    return  # Allow the request to proceed
                    
                except Exception as e:
                    # Handle session token expiration gracefully for emulation mode
                    if "Signature has expired" in str(e) or "ExpiredSignatureError" in str(e):
                        logger.warning(f"Session token expired during emulation mode for store {emulate_store_id}: {str(e)}")
                        return "Session token has expired. Please refresh the page and try again.", 401
                    logger.error(f"Admin emulation error for store {emulate_store_id}: {str(e)}")
                    return f"Admin emulation error: {str(e)}", 500
            else:
                return "Authorization header required for admin emulation", 401
        
        # Normal Shopify authentication
        try:
            if auth_header:
                request.shopify_session = session_token.decode_from_header(
                    auth_header,
                    app.config['SHOPIFY_API_KEY'],
                    app.config['SHOPIFY_SHARED_SECRET']
                )
            elif token:
                request.shopify_session = session_token._decode_session_token(
                    token,
                    app.config['SHOPIFY_API_KEY'],
                    app.config['SHOPIFY_SHARED_SECRET']
                )
            else:
                return "Authorization header is missing", 401
        except Exception as e:
            # Handle session token expiration gracefully for normal authentication
            if "Signature has expired" in str(e) or "ExpiredSignatureError" in str(e):
                logger.warning(f"Session token expired during normal authentication for path {request.path}: {str(e)}")
                return "Session token has expired. Please refresh the page and try again.", 401
            # Re-raise other exceptions
            logger.error(f"Authentication error for path {request.path}: {str(e)}")
            raise e

def include_migration_object(object, name, type_, reflected, compare_to):
    """adds backwards compatibility to environments with existing "sessions" tables
    created by flask-session"""
    if (
        type_ == "table" and name == "sessions"
    ):
        return False
    else:
        return True


migrate = Migrate(app, db, include_object=include_migration_object)
app.config['SESSION_SQLALCHEMY'] = db

# register blueprints
app.register_blueprint(base_nudge_api, url_prefix="/base_nudge")
app.register_blueprint(pick_up_where_you_left_off_api, url_prefix="/intervention/pick_up_where_you_left_off")
app.register_blueprint(discount_blueprint)

@app.before_request
def before_request():
    # Handle CORS preflight requests early to avoid authentication issues
    if request.method == 'OPTIONS':
        # For OPTIONS requests, skip authentication and let CORS middleware handle it
        return
    
    auth_check = check_shopify_auth()
    if auth_check: 
        return auth_check

@app.errorhandler(500)
def error_handler_500(e):
    session_cookie = request.args.get("session_cookie") or request.form.get("session_cookie")
    sentry_sdk.set_context("session", {
        "cookie": session_cookie
    })
    return "There was an error", 500

# External endpoints for .js functionality

@app.route("/app_js_include.js")
def app_js_include_route():
    return send_from_directory("static/", "app_js_include.js")

@app.route("/extension/<file_name>")
def app_extension_route(file_name):
    return send_from_directory("../extensions/vandra-js/assets/", file_name)

@app.route('/favicon.ico')
def favicon_route():
    return redirect('/static/img/favicon.ico')

'''@app.route("/popup_image/<path:path>")
def popup_image_route(path):
    return send_from_directory("static/popup_images/", path)'''

@app.route("/record_js_error", methods=["POST"])
def record_js_error_route():
    email_body = "Message: " + request.form.get("message", "") + "\n"
    email_body += "Stack: " + request.form.get("stack", "")
    
    if "vandra-" not in request.form.get("message", "") and "vandra-" not in request.form.get("stack", ""):
        return "error"
    
    return "okay"

@app.route("/vandra_admin_login")
def vandra_admin_login_route():
    return render_template("/vandra_admin_login.html")

@app.route("/vandra_admin_login_process", methods=["POST"])
def vandra_admin_login_process_route():
    from modules.vandra_admin import vandra_admin_login_process_handler
    return vandra_admin_login_process_handler()

@app.route("/vandra_admin", methods=["GET", "POST"])
def vandra_admin_route():
    from modules.vandra_admin import vandra_admin_handler
    return vandra_admin_handler()

@app.route("/vandra_admin_manage_admins")
def vandra_admin_manage_admins_route():
    from modules.vandra_admin import vandra_admin_manage_admins_handler
    return vandra_admin_manage_admins_handler()

@app.route("/vandra_admin_delete_admin")
def vandra_admin_delete_admin_route():
    from modules.vandra_admin import vandra_admin_delete_admin_handler
    return vandra_admin_delete_admin_handler()

@app.route("/vandra_admin_add_admin", methods=["POST"])
def vandra_admin_add_admin_route():
    from modules.vandra_admin import vandra_admin_add_admin_handler
    return vandra_admin_add_admin_handler()

@app.route("/vandra_admin_toggle_merchant_show_discount", methods=["POST"])
def vandra_admin_toggle_merchant_show_discount_route():
    from modules.vandra_admin import vandra_admin_toggle_merchant_show_discount_handler
    return vandra_admin_toggle_merchant_show_discount_handler()

@app.route("/vandra_admin_toggle_admin_show_discount", methods=["POST"])
def vandra_admin_toggle_admin_show_discount_route():
    from modules.vandra_admin import vandra_admin_toggle_admin_show_discount_handler
    return vandra_admin_toggle_admin_show_discount_handler()

@app.route("/vandra_admin_submit_charge", methods=["POST"])
def vandra_admin_submit_charge_route():
    from modules.vandra_admin import vandra_admin_submit_charge_handler
    return vandra_admin_submit_charge_handler()

@app.route("/vandra_admin_delete_usage_charge")
def vandra_admin_delete_usage_charge_route():
    from modules.vandra_admin import vandra_admin_delete_usage_charge_handler
    return vandra_admin_delete_usage_charge_handler()

@app.route("/vandra_admin_dashboard")
def vandra_admin_dashboard_route():
    from modules.vandra_admin import vandra_admin_dashboard_handler
    return vandra_admin_dashboard_handler()

@app.route("/vandra_admin_user_session", methods=["GET", "POST"])
def vandra_admin_user_session_route():
    from modules.vandra_admin import vandra_admin_user_session_handler
    return vandra_admin_user_session_handler()

@app.route("/vandra_admin_webhooks")
def vandra_admin_webhooks_route():
    from modules.vandra_admin import vandra_admin_webhooks_handler
    return vandra_admin_webhooks_handler()

@app.route("/vandra_admin_error_log")
def vandra_admin_error_log_route():
    from modules.vandra_admin import vandra_admin_error_log_handler
    return vandra_admin_error_log_handler()

@app.route("/vandra_admin_store", methods=["GET", "POST"])
def vandra_admin_store_route():
    from modules.vandra_admin import vandra_admin_store_handler
    return vandra_admin_store_handler()

@app.route("/vandra_admin_store_save_settings", methods=["POST"])
def vandra_admin_store_save_settings_route():
    from modules.vandra_admin import vandra_admin_store_save_settings_handler
    return vandra_admin_store_save_settings_handler()

@app.route("/vandra_admin_store_analytics", methods=["GET", "POST"])
def vandra_admin_store_analytics_route():
    from modules.vandra_admin import vandra_admin_store_analytics_handler
    return vandra_admin_store_analytics_handler()

@app.route("/vandra_admin_daily_stats", methods=["GET"])
def vandra_admin_daily_stats_route():
    from modules.vandra_admin import vandra_admin_daily_stats_handler
    return vandra_admin_daily_stats_handler()

@app.route("/vandra_admin_merchant_stats_over_time", methods=["GET"])
def vandra_admin_merchant_stats_over_time_route():
    from modules.vandra_admin import vandra_admin_merchant_stats_over_time_handler
    return vandra_admin_merchant_stats_over_time_handler()

@app.route("/vandra_admin_delete_merchant_pii")
def vandra_admin_delete_merchant_pii_route():
    from modules.vandra_admin import vandra_admin_delete_merchant_pii_handler
    return vandra_admin_delete_merchant_pii_handler()

@app.route("/vandra_admin_ui_experiments", methods=["GET"])
def vandra_admin_ui_experiments_route():
    from modules.vandra_admin_ui import vandra_admin_ui_experiments_handler
    return vandra_admin_ui_experiments_handler()

@app.route("/vandra_admin_front_end_experiment_add", methods=["POST"])
def vandra_admin_front_end_experiment_add_route():
    from modules.vandra_admin_ui import vandra_admin_front_end_experiment_add_handler
    return vandra_admin_front_end_experiment_add_handler()

@app.route("/vandra_admin_experiment_toggle_active", methods=["POST"])
def vandra_admin_experiment_toggle_active_route():
    from modules.vandra_admin_ui import vandra_admin_experiment_toggle_active_handler
    return vandra_admin_experiment_toggle_active_handler()

@app.route("/vandra_admin_add_ui_to_experiment", methods=["POST"])
def vandra_admin_add_ui_to_experiment_route():
    from modules.vandra_admin_ui import vandra_admin_add_ui_to_experiment_handler
    return vandra_admin_add_ui_to_experiment_handler()

@app.route("/vandra_admin_front_end_ui_add", methods=["POST"])
def vandra_admin_front_end_ui_add_route():
    from modules.vandra_admin_ui import vandra_admin_front_end_ui_add_handler
    return vandra_admin_front_end_ui_add_handler()

@app.route("/vandra_admin_ui_experiment_analytics", methods=["GET"])
def vandra_admin_ui_experiment_analytics_route():
    from modules.vandra_admin_ui import vandra_admin_ui_experiment_analytics_handler
    return vandra_admin_ui_experiment_analytics_handler()

@app.route("/vandra_admin_ml_models", methods=["GET"])
def vandra_admin_ml_models_route():
    from modules.vandra_admin_ml import vandra_admin_ml_models_handler
    return vandra_admin_ml_models_handler()

@app.route("/vandra_admin_ml_models_add", methods=["POST"])
def vandra_admin_ml_models_add_route():
    from modules.vandra_admin_ml import vandra_admin_ml_models_add_handler
    return vandra_admin_ml_models_add_handler()

@app.route("/vandra_admin_toggle_run_predictions", methods=["POST"])
def vandra_admin_toggle_run_predictions_route():
    from modules.vandra_admin_ml import vandra_admin_toggle_run_predictions_handler
    return vandra_admin_toggle_run_predictions_handler()

@app.route("/vandra_admin_toggle_live_version", methods=["POST"])
def vandra_admin_toggle_live_version_route():
    from modules.vandra_admin_ml import vandra_admin_toggle_live_version_handler
    return vandra_admin_toggle_live_version_handler()

@app.route("/vandra_admin_ml_model_thresholds", methods=["GET"])
def vandra_admin_ml_model_thresholds_route():
    from modules.vandra_admin_ml import vandra_admin_ml_model_thresholds_handler
    return vandra_admin_ml_model_thresholds_handler()

@app.route("/vandra_admin_ml_model_save_thresholds", methods=["POST"])
def vandra_admin_ml_model_save_thresholds_route():
    from modules.vandra_admin_ml import vandra_admin_ml_model_save_thresholds_handler
    return vandra_admin_ml_model_save_thresholds_handler()

@app.route("/vandra_admin_os_version_time")
def vandra_admin_os_version_time_route():
    from modules.vandra_admin import vandra_admin_os_version_time_handler
    return vandra_admin_os_version_time_handler()

@app.route("/vandra_admin_os_version_time_add", methods=["POST"])
def vandra_admin_os_version_time_add_route():
    from modules.vandra_admin import vandra_admin_os_version_time_add_handler
    return vandra_admin_os_version_time_add_handler()

@app.route("/vandra_admin_os_version_time_delete")
def vandra_admin_os_version_time_delete_route():
    from modules.vandra_admin import vandra_admin_os_version_time_delete_handler
    return vandra_admin_os_version_time_delete_handler()

@app.route("/app_install")
def app_install_route():
    from modules.oauth import app_install_handler
    return app_install_handler()

@app.route("/install_finalize")
def install_finalize_route():
    from modules.oauth import install_finalize_handler
    return install_finalize_handler()

@app.route("/post_install_account_setup")
def post_install_account_setup_route():
    from modules.oauth import post_install_account_setup_handler
    return post_install_account_setup_handler()

@app.route("/app_uninstalled", methods=['POST'])
def app_uninstalled_route():
    from modules.oauth import app_uninstalled_handler
    return app_uninstalled_handler()

@app.route("/record_discount_applied", methods=["POST"])
def record_discount_applied_route():
    from modules.record_page_view import record_discount_applied_handler
    return record_discount_applied_handler()

@app.route("/get_discount_codes", methods=["GET"])
def get_discount_codes_route():
    from modules.record_checkout import get_discount_codes_handler
    return get_discount_codes_handler()

@app.route("/get_model_decision", methods=["POST"])
def get_model_decision_route():
    from modules.predictor import get_model_decision_handler
    return get_model_decision_handler()

@app.route("/get_popup_status", methods=["GET"])
def get_popup_status_route():
    from modules.record_page_view import get_popup_status_handler
    return get_popup_status_handler()

@app.route("/get_renudge_status", methods=["GET"])
def get_renudge_status_route():
    from modules.record_page_view import get_renudge_status_handler
    return get_renudge_status_handler()

@app.route("/get_session", methods=["GET"])
def get_session_route():
    from modules.record_checkout import get_session_handler
    return get_session_handler()

@app.route("/log/supression", methods=["POST"]) # adding for backwards compatibility during release, multiple routes for same endpoint
@app.route("/log/suppression", methods=["POST"])
def record_log_suppression_route():
    from modules.record_actions import record_intervention_suppressed
    return record_intervention_suppressed()

@app.route("/record_cart_change", methods=["POST"])
def record_cart_change_route():
    from modules.record_cart_change import record_cart_change_handler
    return record_cart_change_handler()

@app.route("/record_cart_token", methods=["POST"])
def record_cart_token_route():
    from modules.record_cart_token import record_cart_token_handler
    return record_cart_token_handler()

@app.route("/record_checkout_create", methods=["POST"])
def record_checkout_create_route():
    from modules.record_checkout import record_checkout_create_handler
    return record_checkout_create_handler()

@app.route("/record_checkout_update", methods=["POST"])
def record_checkout_update_route():
    from modules.record_checkout import record_checkout_update_handler
    return record_checkout_update_handler()

@app.route("/record_click", methods=["POST"])
def record_click_route():
    from modules.record_click import record_click_handler
    return record_click_handler()

@app.route("/record_countdown_deadline", methods=["POST"])
def record_countdown_deadline_route():
    from modules.record_page_view import record_countdown_deadline_handler
    return record_countdown_deadline_handler()

@app.route("/record_dwell_time", methods=["POST"])
def record_dwell_time_route():
    from modules.record_dwell_time import record_dwell_time_handler
    return record_dwell_time_handler()

@app.route("/record_ineligible_product", methods=["POST"])
def record_ineligible_product_route():
    from modules.record_page_view import record_ineligible_product_handler
    return record_ineligible_product_handler()

@app.route("/record_order", methods=["POST"])
def record_order_route():
    from modules.record_order import record_order_handler
    return record_order_handler()

@app.route("/record_page_focus_change", methods=["POST"])
def record_page_focus_change_route():
    from modules.record_page_focus_change import record_page_focus_change_handler
    return record_page_focus_change_handler()

@app.route("/record_page_view", methods=["POST"])
def record_page_view_route():
    from modules.record_page_view import record_page_view_handler
    return record_page_view_handler()

@app.route("/record_popup_dismissed", methods=["POST"])
def record_popup_dismissed_route():
    from modules.record_page_view import record_popup_dismissed_handler
    return record_popup_dismissed_handler()

@app.route("/record_popup_shown", methods=["POST"])
def record_popup_shown_route():
    from modules.record_page_view import record_popup_shown_handler
    return record_popup_shown_handler()

@app.route("/record_search", methods=["POST"])
def record_search_route():
    from modules.record_search import record_search_handler
    return record_search_handler()

@app.route("/update_nudge_parameters", methods=['POST'])
@app.route("/record_action", methods=["POST"])
def record_action_route():
    from modules.record_actions import record_action_handler
    return record_action_handler()

@app.route("/products/is_bestseller", methods=["GET"])
def get_product_is_bestseller():
    from modules.products import get_product_is_bestseller
    return get_product_is_bestseller()

@app.route("/react/initialize_store")
def initialize_store_route():
    from modules.merchant_admin import initialize_store_handler
    return initialize_store_handler()

@app.route("/react/merchant_confirm_billing", methods=["POST"])
def merchant_confirm_billing_route():
    from modules.merchant_admin import merchant_confirm_billing_handler
    return merchant_confirm_billing_handler()

@app.route("/react/merchant_fetch_pricing_plan", methods=["GET"])
def merchant_fetch_pricing_plan_route():
    from modules.merchant_admin import merchant_fetch_pricing_plan_handler
    return merchant_fetch_pricing_plan_handler()

@app.route("/react/merchant_update_onboarding", methods=["POST"])
def merchant_update_onboarding_route():
    from modules.merchant_admin import merchant_update_onboarding_handler
    return merchant_update_onboarding_handler()

@app.route("/react/merchant_fetch_details", methods=["GET"])
def merchant_fetch_details_route():
    from modules.merchant_admin import merchant_fetch_details
    return merchant_fetch_details()

@app.route("/react/merchant_update_details", methods=["POST"])
def merchant_update_details_route():
    from modules.merchant_admin import merchant_update_details
    return merchant_update_details()

@app.route("/react/merchant_onboarding_script", methods=["GET"])
def merchant_onboarding_script_route():
    from modules.merchant_admin import merchant_onboarding_script_handler
    return merchant_onboarding_script_handler()

@app.route("/react/merchant_intervention_types")
def merchant_intervention_types():
    from modules.merchant_admin import merchant_intervention_types_handler
    return merchant_intervention_types_handler()

@app.route("/react/merchant_nudge_requests")
def merchant_nudge_requests():
    from modules.merchant_admin import merchant_nudge_requests_handler
    return merchant_nudge_requests_handler()

@app.route("/react/merchant_nudge_request", methods=["POST"])
def merchant_request_nudge():
    from modules.merchant_admin import merchant_request_nudge_handler
    return merchant_request_nudge_handler()

@app.route("/react/merchant_dashboard")
def merchant_dashboard_route():
    from modules.merchant_admin import merchant_dashboard_handler
    return merchant_dashboard_handler()

@app.route("/react/merchant_dashboard_sales", methods=["GET"])
def merchant_dashboard_sales_route():
    from modules.merchant_admin import merchant_dashboard_sales_handler
    return merchant_dashboard_sales_handler()

@app.route("/react/merchant_analytics_test_mode_verification", methods=["GET"])
def merchant_analytics_test_mode_verification_route():
    from modules.merchant_admin import merchant_analytics_test_mode_verification_handler
    return merchant_analytics_test_mode_verification_handler()

@app.route("/react/merchant_analytics_test_stores", methods=["GET"])
def merchant_analytics_test_store_list_route():
    from modules.merchant_admin import merchant_analytics_test_stores_handler
    return merchant_analytics_test_stores_handler()

@app.route("/react/merchant_analytics", methods=["GET"])
def merchant_analytics_route():
    from modules.merchant_admin import merchant_analytics_handler
    return merchant_analytics_handler()

@app.route("/react/merchant_analytics_export", methods=["GET"])
def merchant_analytics_export_route():
    from modules.merchant_admin import merchant_analytics_export_handler
    return merchant_analytics_export_handler()

@app.route("/react/merchant_settings")
def merchant_settings_route():
    from modules.merchant_admin import merchant_settings_handler
    return merchant_settings_handler()

@app.route("/react/merchant_discount_nudge_activate", methods=["POST"])
def merchant_discount_nudge_activate_route():
    from modules.merchant_admin import merchant_discount_nudge_activate_handler
    return merchant_discount_nudge_activate_handler()

@app.route("/react/merchant_discount_nudge_save_settings", methods=["POST"])
def discount_nudge_save_settings_route():
    from modules.merchant_admin import merchant_discount_nudge_save_settings_handler
    return merchant_discount_nudge_save_settings_handler()

@app.route("/react/merchant_discount_settings_save", methods=["POST"])
def merchant_discount_settings_save_route():
    from modules.merchant_admin import merchant_discount_settings_save_handler
    return merchant_discount_settings_save_handler()

@app.route("/react/merchant_widget_settings_save", methods=["POST"])
def merchant_widget_settings_save_route():
    from modules.merchant_admin import merchant_widget_settings_save_handler
    return merchant_widget_settings_save_handler()

@app.route("/react/merchant_contact", methods=["POST"])
def merchant_contact_route():
    from modules.merchant_admin import merchant_contact_handler
    return merchant_contact_handler()

@app.route("/react/metabase_iframe")
def metabase_sso():
    from modules.merchant_admin import metabase_iframe_handler
    return metabase_iframe_handler()

@app.route("/react/merchant_nudge_parameters_save", methods=["POST"])
def merchant_nudge_parameters_save_route():
    from modules.merchant_admin import merchant_update_nudge_parameters_handler
    return merchant_update_nudge_parameters_handler()

@app.route("/react/admin_check_status", methods=["GET"])
def admin_check_status_route():
    from modules.merchant_admin import admin_check_status_handler
    return admin_check_status_handler()

@app.route("/react/admin_available_stores", methods=["GET"])
def admin_available_stores_route():
    from modules.merchant_admin import admin_available_stores_handler
    return admin_available_stores_handler()

@app.route("/react/merchant_send_support_email", methods=["POST"])
def merchant_send_support_email_route():
    from modules.merchant_admin import send_support_email_handler
    return send_support_email_handler()

@app.route("/new_order_webhook", methods=["POST", "GET"])
def new_order_webhook_route():
    from modules.record_order import record_order_handler
    return record_order_handler()

@app.route("/consent/record", methods=["POST"])
@cross_origin()
def consent_record_handler():
    from modules.record_consent import record_consent_handler
    return record_consent_handler()

@app.route("/user_data_request", methods=["POST", "GET"])
def user_data_request_route():
    from helpers import process_webhook_request
    event, shop_url = process_webhook_request()
    if not event:
        return abort(401)

    request_data = request.get_json()

    new_webhook_request = webhook_request()
    new_webhook_request.uuid = str(uuid.uuid4())
    new_webhook_request.time = time.time()
    new_webhook_request.event = event
    new_webhook_request.shop_url = shop_url
    new_webhook_request.data = json.dumps(request_data)
    db_session.add(new_webhook_request)
    db_session.commit()

    # Email admins
    from helpers import send_mail
    send_mail("Vandra shopify app - new user data request", new_webhook_request.data, ["<EMAIL>","<EMAIL>"])

    return "okay"

@app.route("/user_delete_request", methods=["POST", "GET"])
def user_delete_request_route():
    from helpers import process_webhook_request
    event, shop_url = process_webhook_request()
    if not event:
        return abort(401)

    request_data = request.get_json()

    new_webhook_request = webhook_request()
    new_webhook_request.uuid = str(uuid.uuid4())
    new_webhook_request.time = time.time()
    new_webhook_request.event = event
    new_webhook_request.shop_url = shop_url
    new_webhook_request.data = json.dumps(request_data)
    db_session.add(new_webhook_request)
    db_session.commit()

    # Unless we have order information, we have nothing we need to delete
    # TODO - 3/14/24 - we may have the shopify customer ID and/or email in the webhook_request table,
    # which we are not currently trying to search for deletions
    if request_data["orders_to_redact"] == []:
        return 'okay'
    
    # Remove PII for all tied sessions
    for order_id in request_data["orders_to_redact"]:
        order_id_str = str(order_id)
        check_order = order.query.filter_by(shopify_id=order_id_str).first()
        if check_order is None:
            continue

        user_session_record = user_session.query.filter_by(uuid=check_order.user_session_id).first()
        if user_session_record is not None:
            user_session_record.customer_cookie = None
            user_session_record.ip_address = None
            user_session_record.shopify_customer_id = None

        # Disconnect cart from user_session to protect anonymity
        from db import cart_token_record
        cart_token_record_obj = cart_token_record.query.filter_by(user_session_id=user_session_record.uuid).first()
        if cart_token_record_obj is not None:
            cart_token_record_obj.user_session_id = None
        db_session.commit()

    from helpers import send_mail
    send_mail("Vandra shopify app - new user delete request", new_webhook_request.data, ["<EMAIL>","<EMAIL>"])

    return 'okay'

@app.route("/shop_delete_request", methods=["POST", "GET"])
def shop_delete_request_route():
    print('in shop delete request')
    from helpers import process_webhook_request
    event, shop_url = process_webhook_request()
    if not event:
        return abort(401)

    request_data = request.get_json()

    new_webhook_request = webhook_request()
    new_webhook_request.uuid = str(uuid.uuid4())
    new_webhook_request.time = time.time()
    new_webhook_request.event = event
    new_webhook_request.shop_url = shop_url
    new_webhook_request.data = json.dumps(request_data)
    db_session.add(new_webhook_request)
    db_session.commit()

    from helpers import send_mail
    send_mail("Vandra shopify app - new shop delete request", new_webhook_request.data, ["<EMAIL>","<EMAIL>"])

    return 'okay'

@app.teardown_request
def teardown_request_func(exception):
    if exception:
        db_session.rollback()
    db_session.remove()


@app.teardown_appcontext
def shutdown_session(exception=None):
    if exception:
        db_session.rollback()
    db_session.remove()



@app.before_request
def before_request_func():
    pass

@app.after_request
def after_request_func(response):
    db_session.remove()

    # Remove manual CORS header setting, let Flask-CORS handle it
    # response.headers["Access-Control-Allow-Origin"] = "*"
    
    # Keep Content-Security-Policy
    shop_domain = getattr(g, "shop", None)
    if shop_domain:
        # Ensure shop_domain is clean before inserting into header
        # Basic sanitization example (adjust as needed for security)
        safe_shop_domain = ''.join(c for c in shop_domain if c.isalnum() or c in '.-_') 
        if safe_shop_domain == shop_domain: # Check if sanitization changed it
            response.headers["Content-Security-Policy"] = f"frame-ancestors https://{safe_shop_domain} https://admin.shopify.com;"
        else:
             # Fallback or error if domain seems suspicious
             response.headers["Content-Security-Policy"] = "frame-ancestors 'self' https://admin.shopify.com;"
    else:
        response.headers["Content-Security-Policy"] = "frame-ancestors 'self' https://vandra-dev.myshopify.com https://admin.shopify.com;"
    return response

@app.route("/debug-sentry")
def trigger_error():
    return 1 / 0

@app.route("/healthcheck")
def healthcheck():
    return "Running"

@app.route("/vandra_admin_store_trigger_calibration", methods=["POST"])
def vandra_admin_store_trigger_calibration_route():
    from modules.vandra_admin import vandra_admin_store_trigger_calibration_handler
    return vandra_admin_store_trigger_calibration_handler()

@app.route("/react/search_products", methods=["GET"])
def merchant_search_products_route():
    from modules.social_media_nudge_use_cases import merchant_search_products_handler
    return merchant_search_products_handler()

@app.route("/react/merchant_get_social_video_upload_presigned_url", methods=["GET"])
def merchant_get_social_video_upload_presigned_url_route():
    from modules.social_media_nudge_use_cases import merchant_get_social_video_upload_presigned_url_handler
    return merchant_get_social_video_upload_presigned_url_handler()

@app.route("/react/merchant_add_social_video_asset", methods=["POST"])
def merchant_add_social_video_asset_route():
    from modules.social_media_nudge_use_cases import merchant_add_social_video_asset_handler
    return merchant_add_social_video_asset_handler()

@app.route("/react/merchant_delete_social_video_asset", methods=["POST"])
def merchant_delete_social_video_asset_route():
    from modules.social_media_nudge_use_cases import merchant_delete_social_video_asset_handler
    return merchant_delete_social_video_asset_handler()

@app.route("/react/get_products_by_ids", methods=["GET"])
def get_products_by_ids_route():
    from modules.products import get_products_by_ids
    return get_products_by_ids()

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=8080)
