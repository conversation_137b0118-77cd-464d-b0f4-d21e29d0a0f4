import os
import sys
import inspect
import json
import argparse


currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

from constants import INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT
from app import app, db_session
from database_models import store_intervention_association_assets, store_intervention_association,  intervention_type
from db import store
from utils import get_generic_logger, unique_id

logger = get_generic_logger(__name__)


def create_intervention_assets(assets_data):    
    """Create intervention assets from the provided data."""
    created_assets = []
    
    social_media_intervention_type = intervention_type.query.filter_by(
        name=INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT
    ).first()
    
    for asset_info in assets_data:
        # Look up store from URL
        store_record = store.query.filter_by(
            uuid=asset_info['store_uuid']
        ).first()
        if not store_record:
            logger.error(f"No store found for UUID: {asset_info['store_uuid']}")
            continue
            
        # Get store intervention associations for this store
        store_associations = store_intervention_association.query.filter_by(
            store_id=store_record.uuid,
            intervention_type_id=social_media_intervention_type.uuid,
            active=True
        ).all()
        
        if not store_associations:
            logger.error(f"No active intervention associations found for store: {store_record.uuid}")
            continue

        # Create asset for each association
        for association in store_associations:
            new_asset = store_intervention_association_assets(
                uuid=unique_id(),
                store_intervention_association_id=association.uuid,
                asset_key=asset_info['key'],
                asset_url=asset_info['asset_url'],
                asset_type=asset_info['type'],
                asset_metadata=asset_info.get('asset_metadata', {})
            )

            db_session.add(new_asset)
            created_assets.append(new_asset)
            
            logger.info(
                f"Created asset for store {store_record.uuid}, "
                f"association {association.uuid}, "
                f"asset key: {asset_info['key']}"
            )

    db_session.commit()
    return created_assets


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--json-file', 
        help='Path to JSON file containing assets data',
        dest="json_file")
    args = parser.parse_args()

    try:
        with open(args.json_file, 'r') as f:
            data = json.load(f)
            assets_data = data.get('assets', [])
    except FileNotFoundError:
        logger.error(f"Could not find JSON file: {args.json_file}")
        sys.exit(1)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON file: {args.json_file}")
        sys.exit(1)
    except KeyError:
        logger.error(f"JSON file must contain an 'assets' array")
        sys.exit(1)

    with app.app_context():
        created_assets = create_intervention_assets(assets_data)
        logger.info(f"Created {len(created_assets)} intervention assets") 