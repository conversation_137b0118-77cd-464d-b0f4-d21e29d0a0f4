import os
import json
import requests
import argparse
from urllib.parse import urljoin

def get_product_info(store_url, product_handle):
    """Fetch product information from Shopify store"""
    product_url = f"https://{store_url}/products/{product_handle}.json"
    response = requests.get(product_url)
    if response.status_code != 200:
        raise Exception(f"Failed to fetch product info: {response.status_code}")
    return response.json()

def process_video_files(store_url, s3_base_path, local_path):
    """Process all MP4 files in the local path and generate assets data"""
    assets = []
    
    # Walk through the local directory
    for root, _, files in os.walk(local_path):
        for file in files:
            if file.lower().endswith('.mp4'):
                # Get filename without extension
                filename = os.path.splitext(file)[0]
                
                # Construct S3 asset URL
                asset_url = urljoin(s3_base_path.rstrip('/') + '/', file)
                
                # Check if this is a fallback file
                if filename.lower().startswith('fallback'):
                    # Create fallback asset entry
                    asset = {
                        "store_url": store_url,
                        "type": "product_video",
                        "key": "fallback",
                        "asset_url": asset_url,
                        "asset_metadata": {}
                    }
                    assets.append(asset)
                    print(f"Processed fallback file: {file}")
                else:
                    try:
                        # Fetch product data from Shopify
                        product_data = get_product_info(store_url, filename)
                        product = product_data['product']
                        
                        # Get first variant
                        first_variant = product['variants'][0]
                        
                        # Create asset entry
                        asset = {
                            "store_url": store_url,
                            "type": "product_video",
                            "key": f"{product['id']}-{first_variant['id']}",
                            "asset_url": asset_url,
                            "asset_metadata": {
                                "product_id": str(product['id']),
                                "variant_id": str(first_variant['id'])
                            }
                        }
                        
                        assets.append(asset)
                        print(f"Processed {file}: Product ID {product['id']}, Variant ID {first_variant['id']}")
                        
                    except Exception as e:
                        print(f"Error processing {file}: {str(e)}")
    
    return {"assets": assets}

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description='Generate assets data from video files',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--store-url',
        help='Shopify store URL (e.g., store-name.myshopify.com)',
        required=True,
        dest='store_url'
    )
    
    parser.add_argument(
        '--s3-base-path',
        help='S3 base path (e.g., https://bucket-name.s3.region.amazonaws.com/path/)',
        required=True,
        dest='s3_base_path'
    )
    
    parser.add_argument(
        '--local-path',
        help='Local path containing MP4 files',
        required=True,
        dest='local_path'
    )
    
    parser.add_argument(
        '--output',
        help='Output JSON file path',
        default='generated_assets.json',
        dest='output_file'
    )

    args = parser.parse_args()
    
    # Process videos and generate assets data
    assets_data = process_video_files(args.store_url, args.s3_base_path, args.local_path)
    
    # Save to JSON file
    with open(args.output_file, 'w') as f:
        json.dump(assets_data, f, indent=2)
    
    print(f"\nAssets data has been saved to {args.output_file}")

if __name__ == "__main__":
    main() 