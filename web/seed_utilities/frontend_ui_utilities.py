import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
import time
from app import app, db_session
from db import front_end_ui, front_end_experiment, front_end_experiment_ui_allocation
from utils import get_generic_logger, unique_id

logger = get_generic_logger(__name__)

def create_new_frontend_ui_experiment(name, description, front_end_ui_id, active):
    ui_id = unique_id()
    #url
    fe = front_end_experiment(
        uuid = unique_id(),
        time = time.time(),
        name=name,
        description=description,
        active=active,
        store_specific=False
    )
    
    db_session.add(fe)

    allocation = front_end_experiment_ui_allocation(
        uuid = unique_id(),
        time = time.time(),
        front_end_ui_id=front_end_ui_id,
        front_end_experiment_id=fe.uuid,
        weight=100
    )
    db_session.add(allocation)
    db_session.commit()
    return fe, allocation

def create_new_frontend_ui(name, filename, default):
    ui_id = unique_id()
    #url
    fe = front_end_ui(
        uuid = unique_id(),
        time = time.time(),
        name=name,
        filename=filename,
        default=default
    )
    
    db_session.add(fe)
    db_session.commit()
    return fe

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "add-new-ui",
            "add-new-ui-experiment",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--name",
        help="Name.",
        dest="name",
    )

    parser.add_argument(
        "--filename",
        help="File Name.",
        dest="filename",
    )

    parser.add_argument(
        "--description",
        help="Experiment description.",
        dest="description",
    )

    parser.add_argument(
        "--frontend-ui-id",
        help="Frontend ui id.",
        dest="frontend_ui_id",
    )

    parser.add_argument("--default", default=False, action="store_true", dest="default")
    parser.add_argument("--active", default=False, action="store_true", dest="active")



    args = parser.parse_args()
    if args.action == "add-new-ui":
        with app.app_context():
            fe = create_new_frontend_ui(args.name, args.filename, args.default)
            logger.info(f"New ui created, id: {fe.uuid}")
    elif args.action == "add-new-ui-experiment":
        with app.app_context():
            fe, allocation = create_new_frontend_ui_experiment(args.name, args.description, args.frontend_ui_id, args.active)
            logger.info(f"New ui experiment created, experiment id: {fe.uuid}, allocation id {allocation.uuid}")

