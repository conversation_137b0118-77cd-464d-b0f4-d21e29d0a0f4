import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import uuid
import time
from app import app, db_session
from db import store, onboarding_details, subscription_plans
from utils import get_generic_logger

logger = get_generic_logger(__name__)

def backfill_onboarding_details():
    # Get store URL from store_url table
    subscription_plan = subscription_plans.query.first()

    stores = store.query.filter_by(onboarding_details=None).all()
    for update_store in stores:
      new_details = onboarding_details(
        uuid = str(uuid.uuid4()),
        terms_start = update_store.time,
        terms_signed = update_store.time,
        script_start = time.time(),
        script_activated = time.time(),
        details_start = time.time(),
        details_confirmed = update_store.time,
        billing_start = update_store.time,
        billing_confirmed = update_store.time
      )
      db_session.add(new_details)
      update_store.onboarding_details = new_details.uuid
      update_store.subscription_plan = subscription_plan.uuid

    db_session.commit()
    return True


if __name__ == "__main__":
  with app.app_context():
    backfill_onboarding_details()
