import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
from app import app, db_session
from db import scoring_layer, store, scoring_layer_thresholds, model_store_threshold, scoring_artifact,model_version 
from utils import get_generic_logger
import time
from constants import (
    PIPELINE_FILE_KEYNAME,
    SCORING_ARTIFACT_TYPE_INTENT,
    SCORING_ARTIFACT_TYPE_UPLIFT_CTL,
    SCORING_ARTIFACT_TYPE_UPLIFT_TRT,
    SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY,
    DECISION_ALGORITHM_INTENT_ONLY,
    DECISION_ALGORITHM_THRESHOLD_UPLIFT,
    DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED,
    DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH,
    DECISION_ALGORITHM_UPLIFT_ONLY,
    DEFAULT_UPLIFT_THRESHOLD,
    DECISION_ALGORITHM_INTENT_AND_UPLIFT
)

logger = get_generic_logger(__name__)

def copy_models_to_layers(model_version_id=None):
    if model_version_id:
        model_versions = model_version.query.filter_by(
            uuid=model_version_id
        ).all()
    else:
        model_versions = model_version.query.filter_by(
            run_predictions=True,
        ).all()
    for model_version_record in model_versions:
        #create scoring artifact
        scoring_artifact_record = scoring_artifact(
            deployment_external_reference=model_version_record.deployment_id,
            artifact_metadata={
                PIPELINE_FILE_KEYNAME: model_version_record.filename},
            time = model_version_record.time,
            description = model_version_record.description,
            component_type = SCORING_ARTIFACT_TYPE_INTENT
        )
        db_session.add(scoring_artifact_record)
        db_session.commit()
        #create scoring layer
        scoring_layer_record = scoring_layer(
            time = model_version_record.time,
            description = model_version_record.description,
            active = True,
            store_specific = model_version_record.store_specific_model,
            run_predictions = model_version_record.run_predictions,
            live_decisioning = model_version_record.live_version,
            scoring_algorithm_type = DECISION_ALGORITHM_INTENT_ONLY,
            scoring_artifacts = {
                SCORING_ARTIFACT_TYPE_INTENT: scoring_artifact_record.uuid
            },
            scoring_time_cutoff = model_version_record.time_cutoff
        )
        db_session.add(scoring_layer_record)
        db_session.commit()
        #create scoring layer thresholds
        #create thresholds for scoring layer and all stores with run prediction on
        stores = store.query.all()
        for store_record in stores:
            anti_holdout_threshold = model_store_threshold.query.filter_by(
                store_id = store_record.uuid,
                model_version_id = model_version_record.uuid,
                threshold_type = "anti-holdout",
                active=True
            ).order_by(model_store_threshold.time.desc()).first()
            low_intent_threshold = model_store_threshold.query.filter_by(
                store_id = store_record.uuid,
                model_version_id = model_version_record.uuid,
                threshold_type = "low-intent",
                active=True
            ).order_by(model_store_threshold.time.desc()).first()
            #create scoring layer thresholds
            if not low_intent_threshold:
                continue
            scoring_layer_thresholds_record = scoring_layer_thresholds(
                scoring_layer_id = scoring_layer_record.uuid,
                store_id = store_record.uuid,
                time = low_intent_threshold.time,
                active = True,
                scoring_artifact_thresholds = {
                    DECISION_ALGORITHM_INTENT_ONLY: {
                        DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED: float(low_intent_threshold.decision_threshold),
                    }
                }
            )
            db_session.add(scoring_layer_thresholds_record)
            db_session.commit()
            if anti_holdout_threshold is not None:
                scoring_layer_thresholds_record.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_ONLY][DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH] = float(anti_holdout_threshold.decision_threshold)
                db_session.commit()
            logger.info(f"{scoring_layer_thresholds_record.uuid} scoring_layer_thresholds_record: {scoring_layer_thresholds_record.scoring_artifact_thresholds}")
            
def update_thresholds_for_model(model_version_id, scoring_layer_id):
    #create thresholds for scoring layer and all stores with run prediction on
    model_version_record = model_version.query.filter_by(uuid=model_version_id).first()
    scoring_layer_record = scoring_layer.query.filter_by(uuid=scoring_layer_id).first()
    stores = store.query.all()
    for store_record in stores:
        #set active to false for all previous thresholds
        scoring_layer_thresholds.query.filter_by(
            scoring_layer_id = scoring_layer_id,
            store_id = store_record.uuid,
            active=True
        ).update({"active": False})
        db_session.commit()
        #create new scoring layer thresholds
        anti_holdout_threshold = model_store_threshold.query.filter_by(
            store_id = store_record.uuid,
            model_version_id = model_version_record.uuid,
            threshold_type = "anti-holdout",
            active=True
        ).order_by(model_store_threshold.time.desc()).first()
        low_intent_threshold = model_store_threshold.query.filter_by(
            store_id = store_record.uuid,
            model_version_id = model_version_record.uuid,
            threshold_type = "low-intent",
            active=True
        ).order_by(model_store_threshold.time.desc()).first()
        #create scoring layer thresholds
        if not low_intent_threshold:
            continue
        scoring_layer_thresholds_record = scoring_layer_thresholds(
            scoring_layer_id = scoring_layer_record.uuid,
            store_id = store_record.uuid,
            time = low_intent_threshold.time,
            active = True,
            scoring_artifact_thresholds = {
                DECISION_ALGORITHM_INTENT_ONLY: {
                    DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED: float(low_intent_threshold.decision_threshold),
                }
            }
        )
        db_session.add(scoring_layer_thresholds_record)
        db_session.commit()
        if anti_holdout_threshold is not None:
            scoring_layer_thresholds_record.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_ONLY][DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH] = float(anti_holdout_threshold.decision_threshold)
            db_session.commit()
        logger.info(f"{scoring_layer_thresholds_record.uuid} scoring_layer_thresholds_record: {scoring_layer_thresholds_record.scoring_artifact_thresholds}")
    
def create_test_intent_decision_layer(
    intent_deployment_reference,
    filename
):
    #create scoring artifact
    ctime = time.time()
    scoring_artifact_record = scoring_artifact(
        deployment_external_reference=intent_deployment_reference,
        artifact_metadata={
            PIPELINE_FILE_KEYNAME: filename},
        time = ctime,
        description = "Intent Scoring Artifact",
        component_type = SCORING_ARTIFACT_TYPE_INTENT
    )
    db_session.add(scoring_artifact_record)
    db_session.commit()
    #create scoring layer
    scoring_layer_record = scoring_layer(
        time = ctime,
        description = "Intent Scoring Layer",
        active = True,
        store_specific = True,
        run_predictions = True,
        live_decisioning = True,
        scoring_algorithm_type = DECISION_ALGORITHM_INTENT_ONLY,
        scoring_artifacts = {
            SCORING_ARTIFACT_TYPE_INTENT: scoring_artifact_record.uuid
        },
        scoring_time_cutoff = 71
    )
    db_session.add(scoring_layer_record)
    db_session.commit()

    logger.info(f"Created scoring layer: {scoring_layer_record.uuid}")


def seed_intent_scoring_layer_thresholds_for_store(store_id, intent_scoring_layer_id):
    """
    Seeds the scoring layer thresholds for a given store and intent-only scoring layer.

    Args:
        store_id (str): The UUID of the store.
        intent_scoring_layer_id (str): The UUID of the intent-only scoring layer.

    Returns:
        None
    """
    store_record = store.query.filter_by(uuid=store_id).first()
    if not store_record:
        logger.error(f"Store with id {store_id} not found.")
        return

    intent_scoring_layer_record = scoring_layer.query.filter_by(uuid=intent_scoring_layer_id).first()
    if not intent_scoring_layer_record:
        logger.error(f"Intent scoring layer with id {intent_scoring_layer_id} not found.")
        return


    scoring_layer_thresholds_record = scoring_layer_thresholds(
        scoring_layer_id=intent_scoring_layer_record.uuid,
        store_id=store_record.uuid,
        time=time.time(),
        active=True,
        scoring_artifact_thresholds={
            DECISION_ALGORITHM_INTENT_ONLY: {
                DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED: float(1.0),
            }
        }
    )
    db_session.add(scoring_layer_thresholds_record)
    db_session.commit()
    logger.info(f"Seeded scoring layer thresholds for store {store_id} and intent-only scoring layer {intent_scoring_layer_id}")

def create_uplift_decision_layer(
            trt_deployment_reference,
            ctl_deployment_reference,
            propensity_deployment_reference,
            filename,
            time_cutoff=71
    ):
        #trt
        trt_scoring_artifact_record = scoring_artifact(
            deployment_external_reference=trt_deployment_reference,
            artifact_metadata={
                PIPELINE_FILE_KEYNAME: filename},
            time = time.time(),
            description = "TRT Component Layer of Uplift",
            component_type = SCORING_ARTIFACT_TYPE_UPLIFT_TRT
        )
        db_session.add(trt_scoring_artifact_record)
        db_session.commit()
        #ctl
        ctl_scoring_artifact_record = scoring_artifact(
            deployment_external_reference=ctl_deployment_reference,
            artifact_metadata={
                PIPELINE_FILE_KEYNAME: filename},
            time = time.time(),
            description = "CTL Component Layer of Uplift",
            component_type = SCORING_ARTIFACT_TYPE_UPLIFT_CTL
        )
        db_session.add(ctl_scoring_artifact_record)
        db_session.commit()
        #propensity
        propensity_scoring_artifact_record = scoring_artifact(
            deployment_external_reference=propensity_deployment_reference,
            artifact_metadata={
                PIPELINE_FILE_KEYNAME: filename},
            time = time.time(),
            description = "Propensity Component Layer of Uplift",
            component_type = SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY
        )
        db_session.add(propensity_scoring_artifact_record)
        db_session.commit()
        #create scoring layer
        scoring_layer_record = scoring_layer(
            time = time.time(),
            description = "Uplift Only Scoring Layer",
            active = True,
            run_predictions = True,
            live_decisioning = False,
            scoring_algorithm_type = DECISION_ALGORITHM_UPLIFT_ONLY,
            scoring_artifacts = {
                SCORING_ARTIFACT_TYPE_UPLIFT_CTL: ctl_scoring_artifact_record.uuid,
                SCORING_ARTIFACT_TYPE_UPLIFT_TRT: trt_scoring_artifact_record.uuid,
                SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY: propensity_scoring_artifact_record.uuid
            },
            scoring_time_cutoff = time_cutoff
        )
        db_session.add(scoring_layer_record)
        db_session.commit()
        #layer thresholds
        # do it for every store
        stores = store.query.filter_by(show_discount=True).all()
        for store_record in stores:
            scoring_layer_thresholds_record = scoring_layer_thresholds(
                scoring_layer_id = scoring_layer_record.uuid,
                store_id = store_record.uuid,
                time = time.time(),
                active = True,
                scoring_artifact_thresholds = {
                    DECISION_ALGORITHM_UPLIFT_ONLY: {
                        DECISION_ALGORITHM_THRESHOLD_UPLIFT: DEFAULT_UPLIFT_THRESHOLD
                    }
                }
            )
            db_session.add(scoring_layer_thresholds_record)
            db_session.commit()
        
def clone_intent_decision_layer_into_store_specific(
            intent_scoring_layer_id,
            store_id):
        #get existing layers
        intent_scoring_layer_record = scoring_layer.query.filter_by(uuid=intent_scoring_layer_id).first()
        #create scoring layer
        scoring_layer_record = scoring_layer(
            time = time.time(),
            description = "Intent Scoring Layer",
            active = True,
            run_predictions = True,
            live_decisioning = False,
            scoring_algorithm_type = DECISION_ALGORITHM_INTENT_ONLY,
            scoring_artifacts = {
                SCORING_ARTIFACT_TYPE_INTENT: intent_scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_INTENT]
            },
            scoring_time_cutoff = intent_scoring_layer_record.scoring_time_cutoff,
            store_specific = True
        )
        db_session.add(scoring_layer_record)
        db_session.commit()
        #layer thresholds
        #get active thresholds for the existing layer
        intent_layer_thresholds = scoring_layer_thresholds.query.filter_by(
            store_id = store_id,
            active=True,
            scoring_layer_id = intent_scoring_layer_id
        ).order_by(scoring_layer_thresholds.time.desc()).first()
        #add thresholds for this layer
        scoring_artifact_thresholds = {
            DECISION_ALGORITHM_INTENT_ONLY: intent_layer_thresholds.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_ONLY]
        }
        scoring_layer_thresholds_record = scoring_layer_thresholds(
            scoring_layer_id = scoring_layer_record.uuid,
            store_id = store_id,
            time = time.time(),
            active = True,
            scoring_artifact_thresholds = scoring_artifact_thresholds
        )
        db_session.add(scoring_layer_thresholds_record)
        db_session.commit()

def clone_thresholds_for_store(
            from_scoring_layer_id,
            to_scoring_layer_id,
            store_id):
        #layer thresholds
        #get active thresholds for the existing layer
        from_layer_thresholds = scoring_layer_thresholds.query.filter_by(
            store_id = store_id,
            active=True,
            scoring_layer_id = from_scoring_layer_id
        ).order_by(scoring_layer_thresholds.time.desc()).first()
        #add thresholds for this layer
        scoring_layer_thresholds_record = scoring_layer_thresholds(
            scoring_layer_id = to_scoring_layer_id,
            store_id = store_id,
            time = time.time(),
            active = True,
            scoring_artifact_thresholds = from_layer_thresholds.scoring_artifact_thresholds
        )
        db_session.add(scoring_layer_thresholds_record)
        db_session.commit()



def create_intent_and_uplift_decision_layer(
            copy_uplift_only_scoring_layer_id,
            copy_intent_scoring_layer_id,
            time_cutoff=71,
            store_id=None,
    ):
        #get existing layers
        uplift_only_scoring_layer_record = scoring_layer.query.filter_by(uuid=copy_uplift_only_scoring_layer_id).first()
        intent_scoring_layer_record = scoring_layer.query.filter_by(uuid=copy_intent_scoring_layer_id).first()
        #create scoring layer
        scoring_layer_record = scoring_layer(
            time = time.time(),
            description = "Capped Uplift Scoring Layer",
            active = True,
            run_predictions = True,
            live_decisioning = False,
            scoring_algorithm_type = DECISION_ALGORITHM_INTENT_AND_UPLIFT,
            scoring_artifacts = {
                SCORING_ARTIFACT_TYPE_UPLIFT_CTL: uplift_only_scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_CTL],
                SCORING_ARTIFACT_TYPE_UPLIFT_TRT: uplift_only_scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_TRT],
                SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY: uplift_only_scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY],
                SCORING_ARTIFACT_TYPE_INTENT: intent_scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_INTENT]
            },
            scoring_time_cutoff = time_cutoff,
            store_specific = store_id is not None
        )
        db_session.add(scoring_layer_record)
        db_session.commit()
        #layer thresholds
        # do it for every store
        if store_id:
            stores = store.query.filter_by(uuid=store_id).all()
        else:
            stores = store.query.filter_by(show_discount=True).all()
        for store_record in stores:
            #get active thresholds for the existing intent layer
            intent_layer_thresholds = scoring_layer_thresholds.query.filter_by(
                store_id = store_record.uuid,
                active=True,
                scoring_layer_id = copy_intent_scoring_layer_id
            ).order_by(scoring_layer_thresholds.time.desc()).first()
            #uplift layer thresholds
            
            uplift_layer_thresholds = scoring_layer_thresholds.query.filter_by(
                store_id = store_record.uuid,
                active=True,
                scoring_layer_id = copy_uplift_only_scoring_layer_id
            ).order_by(scoring_layer_thresholds.time.desc()).first()
            
            #add thresholds for this layer
            scoring_artifact_thresholds = {
                DECISION_ALGORITHM_INTENT_AND_UPLIFT: uplift_layer_thresholds.scoring_artifact_thresholds[DECISION_ALGORITHM_UPLIFT_ONLY] | intent_layer_thresholds.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_ONLY]
            }
            scoring_layer_thresholds_record = scoring_layer_thresholds(
                scoring_layer_id = scoring_layer_record.uuid,
                store_id = store_record.uuid,
                time = time.time(),
                active = True,
                scoring_artifact_thresholds = scoring_artifact_thresholds
            )
            db_session.add(scoring_layer_thresholds_record)
            db_session.commit()
        


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "copy-intent-model-layers",
            "create-uplift-decision-layer",
            "update-thresholds-for-model",
            "create-intent-and-uplift-decision-layer",
            "clone-intent-decision-layer-into-store-specific",
            "clone-thresholds-for-store",
            "create-test-intent-scoring-layer",
            "seed-intent-scoring-layer-thresholds-for-store"
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--uplift-trt-deployment-reference",
        help="Name.",
        dest="uplift_trt_deployment_reference")
    
    parser.add_argument(
        "--uplift-ctl-deployment-reference",
        help="Name.",
        dest="uplift_ctl_deployment_reference")
    
    parser.add_argument(
        "--uplift-propensity-deployment-reference",
        help="Name.",
        dest="uplift_propensity_deployment_reference")
    
    parser.add_argument(
        "--intent-deployment-reference",
        help="Name.",
        dest="intent_deployment_reference"
    )
    
    parser.add_argument(
        "--filename",
        help="Name.",
        dest="filename")
    
    parser.add_argument(
        "--model-version-id",
        help="Name.",
        dest="model_version_id")
    
    parser.add_argument(
        "--scoring-layer-id",
        help="Name.",
        dest="scoring_layer_id")

    #intent and uplift args
    parser.add_argument(
        "--store-id",
        help="Name.",
        dest="store_id")
    
    parser.add_argument(
        "--copy-uplift-only-scoring-layer-id",
        help="Name.",
        dest="copy_uplift_only_scoring_layer_id")
    
    parser.add_argument(
        "--copy-intent-scoring-layer-id",
        help="Name.",
        dest="copy_intent_scoring_layer_id")
    
    parser.add_argument(
        "--cloned-intent-scoring-layer-id",
        help="Name.",
        dest="cloned_intent_scoring_layer_id")


    args = parser.parse_args()
    if args.action == "copy-intent-model-layers":
        with app.app_context():
            copy_models_to_layers(
                args.model_version_id
            )
    if args.action == "create-uplift-decision-layer":
        with app.app_context():
            create_uplift_decision_layer(
                args.uplift_trt_deployment_reference,
                args.uplift_ctl_deployment_reference,
                args.uplift_propensity_deployment_reference,
                args.filename
            )
    if args.action == "update-thresholds-for-model":
        with app.app_context():
            update_thresholds_for_model(
                args.model_version_id,
                args.scoring_layer_id
            )
    if args.action == "create-test-intent-scoring-layer":
        with app.app_context():
            create_test_intent_decision_layer(
                args.intent_deployment_reference,
                args.filename
            )
    
    if args.action == "seed-intent-scoring-layer-thresholds-for-store":
        with app.app_context():
            seed_intent_scoring_layer_thresholds_for_store(
                args.store_id,
                args.scoring_layer_id
            )
    if args.action == "create-intent-and-uplift-decision-layer":
        with app.app_context():
            create_intent_and_uplift_decision_layer(
                args.copy_uplift_only_scoring_layer_id, #copy_uplift_only_scoring_layer_id,
                args.copy_intent_scoring_layer_id, #copy_intent_scoring_layer_id,
                store_id=args.store_id,
            )
    if args.action == "clone-intent-decision-layer-into-store-specific":
        with app.app_context():
            clone_intent_decision_layer_into_store_specific(
                args.copy_intent_scoring_layer_id,
                args.store_id
            )
    if args.action == "clone-thresholds-for-store":
         with app.app_context():
            clone_thresholds_for_store(
                args.copy_intent_scoring_layer_id,
                args.cloned_intent_scoring_layer_id,
                args.store_id
            )