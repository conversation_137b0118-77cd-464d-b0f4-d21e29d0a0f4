import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
import time
from app import app, db_session
from db import metabase_analytics
from utils import get_generic_logger, unique_id

logger = get_generic_logger(__name__)

def create_new_metabase_association(intervention_id, dashboard_id):
    #url
    new_association = metabase_analytics()
    new_association.uuid = unique_id()
    new_association.intervention_type_id = intervention_id
    new_association.dashboard_id = dashboard_id

    db_session.add(new_association)
    db_session.commit()
    return new_association


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "add-metabase",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--intervention-id",
        help="Intervention ID.",
        dest="intervention_id",
    )

    parser.add_argument(
        "--dashboard-id",
        help="Dashboard ID.",
        dest="dashboard_id",
    )


    args = parser.parse_args()
    if args.action == "add-metabase":
        with app.app_context():
            assoication = create_new_metabase_association(args.intervention_id, args.dashboard_id)
            logger.info(f"New metabase association created, id: {assoication.uuid}")

