import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
import time
from app import app, db_session
from db import model_version
from utils import get_generic_logger, unique_id

logger = get_generic_logger(__name__)

def create_new_model_version(filename, deployment_id, description, run_predictions, live_version):
    
    new_model_version = model_version(
        uuid = unique_id(),
        time = time.time(),
        filename = filename,
        deployment_id = deployment_id,
        description = description,
        #default values
        decision_threshold = 0.05,
        min_decision_threshold = 0.0,
        holdout_percent = 0.2,
        time_cutoff = 71,
        run_predictions = run_predictions,
        live_version = live_version,
        store_specific_model = False
    )
    
    db_session.add(new_model_version)
    db_session.commit()
    return new_model_version


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "add-model-version",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--filename",
        help="File Name.",
        dest="filename",
    )

    parser.add_argument(
        "--deployment-id",
        help="Deployment Id.",
        dest="deployment_id",
    )

    parser.add_argument(
        "--description",
        help="Description.",
        dest="description",
    )

    parser.add_argument(
        "--run-predictions", 
        default=False, 
        action="store_true", 
        dest="run_predictions"
    )

    parser.add_argument(
        "--live-version", 
        default=False, 
        action="store_true", 
        dest="live_version"
    )


    args = parser.parse_args()
    if args.action == "add-model-version":
        with app.app_context():
            model_version = create_new_model_version(args.filename, 
                args.deployment_id, 
                args.description, 
                args.run_predictions, 
                args.live_version
            )
            logger.info(f"New model created, id: {model_version.uuid}, please edit default parameters in DB.")
