import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
import time
import json
import shopify
from app import app, db_session
from db import store_url, store
from utils import get_generic_logger, unique_id

logger = get_generic_logger(__name__)

def create_new_test_store(store_url_value):
    store_id = unique_id()
    #url
    new_store_url = store_url()
    new_store_url.uuid = unique_id()
    new_store_url.time = time.time()
    new_store_url.default = True
    new_store_url.store_id = store_id
    new_store_url.url = store_url_value

    db_session.add(new_store_url)

    new_store = store()
    new_store.uuid = store_id
    new_store.time = time.time()
    new_store.name = "Test Store"
    new_store.vandra_admin_show_discount = True
    db_session.add(new_store)
    db_session.commit()
    return new_store

def print_settings_data(store_url_value):
    """Print the contents of settings_data.json for a given store."""
    store_url_record = store_url.query.filter_by(url=store_url_value).first()
    if store_url_record is None:
        logger.error(f"Store URL record not found for: {store_url_value}")
        return
    
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        logger.error(f"Store record not found for store_id: {store_url_record.store_id}")
        return

    try:
        session = shopify.Session(store_url_value, app.config["SHOPIFY_LEGACY_API_VERSION"], store_record.access_token)
        shopify.ShopifyResource.activate_session(session)
        
        asset = shopify.Asset.find("config/settings_data.json")
        value = json.loads(asset.value)
        
        print("\nSettings Data Contents:")
        print("=======================")
        print(json.dumps(value, indent=2))
        print("=======================\n")

        # Check Vandra app blocks
        print("\nVandra App Block Status:")
        print("=======================")
        
        if isinstance(value["current"], str) and value["current"] == "Default":
            print("❌ Settings are in default state")
            return
            
        if "blocks" not in value["current"]:
            print("❌ No blocks found in settings")
            return
            
        blocks = value["current"]["blocks"]
        
        # Check old Vandra app ID
        if "13652151254090710087" in blocks:
            status = "✅" if not blocks["13652151254090710087"]["disabled"] else "❌"
            print(f"{status} Old Vandra app block (13652151254090710087) found - {'enabled' if not blocks['13652151254090710087']['disabled'] else 'disabled'}")
        
        # Check new Vandra block ID
        if app.config["SHOPIFY_VANDRA_THEME_EXTENSION_BLOCK_ID"] in blocks:
            status = "✅" if not blocks[app.config["SHOPIFY_VANDRA_THEME_EXTENSION_BLOCK_ID"]]["disabled"] else "❌"
            print(f"{status} New Vandra block ({app.config['SHOPIFY_VANDRA_THEME_EXTENSION_BLOCK_ID']}) found - {'enabled' if not blocks[app.config['SHOPIFY_VANDRA_THEME_EXTENSION_BLOCK_ID']]['disabled'] else 'disabled'}")
        
        # Check Vandra extension ID in block types
        if app.config["SHOPIFY_VANDRA_THEME_EXTENSION_ID"]:
            found = False
            for block_id, block in blocks.items():
                if app.config["SHOPIFY_VANDRA_THEME_EXTENSION_ID"] in block["type"]:
                    found = True
                    status = "✅" if not block["disabled"] else "❌"
                    print(f"{status} Vandra extension ({app.config['SHOPIFY_VANDRA_THEME_EXTENSION_ID']}) found in block {block_id} - {'enabled' if not block['disabled'] else 'disabled'}")
            if not found:
                print(f"❌ Vandra extension ({app.config['SHOPIFY_VANDRA_THEME_EXTENSION_ID']}) not found in any blocks")
        
        print("=======================\n")
        
    except Exception as e:
        logger.error(f"Error fetching settings data: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "add-test-store",
            "print-settings",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--store-url",
        help="Store URL.",
        dest="store_url",
    )

    args = parser.parse_args()
    if args.action == "add-test-store":
        with app.app_context():
            store = create_new_test_store(args.store_url)
            logger.info(f"New store created, id: {store.uuid}")
    elif args.action == "print-settings":
        with app.app_context():
            print_settings_data(args.store_url)

