import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
from app import app, db_session
from db import intervention_type
from utils import get_generic_logger

logger = get_generic_logger(__name__)

def backfill_intervention_types():
    """
    Backfills title and description for all intervention types
    """
    mapping = {
        "intent_based_discount": {
            "title": "Intent Based Discount",
            "description": "Intent based discount intervention"
        },
        "cart_abandonment_returning": {
            "title": "Cart Abandonment for Return Visitors",
            "description": "Nudge shoppers that come back to your site and have an active cart from a previous session. Foregrounds the most recently added item and, if applicable, any savings"
        },
        "cart_abandonment_in_session": {
            "title": "Cart Abandonment In-Session",
            "description": "Nudge shoppers that are \"stalling out\" after adding items to their cart. Foregrounds the most recently added item and, if applicable, any savings"
        },
        "pick_up_where_you_left_off": {
            "title": "Pick Up Where You Left Off (PUWYLO)",
            "description": "Nudge return visitors that viewed products on a recent session, but did not make a purchase and do not have an active cart. Foregrounds up to 3 products they've viewed in the last week"
        },
        "social_media_content": {
            "title": "Excessive Dwell Time on a PDP - Social Media Content",
            "description": "Help shoppers get \"unstuck\" while viewing a product. Surfaces a relevant video about your product / brand if a shopper has been on a PDP for an extended period of time"
        },
        "navigational_nudge": {
            "title": "New Visitor Data Collection / Navigation",
            "description": "Nudge first-time visitors with a question that can help you direct them to a good next step in their journey. Surfaces a question and up to 4 options. Each option is a link to a relevant page on your site (e.g., a collection, a landing page)"
        },
        "savings_tab": {
            "title": "Savings Tab",
            "description": "Nudge shoppers to make a purchase by continually reminding them of the savings building up in their cart. Surfaces a tab on the side of the page that shows them the total savings in their cart"
        }
    }
    
    try:
        updated_count = 0
        intervention_types = intervention_type.query.all()
        
        for intervention in intervention_types:
            if intervention.name in mapping:
                intervention.title = mapping[intervention.name]["title"]
                intervention.description = mapping[intervention.name]["description"]
                updated_count += 1
        
        db_session.commit()
        logger.info(f"Successfully updated {updated_count} intervention types")
        return True
        
    except Exception as e:
        logger.error(f"Error backfilling intervention types: {str(e)}")
        db_session.rollback()
        return False

def main():
    parser = argparse.ArgumentParser(description='Intervention Type Backfill Utility')
    parser.add_argument('--action', choices=['backfill'], required=True,
                      help='Action to perform')

    args = parser.parse_args()

    if args.action == 'backfill':
        with app.app_context():
            success = backfill_intervention_types()
            if success:
                logger.info("Intervention type backfill completed successfully")
            else:
                logger.error("Intervention type backfill failed")

if __name__ == "__main__":
    main() 