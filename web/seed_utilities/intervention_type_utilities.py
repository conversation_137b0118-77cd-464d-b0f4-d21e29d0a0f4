import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
from app import app
from modules import intervention_orchestration
from db import store as Store, intervention_type
from utils import get_generic_logger
from sqlalchemy import or_

logger = get_generic_logger(__name__)

def create_associations_all_stores(type_name, store_id=None):
    intervention_type_record = intervention_type.query.filter_by(name=type_name).first()
    if not intervention_type:
        logger.error(f"Intervention type with name {type_name} not found.")
        return
    if store_id:
        stores = Store.query.filter_by(uuid=store_id).all()
        if not stores:
            logger.error(f"Store with id {store_id} not found.")
            return
    else:    
        stores = Store.query.filter(or_(Store.status == 'Active', Store.status == None)).all()
    for store in stores:
        intervention_orchestration.create_store_intervention_association(
            store, intervention_type_record
        )
        logger.info(f"Created association for store {store.uuid} and intervention type {intervention_type_record.uuid}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "add-new-intervention-type",
            "seed-intervention-store-associations",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--intervention-type-name",
        help="Name.",
        dest="intervention_type_name",
    )


    parser.add_argument(
        "--store-id",
        help="Store id.",
        dest="store_id",
    )


    args = parser.parse_args()
    if args.action == "add-new-intervention-type":
        with app.app_context():
            fe = intervention_orchestration.create_intervention_type(args.intervention_type_name)
            logger.info(f"New intervention created, id: {fe.uuid}")
    elif args.action == "seed-intervention-store-associations":
        with app.app_context():
            if args.store_id == "all":
                create_associations_all_stores(args.intervention_type_name)
            else:
                create_associations_all_stores(args.intervention_type_name, args.store_id)
    

