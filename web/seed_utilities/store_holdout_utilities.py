import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
import time
from app import app, db_session
from db import store, model_store_holdout, model_version
from utils import get_generic_logger, unique_id
from constants import ANTI_HOLDOUT_LABEL, LOW_INTENT_LABEL

logger = get_generic_logger(__name__)

def set_store_specific_holdouts(stores, holdout_percent, holdout_type, models_to_include):
    #url
    if stores:
        store_ids = stores
    else:
        store_ids = [x.uuid for x in store.query.filter_by(vandra_admin_show_discount=True).all()]
    if models_to_include:
        model_ids_to_include = models_to_include
    else:
        model_ids_to_include = [x.uuid for x in model_version.query.filter_by(live_version=True).all()]

    for store_id in store_ids:
        for model_version_id in model_ids_to_include:
            holdout_record = model_store_holdout(
                uuid = unique_id(),
                time=time.time(),
                holdout_percent = holdout_percent,
                model_version_id = model_version_id,
                store_id = store_id,
                active = True,
                holdout_type = holdout_type
            )
            db_session.add(holdout_record)
    db_session.commit()



if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "set-holdout",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--store-ids",
        nargs="+",
        default=None,
        action="store",
        dest='store_ids'
    )

    parser.add_argument(
        '--holdout-type',
        choices=[LOW_INTENT_LABEL, ANTI_HOLDOUT_LABEL],
        required=False,
        help='Which type of holdout are we setting',
        dest="holdout_type"
    )


    parser.add_argument(
        "--holdout-percent",
        help="Holdout %",
        default=None,
        action="store",
        dest="holdout_percent",
        type=float
    )

    parser.add_argument(
        "--models-to-include",
        help="Include only these models (None for all in decisioning)",
        nargs='+',
        default=None,
        action="store",
        dest='models_to_include'
    )

    args = parser.parse_args()
    if args.action == "set-holdout":
        with app.app_context():
            set_store_specific_holdouts(args.store_ids, args.holdout_percent, args.holdout_type, args.models_to_include)
    