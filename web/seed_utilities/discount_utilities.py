import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
import time
from app import app, db_session
from db import discount_code, store_url
from utils import get_generic_logger, unique_id

logger = get_generic_logger(__name__)

def create_new_discount(code, store_id):
    # Get store URL from store_url table
    store_url_record = store_url.query.filter_by(store_id=store_id).\
        order_by(store_url.default.desc()).first()
    if store_url_record is None:
        logger.error(f"No store URL found for store_id: {store_id}")
        return None
    
    new_discount = discount_code(
        uuid = unique_id(),
        discount_code_code=code,
        ends_at_time = time.time() + 86400*365,
        time = time.time() + 86400*365,
        price_rule_value=-20,
        store_id = store_id,
        expired = False,     
    )
    

    db_session.add(new_discount)
    db_session.commit()
    return new_discount


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "add-discount",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--code",
        help="Code.",
        dest="code",
    )

    parser.add_argument(
        "--store-id",
        help="Store Id.",
        dest="store_id",
    )

    args = parser.parse_args()
    if args.action == "add-discount":
        with app.app_context():
            discount = create_new_discount(args.code, 
                args.store_id,
            )
            if discount:
                logger.info(f"New discount created, id: {discount.uuid}, please edit as needed in the DB.")
            else:
                logger.error("Failed to create discount")
