import os
import sys
import inspect
import json
import time

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
from app import app, db_session
from db import subscription_plans
from utils import get_generic_logger

logger = get_generic_logger(__name__)

def create_default_susbcription_plans():
    data = {
      "Starter": {
        "maximumOrders": 500,
        "price": 39
      },
      "Pro": {
        "maximumOrders": 1000,
        "price": 99
      },
      "Advanced": {
        "maximumOrders": 2500,
        "price": 249
      },
      "Growth": {
        "maximumOrders": 5000,
        "price": 499
      },
      "Enterprise": {
        "maximumOrders": 10000,
        "price": 999,
        "overflow_orders": 1000,
        "overflow_cost": 50
      },
    }
    db_session.add(subscription_plans(
      created_at=time.time(),
      pricing_details=json.dumps(data)
    ))
    db_session.commit()
    return True


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "add-plans",
        ],
        required=True,
        dest="action",
    )

    args = parser.parse_args()
    if args.action == "add-plans":
        with app.app_context():
            subscription_plans = create_default_susbcription_plans()
            logger.info(f"Default subscription plans created.")
