import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
from modules.vandra_admin import create_admin_user, generate_password
from app import app
from utils import get_generic_logger

logger = get_generic_logger(__name__)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "add-admin",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--email",
        help="Email address.",
        dest="email",
    )

    parser.add_argument(
        "--password",
        help="(optional) Password. If not passed, a random one will be generated automatically",
        dest="password",
    )

    parser.add_argument("--super-admin", default=False, action="store_true", dest="super_admin")

    args = parser.parse_args()
    if args.action == "add-admin":
        with app.app_context():
            password = args.password
            if not password:
                password = generate_password()
            new_admin = create_admin_user(args.email,password, args.super_admin)
            logger.info(f"New admin created, password: {password}")

