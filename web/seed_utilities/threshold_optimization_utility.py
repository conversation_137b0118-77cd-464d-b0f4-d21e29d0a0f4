import argparse
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from modules.scoring_threshold_use_cases import update_intent_thresholds_based_on_show_rate_optimization
from utils import get_generic_logger

logger = get_generic_logger(__name__)

def trigger_threshold_optimization(store_id):
    """
    Triggers the threshold optimization cycle for a specific store.
    
    Args:
        store_id: UUID of the store to optimize thresholds for
    
    Returns:
        dict: Results of the optimization containing:
            - success: bool indicating if operation completed without errors
            - updates: dict of layer updates or None if error
            - needs_more_data: bool indicating if more data collection is needed
    """
    try:
        updates, needs_more_data = update_intent_thresholds_based_on_show_rate_optimization(store_id)
        
        return {
            "success": True,
            "updates": updates,
            "needs_more_data": needs_more_data
        }
        
    except Exception as e:
        logger.error(f"Error triggering threshold optimization: {str(e)}")
        return {
            "success": False,
            "updates": None,
            "needs_more_data": None,
            "error": str(e)
        }

def main():
    parser = argparse.ArgumentParser(description='Threshold Optimization Utility')
    parser.add_argument('--store-id', type=str, required=True,
                      help='UUID of the store to optimize thresholds for')

    args = parser.parse_args()

    with app.app_context():
        result = trigger_threshold_optimization(args.store_id)
        if result["success"]:
            logger.info(f"Successfully updated thresholds for store {args.store_id}")
            logger.info("Updates made:")
            for layer_id, threshold in result["updates"].items():
                logger.info(f"Layer {layer_id}: new threshold = {threshold}")
        else:
            logger.error(f"Error: {result['error']}")
            if result["needs_more_data"]:
                logger.info(f"Will retry in 6 hours when more data is available")
            else:
                logger.info("Store is either using a different methodology or has no qualifying layers")

if __name__ == "__main__":
    main() 