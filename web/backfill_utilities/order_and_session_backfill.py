import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

import argparse
from app import app
from db import order, webhook_request, user_session, store_url, store, discount_code, db
from utils import get_generic_logger, unique_id
import json
import time
import datetime
import requests

logger = get_generic_logger(__name__)

def backfill_sessions_based_on_webhooks_data(epoch_filter, write_to_db=False):
    webhooks = webhook_request.query.filter(webhook_request.event=='orders/create',
         webhook_request.time>=epoch_filter
         #, webhook_request.uuid=='ca0cb6db-1b0a-457c-84ce-dd5a776586ad'
         )
    properly_linked_orders = 0
    mislinked_orders = 0
    unlinked_orders = 0
    unlinked_vandra_conversions = 0
    cutoff_orders = 0 #orders that could have been linked, but would not due to cutoff
    for wb in webhooks:
            
        payload = json.loads(wb.data)
        created_at_timestamp = datetime.datetime.strptime(payload["created_at"], "%Y-%m-%dT%H:%M:%S%z") 
        wb_time = round(time.mktime(created_at_timestamp.timetuple()))
        
        #skip if order id already tied to a session
        tagged_session = user_session.query.filter(user_session.order_id==str(payload["id"])).first()
        #get true session
        session_id = None
        for att in [x for x in payload["note_attributes"] if x["name"] == "__vandra_session"]:
            session_id = att["value"]
            break
        if session_id is None:
            continue
        
        #TODO: revisit 24 hr assumption
        existing_session = user_session.query.filter_by(uuid=session_id).first()
        
        if tagged_session is not None:
            if existing_session and existing_session.uuid != tagged_session.uuid:
                mislinked_orders +=1 
            elif existing_session:
                properly_linked_orders +=1
            continue

        if existing_session is None:
            continue
        #change to be based on the created_time, not webhook time, remove if 24 hr logic gets debunked
        elif existing_session.time < (wb_time - 86400):
            cutoff_orders +=1
            continue
        else:
            unlinked_orders +=1 
        
        if write_to_db:
            existing_session.conversion = True
            existing_session.cart_size = payload["subtotal_price"]
            existing_session.order_id = payload["id"]
            db.session.commit()
            
        #if no discounts
        if payload["discount_codes"] == []:
            continue

        store_url_record = store_url.query.filter_by(url=wb.shop_url).first()
        if store_url_record is None:
            continue
        
        store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
        if store_record is None:
            continue

        store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
            order_by(store_url.default.desc()).first()
        if store_url_record is None:
            continue

        for each_discount in payload["discount_codes"]:
            if write_to_db:
                existing_session.conversion_discount_code = each_discount["code"]
                existing_session.conversion_discount_amount = each_discount["amount"]
                db.session.commit()
                
            check_discount_code = discount_code.query.filter_by(discount_code_code=each_discount["code"]).filter_by(store_id=store_record.uuid).first()
            if check_discount_code is None:
                continue

            unlinked_vandra_conversions +=1
            if write_to_db:
                existing_session.vandra_conversion = True
                db.session.commit()

                # Add Vandra tag to the order
                graphql_url = "https://" + store_url_record.url + "/admin/api/" + app.config['SHOPIFY_LEGACY_API_VERSION'] + "/graphql.json"
                headers = {
                    "Content-Type": "application/json",
                    "X-Shopify-Access-Token": store_record.access_token
                }
                query = '''mutation tagsAdd($id: ID!, $tags: [String!]!) {
                    tagsAdd(id: $id, tags: $tags) {
                        node {
                            id
                        }
                        userErrors {
                            field
                            message
                        }
                    }
                }'''
                variables = {
                    "id": "gid://shopify/Order/" + str(payload["id"]),
                    "tags": [
                        "Vandra"
                    ]
                }

                resp = requests.post(graphql_url, headers=headers, json={'query': query, 'variables': variables})
    #totals
    logger.info(f"{properly_linked_orders} properly linked orders")
    logger.info(f"{mislinked_orders} mislinked orders")
    logger.info(f"{cutoff_orders} cutoff sessions")
    logger.info(f"{unlinked_orders} unlinked orders")
    logger.info(f"{unlinked_vandra_conversions} unlinked vandra conversions")
            


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Pass action name.",
        choices=[
            "backfill-order-webhooks",
        ],
        required=True,
        dest="action",
    )

    parser.add_argument(
        "--time",
        help="time.",   
        dest="time",
    )

    parser.add_argument(
        "--write-to-db",
        help="Write to db.",
        dest="write_to_db",
        action="store_true",
        default=False
    )

    args = parser.parse_args()
    if args.action == "backfill-order-webhooks":
        with app.app_context():
            backfill_sessions_based_on_webhooks_data(int(args.time), 
                args.write_to_db,
            )
        
