DEFAULT_HOLDOUT_PERC = 0.2
DEFAULT_ANTI_HOLDOUT_PERC = 0.08214
DEFAULT_ANTI_HOLDOUT_HOLDOUT_PERC = 1 - DEFAULT_ANTI_HOLDOUT_PERC
ANTI_HOLDOUT_LABEL = 'anti-holdout'
LOW_INTENT_LABEL = 'low-intent'
MEDIUM_INTENT_LABEL = 'medium-intent'
HIGH_INTENT_LABEL = 'high-intent'
INCONCLUSIVE_INTENT_LABEL = 'inconclusive-intent'
#cart abandonment holdout
CART_ABANDONMENT_DEFAULT_HOLDOUT_PERC = 0.5
NAVIGATIONAL_NUDGE_DEFAULT_HOLDOUT_PERC = 0.5
PICK_UP_WHERE_YOU_LEFT_OFF_DEFAULT_HOLDOUT_PERC = 0.2
SAVINGS_NUDGE_DEFAULT_HOLDOUT_PERC = 0.2
#uplift
LOW_UPLIFT_LABEL= "low-uplift"
HIGH_UPLIFT_LABEL= "high-uplift"
FORCE_DEFAULT_RIGHT_ANTI_HOLDOUT = True
#intervention status constants
INTERVENTION_STATUS_ASSIGNED = "assigned"
INTERVENTION_STATUS_SHOWN = "shown"
INTERVENTION_STATUS_DISMISSED = "dismissed"
INTERVENTION_STATUS_ACTIONED = "actioned"
INTERVENTION_STATUS_SUPPRESSED = "suppressed"
INTERVENTION_STATUS_MISSING_ASSET = "missing_asset"
INTERVENTION_STATUS_ENGAGED = "engaged"
#intervention event types
INTERVENTION_EVENT_TYPE_CTA_CLICKED = "cta_clicked"
INTERVENTION_EVENT_TYPE_DISMISSED = "popup_dismissed"
INTERVENTION_EVENT_TYPE_SHOWN = "popup_shown"
INTERVENTION_EVENT_TYPE_MISSING_ASSET = "missing_asset"
INTERVENTION_EVENT_TYPE_EXPANDED = "expanded"
INTERVENTION_EVENT_TYPE_MINIMIZED = "minimized"
INTERVENTION_EVENT_TYPE_PLAYED = "played"
INTERVENTION_EVENT_TYPE_PAUSED = "paused"
INTERVENTION_EVENT_TYPE_UNMUTED = "unmuted"
INTERVENTION_EVENT_TYPE_MUTED = "muted"
#intervention types
INTERVENTION_TYPE_INTENT_BASED_DISCOUNT = "intent_based_discount"
INTERVENTION_TYPE_CART_ABANDONMENT_IN_SESSION = "cart_abandonment_in_session"
INTERVENTION_TYPE_CART_ABANDONMENT_RETURNING = "cart_abandonment_returning"
INTERVENTION_TYPE_NAVIGATIONAL_NUDGE = "navigational_nudge"
INTERVENTION_TYPE_PICK_UP_WHERE_YOU_LEFT_OFF = "pick_up_where_you_left_off"
INTERVENTION_TYPE_SAVINGS_NUDGE = "savings_nudge"
INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT = "social_media_content"
#opportunity labels
OPPORTUNITY_LABEL_SAVINGS_NUDGE_ELIGIBLE = "savings_nudge_eligible"
OPPORTUNITY_LABEL_CART_ABANDONMENT_ELIGIBLE = "cart_abandonment_eligible"
OPPORTUNITY_LABEL_NAVIGATIONAL_NUDGE_ELIGIBLE = "navigational_nudge_eligible"
OPPORTUNITY_LABEL_PICK_UP_WHERE_YOU_LEFT_OFF_ELIGIBLE = "pick_up_where_you_left_off_eligible"
#valid intervention types
VALID_INTERVENTION_TYPES = [
    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
    INTERVENTION_TYPE_CART_ABANDONMENT_IN_SESSION,
    INTERVENTION_TYPE_CART_ABANDONMENT_RETURNING,
    INTERVENTION_TYPE_PICK_UP_WHERE_YOU_LEFT_OFF,
    INTERVENTION_TYPE_NAVIGATIONAL_NUDGE,
    INTERVENTION_TYPE_SAVINGS_NUDGE,
    INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT
]
#valid assignment changes
VALID_INTERVENTION_ASSIGNMENT_CHANGES = {
    #for intent based discounts, these are the valid changes
    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT : {
        INTERVENTION_STATUS_ASSIGNED: [INTERVENTION_STATUS_SHOWN, INTERVENTION_STATUS_SUPPRESSED], #from assigned to show
        INTERVENTION_STATUS_SHOWN: [INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ACTIONED],
        INTERVENTION_STATUS_DISMISSED: [INTERVENTION_STATUS_ACTIONED], #dismissed can only move to actioned
        INTERVENTION_STATUS_ACTIONED: [], #if actioned, that's it
        INTERVENTION_STATUS_SUPPRESSED: [INTERVENTION_STATUS_SHOWN]
    },
    INTERVENTION_TYPE_SAVINGS_NUDGE : {
        INTERVENTION_STATUS_ASSIGNED: [INTERVENTION_STATUS_SHOWN, INTERVENTION_STATUS_SUPPRESSED], #from assigned to show
        INTERVENTION_STATUS_SHOWN: [INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ENGAGED],
        INTERVENTION_STATUS_ENGAGED: [INTERVENTION_STATUS_ACTIONED, INTERVENTION_STATUS_DISMISSED],
        INTERVENTION_STATUS_DISMISSED: [INTERVENTION_STATUS_ACTIONED], #dismissed can only move to actioned
        INTERVENTION_STATUS_ACTIONED: [], #if actioned, that's it
        INTERVENTION_STATUS_SUPPRESSED: [INTERVENTION_STATUS_SHOWN]
    },
    INTERVENTION_TYPE_CART_ABANDONMENT_IN_SESSION: {
        INTERVENTION_STATUS_ASSIGNED: [INTERVENTION_STATUS_SHOWN, INTERVENTION_STATUS_SUPPRESSED],
        INTERVENTION_STATUS_SHOWN: [INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ACTIONED],
        INTERVENTION_STATUS_SUPPRESSED: [INTERVENTION_STATUS_SHOWN]
    },
    INTERVENTION_TYPE_CART_ABANDONMENT_RETURNING: {
        INTERVENTION_STATUS_ASSIGNED: [INTERVENTION_STATUS_SHOWN, INTERVENTION_STATUS_SUPPRESSED],
        INTERVENTION_STATUS_SHOWN: [INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ACTIONED],
        INTERVENTION_STATUS_SUPPRESSED: [INTERVENTION_STATUS_SHOWN]
    },
    INTERVENTION_TYPE_NAVIGATIONAL_NUDGE: {
        INTERVENTION_STATUS_ASSIGNED: [INTERVENTION_STATUS_SHOWN, INTERVENTION_STATUS_SUPPRESSED],
        INTERVENTION_STATUS_SHOWN: [INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ACTIONED],
        INTERVENTION_STATUS_DISMISSED: [INTERVENTION_STATUS_ACTIONED], #dismissed can only move to actioned
        INTERVENTION_STATUS_ACTIONED: [], #if actioned, that's it
        INTERVENTION_STATUS_SUPPRESSED: [INTERVENTION_STATUS_SHOWN],
    },
    INTERVENTION_TYPE_PICK_UP_WHERE_YOU_LEFT_OFF: {
        INTERVENTION_STATUS_ASSIGNED: [INTERVENTION_STATUS_SHOWN, INTERVENTION_STATUS_SUPPRESSED],
        INTERVENTION_STATUS_SHOWN: [INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ACTIONED],
    },
    INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT: {
        INTERVENTION_STATUS_ASSIGNED: [INTERVENTION_STATUS_SHOWN, INTERVENTION_STATUS_SUPPRESSED, INTERVENTION_STATUS_MISSING_ASSET],
        INTERVENTION_STATUS_SHOWN: [INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ACTIONED, INTERVENTION_STATUS_ENGAGED],
        INTERVENTION_STATUS_MISSING_ASSET: [INTERVENTION_STATUS_SHOWN],
        INTERVENTION_STATUS_ENGAGED: [INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ACTIONED, INTERVENTION_STATUS_ENGAGED]
    }
}
INTERVENTION_DECISION_SHOW = "show"
INTERVENTION_DECISION_NO_SHOW = "no_show"
#
INTENT_BASED_DISCOUNT_MAX_DECISION_DELAY = 20
DECISION_DELAY_MAPPING = { #mapping of decision delay to actual delay
    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT: INTENT_BASED_DISCOUNT_MAX_DECISION_DELAY,
}
#decision algorithm names
DECISION_ALGORITHM_INTENT_ONLY = "intent_only"
DECISION_ALGORITHM_INTENT_AND_UPLIFT = "intent_and_uplift"
DECISION_ALGORITHM_UPLIFT_ONLY = "uplift_only"
#decision algorithm thresholds
DECISION_ALGORITHM_THRESHOLD_UPLIFT = "minimum_uplift"
DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED = "low_med_threshold"
DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH = "med_high_threshold"
#scoring artifact types
SCORING_ARTIFACT_TYPE_INTENT = "intent"
SCORING_ARTIFACT_TYPE_UPLIFT_CTL = "uplift_ctl"
SCORING_ARTIFACT_TYPE_UPLIFT_TRT = "uplift_trt"
SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY = "uplift_propensity"
#determination types
DETERMINATION_TYPE_INTENT = "intent"
DETERMINATION_TYPE_UPLIFT = "uplift"
#
SCORING_ALGORITHM_TYPES = {  # mapping of intervention type to scoring algorithm types
    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT: [
        DECISION_ALGORITHM_INTENT_ONLY,
        DECISION_ALGORITHM_INTENT_AND_UPLIFT,
        DECISION_ALGORITHM_UPLIFT_ONLY,
    ],
}
#
PIPELINE_FILE_KEYNAME = "pipeline_file"
#default uplift threshold
DEFAULT_UPLIFT_THRESHOLD = 0.012
#STORE STATUS
STORE_STATUS_UNINSTALLED = "Uninstalled"
STORE_STATUS_ACTIVE = "Active"
STORE_STATUS_INACTIVE = "Inactive"