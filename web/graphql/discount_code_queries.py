DISCOUNT_CODE_QUERY = """
query codeDiscountNodeByCode($code: String!) {
    codeDiscountNodeByCode(code: $code) {
        codeDiscount {
            __typename
            ... on DiscountCodeBasic {
                title
                summary
                startsAt
                endsAt
                status
                usageLimit
                asyncUsageCount
                appliesOncePerCustomer
                createdAt
                updatedAt
                customerSelection {
                    ... on DiscountCustomerAll {
                        allCustomers
                    }
                    ... on DiscountCustomerSegments {
                        segments {
                            id
                        }
                    }
                }
                customerGets {
                    value {
                        ... on DiscountAmount {
                            amount {
                                amount
                                currencyCode
                            }
                        }
                        ... on DiscountPercentage {
                            percentage
                        }
                    }
                    items {
                        ... on AllDiscountItems {
                            allItems
                        }
                        ... on DiscountProducts {
                            products(first: 10) {
                                edges {
                                    node {
                                        id
                                        title
                                    }
                                }
                            }
                        }
                        ... on DiscountCollections {
                            collections(first: 10) {
                                edges {
                                    node {
                                        id
                                        title
                                    }
                                }
                            }
                        }
                    }
                }
                minimumRequirement {
                    ... on DiscountMinimumQuantity {
                        greaterThanOrEqualToQuantity
                    }
                    ... on DiscountMinimumSubtotal {
                        greaterThanOrEqualToSubtotal {
                            amount
                            currencyCode
                        }
                    }
                }
                combinesWith {
                    orderDiscounts
                    productDiscounts
                    shippingDiscounts
                }
                totalSales {
                    amount
                    currencyCode
                }
            }
            ... on DiscountCodeBxgy {
                title
                summary
                startsAt
                endsAt
                status
                usageLimit
                asyncUsageCount
                appliesOncePerCustomer
                createdAt
                updatedAt
                usesPerOrderLimit
                customerBuys {
                    items {
                        ... on AllDiscountItems {
                            allItems
                        }
                        ... on DiscountProducts {
                            products(first: 10) {
                                edges {
                                    node {
                                        id
                                        title
                                    }
                                }
                            }
                        }
                        ... on DiscountCollections {
                            collections(first: 10) {
                                edges {
                                    node {
                                        id
                                        title
                                    }
                                }
                            }
                        }
                    }
                }
                customerGets {
                    items {
                        ... on AllDiscountItems {
                            allItems
                        }
                        ... on DiscountProducts {
                            products(first: 10) {
                                edges {
                                    node {
                                        id
                                        title
                                    }
                                }
                            }
                        }
                        ... on DiscountCollections {
                            collections(first: 10) {
                                edges {
                                    node {
                                        id
                                        title
                                    }
                                }
                            }
                        }
                    }
                    value {
                        ... on DiscountAmount {
                            amount {
                                amount
                                currencyCode
                            }
                        }
                        ... on DiscountPercentage {
                            percentage
                        }
                    }
                }
                totalSales {
                    amount
                    currencyCode
                }
            }
            ... on DiscountCodeFreeShipping {
                title
                summary
                startsAt
                endsAt
                status
                usageLimit
                asyncUsageCount
                appliesOncePerCustomer
                appliesOnOneTimePurchase
                appliesOnSubscription
                createdAt
                updatedAt
                destinationSelection {
                    ... on DiscountCountries {
                        countries
                    }
                }
                maximumShippingPrice {
                    amount
                    currencyCode
                }
                totalSales {
                    amount
                    currencyCode
                }
                combinesWith {
                    orderDiscounts
                    productDiscounts
                    shippingDiscounts
                }
            }
        }
        id
    }
}
""" 