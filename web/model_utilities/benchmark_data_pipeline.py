import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
sys.path.append(os.getcwd().replace("/web", "") + "/data_science")

try:
    from data_science.prepare_data import prep_data_for_model
except ModuleNotFoundError as _:
    from prepare_data import prep_data_for_model

import datetime
import argparse
from app import app
import time
from utils import get_generic_logger
from db import db

logger=get_generic_logger(__name__)

def benchmark_pipeline(filename,time_cutoff=71, start_time=0):
    run_start_time = time.time()
    read_replica_engine = db.engines['read_replica'].execution_options(postgresql_readonly=True)
    #with db.engines['read_replica'].connect() as db_connection:
    with read_replica_engine.connect() as db_connection:
        df, _ = prep_data_for_model[filename](train_or_predict="TRAIN", 
                                                    time_cutoff=time_cutoff, 
                                                    number_of_user_sessions=150000,
                                                    start_time=start_time,
                                                    end_time=9999999999,
                                                    lookback_weeks=6,                        
                                                    db_connection=db_connection)
        
    logger.info(f"--- seconds to run --- {(time.time() - run_start_time)}")
    logger.info(f"--- df shape --- {df.shape}")



def benchmark_inference(filename,predict_session_id,time_cutoff=71, start_time=0):
    run_start_time = time.time()
    read_replica_engine = db.engines['read_replica'].execution_options(postgresql_readonly=True)
    #with db.engines['read_replica'].connect() as db_connection:
    with read_replica_engine.connect() as db_connection:
        df, _ = prep_data_for_model[filename](train_or_predict="PREDICT", 
                                                    time_cutoff=time_cutoff, 
                                                    number_of_user_sessions=150000,
                                                    start_time=start_time,
                                                    end_time=9999999999,
                                                    lookback_weeks=6,
                                                    predict_session_id= predict_session_id,                       
                                                    db_connection=db_connection)
        
    logger.info(f"--- seconds to run --- {(time.time() - run_start_time)}")
    logger.info(f"--- df shape --- {df.shape}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description='Evaluate and/or calibrate models'
    )
    
    parser.add_argument(
        '--action',
        choices=['benchmark-training', 'benchmark-inference'],
        required=True,
        help='Run benchmark'
    )
    
    parser.add_argument(
        '--filename',
        type=str,
        required=True,
        help='Feat eng filename to benchmark'
    )
    
    parser.add_argument(
        '--session-id',
        type=str,
        required=True,
        help='Session id',
        dest='session_id'
    )
    
    parser.add_argument(
        '--days-to-pull',
        type=float,
        default=7,
        help='Days of training data to pull'
    )

    args = parser.parse_args()
    
    if args.action == 'benchmark-training':
        with app.app_context():
            now = time.time()
            benchmark_pipeline(args.filename, start_time=now - 86400*args.days_to_pull)
    if args.action == 'benchmark-inference':
        with app.app_context():
            now = time.time()
            benchmark_inference(args.filename, args.session_id,start_time=now - 86400*args.days_to_pull)