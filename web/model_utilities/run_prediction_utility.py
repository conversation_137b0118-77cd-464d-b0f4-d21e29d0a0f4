import os
import sys
import inspect

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir) 

from rq import Worker, SimpleWorker
from redis.exceptions import ConnectionError as RedisConnectionError
from task_queue import (
    REDIS_CONNECTION,
    LOADED_MODELS,
    LIVE_SCORING_LAYER_QUEUE,
    BACKGROUND_SCORING_LAYER_QUEUE
)
import argparse
from utils import get_generic_logger
import config
import boto3
from botocore.exceptions import ClientError
import os.path

logger = get_generic_logger(__name__)


def download_file_from_s3(filename, location='../data_science/model_object_files'):
    s3_client = boto3.client("s3",
                aws_access_key_id=app.config["SES_ACCESS_KEY"],
                aws_secret_access_key=app.config["SES_SECRET"],
                region_name="us-east-2"
    )
        
    with open(f"{location}/{filename}", 'wb') as f:
        s3_client.download_fileobj("vandra-model-objects", filename, f)

def upload_file_from_s3(filename, location='../data_science/model_object_files'):
    s3_client = boto3.client("s3",
                aws_access_key_id=app.config["SES_ACCESS_KEY"],
                aws_secret_access_key=app.config["SES_SECRET"],
                region_name="us-east-2"
    )
        
    try:
        _ = s3_client.upload_file(f"{location}/{filename}", "vandra-model-objects", filename)
        logger.info(f"File {filename} uploaded successfully.")
    except ClientError as e:
        logger.error(e)
    
def handle_job_failure(job, exc_type, exc_value, traceback):  # skipcq: PYL-W0613
    if exc_type is RedisConnectionError:
        logger.exception(f"Connection error for job {job.id}")
    return True


if __name__ == "__main__":
    from app import app
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "--action",
        help="Define what you want to do.",
        choices=[
            "run-worker",
            "upload-model-file"
        ],
        required=True,
    )
    parser.add_argument(
        "--queue-name",
        help="Define what you want to do.",
        choices=["all", "live", "background","background-h2o",'live-scoring-layer','background-scoring-layer'],
        default="all",
        dest="queue_name",
    )

    parser.add_argument(
        "--upload-filename",
        help="File to upload",
        dest="upload_filename",
    )
    parser.add_argument(
        "--upload-file-location",
        help="File location, don't include last /",
        dest="upload_file_location",
    )
    parser.add_argument(
        "--skip-preload",
        help="Skip preloading models.",
        default=False,
        action="store_true",
        dest="skip_preload"
    )

    args = parser.parse_args()
    if args.action == "upload-model-file":
        location = args.upload_file_location
        filename = args.upload_filename
        upload_file_from_s3(filename, location)
    elif args.action == "run-worker":
        with app.app_context():
            queues = []
            #add live model settings
            if args.queue_name in ["live-scoring-layer","all"]:
                queues.append(LIVE_SCORING_LAYER_QUEUE)
            if args.queue_name in ["background-scoring-layer","all"]:
                queues.append(BACKGROUND_SCORING_LAYER_QUEUE)
        #setup worker
        if config.PYTHON_RQ_SIMPLE_WORKER:
            worker = SimpleWorker(
                queues=queues,
                connection=REDIS_CONNECTION,
                exception_handlers=[handle_job_failure],
            )
        else:
            worker = Worker(
                queues=queues,
                connection=REDIS_CONNECTION,
                exception_handlers=[handle_job_failure],
            )
        #run worker
        worker.work()