import os
import sys
import inspect
import time
import logging
from sklearn.metrics import precision_recall_curve
import numpy as np

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)

from db import (db, intervention_type, store, store_url, session_intervention, session_intervention_event,
                scoring_layer, scoring_layer_determination, scoring_layer_thresholds, user_session)
import datetime
import pandas as pd
import itertools
from sqlalchemy import and_, case, desc, func, tuple_
from sqlalchemy.types import JSON
from sqlalchemy.sql.expression import over
import uuid
import argparse
import json
from app import app
from constants import INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, INTERVENTION_STATUS_SHOWN, INTERVENTION_STATUS_DISMISSED, INTERVENTION_STATUS_ACTIONED

sys.path.append(os.path.abspath("../data_science"))
from ds_constants import BOT_BROWSERS
from ds_helpers import flatten_cols

from utils import get_generic_logger

logger = get_generic_logger(__name__)


EXCLUDE_BOTS_ETC = True
CANNIBALIZATION_CONSIDERATION_FILTER_STR = '(holdout==True or opportunity==False) and not vandra_shown'

def get_cmatrix_weight(confusion_matrix_wts, conv_pred, conv_act):
    try:
        return confusion_matrix_wts.query(f'conversion_predicted == {conv_pred} and conversion == {conv_act}')['record_weight'].sum()
    except IndexError:
        return 0

def calc_decayed_weight(time_diff, time_decay_alpha):
        return (1 - time_decay_alpha) ** time_diff

def calc_time_wtd_metric(results_df, threshold_to_test, metric):
    confusion_matrix_wts = (results_df
                           .assign(conversion_predicted=lambda df_: df_['conversion_1_probability'] > threshold_to_test)
                           [['conversion_predicted', 'conversion', 'record_weight', 'opportunity', 'holdout', 'vandra_shown']]
                           )

    #positive event is conversion, negative is no conversion
    # Note: All metrics only consider sessions that reach a decision point, which is all the model was trained on
    if metric == 'discount_offer_rate':
        tn_weight = get_cmatrix_weight(confusion_matrix_wts, False, False)
        fp_weight = get_cmatrix_weight(confusion_matrix_wts, True, False)
        return tn_weight / (tn_weight + fp_weight) # Discount Offer Rate: Weighted number of correct decisions to offer discount / Total weighted number who didn't convert and therefore should have been offered discount
    if metric == 'cannibalization_rate':
        confusion_matrix_wts = confusion_matrix_wts.query(CANNIBALIZATION_CONSIDERATION_FILTER_STR)
        tp_weight = get_cmatrix_weight(confusion_matrix_wts, True, True)
        fn_weight = get_cmatrix_weight(confusion_matrix_wts, False, True)
        
        if fn_weight + tp_weight == 0:
            raise ValueError('No conversions for this store to calculate a cannibalization rate with')
        
        return fn_weight / (fn_weight + tp_weight)  # Cannibalization Rate: Weighted number of incorrect decisions to offer discount / Total weighted number who converted and therefore should not have been offered discount

def find_threshold(results_df, cannibalization_target, threshold_min=0.001, threshold_max=0.6, tolerance=0.000000001):
    while threshold_max - threshold_min > tolerance:
        mid = (threshold_min + threshold_max) / 2
        if calc_time_wtd_metric(results_df=results_df, threshold_to_test=mid, metric='cannibalization_rate') > cannibalization_target:
            threshold_max = mid
        else:
            threshold_min = mid
    return threshold_min

def _validate_and_prepare_threshold_data(results_df, cannibalization_target, min_samples=10):
    """
    Common validation and data preparation for threshold finding methods.
    
    Args:
        results_df (pd.DataFrame): DataFrame with required columns
        cannibalization_target (float): Target cannibalization rate
        min_samples (int): Minimum number of samples required
        
    Returns:
        tuple: (y_true, y_pred_probs, sample_weights) for cannibalization data
        
    Raises:
        ValueError: If validation fails
    """
    # Filter data for cannibalization consideration
    cannibalization_data = results_df.query(CANNIBALIZATION_CONSIDERATION_FILTER_STR)
    
    if len(cannibalization_data) < min_samples:
        raise ValueError(f'Insufficient data for cannibalization calculation: {len(cannibalization_data)} samples (minimum {min_samples} required)')
    
    # Extract relevant columns
    y_true = cannibalization_data['conversion'].values
    y_pred_probs = cannibalization_data['conversion_1_probability'].values
    sample_weights = cannibalization_data['record_weight'].values
    
    if y_true.sum() == 0:
        raise ValueError('No conversions for this store to calculate a cannibalization rate with')
    
    return y_true, y_pred_probs, sample_weights

def _calculate_weighted_cannibalization_rate(y_true, y_pred_probs, sample_weights, threshold):
    """
    Calculate weighted cannibalization rate for a given threshold.
    
    Args:
        y_true (np.array): True conversion labels
        y_pred_probs (np.array): Predicted probabilities
        sample_weights (np.array): Sample weights
        threshold (float): Threshold to test
        
    Returns:
        float: Cannibalization rate (fn_weight / (fn_weight + tp_weight))
    """
    predictions = (y_pred_probs >= threshold).astype(int)
    
    # True positives: predicted 1, actual 1
    tp_mask = (predictions == 1) & (y_true == 1)
    tp_weight = sample_weights[tp_mask].sum() if tp_mask.any() else 0
    
    # False negatives: predicted 0, actual 1  
    fn_mask = (predictions == 0) & (y_true == 1)
    fn_weight = sample_weights[fn_mask].sum() if fn_mask.any() else 0
    
    # Calculate cannibalization rate: fn_weight / (fn_weight + tp_weight)
    total_positive_weight = fn_weight + tp_weight
    if total_positive_weight == 0:
        return float('inf')  # Invalid threshold
        
    return fn_weight / total_positive_weight

def find_threshold_precision_recall(results_df, cannibalization_target, min_samples=10):
    """
    Alternative threshold finding method using custom precision-recall implementation.
    
    This method manually calculates precision/recall with sample weights and finds the optimal 
    threshold that meets the cannibalization target.
    
    Args:
        results_df (pd.DataFrame): DataFrame with columns 'conversion_1_probability', 'conversion', 
                                  'record_weight', 'opportunity', 'holdout', 'vandra_shown'
        cannibalization_target (float): Target cannibalization rate (e.g., 0.15 for 15%)
        min_samples (int): Minimum number of samples required for calculation
        
    Returns:
        float: Optimal threshold that meets or is below the cannibalization target
        
    Raises:
        ValueError: If no conversions exist or insufficient data for calculation
    """
    y_true, y_pred_probs, sample_weights = _validate_and_prepare_threshold_data(
        results_df, cannibalization_target, min_samples
    )
    
    # Get unique thresholds from the prediction probabilities
    unique_thresholds = np.unique(y_pred_probs)
    unique_thresholds = np.sort(unique_thresholds)[::-1]  # Sort descending
    
    # Add boundary thresholds
    thresholds_to_test = np.concatenate([[1.0], unique_thresholds, [0.0]])
    
    best_threshold = None
    
    for threshold in thresholds_to_test:
        cannibalization_rate = _calculate_weighted_cannibalization_rate(
            y_true, y_pred_probs, sample_weights, threshold
        )
        
        if cannibalization_rate == float('inf'):
            continue
            
        # Find the highest threshold that meets or is below the target
        if cannibalization_rate <= cannibalization_target:
            if best_threshold is None or threshold > best_threshold:
                best_threshold = threshold
    
    if best_threshold is None:
        # If no threshold meets the target, return the threshold with the lowest cannibalization rate
        min_cannibalization_rate = float('inf')
        for threshold in thresholds_to_test:
            cannibalization_rate = _calculate_weighted_cannibalization_rate(
                y_true, y_pred_probs, sample_weights, threshold
            )
            
            if cannibalization_rate == float('inf'):
                continue
                
            if cannibalization_rate < min_cannibalization_rate:
                min_cannibalization_rate = cannibalization_rate
                best_threshold = threshold
        
        if best_threshold is None:
            raise ValueError(f'Unable to find any valid threshold. Target cannibalization rate: {cannibalization_target:.4f}')
    
    return best_threshold

def find_threshold_sklearn_pr_curve(results_df, cannibalization_target, min_samples=10):
    """
    Alternative threshold finding method using sklearn's precision_recall_curve directly.
    
    This method uses sklearn's precision_recall_curve function and converts the cannibalization
    problem into a precision-recall optimization problem.
    
    Args:
        results_df (pd.DataFrame): DataFrame with columns 'conversion_1_probability', 'conversion', 
                                  'record_weight', 'opportunity', 'holdout', 'vandra_shown'
        cannibalization_target (float): Target cannibalization rate (e.g., 0.15 for 15%)
        min_samples (int): Minimum number of samples required for calculation
        
    Returns:
        float: Optimal threshold that meets or is below the cannibalization target
        
    Raises:
        ValueError: If no conversions exist or insufficient data for calculation
    """
    y_true, y_pred_probs, _ = _validate_and_prepare_threshold_data(
        results_df, cannibalization_target, min_samples
    )
    
    # Use sklearn's precision_recall_curve
    # Note: We need to invert the problem since cannibalization_rate = fn/(fn+tp) = 1 - recall
    # So we want recall >= (1 - cannibalization_target)
    min_recall = 1 - cannibalization_target
    
    try:
        precision, recall, thresholds = precision_recall_curve(y_true, y_pred_probs)
        
        # Find thresholds that meet the minimum recall requirement
        valid_idx = np.where(recall[:-1] >= min_recall)[0]
        
        if len(valid_idx) == 0:
            # If no threshold meets the target, find the one with the highest recall (lowest cannibalization)
            best_idx = np.argmax(recall[:-1])
            best_threshold = thresholds[best_idx]
            logger.warning(f"No threshold satisfies cannibalization target ≤ {cannibalization_target:.4f}. "
                         f"Using threshold with best recall: {recall[best_idx]:.4f} "
                         f"(cannibalization rate: {1 - recall[best_idx]:.4f})")
        else:
            # Among valid thresholds, choose the one with highest precision (most conservative)
            # This corresponds to the highest threshold that still meets the recall requirement
            best_idx = valid_idx[-1]  # Last valid index gives highest threshold
            best_threshold = thresholds[best_idx]
            
        return float(best_threshold)
        
    except ValueError as e:
        raise ValueError(f'Error in precision_recall_curve calculation: {str(e)}')

def find_threshold_with_method(results_df, cannibalization_target, method='binary_search', **kwargs):
    """
    Unified interface for finding thresholds using different methods.
    
    Args:
        results_df (pd.DataFrame): DataFrame with required columns
        cannibalization_target (float): Target cannibalization rate
        method (str): Method to use. Options:
            - 'binary_search': Original binary search method (default)
            - 'precision_recall': Custom precision-recall implementation with weights
            - 'sklearn_pr_curve': Direct sklearn precision_recall_curve method
        **kwargs: Additional arguments passed to the specific method
        
    Returns:
        float: Optimal threshold
    """
    if method == 'binary_search':
        return find_threshold(results_df, cannibalization_target, **kwargs)
    elif method == 'precision_recall':
        return find_threshold_precision_recall(results_df, cannibalization_target, **kwargs)
    elif method == 'sklearn_pr_curve':
        return find_threshold_sklearn_pr_curve(results_df, cannibalization_target, **kwargs)
    else:
        raise ValueError(f"Unknown method: {method}. Choose from: 'binary_search', 'precision_recall', 'sklearn_pr_curve'")

def prep_perf_data(stores_to_include, stores_to_exclude, scoring_layers_to_include, scoring_layers_to_exclude, min_eval_start_time, eval_end_time, time_decay_alpha, read_db_connection):
    # Configure logging
    start_time = time.time()
    
    logger.info("=== prep_perf_data Parameters ===")
    logger.info(f"stores_to_include: {stores_to_include}")
    logger.info(f"stores_to_exclude: {stores_to_exclude}")
    logger.info(f"scoring_layers_to_include: {scoring_layers_to_include}")
    logger.info(f"scoring_layers_to_exclude: {scoring_layers_to_exclude}")
    logger.info(f"min_eval_start_time: {min_eval_start_time}")
    logger.info(f"eval_end_time: {eval_end_time}")
    logger.info(f"time_decay_alpha: {time_decay_alpha}")
    logger.info(f"read_db_connection: {read_db_connection}")
    if read_db_connection:
        logger.info(f"DB Host: {read_db_connection.engine.url.host}")
        logger.info(f"DB Database: {read_db_connection.engine.url.database}")
        logger.info(f"DB Username: {read_db_connection.engine.url.username}")
    logger.info("=" * 35)

    
    #### PULL STORES ####
    store_query = (db.session.query(store)
        .filter(store.use_in_datascience == True)
        .with_entities(store.uuid, store.show_discount, store.vandra_admin_show_discount)
    )
    
    if stores_to_include:
        store_query = store_query.filter(store.uuid.in_(stores_to_include))
    if stores_to_exclude:
        store_query = store_query.filter(~store.uuid.in_(stores_to_exclude))
    
    store_df = pd.read_sql(store_query.statement, read_db_connection if read_db_connection is not None else store_query.session.connection())
    logger.info(f"Store data pull completed in {time.time() - start_time:.2f} seconds")
    
    #### PULL SCORING_LAYERS (MODEL_VERSIONS) ####
    scoring_layer_start = time.time()
    scoring_layer_query = (db.session.query(scoring_layer)
        .filter(scoring_layer.run_predictions == True, scoring_layer.active == True)
    )
        
    if scoring_layers_to_include:
        scoring_layer_query = scoring_layer_query.filter(scoring_layer.uuid.in_(scoring_layers_to_include))
    if scoring_layers_to_exclude:
        scoring_layer_query = scoring_layer_query.filter(~scoring_layer.uuid.in_(scoring_layers_to_exclude))
    
    scoring_layer_df = pd.read_sql(scoring_layer_query.statement, read_db_connection if read_db_connection is not None else scoring_layer_query.session.connection())
    logger.info(f"Scoring layer data pull completed in {time.time() - scoring_layer_start:.2f} seconds")
    
    eval_start_time = int(max(min_eval_start_time, scoring_layer_df.time.min()))
    
    #### PULL SCORING LAYER DETERMINATIONS AND USER SESSIONS ####
    query_start = time.time()
    scoring_layer_determination_query = (db.session.query(scoring_layer_determination)
        .filter(
            scoring_layer_determination.time >= eval_start_time,
            scoring_layer_determination.time < eval_end_time,
            scoring_layer_determination.scoring_layer_id.in_(scoring_layer_df.uuid.tolist()),
            scoring_layer_determination.scoring_layer_scores['intent'].isnot(None)
        )
        .distinct(
            scoring_layer_determination.user_session_id,
            scoring_layer_determination.scoring_layer_id
        )
        .with_entities(
            scoring_layer_determination.user_session_id,
            scoring_layer_determination.scoring_layer_scores['intent'].label('conversion_1_probability'),
            scoring_layer_determination.scoring_layer_id,
            scoring_layer_determination.determination,
            user_session.uuid,
            user_session.time,
            user_session.store_id,
            user_session.conversion,
            user_session.os,
            user_session.browser,
            user_session.domain,
            session_intervention.opportunity,
            session_intervention.holdout,
            case(
                (session_intervention.status.in_([
                    INTERVENTION_STATUS_SHOWN,
                    INTERVENTION_STATUS_DISMISSED,
                    INTERVENTION_STATUS_ACTIONED
                ]), True),
                else_=False
            ).label('vandra_shown')
        )
        .join(
            user_session,
            and_(
                scoring_layer_determination.user_session_id == user_session.uuid,
                user_session.time >= eval_start_time,
                user_session.time < eval_end_time
            )
        )
        .join(
            session_intervention,
            user_session.uuid == session_intervention.user_session_id,
            isouter=False
        )
        .join(
            intervention_type,
            and_(
                intervention_type.uuid == session_intervention.intervention_type_id,
                intervention_type.name == INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
            ),
            isouter=False
        )
        .filter(
            user_session.vandra_conversion == False,
            user_session.store_id.in_(store_df.uuid.tolist())
        )
    )

    if EXCLUDE_BOTS_ETC:
        scoring_layer_determination_query = (scoring_layer_determination_query
            .filter(
                user_session.os != 'Search Bot',
                ~user_session.browser.in_(BOT_BROWSERS),
                ~(user_session.domain.contains("shopifypreview") | user_session.domain.contains("myshopify"))
            )
        )
    
    final_df = pd.read_sql(scoring_layer_determination_query.statement, read_db_connection if read_db_connection is not None else scoring_layer_determination_query.session.connection())
    logger.info(f"Data pull completed in {time.time() - query_start:.2f} seconds")

    #### FINAL PROCESSING ####
    merge_start = time.time()
    final_df = (final_df
        .assign(decided_low_intent = lambda df_: ((df_['determination'] == 'low-intent') | df_['determination'].isnull()),
                record_weight = lambda df_: calc_decayed_weight(eval_end_time - df_['time'], time_decay_alpha=time_decay_alpha))
    )
    logger.info(f"Final processing completed in {time.time() - merge_start:.2f} seconds")
    logger.info(f"Total execution time: {time.time() - start_time:.2f} seconds")
    return (store_df,
            scoring_layer_df,
            final_df
    )

def eval_models(perf_data, store_df, scoring_layer_df, write_to_file, read_db_connection):
    store_url_query = (
        db.session.query(store_url.store_id, store_url.url)
        .filter(
            store_url.default == True,
            store_url.store_id.in_(store_df.uuid.tolist())
        )
        .with_entities(store_url.store_id, store_url.url)
    )
    
    store_url_df = pd.read_sql(store_url_query.statement, read_db_connection if read_db_connection is not None else store_url_query.session.connection())
    
    total_session_count = (perf_data
        .groupby(['scoring_layer_id', 'store_id'])
        .agg({'uuid': 'nunique'})
        .rename(columns={'uuid': 'total_session_count'})
    )
    
    cannibalization_count = (perf_data
        .query(f'{CANNIBALIZATION_CONSIDERATION_FILTER_STR} and conversion == True and decided_low_intent == True')
        .groupby(['scoring_layer_id', 'store_id'])
        .agg({'uuid': 'nunique'})
        .rename(columns={'uuid': 'cannibalization_count'})
    )
    
    offer_rates_holdout_only = (
        perf_data
        [['scoring_layer_id', 'store_id', 'holdout', 'conversion', 'decided_low_intent']]
        .query('conversion == False and holdout == True')
        .groupby(['scoring_layer_id', 'store_id'])
        .agg({'decided_low_intent': ['mean', 'sum']})
        .pipe(flatten_cols)
        .rename(columns={'decided_low_intent_mean': 'discount_offer_rate_holdout_only',
                        'decided_low_intent_sum': 'discount_offer_count_holdout_only'})
    )
    
    #Note: All else constant, deciding not to show a discount for an opportunity, non-holdout session means they're less likely to convert and therefore more likely to be included in the offer_rate average.
    #Note: All else constant, deciding to show a discount for an opportunity, non-holdout session means they're more likely to convert and therefore less likely to be included in the offer_rate average.
    offer_rates = (
        perf_data
        [['scoring_layer_id', 'store_id', 'conversion', 'decided_low_intent']]
        .query('conversion == False')
        .groupby(['scoring_layer_id', 'store_id'])
        .agg({'decided_low_intent': ['mean', 'sum']})
        .pipe(flatten_cols)
        .rename(columns={'decided_low_intent_mean': 'discount_offer_rate',
                        'decided_low_intent_sum': 'discount_offer_count'})
    )
    
    cannibalization_rates = (
        perf_data
        [['scoring_layer_id', 'store_id', 'opportunity', 'vandra_shown', 'holdout', 'conversion', 'decided_low_intent']]
        .query(f'{CANNIBALIZATION_CONSIDERATION_FILTER_STR} and conversion == True')
        .groupby(['scoring_layer_id', 'store_id'])
        .agg({'decided_low_intent': 'mean'}).rename(columns={'decided_low_intent': 'cannibalization_rate'})
    )
    
    store_df_w_url = (store_df
                    .merge(store_url_df, left_on='uuid', right_on='store_id', how='left')
                    .drop(columns='store_id')
                    .rename(columns={'url': 'store_url',
                        'show_discount': 'store_show_discount',
                        'vandra_admin_show_discount': 'store_vandra_admin_show_discount'})
                    )
    
    model_eval = (
        pd.concat([store_df_w_url] * len(scoring_layer_df.uuid.tolist()), keys=scoring_layer_df.uuid.tolist())
        .droplevel(level=-1)
        .set_index('uuid', append=True)
        .rename_axis(['scoring_layer_id', 'store_id'])
        .join(total_session_count, how='left')
        .join(cannibalization_count, how='left')
        .join(offer_rates_holdout_only, how='left')
        .join(offer_rates, how='left')
        .join(cannibalization_rates, how='left')
        .fillna({'cannibalization_count': 0})
        .sort_values('total_session_count', ascending=False)
        [['store_url', 'cannibalization_rate', 'discount_offer_rate', 'discount_offer_rate_holdout_only', 'total_session_count', 'cannibalization_count', 'discount_offer_count', 'discount_offer_count_holdout_only', 'store_show_discount', 'store_vandra_admin_show_discount']]
        .sort_values(['scoring_layer_id', 'total_session_count'], ascending=False)
    )
    
    model_eval_summary_by_model = (
        model_eval
        .query('~total_session_count.isna()')
        .assign(cann_consideration_pop_size = lambda df_: df_['cannibalization_count'] / df_['cannibalization_rate'],
                offer_consideration_pop_size = lambda df_: df_['discount_offer_count'] / df_['discount_offer_rate'])
        .groupby('scoring_layer_id')
        .agg({'cannibalization_count': 'sum',
              'cann_consideration_pop_size': 'sum',
              'discount_offer_count': 'sum',
              'offer_consideration_pop_size': 'sum',
              'store_url': 'nunique',
              'total_session_count': 'sum'})
        .assign(cannibalization_rate_wtd_avg=lambda x: x['cannibalization_count'] / x['cann_consideration_pop_size'],
                discount_offer_rate_wtd_avg=lambda x: x['discount_offer_count'] / x['offer_consideration_pop_size'])
        .rename(columns={'store_url': 'store_url_nunique'})
        .sort_values('cannibalization_rate_wtd_avg', ascending=True)
        .merge(scoring_layer_df[['uuid', 'store_specific']], left_index=True, right_on='uuid', how='left')
        .rename(columns={'uuid': 'scoring_layer_id'})
        .pipe(lambda df_: df_.reindex(columns=['scoring_layer_id', 'store_url_nunique', 'cannibalization_rate_wtd_avg', 'discount_offer_rate_wtd_avg'] + [col for col in df_.columns if col not in ['scoring_layer_id', 'store_url_nunique', 'cannibalization_rate_wtd_avg', 'discount_offer_rate_wtd_avg']]))
    )
    
    if write_to_file:
        model_eval.to_pickle('most_recent_model_eval.pkl')
        model_eval.to_csv('most_recent_model_eval.csv')
        model_eval_summary_by_model.to_pickle('most_recent_model_eval_summary_by_model.pkl')
        model_eval_summary_by_model.to_csv('most_recent_model_eval_summary_by_model.csv')
    
    return model_eval

def calibrate_models(perf_data, 
                     store_df, 
                     scoring_layer_df, 
                     cannibalization_target, 
                     prod_to_valid_weight, 
                     write_to_file, 
                     write_to_db, 
                     active,
                     threshold_type,
                     include_cv_thresholds,
                     read_db_connection):
    import time
    import logging
    
    
    
    start_time = time.time()
    logger.info("Starting model calibration")
    
    #### PULL ORIGINAL CROSS-VALIDATION-BASED THRESHOLDS ####
    if include_cv_thresholds:
        raise NotImplementedError('cv thresholds incorporation not yet implemented for new data model')
    
    thresholds_df = pd.DataFrame()
    
    # Track time for threshold calculations
    threshold_calc_start = time.time()
    processed_combinations = 0
    for scoring_layer_id, store_id in itertools.product(list(scoring_layer_df.uuid), list(store_df.uuid)):
        if include_cv_thresholds:
            cv_threshold_ms = cv_threshold_df.query('store_id == @store_id and scoring_layer_id == @scoring_layer_id')
            if len(cv_threshold_ms) == 0:
                if scoring_layer_df.query('uuid == @scoring_layer_id').store_specific_model.values[0]: # don't create thresholds for store-specific models that don't have a threshold -- currently, that would cause them to make decisions for additional stores
                    continue
                total_cv_count = 0
                cv_threshold = 0.01 # arbitrary value, will be multiplied by 0
                cv_offer_rate_expectation = 0.5 # arbitrary value, will be multiplied by 0
            else:
                total_cv_count = 7404#cv_threshold_ms.threshold_based_on_count.values[0]
                cv_threshold = 0.011#cv_threshold_ms.low_med_threshold.values[0] if threshold_type == 'low_med_threshold' else cv_threshold_ms.med_high_threshold.values[0] if threshold_type == 'med_high_threshold' else None
        
        #### CALCULATE NEW THRESHOLDS ####
        perf_data_ms = perf_data.query('store_id == @store_id and scoring_layer_id == @scoring_layer_id')
        total_prod_count = len(perf_data_ms.query(CANNIBALIZATION_CONSIDERATION_FILTER_STR))
        
        if total_prod_count < 300:
            continue
        
        logger.info(f"Setting threshold for {scoring_layer_id} and {store_id}...")
        
        try:
            prod_only_threshold = find_threshold_with_method(perf_data_ms, cannibalization_target=cannibalization_target)
        except ValueError: #if there are no conversions for the given store to calculate a cannibalization rate with, then continue
            continue
        
        if include_cv_thresholds:
            new_blended_threshold = (cv_threshold * total_cv_count + prod_only_threshold * total_prod_count * prod_to_valid_weight) / (total_cv_count + total_prod_count * prod_to_valid_weight)
        else:
            new_blended_threshold = prod_only_threshold
        
        prod_only_offer_rate_expectation = calc_time_wtd_metric(perf_data_ms, new_blended_threshold, metric='discount_offer_rate')
        
        if include_cv_thresholds:
            new_blended_offer_rate_expectation = (cv_offer_rate_expectation * total_cv_count + prod_only_offer_rate_expectation * total_prod_count * prod_to_valid_weight) / (total_cv_count + total_prod_count * prod_to_valid_weight)
        else:
            new_blended_offer_rate_expectation = prod_only_offer_rate_expectation
              
        thresholds_df = pd.concat(
            [thresholds_df,
            pd.DataFrame({ # time will be assigned when writing to db
                'uuid': uuid.uuid4(), 
                'scoring_layer_id': scoring_layer_id,
                'store_id': store_id,
                'active':  active,
                'threshold_type' : threshold_type,
                'new_blended_threshold': new_blended_threshold,
                'cannibalization_rate_target': cannibalization_target,
                'expected_discount_offer_rate': new_blended_offer_rate_expectation,
                'expected_cannibalization_rate_prod': calc_time_wtd_metric(perf_data_ms, new_blended_threshold, metric='cannibalization_rate'), # based on prod only, this is the expected cannibalization rate from the new blended threshold
                'expected_discount_offer_rate_prod': prod_only_offer_rate_expectation # based on prod only, this is the expected discount offer rate from the new blended threshold
            },
            index = [0])],
            ignore_index=True
        )
        processed_combinations += 1
    logger.info(f"{processed_combinations} combinations processed in {time.time() - threshold_calc_start:.2f} seconds")

    thresholds_df = (thresholds_df
        .sort_values('scoring_layer_id', ascending=False)
        .reset_index(drop=True)
    )
    
    #### WRITE TO FILE AND/OR DB ####
    if write_to_file:
        file_write_start = time.time()
        thresholds_df.to_pickle('most_recent_calculated_thresholds.pkl')
        thresholds_df.to_csv('most_recent_calculated_thresholds.csv')
        logger.info(f"File writing completed in {time.time() - file_write_start:.2f} seconds")
    
    if write_to_db:
        db_write_start = time.time()
        def update_row_threshold(row, threshold_type):
            if 'intent_only' in row["scoring_artifact_thresholds"]:
                row["scoring_artifact_thresholds"]["intent_only"][threshold_type] = row["new_blended_threshold"]
            elif 'intent_and_uplift' in row["scoring_artifact_thresholds"]:
                row["scoring_artifact_thresholds"]["intent_and_uplift"][threshold_type] = row["new_blended_threshold"]
            else:
                raise ValueError(f"scoring_artifact_thresholds does not contain either 'intent_only' or 'intent_and_uplift'")
            return row
        
        def update_df_threshold(df, threshold_type):
            return df.apply(lambda row: update_row_threshold(row, threshold_type), axis=1)
                   
        scoring_layer_store_combos_affected = list(thresholds_df[['scoring_layer_id', 'store_id']].itertuples(index=False, name=None))
        
        # Pull current thresholds to update, since we need to preserve the thresholds of other threshold_types
        current_thresholds_start = time.time()
        current_scoring_layer_thresholds_subquery = (db.session.query(scoring_layer_thresholds)
            .filter(
                tuple_(scoring_layer_thresholds.scoring_layer_id, scoring_layer_thresholds.store_id).in_(scoring_layer_store_combos_affected),
                (scoring_layer_thresholds.active)
            )
            .add_columns(
                over(
                    func.row_number(),
                    partition_by=[scoring_layer_thresholds.scoring_layer_id, scoring_layer_thresholds.store_id],
                    order_by=desc(scoring_layer_thresholds.time),
                ).label("time_rank")
            )
            .subquery()
        )

        current_scoring_layer_thresholds_query = (db.session.query(current_scoring_layer_thresholds_subquery)
            .filter(current_scoring_layer_thresholds_subquery.c.time_rank == 1)
            .with_entities(
                current_scoring_layer_thresholds_subquery.c.uuid,
                current_scoring_layer_thresholds_subquery.c.store_id,
                current_scoring_layer_thresholds_subquery.c.scoring_layer_id,
                current_scoring_layer_thresholds_subquery.c.scoring_artifact_thresholds
            )
        )

        current_scoring_layer_thresholds_df = pd.read_sql(current_scoring_layer_thresholds_query.statement, read_db_connection if read_db_connection is not None else current_scoring_layer_thresholds_query.session.connection())
        logger.info(f"Current thresholds query completed in {time.time() - current_thresholds_start:.2f} seconds")
        
        db_update_start = time.time()
        try:
            connection = db.session.get_bind()
            
            (thresholds_df
                [['uuid', 'scoring_layer_id', 'store_id', 'new_blended_threshold', 'active']]
                .merge(current_scoring_layer_thresholds_df[['scoring_layer_id', 'store_id', 'scoring_artifact_thresholds']], on=['scoring_layer_id', 'store_id'], how='left')
                .pipe(update_df_threshold, threshold_type)
                .drop(columns=['new_blended_threshold'])
                .assign(time = int(datetime.datetime.now().timestamp()))
                .to_sql('scoring_layer_thresholds', con=connection, if_exists='append', index=False, dtype={"scoring_artifact_thresholds": JSON})
            )
            
            if active:
                # Update previous active thresholds to inactive
                db.session.query(scoring_layer_thresholds).filter(scoring_layer_thresholds.uuid.in_(current_scoring_layer_thresholds_df.uuid.tolist())).update({"active": False}, synchronize_session=False)
            
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e
        logger.info(f"Database update completed in {time.time() - db_update_start:.2f} seconds")
        logger.info(f"Total database operations completed in {time.time() - db_write_start:.2f} seconds")
    
    logger.info(f"Total calibration completed in {time.time() - start_time:.2f} seconds")
    return thresholds_df

def calibrate_models_optimized(perf_data, 
                              store_df, 
                              scoring_layer_df, 
                              cannibalization_target, 
                              prod_to_valid_weight, 
                              write_to_file, 
                              write_to_db, 
                              active,
                              threshold_type,
                              include_cv_thresholds,
                              read_db_connection):
    """
    Optimized version of calibrate_models using vectorized operations.
    
    Key optimizations:
    - Pre-filters data once instead of repeated queries
    - Uses boolean indexing instead of pandas.query()
    - Vectorized sample counting with groupby
    - Eliminates expensive DataFrame concatenations
    - Early filtering of invalid combinations
    """
    import time
    import logging
    
    start_time = time.time()
    logger.info("Starting OPTIMIZED model calibration")
    
    #### PULL ORIGINAL CROSS-VALIDATION-BASED THRESHOLDS ####
    if include_cv_thresholds:
        raise NotImplementedError('cv thresholds incorporation not yet implemented for new data model')
    
    # Track time for threshold calculations
    threshold_calc_start = time.time()
    
    # VECTORIZED APPROACH: Pre-filter and group data once
    logger.info("Pre-processing data for vectorized threshold calculation...")
    
    # Filter data for cannibalization consideration and add sample counts
    cannibalization_data = perf_data.query(CANNIBALIZATION_CONSIDERATION_FILTER_STR)
    
    # Get sample counts per store/scoring_layer combination
    sample_counts = (cannibalization_data
                    .groupby(['store_id', 'scoring_layer_id'])
                    .size()
                    .reset_index(name='total_prod_count'))
    
    # Filter combinations with sufficient data (>=300 samples)
    valid_combinations = sample_counts[sample_counts['total_prod_count'] >= 300]
    
    if len(valid_combinations) == 0:
        logger.warning("No store/scoring_layer combinations have sufficient data (>=300 samples)")
        return pd.DataFrame()
    
    logger.info(f"Processing {len(valid_combinations)} valid combinations (filtered from {len(sample_counts)} total)")
    
    # Process each valid combination efficiently
    threshold_results = []
    
    def process_combination(store_id, scoring_layer_id):
        """Process a single store/scoring_layer combination"""
        try:
            # Use boolean indexing for fast filtering
            cann_mask = (cannibalization_data['store_id'] == store_id) & (cannibalization_data['scoring_layer_id'] == scoring_layer_id)
            cann_subset = cannibalization_data[cann_mask]
            
            if len(cann_subset) < 300:
                return None
                
            # Calculate threshold using the subset
            prod_only_threshold = find_threshold_with_method(
                cann_subset, 
                cannibalization_target=cannibalization_target, 
                method='binary_search'
            )
            
            # Get the full perf_data subset for offer rate calculation
            perf_mask = (perf_data['store_id'] == store_id) & (perf_data['scoring_layer_id'] == scoring_layer_id)
            perf_subset = perf_data[perf_mask]
            
            if include_cv_thresholds:
                new_blended_threshold = prod_only_threshold  # Simplified since cv not implemented
            else:
                new_blended_threshold = prod_only_threshold
            
            # Calculate offer rate expectation
            prod_only_offer_rate_expectation = calc_time_wtd_metric(
                perf_subset, new_blended_threshold, metric='discount_offer_rate'
            )
            
            if include_cv_thresholds:
                new_blended_offer_rate_expectation = prod_only_offer_rate_expectation
            else:
                new_blended_offer_rate_expectation = prod_only_offer_rate_expectation
            
            return {
                'uuid': uuid.uuid4(),
                'scoring_layer_id': scoring_layer_id,
                'store_id': store_id,
                'active': active,
                'threshold_type': threshold_type,
                'new_blended_threshold': new_blended_threshold,
                'cannibalization_rate_target': cannibalization_target,
                'expected_discount_offer_rate': new_blended_offer_rate_expectation,
                'expected_cannibalization_rate_prod': calc_time_wtd_metric(
                    perf_subset, new_blended_threshold, metric='cannibalization_rate'
                ),
                'expected_discount_offer_rate_prod': prod_only_offer_rate_expectation
            }
            
        except ValueError:
            # No conversions for this combination
            return None
        except Exception as e:
            logger.warning(f"Error processing {scoring_layer_id[:8]}.../{store_id[:8]}...: {str(e)}")
            return None
    
    # Create list of combinations to process
    combinations_to_process = [
        (row['store_id'], row['scoring_layer_id']) 
        for _, row in valid_combinations.iterrows()
    ]
    
    logger.info(f"Processing {len(combinations_to_process)} combinations...")
    
    # Process combinations efficiently
    for i, (store_id, scoring_layer_id) in enumerate(combinations_to_process):
        if i % 10 == 0:
            logger.info(f"Processing combination {i+1}/{len(combinations_to_process)}")
        
        result = process_combination(store_id, scoring_layer_id)
        if result is not None:
            threshold_results.append(result)
    
    # Convert results to DataFrame (single operation instead of repeated concat)
    if threshold_results:
        thresholds_df = pd.DataFrame(threshold_results)
        logger.info(f"Successfully processed {len(threshold_results)} combinations in {time.time() - threshold_calc_start:.2f} seconds")
    else:
        logger.warning("No valid thresholds calculated")
        thresholds_df = pd.DataFrame()

    thresholds_df = (thresholds_df
        .sort_values('scoring_layer_id', ascending=False)
        .reset_index(drop=True)
    )
    
    #### WRITE TO FILE AND/OR DB ####
    if write_to_file:
        file_write_start = time.time()
        thresholds_df.to_pickle('most_recent_calculated_thresholds_optimized.pkl')
        thresholds_df.to_csv('most_recent_calculated_thresholds_optimized.csv')
        logger.info(f"File writing completed in {time.time() - file_write_start:.2f} seconds")
    
    if write_to_db:
        db_write_start = time.time()
        def update_row_threshold(row, threshold_type):
            if 'intent_only' in row["scoring_artifact_thresholds"]:
                row["scoring_artifact_thresholds"]["intent_only"][threshold_type] = row["new_blended_threshold"]
            elif 'intent_and_uplift' in row["scoring_artifact_thresholds"]:
                row["scoring_artifact_thresholds"]["intent_and_uplift"][threshold_type] = row["new_blended_threshold"]
            else:
                raise ValueError(f"scoring_artifact_thresholds does not contain either 'intent_only' or 'intent_and_uplift'")
            return row
        
        def update_df_threshold(df, threshold_type):
            return df.apply(lambda row: update_row_threshold(row, threshold_type), axis=1)
                   
        scoring_layer_store_combos_affected = list(thresholds_df[['scoring_layer_id', 'store_id']].itertuples(index=False, name=None))
        
        # Pull current thresholds to update, since we need to preserve the thresholds of other threshold_types
        current_thresholds_start = time.time()
        current_scoring_layer_thresholds_subquery = (db.session.query(scoring_layer_thresholds)
            .filter(
                tuple_(scoring_layer_thresholds.scoring_layer_id, scoring_layer_thresholds.store_id).in_(scoring_layer_store_combos_affected),
                (scoring_layer_thresholds.active)
            )
            .add_columns(
                over(
                    func.row_number(),
                    partition_by=[scoring_layer_thresholds.scoring_layer_id, scoring_layer_thresholds.store_id],
                    order_by=desc(scoring_layer_thresholds.time),
                ).label("time_rank")
            )
            .subquery()
        )

        current_scoring_layer_thresholds_query = (db.session.query(current_scoring_layer_thresholds_subquery)
            .filter(current_scoring_layer_thresholds_subquery.c.time_rank == 1)
            .with_entities(
                current_scoring_layer_thresholds_subquery.c.uuid,
                current_scoring_layer_thresholds_subquery.c.store_id,
                current_scoring_layer_thresholds_subquery.c.scoring_layer_id,
                current_scoring_layer_thresholds_subquery.c.scoring_artifact_thresholds
            )
        )

        current_scoring_layer_thresholds_df = pd.read_sql(current_scoring_layer_thresholds_query.statement, read_db_connection if read_db_connection is not None else current_scoring_layer_thresholds_query.session.connection())
        logger.info(f"Current thresholds query completed in {time.time() - current_thresholds_start:.2f} seconds")
        
        db_update_start = time.time()
        try:
            connection = db.session.get_bind()
            
            (thresholds_df
                [['uuid', 'scoring_layer_id', 'store_id', 'new_blended_threshold', 'active']]
                .merge(current_scoring_layer_thresholds_df[['scoring_layer_id', 'store_id', 'scoring_artifact_thresholds']], on=['scoring_layer_id', 'store_id'], how='left')
                .pipe(update_df_threshold, threshold_type)
                .drop(columns=['new_blended_threshold'])
                .assign(time = int(datetime.datetime.now().timestamp()))
                .to_sql('scoring_layer_thresholds', con=connection, if_exists='append', index=False, dtype={"scoring_artifact_thresholds": JSON})
            )
            
            if active:
                # Update previous active thresholds to inactive
                db.session.query(scoring_layer_thresholds).filter(scoring_layer_thresholds.uuid.in_(current_scoring_layer_thresholds_df.uuid.tolist())).update({"active": False}, synchronize_session=False)
            
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e
        logger.info(f"Database update completed in {time.time() - db_update_start:.2f} seconds")
        logger.info(f"Total database operations completed in {time.time() - db_write_start:.2f} seconds")
    
    logger.info(f"Total OPTIMIZED calibration completed in {time.time() - start_time:.2f} seconds")
    return thresholds_df

def calibrate_models_lambda():
    with app.app_context():
        # Default values for lambda execution
        now_time = int(datetime.datetime.now().timestamp())
        min_eval_start_time = now_time - 604800*4
        eval_end_time = now_time
        read_db_connection = db.engines['read_replica'].execution_options(postgresql_readonly=True).connect()
        #scoring layers to include are all live decisioning models
        scoring_layer_query = (db.session.query(scoring_layer)
            .filter(scoring_layer.run_predictions == True, scoring_layer.active == True)
        )
            
        #further filter for live_decisioning == True
        scoring_layer_query = scoring_layer_query.filter(scoring_layer.live_decisioning == True)
        scoring_layers_to_include = scoring_layer_query.all()
        scoring_layer_ids = [scoring_layer.uuid for scoring_layer in scoring_layers_to_include]
        
        
        # Run the calibration
        store_df, scoring_layer_df, pulled_perf_data = prep_perf_data(
            None,
            None,
            scoring_layer_ids,
            None,
            min_eval_start_time,
            eval_end_time,
            0.0000006,
            read_db_connection=read_db_connection
        )
        
        for threshold_type in [('low_med_threshold',0.32), ('med_high_threshold',0.7)]:
            calibrate_models_optimized(
                pulled_perf_data,
                store_df,
                scoring_layer_df,
                threshold_type[1],
                35,
                False,
                True,
                True,
                threshold_type[0],
                False,
                read_db_connection=read_db_connection
           )

def lambda_handler(event, context):
    """AWS Lambda handler for model calibration"""
    calibrate_models_lambda()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description='Evaluate and/or calibrate models'
    )
    
    parser.add_argument(
        '--action',
        choices=['eval_models', 'calibrate_models', 'calibrate_models_optimized', 'calibrate_models_lambda'],
        nargs='+',
        required=True,
        help='Actions to perform: eval_models, calibrate_models, calibrate_models_optimized, or calibrate_models_lambda'
    )

    parser.add_argument(
        '--threshold-type',
        choices=['low_med_threshold', 'med_high_threshold','all'],
        required=False,
        help='Which type of threshold are we setting',
        dest="threshold_type"
    )
    
    #args for prep_perf_data():
    parser.add_argument(
        "--stores-to-include",
        help="Include only these stores (None for all)",
        nargs='+',
        default=None,
        action="store",
        dest='stores_to_include'
    )
    
    parser.add_argument(
        "--live-layers-only",
        help="Only include live layers",
        default=False,
        action="store_true",
        dest="live_layers_only"
    )

    parser.add_argument(
        "--scoring-layers-to-include",
        help="Include only these scoring layers (None for all in decisioning)",
        nargs='+',
        default=None,
        action="store",
        dest='scoring_layers_to_include'
    )

    parser.add_argument(
        "--stores-to-exclude",
        help="Also exclude these stores (None for no additional exclusions). If both stores-to-include and stores-to-exclude include a given store, it will be excluded.",
        nargs='+',
        default=None,
        action="store",
        dest='stores_to_exclude'
    )

    parser.add_argument(
        "--scoring-layers-to-exclude",
        help="Also exclude these scoring layers (None for no additional exclusions). If both scoring-layers-to-include and scoring-layers-to-exclude include a given model, it will be excluded.",
        nargs='+',
        default=None,
        action="store",
        dest='scoring_layers_to_exclude'
    )
     
    parser.add_argument(
        "--min-eval-start-time",
        help="eval_start_time will be the greater of this and the earliest relevant scoring_layer.time. eval_start_time is the int time (seconds since epoch) to end the evaluation at.",
        default=None,
        action="store",
        dest="min_eval_start_time",
        type=int
    )
    
    parser.add_argument(
        "--eval-end-time",
        help="Int time (seconds since epoch) to end the evaluation at",
        default=None,
        action="store",
        dest="eval_end_time",
        type=int
    )
    
    parser.add_argument(
        "--time-decay-alpha",
        help="Time decay alpha for weighting record importances when calculating thresholds",
        default=0.0000006,
        action="store",
        dest="time_decay_alpha",
        type=float
    )
    
    # args for calibrate_models():
    parser.add_argument(
        "--cannibalization-target",
        help="Model-perspective cannibalization target (of those reaching decision)",
        default=None,
        action="store",
        dest="cannibalization_target",
        type=float
    )
    
    parser.add_argument(
        "--prod-to-valid-weight",
        help="All else equal, how many times more heavily should a production record be weighted than a validation record?",
        default=14,
        action="store",
        dest="prod_to_valid_weight",
        type=float 
    )
    
    parser.add_argument(
        "--include-cv-thresholds",
        help="Include the initial cross-validation-based thresholds in the calibration calculations",
        default=True,
        action=argparse.BooleanOptionalAction,
        dest="include_cv_thresholds"
    )
    
    parser.add_argument(
        "--write-to-file",
        help="Write results to file(s).",
        default=True,
        action="store_true",
        dest="write_to_file"
    )

    parser.add_argument(
        "--active",
        help="Set thresholds as active.",
        default=True,
        action="store_true",
        dest="active"
    )
    
    parser.add_argument(
        "--write-to-db",
        help="Write to database.",
        default=False,
        action="store_true",
        dest="write_to_db"
    )
 
    args = parser.parse_args()
    
    RUN_START_TIME = int(datetime.datetime.now().timestamp())
    if 'calibrate_models_lambda' in args.action:
            calibrate_models_lambda()
    else:
        if ('calibrate_models' in args.action or 'calibrate_models_optimized' in args.action) and args.cannibalization_target is None:
            raise ValueError("cannibalization_target is required when calibrating models")
        
        if ('calibrate_models' in args.action or 'calibrate_models_optimized' in args.action) and args.threshold_type is None:
            raise ValueError("threshold-type is required when calibrating models")
        
        if args.cannibalization_target is not None and not 0 <= args.cannibalization_target <= 1:
            raise ValueError(f"arg `cannibalization_target` must be between 0 and 1, inclusive, but got {args.cannibalization_target}")     
        
        if args.min_eval_start_time is None:
            args.min_eval_start_time = RUN_START_TIME - 604800*4 # 4 weeks ago
        elif args.min_eval_start_time >= RUN_START_TIME:
            raise ValueError(f"arg `min_eval_start_time` must be in the past, but got {args.min_eval_start_time}")
            
        if args.eval_end_time is None:
            args.eval_end_time = RUN_START_TIME
        elif args.eval_end_time > RUN_START_TIME:
            raise ValueError(f"arg `eval_end_time` must not be in the future, but got {args.eval_end_time}")
        
        if args.min_eval_start_time >= args.eval_end_time:
            raise ValueError(f"arg `min_eval_start_time` must be before arg `eval_end_time`, but got {args.min_eval_start_time} and {args.eval_end_time} respectively")
        
        with app.app_context():
            scoring_layer_ids = []
            read_db_connection = db.engines['read_replica'].execution_options(postgresql_readonly=True).connect()
            if args.live_layers_only or args.scoring_layers_to_include:
                scoring_layer_query = (db.session.query(scoring_layer)
                    .filter(scoring_layer.run_predictions == True, scoring_layer.active == True)
                )
                    
                if args.scoring_layers_to_include:
                    scoring_layer_query = scoring_layer_query.filter(scoring_layer.uuid.in_(args.scoring_layers_to_include))
                if args.live_layers_only:
                    #further filter for live_decisioning == True
                    scoring_layer_query = scoring_layer_query.filter(scoring_layer.live_decisioning == True)
                scoring_layers_to_include = scoring_layer_query.all()
                scoring_layer_ids = [scoring_layer.uuid for scoring_layer in scoring_layers_to_include]
            store_df, scoring_layer_df, pulled_perf_data = prep_perf_data(args.stores_to_include,
                                                                        args.stores_to_exclude,
                                                                        scoring_layer_ids,
                                                                        args.scoring_layers_to_exclude,
                                                                        args.min_eval_start_time,
                                                                        args.eval_end_time,
                                                                        args.time_decay_alpha,
                                                                        read_db_connection)
            
            if 'eval_models' in args.action:
                eval_models(pulled_perf_data,
                            store_df,
                            scoring_layer_df,
                            args.write_to_file,
                            read_db_connection)
        
            elif 'calibrate_models' in args.action:
                if args.threshold_type == 'all':
                    threshold_types = ['low_med_threshold', 'med_high_threshold']
                else:
                    threshold_types = [args.threshold_type]

                for threshold_type in threshold_types:
                    calibrate_models(pulled_perf_data,
                                    store_df,
                                    scoring_layer_df,
                                    args.cannibalization_target,
                                    args.prod_to_valid_weight,
                                    args.write_to_file,
                                    args.write_to_db,
                                    args.active,
                                    threshold_type,
                                    args.include_cv_thresholds,
                                    read_db_connection)
            
            if 'calibrate_models_optimized' in args.action:
                if args.threshold_type == 'all':
                    threshold_types = ['low_med_threshold', 'med_high_threshold']
                else:
                    threshold_types = [args.threshold_type]

                for threshold_type in threshold_types:
                    calibrate_models_optimized(pulled_perf_data,
                                              store_df,
                                              scoring_layer_df,
                                              args.cannibalization_target,
                                              args.prod_to_valid_weight,
                                              args.write_to_file,
                                              args.write_to_db,
                                              args.active,
                                              threshold_type,
                                              args.include_cv_thresholds,
                                              read_db_connection)

    print("--- %s seconds ---" % (int(datetime.datetime.now().timestamp()) - RUN_START_TIME))