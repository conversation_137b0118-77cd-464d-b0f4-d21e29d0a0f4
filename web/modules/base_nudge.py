import json
import random
import uuid
import time
from flask import request, jsonify

from sqlalchemy import text

from db import user_session, db
from modules.intervention_orchestration import (
    assign_session_intervention,
    capture_session_intervention_event,
    get_store_intervention_association,
    update_session_intervention_holdout,
    update_session_intervention_metadata,
    update_session_intervention_opportunity,
    update_session_intervention_status,
    get_session_intervention_record_if_exists,
    create_store_intervention_association,
)

from utils import get_generic_logger
from constants import (
    CART_ABANDONMENT_DEFAULT_HOLDOUT_PERC,
    INTERVENTION_TYPE_CART_ABANDONMENT_IN_SESSION,
    INTERVENTION_TYPE_CART_ABANDONMENT_RETURNING,
    INTERVENTION_TYPE_PICK_UP_WHERE_YOU_LEFT_OFF,
    INTERVENTION_TYPE_NAVIGATIONAL_NUDGE,
    INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT,
    INTERVENTION_TYPE_SAVINGS_NUDGE,
    OPPORTUNITY_LABEL_CART_ABANDONMENT_ELIGIBLE,
    OPPORTUNITY_LABEL_PICK_UP_WHERE_YOU_LEFT_OFF_ELIGIBLE,
    OPPORTUNITY_LABEL_NAVIGATIONAL_NUDGE_ELIGIBLE,
    OPPORTUNITY_LABEL_SAVINGS_NUDGE_ELIGIBLE,
    PICK_UP_WHERE_YOU_LEFT_OFF_DEFAULT_HOLDOUT_PERC,
    NAVIGATIONAL_NUDGE_DEFAULT_HOLDOUT_PERC,
    SAVINGS_NUDGE_DEFAULT_HOLDOUT_PERC,
    VALID_INTERVENTION_TYPES,
)
from database_models import store_intervention_association_assets, store_intervention_association, intervention_type

logger = get_generic_logger(__name__)


def get_store_intervention_types():
    session_cookie = request.args.get("session_cookie")
    user_session_record = user_session.query.filter_by(
        session_cookie=session_cookie
    ).first()

    if user_session_record is None:
        return (
            jsonify(
                {
                    "error": "Session not found",
                    "message": "Could not find user session record",
                }
            ),
            404,
        )

    association_types = []
    if user_session_record.parent.store_intervention_associations:
        association_types = [
            association.intervention_type.name
            for association in user_session_record.parent.store_intervention_associations
            if association.active
        ]

    return jsonify({"data": {"intervention_types": association_types}})


def get_store_intervention_parameters():
    session_cookie = request.args.get("session_cookie")
    intervention_type_name = request.args.get("intervention_type_name")

    user_session_record = user_session.query.filter_by(
        session_cookie=session_cookie
    ).first()

    if user_session_record is None:
        return (
            jsonify(
                {
                    "error": "Session not found",
                    "message": "Could not find user session record",
                }
            ),
            404,
        )

    association_record = get_store_intervention_association(
        user_session_record.parent, intervention_type_name
    )

    if association_record is None:
        return (
            jsonify(
                {
                    "error": "Association not found",
                    "message": "Could not find store intervention association record",
                }
            ),
            404,
        )

    return jsonify({"data": {"parameters": association_record.parameters or {}}})


def update_parameters_handler():
    """
    Handler for updating session-level intervention metadata.
    This is used by the client-side JavaScript to update specific metadata for a user session.
    """
    session_cookie = request.form.get("session_cookie")
    intervention_type_name = request.form.get("intervention_type_name")
    metadata = request.form.get("metadata")

    user_session_record = user_session.query.filter_by(
        session_cookie=session_cookie
    ).first()
    if user_session_record is None:
        return (
            jsonify(
                {
                    "error": "Session not found",
                    "message": "Could not find user session record",
                }
            ),
            404,
        )

    try:
        obj = request.form.to_dict()
        metadata_str = obj.get("metadata", "{}")
        metadata = json.loads(metadata_str)
    except Exception as e:
        metadata = {}
        logger.error(f"Error parsing the metadata for updating parameters: {e}")

    update_session_intervention_metadata(
        user_session_record, intervention_type_name, metadata
    )

    return jsonify({"success": True})


def get_default_holdout_percentage(intervention_type):
    if intervention_type == INTERVENTION_TYPE_CART_ABANDONMENT_RETURNING:
        return CART_ABANDONMENT_DEFAULT_HOLDOUT_PERC
    if intervention_type == INTERVENTION_TYPE_CART_ABANDONMENT_IN_SESSION:
        return CART_ABANDONMENT_DEFAULT_HOLDOUT_PERC
    if intervention_type == INTERVENTION_TYPE_PICK_UP_WHERE_YOU_LEFT_OFF:
        return PICK_UP_WHERE_YOU_LEFT_OFF_DEFAULT_HOLDOUT_PERC
    if intervention_type == INTERVENTION_TYPE_NAVIGATIONAL_NUDGE:
        return NAVIGATIONAL_NUDGE_DEFAULT_HOLDOUT_PERC
    if intervention_type == INTERVENTION_TYPE_SAVINGS_NUDGE:
        return SAVINGS_NUDGE_DEFAULT_HOLDOUT_PERC
    if intervention_type == INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT:
        return 0.2
    return 0.5


def get_opportunity_label(intervention_type):
    if intervention_type == INTERVENTION_TYPE_CART_ABANDONMENT_RETURNING:
        return OPPORTUNITY_LABEL_CART_ABANDONMENT_ELIGIBLE
    if intervention_type == INTERVENTION_TYPE_CART_ABANDONMENT_IN_SESSION:
        return OPPORTUNITY_LABEL_CART_ABANDONMENT_ELIGIBLE
    if intervention_type == INTERVENTION_TYPE_PICK_UP_WHERE_YOU_LEFT_OFF:
        return OPPORTUNITY_LABEL_PICK_UP_WHERE_YOU_LEFT_OFF_ELIGIBLE
    if intervention_type == INTERVENTION_TYPE_NAVIGATIONAL_NUDGE:
        return OPPORTUNITY_LABEL_NAVIGATIONAL_NUDGE_ELIGIBLE
    if intervention_type == INTERVENTION_TYPE_SAVINGS_NUDGE:
        return OPPORTUNITY_LABEL_SAVINGS_NUDGE_ELIGIBLE
    return None

def get_or_create_intervention():
    session_cookie = request.form.get("session_cookie")
    intervention_type = request.form.get("intervention_type")
    #get user session record
    user_session_record = user_session.query.filter_by(
        session_cookie=session_cookie
    ).first()
    if user_session_record is None:
        return (
            jsonify(
                {
                    "error": "Session not found",
                    "message": "Could not find user session record",
                }
            ),
            404,
        )
    #get intervention if exists already
    intervention_record = get_session_intervention_record_if_exists(user_session_record, intervention_type)
    if intervention_record:
        return jsonify({"success": True, "holdout": intervention_record.holdout, "opportunity": intervention_record.opportunity})

    return create_intervention()
def create_intervention():
    session_cookie = request.form.get("session_cookie")
    intervention_type = request.form.get("intervention_type")
    vandra_opportunity = request.form.get("vandra_opportunity")
    vandra_opportunity_label = request.form.get("vandra_opportunity_label")

    user_session_record = user_session.query.filter_by(
        session_cookie=session_cookie
    ).first()
    if user_session_record is None:
        return (
            jsonify(
                {
                    "error": "Session not found",
                    "message": "Could not find user session record",
                }
            ),
            404,
        )

    try:
        obj = request.form.to_dict()
        metadata_str = obj.get("metadata", "{}")
        metadata = json.loads(metadata_str)
    except Exception as e:
        metadata = {}
        logger.error(f"Error parsing the metadata for create intervention: {e}")

    # get association record
    association_record = get_store_intervention_association(
        user_session_record.parent, intervention_type
    )

    default_holdout_percentage = get_default_holdout_percentage(intervention_type)

    opportunity = True
    opportunity_label = get_opportunity_label(intervention_type)
    if vandra_opportunity is not None:
        opportunity = vandra_opportunity == "true"
        opportunity_label = vandra_opportunity_label

    # get parameters
    holdout_percentage = (
        association_record.parameters.get(
            "holdout_percentage", default_holdout_percentage
        )
        if association_record.parameters
        else default_holdout_percentage
    )
    holdout = random.random() < holdout_percentage
    assign_session_intervention(user_session_record, intervention_type)
    update_session_intervention_opportunity(
        user_session_record,
        intervention_type,
        opportunity,
        opportunity_label,
        metadata=metadata,
    )
    update_session_intervention_holdout(user_session_record, intervention_type, holdout)

    return jsonify({"success": True, "holdout": holdout, "opportunity": opportunity})


def update_session_intervention_holdout_handler():
    session_cookie = request.form.get("session_cookie")
    intervention_type = request.form.get("intervention_type")
    holdout = request.form.get("holdout")
    holdout = False if holdout == "false" else True
    user_session_record = user_session.query.filter_by(
        session_cookie=session_cookie
    ).first()
    if user_session_record is None:
        return (
            jsonify(
                {
                    "error": "Session not found",
                    "message": "Could not find user session record",
                }
            ),
            404,
        )
    update_session_intervention_holdout(user_session_record, intervention_type, holdout)
    return jsonify({"success": True})


def record_intervention_action(status, event):
    session_cookie = request.form.get("session_cookie")
    intervention_type = request.form.get("intervention_type")
    dwell_time = request.form.get("dwell_time", 0)
    focused_dwell_time = request.form.get("focused_dwell_time")
    page_view_id = request.form.get("page_view_id")
    metadata = request.form.get("metadata", {})

    if intervention_type not in VALID_INTERVENTION_TYPES:
        return (
            jsonify(
                {
                    "error": "Invalid intervention type",
                    "message": "Invalid intervention type",
                }
            ),
            400,
        )

    user_session_record = user_session.query.filter_by(
        session_cookie=session_cookie
    ).first()
    if user_session_record is None:
        return (
            jsonify(
                {
                    "error": "Session not found",
                    "message": "Could not find user session record",
                }
            ),
            404,
        )

    try:
        obj = request.form.to_dict()
        metadata_str = obj.get("metadata", "{}")
        metadata = json.loads(metadata_str)
    except Exception as e:
        metadata = {}
        logger.error(
            f"Error parsing the metadata for updating parameters for intervention type {intervention_type}: {e}"
        )

    update_session_intervention_status(user_session_record, intervention_type, status)
    capture_session_intervention_event(
        user_session_record,
        intervention_type,
        event,
        dwell_time,
        focused_dwell_time,
        page_view_id,
        metadata,
    )

    return jsonify({"success": True})

def get_assets():
    session_cookie = request.args.get("session_cookie")
    intervention_type_name = request.args.get("intervention_type")
    asset_types = request.args.getlist("asset_type")
    asset_keys = request.args.getlist("asset_key")
    
    if not intervention_type_name or not asset_types or not asset_keys:
        return (
            jsonify({"error": "Missing intervention_type or asset_type or asset_key"}),
            400,
        )

    # Added filtering by session_cookie to restrict assets to the user's store
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if not user_session_record:
        return (
            jsonify({"error": "Session not found", "message": "Could not find user session record"}),
            404,
        )
    store_id = user_session_record.parent.uuid  # retrieve store id from user session's parent

    # Join with intervention_type table to get assets by intervention type name
    assets = (
        store_intervention_association_assets.query
        .join(
            store_intervention_association,
            store_intervention_association_assets.store_intervention_association_id == store_intervention_association.uuid
        )
        .join(
            intervention_type,
            store_intervention_association.intervention_type_id == intervention_type.uuid
        )
        .filter(
            intervention_type.name == intervention_type_name,
            store_intervention_association_assets.asset_type.in_(asset_types),
            store_intervention_association_assets.asset_key.in_(asset_keys),
            store_intervention_association.store_id == store_id
        )
        .all()
    )

    return jsonify([asset.to_dict() for asset in assets])
