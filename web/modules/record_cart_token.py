from flask import request
from app import db_session
from db import user_session, cart_token

import time
import uuid

def record_cart_token_handler():
    session_cookie = request.form.get("session_cookie")
    session_cart_token = request.form.get("cart_token")
    # user_session record created by record_page_view
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return "error"
    
    existing_cart_token_record = cart_token.query.filter_by(user_session_id=user_session_record.uuid, cart_token=session_cart_token).first()
    if existing_cart_token_record is None:
        cart_token_record = cart_token()
        cart_token_record.uuid = str(uuid.uuid4())
        cart_token_record.time = time.time()
        cart_token_record.user_session_id = user_session_record.uuid
        cart_token_record.cart_token = session_cart_token
        db_session.add(cart_token_record)
        db_session.commit()
    return "okay"