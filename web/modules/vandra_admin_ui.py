from flask import request, redirect, render_template
from db import front_end_ui, front_end_experiment, front_end_experiment_ui_allocation, front_end_experiment_log
from app import db_session
from helpers import balance_front_end_experiment_allocations, check_admin, check_super_admin

import time
import uuid

def vandra_admin_ui_experiments_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    # Get our experiment to UI references
    allocation_dict = dict()
    allocation_uis_dict = dict()
    experiment_ui_allocations = front_end_experiment_ui_allocation.query.all()
    for allocation in experiment_ui_allocations:
        allocation_dict[allocation.front_end_experiment_id + "/" + allocation.front_end_ui_id] = allocation.weight
        if allocation.front_end_experiment_id not in allocation_uis_dict:
            allocation_uis_dict[allocation.front_end_experiment_id] = set()
        allocation_uis_dict[allocation.front_end_experiment_id].add(allocation.front_end_ui_id)
    
    all_front_end_experiments = front_end_experiment.query.order_by(front_end_experiment.active.desc(), front_end_experiment.time.desc()).all()
    
    # Get our UIs and map their UUIDs to their names
    ui_dict = dict()
    all_front_end_uis = front_end_ui.query.order_by(front_end_ui.time.desc()).all()
    for front_end_ui_record in all_front_end_uis:
        ui_dict[front_end_ui_record.uuid] = front_end_ui_record.name
    
    # Get our strings of UIs for each experiment
    experiment_ui_allocations_dict = dict()
    for experiment in all_front_end_experiments:
        experiment_ui_allocations_dict[experiment.uuid] = ", ".join([str(ui_dict[x]) + " (" + str(round(allocation_dict[experiment.uuid + "/" + x], 1)) + ")" for x in allocation_uis_dict.get(experiment.uuid, [])])
    
    return render_template("vandra_admin_ui_experiments.html",
        all_front_end_experiments=all_front_end_experiments,
        experiment_ui_allocations_dict=experiment_ui_allocations_dict,
        all_front_end_uis=all_front_end_uis,
        allocation_uis_dict=allocation_uis_dict
    )

def vandra_admin_front_end_experiment_add_handler():
    if not check_super_admin():
        return redirect('/vandra_admin_login')

    add_front_end_ui_name = request.form.get("add_front_end_ui_name")
    add_front_end_ui_description = request.form.get("add_front_end_ui_description")

    # Ensure front end experiment name is unique
    add_front_end_ui_name_check = front_end_ui.query.filter_by(name=add_front_end_ui_name).first()
    if add_front_end_ui_name_check is not None:
        return "Please enter a unique name for the experiment"
    
    # Create new experiment
    new_front_end_experiment = front_end_experiment()
    new_front_end_experiment.uuid = str(uuid.uuid4())
    new_front_end_experiment.time = time.time()
    new_front_end_experiment.name = add_front_end_ui_name
    new_front_end_experiment.description = add_front_end_ui_description
    new_front_end_experiment.active = False
    new_front_end_experiment.store_specific = False
    db_session.add(new_front_end_experiment)

    # front_end_ui_store_association logic

    # Record front end ui status change
    new_front_end_experiment_log = front_end_experiment_log()
    new_front_end_experiment_log.uuid = str(uuid.uuid4())
    new_front_end_experiment_log.time = time.time()
    new_front_end_experiment_log.front_end_experiment_id = new_front_end_experiment.uuid
    new_front_end_experiment_log.start_time = time.time()
    db_session.add(new_front_end_experiment_log)

    db_session.commit()

    return redirect("/vandra_admin_ui_experiments")

def vandra_admin_experiment_toggle_active_handler():
    if not check_super_admin():
        return redirect('/vandra_admin_login')
    
    experiment_id = request.form.get("toggle_active_experiment_uuid")
    experiment_record = front_end_experiment.query.filter_by(uuid=experiment_id).first()
    if experiment_record is None:
        return redirect("/vandra_admin_ui_experiments")
    
    experiment_record.active = not experiment_record.active

    # Record front end ui status change
    if experiment_record.active == True:
        new_front_end_experiment_log = front_end_experiment_log()
        new_front_end_experiment_log.uuid = str(uuid.uuid4())
        new_front_end_experiment_log.time = time.time()
        new_front_end_experiment_log.front_end_experiment_id = experiment_id
        new_front_end_experiment_log.start_time = time.time()
        db_session.add(new_front_end_experiment_log)
    else:
        front_end_experiment_log_record = front_end_experiment_log.query.filter_by(front_end_experiment_id=experiment_id, end_time=None).first()
        if front_end_experiment_log_record is not None:
            front_end_experiment_log_record.end_time = time.time()

    db_session.commit()
    
    return redirect("/vandra_admin_ui_experiments")

def vandra_admin_add_ui_to_experiment_handler():
    if not check_super_admin():
        return redirect('/vandra_admin_login')
    
    front_end_experiment_id = request.form.get("experiment_id")
    front_end_ui_id = request.form.get("ui_id")
    
    # Make sure UI and experiment are real
    front_end_experiment_record = front_end_experiment.query.filter_by(uuid=front_end_experiment_id).first()
    front_end_ui_record = front_end_ui.query.filter_by(uuid=front_end_ui_id).first()
    
    if front_end_experiment_record is None or front_end_ui_record is None:
        return redirect("/vandra_admin_ui_experiments")
    
    # Make sure we don't already have an allocation
    allocation_check = front_end_experiment_ui_allocation.query.\
        filter_by(front_end_experiment_id=front_end_experiment_id, front_end_ui_id=front_end_ui_id).first()
    if allocation_check is not None:
        return redirect("/vandra_admin_ui_experiments")
    
    # Create the new allocation
    new_allocation = front_end_experiment_ui_allocation()
    new_allocation.uuid = str(uuid.uuid4())
    new_allocation.time = time.time()
    new_allocation.front_end_experiment_id = front_end_experiment_id
    new_allocation.front_end_ui_id = front_end_ui_id
    new_allocation.weight = 100
    db_session.add(new_allocation)
    db_session.commit()
    
    balance_front_end_experiment_allocations(front_end_experiment_id)
    
    return redirect("/vandra_admin_ui_experiments")

def vandra_admin_front_end_ui_add_handler():
    if not check_super_admin():
        return redirect('/vandra_admin_login')

    add_front_end_ui_name = request.form.get("add_front_end_ui_name")
    add_front_end_ui_description = request.form.get("add_front_end_ui_description")
    add_front_end_ui_filename = request.form.get("add_front_end_ui_filename")

    # Ensure front end ui name and filename are unique
    add_front_end_ui_name_check = front_end_ui.query.filter_by(name=add_front_end_ui_name).first()
    add_front_end_ui_filename_check = front_end_ui.query.filter_by(filename=add_front_end_ui_filename).first()
    if add_front_end_ui_name_check is not None or add_front_end_ui_filename_check is not None:
        return "Please enter a unique name and file name for the front end ui"
    
    # Create new front end ui
    new_front_end_ui = front_end_ui()
    new_front_end_ui.uuid = str(uuid.uuid4())
    new_front_end_ui.time = time.time()
    new_front_end_ui.name = add_front_end_ui_name
    new_front_end_ui.description = add_front_end_ui_description
    new_front_end_ui.filename = add_front_end_ui_filename
    new_front_end_ui.default = False
    db_session.add(new_front_end_ui)

    db_session.commit()

    return redirect("/vandra_admin_ui_experiments")

def vandra_admin_ui_experiment_analytics_hander():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    experiment_id = request.form.get("id")
    front_end_experiment_record = front_end_experiment.query.filter_by(uuid=experiment_id).first()
    if front_end_experiment_record is None:
        return redirect("/vandra_admin_ui_experiments")
    
    experiment = {}
    experiment.name = front_end_experiment_record.name
    
    return render_template("vandra_admin_ui_experiment_analytics.html",
        experiment=experiment
    )