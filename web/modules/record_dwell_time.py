from flask import request
from db import user_session, page_activity, page_keystrokes, page_view
from app import db_session

import time
import uuid

def get_element_from_data_string(element):
    val = request.form.get(element)
    if val in ["null", "unknown", "void", "undefined"]:
        val = None
    return val

def record_dwell_time_handler():
    session_cookie = request.form.get("session_cookie")
    
    page_view_record = page_view.query.filter(page_view.parent.has(user_session.session_cookie == session_cookie)).\
        filter_by(front_end_id=request.form.get("page_view_id")).first()
    if page_view_record is None:
        return "error"
    
    int_var_dict = dict()
    int_var_list = ["dwell_time", "focused_dwell_time", "total_scroll", "total_mouse_move"]
    for each_var in int_var_list:
        try:
            int_var_dict[each_var] = int(float(get_element_from_data_string(each_var)))
        except:
            int_var_dict[each_var] = 0
    
    page_activity_record = page_activity()
    page_activity_record.uuid = str(uuid.uuid4())
    page_activity_record.time = time.time()
    page_activity_record.page_view_id = page_view_record.uuid
    page_activity_record.dwell_time = max(min(int_var_dict["dwell_time"] or 0, 9999) - (page_view_record.dwell_time or 0), 0)
    page_activity_record.focused_dwell_time = max(min(int_var_dict["focused_dwell_time"] or 0, 9999) - (page_view_record.focused_dwell_time or 0), 0)
    page_activity_record.scroll = int_var_dict["total_scroll"] - (page_view_record.total_scroll or 0)
    page_activity_record.mouse_move = (int_var_dict["total_mouse_move"] or 0) - (page_view_record.total_mouse_move or 0)
    db_session.add(page_activity_record)
    
    if get_element_from_data_string("keystrokes") not in [None, ""]:
        page_keystrokes_record = page_keystrokes()
        page_keystrokes_record.uuid = str(uuid.uuid4())
        page_keystrokes_record.time = time.time()
        page_keystrokes_record.page_view_id = page_view_record.uuid
        page_keystrokes_record.keys = get_element_from_data_string("keystrokes")
        db_session.add(page_keystrokes_record)
    
    # DB isn't set up to record dwell time values greater than 10k
    page_view_record.dwell_time = min(int_var_dict["dwell_time"], 9999)
    page_view_record.focused_dwell_time = min(int_var_dict["focused_dwell_time"], 9999)
    page_view_record.total_scroll = int_var_dict["total_scroll"]
    page_view_record.total_mouse_move = int_var_dict["total_mouse_move"]

    db_session.commit()

    return "okay"