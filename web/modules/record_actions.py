import time
from flask import request, jsonify
from db import store, user_session, user_session_nudge_parameters
from constants import INTERVENTION_STATUS_SUPPRESSED, INTERVENTION_TYPE_CART_ABANDONMENT_IN_SESSION, INTERVENTION_TYPE_CART_ABANDONMENT_RETURNING, INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, INTERVENTION_TYPE_PICK_UP_WHERE_YOU_LEFT_OFF, INTERVENTION_TYPE_NAVIGATIONAL_NUDGE, INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT, INTERVENTION_TYPE_SAVINGS_NUDGE
from modules.intervention_orchestration import capture_session_intervention_event, update_session_intervention_status, update_session_intervention_metadata
import json
from app import db_session


def record_action_handler():
    trigger_state = request.form.get("trigger_state")
    trigger_action = request.form.get("trigger_action")
    trigger_type = request.form.get("trigger_type")
    session_cookie = request.form.get("session_cookie")

    if trigger_state is None:
        return jsonify({
            "error": "Bad Request",
            "message": "The 'trigget_state' parameter is required and missing from the request"
        }), 400
    
    if trigger_action is None:
        return jsonify({
            "error": "Bad Request",
            "message": "The 'trigget_action' parameter is required and missing from the request"
        }), 400

    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return jsonify({"error": "Session not found", "message": "Could not find user session record"}), 404
    
    user_nudge_params = user_session_nudge_parameters.query.filter_by(
        user_session_id=user_session_record.uuid).order_by(user_session_nudge_parameters.time.desc()).first()
    
    user_nudge_params.trigger_state = trigger_state or user_nudge_params.trigger_state
    user_nudge_params.trigger_action = trigger_action or user_nudge_params.trigger_action
    user_nudge_params.trigger_type = trigger_type or user_nudge_params.trigger_type

    try:
        db_session.commit()
    except Exception as e:
        return {"error": "An error occurred"}, 500

    return jsonify({"success": True, "data": {
        "id": user_nudge_params.uuid,
        "state": user_nudge_params.trigger_state,
        "action": user_nudge_params.trigger_action,
        "type": user_nudge_params.trigger_type
    }}), 200
    
    
def record_intervention_suppressed():
    session_cookie = request.form.get("session_cookie")
    intervention_type = request.form.get("intervention_type")
    page_view_id = request.form.get("page_view_id")
    dwell_time = request.form.get("dwell_time")
    focused_dwell_time = request.form.get("focused_dwell_time")
    metadata = request.form.get("metadata")
    # Retrieve user session record
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if not user_session_record:
        return jsonify({"error": "Session not found", "message": "Could not find user session record"}), 404
    
    #set of supported intervention types
    supported_interventions = {
        INTERVENTION_TYPE_CART_ABANDONMENT_IN_SESSION,
        INTERVENTION_TYPE_CART_ABANDONMENT_RETURNING,
        INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
        INTERVENTION_TYPE_PICK_UP_WHERE_YOU_LEFT_OFF,
        INTERVENTION_TYPE_NAVIGATIONAL_NUDGE,
        INTERVENTION_TYPE_SAVINGS_NUDGE,
        INTERVENTION_TYPE_SOCIAL_MEDIA_CONTENT
    }

    if intervention_type not in supported_interventions:
        return jsonify({"error": "Unsupported intervention type"}), 400

    # Update session intervention status and create store intervention event
    update_session_intervention_status(user_session_record, intervention_type, INTERVENTION_STATUS_SUPPRESSED)
    capture_session_intervention_event(user_session_record, 
                                       intervention_type,
                                       "suppressed",
                                       dwell_time,
                                       focused_dwell_time,
                                       page_view_id,
                                       json.loads(metadata)
                                    )
    if metadata:
        update_session_intervention_metadata(user_session_record, intervention_type, json.loads(metadata))


    # Return success response
    return jsonify({
        "success": True,
        "message": f'{intervention_type} intervention suppressed'
    })
    