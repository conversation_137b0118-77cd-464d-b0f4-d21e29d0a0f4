from app import db_session
from db import front_end_ui, scoring_layer_determination, session_intervention,intervention_type, store_intervention_association, store_intervention_event, session_intervention_event, model_decision
import time
from constants import INTERVENTION_STATUS_ASSIGNED, VALID_INTERVENTION_ASSIGNMENT_CHANGES, FORCE_DEFAULT_RIGHT_ANTI_HOLDOUT, DETERMINATION_TYPE_INTENT, SCORING_ARTIFACT_TYPE_INTENT, INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
from utils import get_generic_logger
from uuid import uuid4
logger = get_generic_logger(__name__)


def create_intervention_type(name, active=True):
    """
    Creates a new intervention type record.
    This function creates a new intervention type record and sets its properties.
    Args:
        name (str): The name of the intervention type.
        active (bool): The active status of the intervention type.
    Returns:
        intervention_type_record (object): The newly created intervention type record object.
    """
    
    existing_intervention_by_name = intervention_type.query.filter_by(name=name).first()
    if existing_intervention_by_name:
        return existing_intervention_by_name
    intervention_type_record = intervention_type(
        uuid = str(uuid4()),
        name=name,
        active=active,
        time=time.time()
    )
    db_session.add(intervention_type_record)
    db_session.commit()
    return intervention_type_record


def create_store_intervention_association(store_record, intervention_type_record):
    """
    Creates a store intervention association record.
    This function creates a new store intervention association record and sets its properties.
    Args:
        store_record (object): The store record object to which the intervention will be associated.
        intervention_type_record (object): The intervention type record object to be associated with the store.
    Returns:
        store_intervention_association_record (object): The newly created store intervention association record object.
    """
    #check if active association already exists
    existing_store_intervention_association_record = store_intervention_association.query.filter_by(
        intervention_type_id=str(intervention_type_record.uuid),
        store_id=store_record.uuid,
        active=True
    ).first()
    if existing_store_intervention_association_record:
        return existing_store_intervention_association_record
    store_intervention_association_record = store_intervention_association(
        uuid = str(uuid4()),
        time=time.time(),
        intervention_type_id=str(intervention_type_record.uuid),
        store_id=store_record.uuid,
        active=True
    )
    db_session.add(store_intervention_association_record)
    db_session.commit()
    #create event
    store_intervention_event_record = store_intervention_event(
        time=time.time(),
        store_intervention_association_id=(store_intervention_association_record.uuid),
        event="created"
    )
    db_session.add(store_intervention_event_record)
    db_session.commit()
    return store_intervention_association_record

def get_session_intervention_record_if_exists(user_session_record, intervention_type_name):
    intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
    existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=intervention_type_record.uuid).first()
    return existing_session_intervention_record

def assign_session_intervention(user_session_record, intervention_type_name):
    """
    Assigns an intervention to a user session.
    This function assigns an intervention of a specified type to a user session. 
    It first checks if the intervention type exists and is active. If the intervention 
    type is found, it then checks if the intervention has already been assigned to the session. 
    If the intervention is not already assigned, it creates a new session intervention record, 
    sets its properties, and commits it to the database.
    Args:
        user_session_record (UserSession): The user session record to which the intervention will be assigned.
        intervention_type_name (str): The name of the intervention type to be assigned.
    Raises:
        ValueError: If the intervention type is already assigned to the session.
    Returns:
        None
    """
    try:
        intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
        if intervention_type_record is None:
            intervention_type_record = create_intervention_type(intervention_type_name)
        elif intervention_type_record.active == False:
            raise ValueError(f"Intervention {intervention_type_name} is inactive")
        store_intervention_association_record = user_session_record.parent.store_intervention_associations.filter_by(intervention_type_id=str(intervention_type_record.uuid)).first()
        if store_intervention_association_record and store_intervention_association_record.active == False:
            logger.info(f"Intervention {intervention_type_name} is inactive for store {user_session_record.parent.uuid}")
            return
        if not store_intervention_association_record:
            #if missing, create association record
            store_intervention_association_record = \
                create_store_intervention_association(user_session_record.parent, intervention_type_record)
        
        if intervention_type_record:
            existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=str(intervention_type_record.uuid)).first()
            if existing_session_intervention_record:
                raise ValueError(f"Intervention {intervention_type_name} already assigned to this session {user_session_record.uuid}")
            session_intervention_record = session_intervention(
                time=time.time(),
                user_session_id=user_session_record.uuid,
                intervention_type_id=intervention_type_record.uuid,
                store_intervention_association_id=store_intervention_association_record.uuid,
                status=INTERVENTION_STATUS_ASSIGNED,
                opportunity=True
            )
            db_session.add(session_intervention_record)
            db_session.commit()
    except Exception as e:
        logger.error(f"Error assigning session intervention {intervention_type_name} for {user_session_record.uuid} {e}")
    
    

def update_session_intervention_status(user_session_record, intervention_type_name, intervention_status, metadata={}):
    """    
    Updates the status of a specific intervention for a user session.

    This function first retrieves the intervention type record and the existing session intervention record.
    If the intervention type does not exist for the given session, it raises a ValueError.
    It then checks if the status change is valid based on predefined valid status changes.
    If the status change is valid, it updates the status and commits the change to the database.

    
    Args:
        user_session_record (object): The user session record object containing session details.
        intervention_type_name (str): The name of the intervention type to update.
        intervention_status (str): The new status to set for the intervention.
        metadata (dict): Additional metadata to be added to the intervention record.
    Raises:
        ValueError: If the specified intervention type does not exist for the given session.
    Returns:
        None
    """
    try:
        intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
        if intervention_type_record is None:
            intervention_type_record = create_intervention_type(intervention_type_name)
        elif intervention_type_record.active == False:
            raise ValueError(f"Intervention {intervention_type_name} is inactive")


        existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=str(intervention_type_record.uuid)).first()
        if not existing_session_intervention_record:
            raise ValueError(f"Intervention {intervention_type_name} doesn't exist for this session {user_session_record.uuid}")
        #skip if status change invalid
        if intervention_status not in VALID_INTERVENTION_ASSIGNMENT_CHANGES[intervention_type_name][existing_session_intervention_record.status]:
            logger.warn(f"Invalid status change from {existing_session_intervention_record.status} to {intervention_status} for {intervention_type_name} for {user_session_record.uuid}")
            return
        existing_session_intervention_record.status = intervention_status
        if metadata:
            existing_session_intervention_record.parameters.update(metadata)
        db_session.commit()
        return
    except Exception as e:
        logger.error(f"Error updating session intervention {intervention_type_name} for {user_session_record.uuid} {e}")
    

def update_session_intervention_opportunity(user_session_record, intervention_type_name, opportunity_value, opportunity_label, metadata={}):
    """
    Updates the opportunity value for a specific intervention type in a user session record.
    Args:
        user_session_record (object): The user session record object containing session and intervention details.
        intervention_type_name (str): The name of the intervention type to be updated.
        opportunity_value (boolean): The new opportunity value to be set for the intervention.
        opportunity_label (string): Reason for the opportunity value (if applicable)
    Raises:
        ValueError: If the specified intervention type does not exist for the given session.
    Returns:
        None
    """
    try:
        intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
        existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=intervention_type_record.uuid).first()
        if not existing_session_intervention_record:
            raise ValueError(f"Intervention {intervention_type_name} doesn't exist for this session {user_session_record.uuid}")
        #skip if status change invalid
        existing_session_intervention_record.opportunity = opportunity_value
        existing_session_intervention_record.opportunity_label = opportunity_label
        if existing_session_intervention_record.parameters is None:
            existing_session_intervention_record.parameters = {}
        existing_session_intervention_record.parameters.update(metadata)
        if intervention_type_name == INTERVENTION_TYPE_INTENT_BASED_DISCOUNT:
            user_session_record.vandra_opportunity = opportunity_value
        db_session.commit()
        return
    except Exception as e:
        logger.error(f"Error updating session intervention {intervention_type_name} for {user_session_record.uuid} {e}")
    
def update_session_intervention_metadata(user_session_record, intervention_type_name, metadata):
    """
    Updates the metadata for a specific intervention type in a user session record.
    Args:
        user_session_record (object): The user session record object containing session details.
        intervention_type_name (str): The name of the intervention type to be updated.
        metadata (dict): The metadata to be updated for the intervention.
    Raises:
        ValueError: If the specified intervention type does not exist for the given session.
    Returns:
        None
    """
    try:
        intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
        existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=intervention_type_record.uuid).first()
        if not existing_session_intervention_record:
            raise ValueError(f"Intervention {intervention_type_name} doesn't exist for this session {user_session_record.uuid}")
        if existing_session_intervention_record.parameters is None:
            existing_session_intervention_record.parameters = {}
        existing_session_intervention_record.parameters.update(metadata)
        db_session.commit()
        return
    except Exception as e:
        logger.error(f"Error updating session intervention {intervention_type_name} for {user_session_record.uuid} {e}")

def update_session_intervention_holdout(user_session_record, intervention_type_name, holdout):
    """
    Updates the holdout status of a session intervention for a given user session record.
    Args:
        user_session_record (object): The user session record object containing session details.
        intervention_type_name (str): The name of the intervention type to be updated.
        holdout (bool): Determines whether the session will receive the treatment (False) or will be in the control group (True).
    Raises:
        ValueError: If the specified intervention type does not exist for the given session.
    """
    try:
        intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
        existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=intervention_type_record.uuid).first()
        if not existing_session_intervention_record:
            raise ValueError(f"Intervention {intervention_type_name} doesn't exist for this session {user_session_record.uuid}")
        existing_session_intervention_record.holdout = holdout
        #for backwards compatibility, update session table
        if intervention_type_name == INTERVENTION_TYPE_INTENT_BASED_DISCOUNT:
            user_session_record.holdout = holdout
        db_session.commit()
        logger.info(f"Updated holdout status for {intervention_type_name} to {holdout} for {user_session_record.uuid}")
    except Exception as e:
        logger.error(f"Error updating session intervention {intervention_type_name} for {user_session_record.uuid} {e}")
    



def update_session_intervention_decision(user_session_record, 
        intervention_type_name, 
        decision, 
        decision_criterion,
        scoring_layer_determinations=None):
    """
    Updates the decision for a specific intervention type in a user's session record.

    Args:
        user_session_record: The user's session record object.
        intervention_type_name (str): The name of the intervention type.
        decision (str): The decision to be updated for the intervention.
        decision_criterion (str): The criterion based on which the decision was made.
    Raises:
        ValueError: If the specified intervention type does not exist for the session.

    Returns:
        None
    """
    try:
        intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
        existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=intervention_type_record.uuid).first()
        if not existing_session_intervention_record:
            raise ValueError(f"Intervention {intervention_type_name} doesn't exist for this session {user_session_record.uuid}")
        existing_session_intervention_record.decision = decision
        existing_session_intervention_record.decision_criterion = decision_criterion
        if scoring_layer_determinations:
            existing_session_intervention_record.scoring_layer_determinations = scoring_layer_determinations
        db_session.commit()
    except Exception as e:
        logger.error(f"Error updating session intervention {intervention_type_name} for {user_session_record.uuid} {e}")
    


def get_store_intervention_association(store_record, intervention_type_name,only_active=True):
    try:
        intervetion_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
        store_intervention_record = store_intervention_association.query.filter(
            store_intervention_association.store_id == store_record.uuid,
            (store_intervention_association.active == True) if only_active else True,
        ).filter(
            store_intervention_association.intervention_type_id == intervetion_type_record.uuid
        ).order_by(
            store_intervention_association.time.desc()
        ).first()    
        
        return store_intervention_record
    except Exception as e:
        logger.error(f"Error getting store intervention {intervention_type_name} for {store_record.uuid} {e}")
    
def replicate_live_decision_in_legacy_construct(user_session_record, 
                                                decided_to_show_discount,
                                                decision_criterion):
    decision = model_decision(
        uuid = str(uuid4()),
        time=time.time(),
        decided_to_show_discount=decided_to_show_discount,
        live_version=True,
        model_version_id=None,
        user_session_id=user_session_record.uuid,
        decision_criterion=decision_criterion
    )
    db_session.add(decision)
    db_session.commit()
                


def handle_anti_holdout_defaults(user_session_record):
    if FORCE_DEFAULT_RIGHT_ANTI_HOLDOUT and user_session_record.front_end_ui.name != "DEFAULT_RIGHT" \
                            and user_session_record.vandra_shown == False:
        default_front_end = front_end_ui.query.filter_by(name="DEFAULT_RIGHT").first()
        user_session_record.front_end_ui_id = default_front_end.uuid
        #remove non-default right nudge parameters if they exist
        if user_session_record.user_session_nudge_parameters:
            user_session_record.user_session_nudge_parameters.trigger_type = None
            user_session_record.user_session_nudge_parameters.trigger_state = None
            user_session_record.user_session_nudge_parameters.trigger_action = None
            user_session_record.user_session_nudge_parameters.trigger_delay = None
            db_session.commit()

def get_session_intervention_record(user_session_record, 
        intervention_type_name, 
        ):
    intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
    existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=intervention_type_record.uuid).first()
    if not existing_session_intervention_record:
        raise ValueError(f"Intervention {intervention_type_name} doesn't exist for this session {user_session_record.uuid}")
    return existing_session_intervention_record

def get_intervention_record_determination_time(intervention_record):
    determination_time = None
    #grab the earliest determination time
    if intervention_record.scoring_layer_determinations:
        for determination in intervention_record.scoring_layer_determinations.values():
            determination_record = scoring_layer_determination.query.filter_by(uuid=determination).first()
            if determination_time is None or determination_record.time < determination_time:
                determination_time = determination_record.time
    return determination_time

def get_intent_score_for_intervention(intervention_record):
    intent_determination_id = intervention_record.scoring_layer_determinations.get(DETERMINATION_TYPE_INTENT) if intervention_record.scoring_layer_determinations else None
    if intent_determination_id:
        determination_record = scoring_layer_determination.query.filter_by(uuid=intent_determination_id).first()
        return determination_record.scoring_layer_scores.get(SCORING_ARTIFACT_TYPE_INTENT)
    return None

def capture_session_intervention_event(user_session_record, intervention_type_name, event, dwell_time=None, focused_dwell_time=None, page_view_id=None, metadata=None):
    """
    Captures an event for a session intervention.
    This function captures an event for a session intervention and sets its properties.
    Args:
        user_session_record (object): The user session record object for which the event is being captured.
        intervention_type_name (str): The name of the intervention type for which the event is being captured.
        event (str): The event being captured.
        dwell_time (int): The dwell time associated with the event.
        page_view_record (object): The page view record object associated with the event.
        metadata (dict): The metadata associated with the event.
    Returns:
        None
    """
    try:
        intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
        existing_session_intervention_record = user_session_record.session_interventions.filter_by(intervention_type_id=intervention_type_record.uuid).first()
        if not existing_session_intervention_record:
            raise ValueError(f"Intervention {intervention_type_name} doesn't exist for this session {user_session_record.uuid}")
        session_intervention_event_record = session_intervention_event(
            time=time.time(),
            session_intervention_id=existing_session_intervention_record.uuid,
            event=event,
            dwell_time=dwell_time,
            focused_dwell_time=focused_dwell_time,
            page_view_id=page_view_id,
            event_metadata=metadata
        )
        db_session.add(session_intervention_event_record)
        db_session.commit()
    except Exception as e:
        logger.error(f"Error capturing session intervention event {intervention_type_name} for {user_session_record.uuid} {e}")
