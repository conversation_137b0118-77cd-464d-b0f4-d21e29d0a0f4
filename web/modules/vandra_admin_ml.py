from flask import request, redirect, render_template
from db import model_store_threshold, model_version, store, store_url
from app import db_session
from helpers import check_admin, check_super_admin

from datetime import datetime
import time
import uuid

def vandra_admin_ml_models_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    model_version_list = []
    model_version_query = model_version.query.filter_by(status='H2O_DEPLOYED').\
        order_by(model_version.live_version.desc().nullslast(), model_version.run_predictions.desc().nullslast(), model_version.time.desc()).all()
    for model_version_record in model_version_query:
        model_version_list.append({
            "uuid": model_version_record.uuid,
            "creation_date": datetime.utcfromtimestamp(model_version_record.time).strftime('%Y-%m-%d'),
            "time_cutoff": model_version_record.time_cutoff,
            "filename": model_version_record.filename,
            "store_specific_model": model_version_record.store_specific_model,
            "test_split_method": model_version_record.test_split_method,
            "description": model_version_record.description,
            "run_predictions": model_version_record.run_predictions,
            "live_version": model_version_record.live_version
        })
    
    return render_template("vandra_admin_ml_models.html", model_version_list=model_version_list)

def vandra_admin_ml_models_add_handler():
    if not check_super_admin():
        return redirect("/vandra_admin_login")
    
    filename = request.form.get("filename")
    deployment_id = request.form.get("deployment_id")
    description = request.form.get("description")
    
    new_model_version = model_version()
    new_model_version.uuid = str(uuid.uuid4())
    new_model_version.time = time.time()
    new_model_version.filename = filename
    new_model_version.deployment_id = deployment_id
    new_model_version.description = description
    new_model_version.decision_threshold = 0.01
    new_model_version.holdout_percent = 0
    new_model_version.time_cutoff = 65
    new_model_version.run_predictions = False
    new_model_version.live_version = False
    new_model_version.store_specific_model = False
    db_session.add(new_model_version)
    db_session.commit()
    
    return redirect("vandra_admin_ml_model_thresholds?model_id=" + new_model_version.uuid)

def vandra_admin_toggle_run_predictions_handler():
    if not check_super_admin():
        return redirect("/vandra_admin_login")
    
    model_version_id = request.form.get("model_id")
    if model_version_id is None:
        return redirect("/vandra_admin_ml_models")
    model_version_record = model_version.query.filter_by(uuid=model_version_id).first()
    if model_version_record is None:
        return redirect("/vandra_admin_ml_models")
    
    model_version_record.run_predictions = not model_version_record.run_predictions
    db_session.commit()
    
    return redirect("/vandra_admin_ml_models")

def vandra_admin_toggle_live_version_handler():
    if not check_super_admin():
        return redirect("/vandra_admin_login")
    
    model_version_id = request.form.get("model_id")
    if model_version_id is None:
        return redirect("/vandra_admin_ml_models")
    model_version_record = model_version.query.filter_by(uuid=model_version_id).first()
    if model_version_record is None:
        return redirect("/vandra_admin_ml_models")
    
    # Turn off any active models
    if not model_version_record.live_version:
        live_models = model_version.query.filter(model_version.live_version == True).all()
        for live_model in live_models:
            live_model.live_version = False
    
    model_version_record.live_version = not model_version_record.live_version
    
    db_session.commit()
    
    return redirect("/vandra_admin_ml_models")

def vandra_admin_ml_model_thresholds_handler():
    if not check_admin():
        return redirect("/vandra_admin_login")
    
    model_version_id = request.args.get("model_id")
    if model_version_id is None:
        return redirect("/vandra_admin_ml_models")
    model_version_record = model_version.query.filter_by(uuid=model_version_id).first()
    if model_version_record is None:
        return redirect("/vandra_admin_ml_models")
    
    model_dict = {
        "uuid": model_version_record.uuid,
        "time": model_version_record.time,
        "description": model_version_record.description,
        "creation_date": datetime.utcfromtimestamp(model_version_record.time).strftime('%Y-%m-%d'),
        "default_threshold": model_version_record.decision_threshold,
        "default_holdout": model_version_record.holdout_percent
    }
    
    store_query = store.query.order_by(store.active_store.desc().nullslast(), store.test_store.nullslast(), store.name).all()
    
    threshold_dict = {}
    model_store_threshold_query = model_store_threshold.query.filter_by(model_version_id=model_version_record.uuid).all()
    for threshold_record in model_store_threshold_query:
        threshold_dict[threshold_record.store_id] = threshold_record.decision_threshold
    
    for store_record in store_query:
        store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
            order_by(store_url.default.desc()).first()
        if store_url_record is None:
            return redirect("/vandra_admin_ml_models")

        store_record.url = store_url_record
        if store_record.uuid not in threshold_dict:
            threshold_dict[store_record.uuid] = ""
    
    return render_template("vandra_admin_ml_model_thresholds.html",
        model_dict=model_dict, store_query=store_query, threshold_dict=threshold_dict
    )

def vandra_admin_ml_model_save_thresholds_handler():
    if not check_super_admin():
        return redirect("/vandra_admin_login")
    
    model_version_id = request.form.get("model_id")
    if model_version_id is None:
        return redirect("/vandra_admin_ml_models")
    model_version_record = model_version.query.filter_by(uuid=model_version_id).first()
    if model_version_record is None:
        return redirect("/vandra_admin_ml_models")
    
    if request.form.get("threshold_0") not in [None, "", 0]:
        model_version_record.decision_threshold = float(request.form.get("threshold_0"))
    default_holdout = request.form.get("holdout_0") or 0
    model_version_record.holdout_percent = float(default_holdout)
    
    threshold_ref_dict = {}
    model_store_threshold_query = model_store_threshold.query.filter_by(model_version_id=model_version_record.uuid).all()
    for threshold_record in model_store_threshold_query:
        threshold_ref_dict[threshold_record.store_id] = threshold_record
    
    store_query = store.query.order_by(store.name).all()
    for store_record in store_query:
        threshold = request.form.get("threshold_" + store_record.uuid)
        if threshold in [None, "", 0]:
            if store_record.uuid in threshold_ref_dict:
                db_session.delete(threshold_ref_dict[store_record.uuid])
            continue
        
        if store_record.uuid in threshold_ref_dict:
            threshold_ref_dict[store_record.uuid].decision_threshold = float(threshold)
        else:
            new_threshold = model_store_threshold()
            new_threshold.uuid = str(uuid.uuid4())
            new_threshold.time = time.time()
            new_threshold.model_version_id = model_version_record.uuid
            new_threshold.store_id = store_record.uuid
            new_threshold.decision_threshold = float(threshold)
            db_session.add(new_threshold)
    
    db_session.commit()
    
    return redirect("/vandra_admin_ml_model_thresholds?model_id=" + model_version_id)