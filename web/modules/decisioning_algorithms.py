import time
from task_queue import BACKGROUND_SCORING_LAYER_QUEUE, LIVE_SCORING_LAYER_QUEUE, REDIS_CONNECTION
from constants import (DECISION_DELAY_MAPPING, 
                       HIGH_INTENT_LABEL, 
                       MEDIUM_INTENT_LABEL,
                       LOW_INTENT_LABEL,
                       ANTI_HOLDOUT_LABEL,
                       INTERVENTION_DECISION_SHOW,
                       INTERVENTION_DECISION_NO_SHOW,
                       INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                       HIGH_UPLIFT_LABEL,
                       LOW_UPLIFT_LABEL,
                       DECISION_ALGORITHM_INTENT_ONLY,
                       DECISION_ALGORITHM_INTENT_AND_UPLIFT,
                       DECISION_ALGORITHM_UPLIFT_ONLY,
                       DETERMINATION_TYPE_INTENT,
                       DETERMINATION_TYPE_UPLIFT,
                       SCORING_ALGORITHM_TYPES,
                       DEFAULT_HOLDOUT_PERC,
                       DEFAULT_ANTI_HOLDOUT_PERC,
                       ANTI_HOLDOUT_LABEL,
                       INCONCLUSIVE_INTENT_LABEL
)
from rq.job import Job
from .intervention_orchestration import update_session_intervention_decision, update_session_intervention_holdout, handle_anti_holdout_defaults, replicate_live_decision_in_legacy_construct
from .scoring_algorithms import IntentScoringLayerLogic, IntentUpliftOnlyScoringLayerLogic, CappedUpliftScoringLayerLogic
from db import (
    scoring_layer_determination, 
    user_session, 
    scoring_layer, 
    scoring_layer_thresholds, 
    store_intervention_association,
    intervention_type
)
from utils import logged
from sqlalchemy import and_
from app import app

from ephemeral_store import EphemeralStore
from datetime import timedelta
import json
import random

@logged
class DecisioningLogic:

    def __init__(self, intervention_type):
        self.intervention_type = intervention_type
        self.max_decision_delay = DECISION_DELAY_MAPPING[intervention_type]
        self.scoring_algorithm_types = SCORING_ALGORITHM_TYPES[intervention_type]

    
    def get_decision_cache_id(self, user_session_record, current_time_cutoff):
        return f"{self.intervention_type}_{user_session_record.uuid}_{current_time_cutoff}"
    
    def add_job_ids_entry_to_cache(self, user_session_record, current_time_cutoff, key, entry):
        cache_id = self.get_decision_cache_id(user_session_record, current_time_cutoff)
        cached_payload = EphemeralStore.find_key(key=cache_id)
        if cached_payload:
            cached_payload = json.loads(cached_payload)
            cached_payload['job_ids'][key] = entry
            EphemeralStore.add_to_store(key=cache_id, expire_timedelta=timedelta(minutes=60), data=json.dumps(cached_payload))
        else:
            self.logger.error(f"Job id cache not found for {user_session_record.uuid} and {current_time_cutoff}")
    
    def decision_already_triggered(self, user_session_record, current_time_cutoff):
        cache_id = self.get_decision_cache_id(user_session_record, current_time_cutoff)
        cached_payload = EphemeralStore.find_key(key=cache_id)
        if cached_payload:
            self.logger.info(f"Decision event cached for {user_session_record.uuid} and {current_time_cutoff}")
            cached_payload = json.loads(cached_payload)
            return cached_payload['is_triggered'], cached_payload['job_ids']
        return False, None

        
    def cache_triggered_decision_event(self, user_session_record, current_time_cutoff):
        delta = timedelta(minutes=60)
        cache_id = self.get_decision_cache_id(user_session_record, current_time_cutoff)
        self.logger.info(f"Caching data for {user_session_record.uuid} and {current_time_cutoff} with cache id {cache_id}")
        EphemeralStore.add_to_store(key=cache_id, expire_timedelta=delta, data=json.dumps({'is_triggered' : True, 'job_ids' : {}})) 



    
    def get_scoring_layers(self, user_session_record, time_cutoff):
        with app.app_context():
            #first search for store specific live scoring layers
            store_specific_live_scoring_layers = (
                scoring_layer.query
                .join(scoring_layer_thresholds, scoring_layer.uuid == scoring_layer_thresholds.scoring_layer_id)
                .filter(and_(
                        scoring_layer.active==True, 
                        scoring_layer.run_predictions==True,
                        scoring_layer.scoring_algorithm_type.in_(self.scoring_algorithm_types),
                        scoring_layer.live_decisioning==True,
                        scoring_layer.store_specific==True,
                        scoring_layer_thresholds.active==True,
                        scoring_layer_thresholds.store_id == user_session_record.store_id,
                        scoring_layer.scoring_time_cutoff == time_cutoff)
                )
                .all()
            )
            if store_specific_live_scoring_layers:
                #pick one of the store specific live scoring layers randomly, equal probability
                live_scoring_record = random.choice(store_specific_live_scoring_layers)
            else:
                live_scoring_record = (
                    scoring_layer.query
                    .join(scoring_layer_thresholds, scoring_layer.uuid == scoring_layer_thresholds.scoring_layer_id)
                    .filter(and_(
                            scoring_layer.active==True, 
                            scoring_layer.run_predictions==True,
                            scoring_layer.scoring_algorithm_type.in_(self.scoring_algorithm_types),
                            scoring_layer.live_decisioning==True,
                            scoring_layer.store_specific==False,
                            scoring_layer_thresholds.active==True,
                            scoring_layer_thresholds.store_id == user_session_record.store_id,
                            scoring_layer.scoring_time_cutoff == time_cutoff)
                    )
                    .first()
                )
            live_scoring_record_id = live_scoring_record.uuid if live_scoring_record else None
            # get all background scoring layers
            background_scoring_layers = (
                scoring_layer.query
                .join(scoring_layer_thresholds, scoring_layer.uuid == scoring_layer_thresholds.scoring_layer_id)
                .filter(and_(
                        scoring_layer.active==True, 
                        scoring_layer.run_predictions==True,
                        scoring_layer.uuid != live_scoring_record_id,  # exclude live scoring layer
                        scoring_layer_thresholds.active==True,
                        scoring_layer_thresholds.store_id == user_session_record.store_id,
                        scoring_layer.scoring_time_cutoff == time_cutoff,
                        scoring_layer.scoring_algorithm_type.in_(self.scoring_algorithm_types)
                        )
                )
                .all()
            )
            return live_scoring_record, background_scoring_layers



    def enqueue_background_determinations(self, user_session_record, background_scoring_layers):
        raise NotImplementedError("Subclasses must implement abstract method")

    def decide(self, user_session_record, current_time_cutoff, scoring_time=None):
        raise NotImplementedError("Subclasses must implement abstract method")

    @classmethod
    def get_store_holdout_percentage(cls, user_session_record, intervention_type_name):
        """
        Get store-specific holdout percentage if configured, otherwise return default.
        
        Args:
            user_session_record: The user session record
            intervention_type_name: The type of intervention to get holdout percentage for
        """
        # First get the intervention type record
        intervention_type_record = intervention_type.query.filter_by(
            name=intervention_type_name
        ).first()
        
        if not intervention_type_record:
            cls.logger.error(f"Intervention type {intervention_type_name} not found")
            return DEFAULT_HOLDOUT_PERC
        
        store_intervention = store_intervention_association.query.filter_by(
            store_id=user_session_record.store_id,
            intervention_type_id=intervention_type_record.uuid,
            active=True
        ).first()
        
        holdout_perc = DEFAULT_HOLDOUT_PERC
        if store_intervention and store_intervention.parameters:
            try:
                params = store_intervention.parameters
                holdout_perc = params.get('holdout_percentage', DEFAULT_HOLDOUT_PERC)
            except Exception as e:
                cls.logger.error(f"Error parsing store intervention parameters: {e}")
        cls.logger.info(f"Holdout percentage for {user_session_record.uuid} and {intervention_type_name} is {holdout_perc}")
        return holdout_perc

    @classmethod
    def get_store_anti_holdout_percentage(cls, user_session_record, intervention_type_name):
        """
        Get store-specific anti-holdout percentage if configured, otherwise return default.
        
        Args:
            user_session_record: The user session record
            intervention_type_name: The type of intervention to get anti-holdout percentage for
        """
        # First get the intervention type record
        intervention_type_record = intervention_type.query.filter_by(
            name=intervention_type_name
        ).first()
        
        if not intervention_type_record:
            cls.logger.error(f"Intervention type {intervention_type_name} not found")
            return DEFAULT_ANTI_HOLDOUT_PERC
        
        store_intervention = store_intervention_association.query.filter_by(
            store_id=user_session_record.store_id,
            intervention_type_id=intervention_type_record.uuid,
            active=True
        ).first()
        
        anti_holdout_perc = DEFAULT_ANTI_HOLDOUT_PERC
        if store_intervention and store_intervention.parameters:
            try:
                params = store_intervention.parameters
                anti_holdout_perc = params.get('anti_holdout_percentage', DEFAULT_ANTI_HOLDOUT_PERC)
            except Exception as e:
                cls.logger.error(f"Error parsing store intervention parameters: {e}")
        cls.logger.info(f"Anti-holdout percentage for {user_session_record.uuid} and {intervention_type_name} is {anti_holdout_perc}")
        return anti_holdout_perc


class IntentDecisioningLogic(DecisioningLogic):

    def __init__(self, intervention_type):
        super().__init__(intervention_type)

    def wait_and_get_determination_result(self, job, session_id):
        result = job.latest_result(timeout=60)
        if not result or result.type != result.Type.SUCCESSFUL:
            if not result:
                job.cancel()
                self.logger.exception(f"Job {job.id} for session {session_id} timed out")
            else:
                raise Exception(f"Inference error for job session {session_id}")
        return result.return_value

    
    def enqueue_background_determinations(self, user_session_record, background_scoring_layers):
        job_ids = []
        if background_scoring_layers:
            for background_scoring_layer in background_scoring_layers:
                if background_scoring_layer.scoring_algorithm_type == DECISION_ALGORITHM_INTENT_ONLY:
                    job = BACKGROUND_SCORING_LAYER_QUEUE.enqueue(IntentScoringLayerLogic.make_determination, 
                                                user_session_record.uuid, 
                                                background_scoring_layer.uuid, 
                                                failure_ttl=10, 
                                                ttl=120)
                elif background_scoring_layer.scoring_algorithm_type == DECISION_ALGORITHM_INTENT_AND_UPLIFT:
                    job = BACKGROUND_SCORING_LAYER_QUEUE.enqueue(CappedUpliftScoringLayerLogic.make_determination, 
                                                user_session_record.uuid, 
                                                background_scoring_layer.uuid, 
                                                failure_ttl=10, 
                                                ttl=120)
                elif background_scoring_layer.scoring_algorithm_type == DECISION_ALGORITHM_UPLIFT_ONLY:
                    job = BACKGROUND_SCORING_LAYER_QUEUE.enqueue(IntentUpliftOnlyScoringLayerLogic.make_determination, 
                                                user_session_record.uuid, 
                                                background_scoring_layer.uuid, 
                                                DECISION_ALGORITHM_UPLIFT_ONLY,
                                                failure_ttl=10, 
                                                ttl=120)
                else:
                    raise ValueError(f"Invalid scoring algorithm type {background_scoring_layer.scoring_algorithm_type}")
                job_ids.append(job.id)
            self.add_job_ids_entry_to_cache(user_session_record, background_scoring_layer.scoring_time_cutoff, 'background_decision', job_ids)

                    
    @staticmethod
    def callback_intent_only_decision(intent_job, connection, result, *args, **kwargs):
        with app.app_context():
            _, determination, determination_record_id = result
            determination_record = scoring_layer_determination.query.filter_by(uuid=determination_record_id).first()
            show_decision = INTERVENTION_DECISION_SHOW if determination == LOW_INTENT_LABEL else INTERVENTION_DECISION_NO_SHOW
            bound_user_session_record = user_session.query.filter_by(uuid=determination_record.user_session_id).first()            
            
            #set default holdout - only if not inconclusive
            if show_decision == INTERVENTION_DECISION_SHOW:
                holdout_perc = IntentDecisioningLogic.get_store_holdout_percentage(
                    bound_user_session_record,
                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
                )
                session_in_holdout = random.random() < holdout_perc
                update_session_intervention_holdout(bound_user_session_record, 
                                                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, 
                                                    session_in_holdout)
            # Only consider anti-holdout for medium intent and not inconclusive
            elif determination == MEDIUM_INTENT_LABEL:
                # Consider antiholdout, override holdout
                anti_holdout_perc = IntentDecisioningLogic.get_store_anti_holdout_percentage(
                    bound_user_session_record,
                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
                )
                if random.random() < anti_holdout_perc:
                    determination = ANTI_HOLDOUT_LABEL
                    show_decision = INTERVENTION_DECISION_SHOW
                    handle_anti_holdout_defaults(bound_user_session_record)

            #update final decision
            update_session_intervention_decision(bound_user_session_record, 
                                                INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, 
                                                show_decision,
                                                determination,
                                                scoring_layer_determinations={DETERMINATION_TYPE_INTENT: determination_record_id})
            #capture decision for legacy purposes
            show_decision_boolean = show_decision == INTERVENTION_DECISION_SHOW
            decision_criterion = ANTI_HOLDOUT_LABEL if determination == MEDIUM_INTENT_LABEL else determination
            replicate_live_decision_in_legacy_construct(bound_user_session_record, show_decision_boolean, decision_criterion)
    
    @staticmethod
    def callback_uplift_only_decision(intent_job, connection, result, *args, **kwargs):
        with app.app_context():
            _, determination, determination_record_id = result
            determination_record = scoring_layer_determination.query.filter_by(uuid=determination_record_id).first()
            show_decision = INTERVENTION_DECISION_SHOW if determination == HIGH_UPLIFT_LABEL else INTERVENTION_DECISION_NO_SHOW
            bound_user_session_record = user_session.query.filter_by(uuid=determination_record.user_session_id).first()

            #set default holdout - only if not inconclusive
            if show_decision == INTERVENTION_DECISION_SHOW:
                holdout_perc = IntentDecisioningLogic.get_store_holdout_percentage(
                    bound_user_session_record,
                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
                )
                session_in_holdout = random.random() < holdout_perc
                update_session_intervention_holdout(bound_user_session_record, 
                                                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, 
                                                    session_in_holdout)
            # Skip anti-holdout for inconclusive determinations
            elif determination != INCONCLUSIVE_INTENT_LABEL:
                # Consider antiholdout, override holdout
                anti_holdout_perc = IntentDecisioningLogic.get_store_anti_holdout_percentage(
                    bound_user_session_record,
                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
                )
                if random.random() < anti_holdout_perc:
                    determination = ANTI_HOLDOUT_LABEL
                    show_decision = INTERVENTION_DECISION_SHOW
                    handle_anti_holdout_defaults(bound_user_session_record)
                
            update_session_intervention_decision(bound_user_session_record, 
                                                INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, 
                                                show_decision,
                                                determination,
                                                scoring_layer_determinations={DETERMINATION_TYPE_UPLIFT : determination_record_id})
            #capture decision for legacy purposes
            show_decision_boolean = show_decision == INTERVENTION_DECISION_SHOW
            replicate_live_decision_in_legacy_construct(bound_user_session_record, show_decision_boolean, None)
    


            
    @staticmethod
    def callback_intent_and_uplift_decision(intent_job, connection, result, *args, **kwargs):
        with app.app_context():
            _, determination, determination_record_id = result
            determination_record = scoring_layer_determination.query.filter_by(uuid=determination_record_id).first()
            show_decision = INTERVENTION_DECISION_SHOW if determination == HIGH_UPLIFT_LABEL else INTERVENTION_DECISION_NO_SHOW
            bound_user_session_record = user_session.query.filter_by(uuid=determination_record.user_session_id).first()

            #set default holdout - only if not inconclusive
            if show_decision == INTERVENTION_DECISION_SHOW:
                holdout_perc = IntentDecisioningLogic.get_store_holdout_percentage(
                    bound_user_session_record,
                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
                )
                session_in_holdout = random.random() < holdout_perc
                update_session_intervention_holdout(bound_user_session_record, 
                                                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, 
                                                    session_in_holdout)
            # Skip anti-holdout for inconclusive determinations
            elif determination == LOW_UPLIFT_LABEL:
                # Consider antiholdout, override holdout
                anti_holdout_perc = IntentDecisioningLogic.get_store_anti_holdout_percentage(
                    bound_user_session_record,
                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
                )
                if random.random() < anti_holdout_perc:
                    determination = ANTI_HOLDOUT_LABEL
                    show_decision = INTERVENTION_DECISION_SHOW
                    handle_anti_holdout_defaults(bound_user_session_record)
                
            update_session_intervention_decision(bound_user_session_record, 
                                                INTERVENTION_TYPE_INTENT_BASED_DISCOUNT, 
                                                show_decision,
                                                determination,
                                                scoring_layer_determinations={DETERMINATION_TYPE_UPLIFT : determination_record_id})
            #capture decision for legacy purposes
            show_decision_boolean = show_decision == INTERVENTION_DECISION_SHOW
            replicate_live_decision_in_legacy_construct(bound_user_session_record, show_decision_boolean, None)
    
    
    
    def run_live_determination(self, user_session_record, live_scoring_layer):
        if live_scoring_layer.scoring_algorithm_type == DECISION_ALGORITHM_INTENT_ONLY:
            job = LIVE_SCORING_LAYER_QUEUE.enqueue(IntentScoringLayerLogic.make_determination, 
                                           user_session_record.uuid, 
                                           live_scoring_layer.uuid, 
                                           failure_ttl=10, 
                                           ttl=120,
                                           on_success=self.callback_intent_only_decision)
            
        elif live_scoring_layer.scoring_algorithm_type == DECISION_ALGORITHM_INTENT_AND_UPLIFT:
            job = LIVE_SCORING_LAYER_QUEUE.enqueue(CappedUpliftScoringLayerLogic.make_determination, 
                                           user_session_record.uuid, 
                                           live_scoring_layer.uuid, 
                                           failure_ttl=10, 
                                           ttl=120,
                                           on_success=self.callback_intent_and_uplift_decision)
            
            
        elif live_scoring_layer.scoring_algorithm_type == DECISION_ALGORITHM_UPLIFT_ONLY:
            job = LIVE_SCORING_LAYER_QUEUE.enqueue(IntentUpliftOnlyScoringLayerLogic.make_determination, 
                                           user_session_record.uuid, 
                                           live_scoring_layer.uuid, 
                                           DECISION_ALGORITHM_UPLIFT_ONLY,
                                           failure_ttl=10, 
                                           ttl=120,
                                           on_success=self.callback_uplift_only_decision)
        #Need to return a job that can be waited on after callback    
        complete_job = LIVE_SCORING_LAYER_QUEUE.enqueue(self.logger.info, f"{job.id} Job completed", failure_ttl=10, ttl=120, depends_on=job)
        self.add_job_ids_entry_to_cache(user_session_record, live_scoring_layer.scoring_time_cutoff, 'live_decision', job.id)
        return complete_job

    def decide(self, user_session_record, current_time_cutoff, scoring_time=None):
        scoring_time = scoring_time or time.time()
        if abs(current_time_cutoff - (scoring_time - user_session_record.time)) > self.max_decision_delay:
            self.logger.info(f"Decision delay threshold breached for {user_session_record.uuid}")
            return False, None
        is_triggered, job_ids = self.decision_already_triggered(user_session_record, current_time_cutoff)
        if is_triggered:
            self.logger.info(f"Decision already triggered for {user_session_record.uuid} with {job_ids}")
            if job_ids.get('live_decision'):
                job = Job.fetch(job_ids['live_decision'], connection=REDIS_CONNECTION)
                #just wait for it to finish before returning
                return True, job
            return False, None
        live_scoring_layer, background_scoring_layers = \
            self.get_scoring_layers(user_session_record, 
                        current_time_cutoff
                        )
        if not live_scoring_layer and not background_scoring_layers:
            return False, None
        #cache the decision event
        self.cache_triggered_decision_event(user_session_record, current_time_cutoff)
        self.enqueue_background_determinations(user_session_record, background_scoring_layers)
        if not live_scoring_layer:
            return False, None
        job = self.run_live_determination(user_session_record, live_scoring_layer)
        return True, job
