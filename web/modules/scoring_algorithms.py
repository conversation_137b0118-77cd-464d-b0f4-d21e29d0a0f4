from utils import logged
from app import db_session, app
from db import (
    user_session,
    scoring_layer_determination,
    scoring_artifact,
    scoring_layer,
    scoring_layer_thresholds,
)
import time
from constants import (
    LOW_INTENT_LABEL,
    HIGH_INTENT_LABEL,
    MEDIUM_INTENT_LABEL,
    LOW_UPLIFT_LABEL,
    HIGH_UPLIFT_LABEL,
    DECISION_ALGORITHM_INTENT_AND_UPLIFT,
    DECISION_ALGORITHM_INTENT_ONLY,
    DECISION_ALGORITHM_THRESHOLD_UPLIFT,
    DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED,
    DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH,
    SCORING_ARTIFACT_TYPE_INTENT,
    SCORING_ARTIFACT_TYPE_UPLIFT_CTL,
    SCORING_ARTIFACT_TYPE_UPLIFT_TRT,
    SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY,
    DETERMINATION_TYPE_INTENT,
    DETERMINATION_TYPE_UPLIFT,
    PIPELINE_FILE_KEYNAME,
    INCONCLUSIVE_INTENT_LABEL,
)

from .predictor_helper import get_prediction_from_sagemaker,get_prediction_from_sagemaker_uplift_stg2,  get_payload


@logged
class ScoringLogic:
    @classmethod
    def make_determination(cls, user_session_id, scoring_layer_id, live_version=True, persist_determination=True):
        raise NotImplementedError("This method should be implemented in the subclass")

    @staticmethod
    def get_prediction_from_artifact(scoring_artifact, user_session_id, time_cutoff):
        data_filename = scoring_artifact.artifact_metadata[PIPELINE_FILE_KEYNAME]
        is_empty, payload = get_payload(data_filename, user_session_id, time_cutoff)  # use cache if not live version
        if is_empty:
            raise ValueError(f"No data found for user_session_id {user_session_id} and filename {data_filename}")
        prediction_value = get_prediction_from_sagemaker(scoring_artifact.deployment_external_reference, payload)
        return prediction_value
    


class IntentScoringLayerLogic(ScoringLogic):

    algorithm_version = "v1"

    @classmethod
    def make_determination(cls, user_session_id, scoring_layer_id):
        with app.app_context():
            scoring_layer_record = scoring_layer.query.filter_by(uuid=scoring_layer_id).first()
            user_session_record = user_session.query.filter_by(uuid=user_session_id).first()
            scoring_layer_thresholds_record = scoring_layer_thresholds.query.filter_by(
                scoring_layer_id=scoring_layer_id, 
                store_id=user_session_record.store_id,
                active=True
            ).order_by(scoring_layer_thresholds.time.desc()).first()

            if scoring_layer_thresholds_record is None:
                raise ValueError(f"No active thresholds found for scoring_layer_id {scoring_layer_id} and store_id {user_session_record.store_id}")
            
            intent_scoring_artifact_id = scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_INTENT]
            intent_score_artifact = scoring_artifact.query.filter_by(uuid=intent_scoring_artifact_id).first()
            # get prediction
            prediction_value = cls.get_prediction_from_artifact(
                intent_score_artifact, user_session_id, scoring_layer_record.scoring_time_cutoff
            )
            # determine intent
            prediction_determination = HIGH_INTENT_LABEL
            low_med_threshold = scoring_layer_thresholds_record.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_ONLY][DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED]
            
            if low_med_threshold == -1:
                prediction_determination = INCONCLUSIVE_INTENT_LABEL
            elif prediction_value < low_med_threshold:
                prediction_determination = LOW_INTENT_LABEL
            elif (
                DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH
                in scoring_layer_thresholds_record.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_ONLY].keys()
                and prediction_value
                < scoring_layer_thresholds_record.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_ONLY][DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH]
            ):
                prediction_determination = MEDIUM_INTENT_LABEL

            # persist determination
            scoring_layer_determination_record = scoring_layer_determination(
                time=int(time.time()),
                scoring_layer_id=scoring_layer_id,
                scoring_layer_thresholds_id=scoring_layer_thresholds_record.uuid,
                user_session_id=user_session_id,
                scoring_layer_scores={DETERMINATION_TYPE_INTENT: prediction_value},
                determination=prediction_determination,
                scoring_algorithm_version=cls.algorithm_version,
            )
            db_session.add(scoring_layer_determination_record)
            db_session.commit()
            return prediction_value, prediction_determination, scoring_layer_determination_record.uuid


class IntentUpliftOnlyScoringLayerLogic(ScoringLogic):
    
    algorithm_version = "v1"

    @staticmethod
    def get_prediction_from_uplift_trt_artifact(scoring_artifact, user_session_id, time_cutoff):
        data_filename = scoring_artifact.artifact_metadata[PIPELINE_FILE_KEYNAME]
        is_empty, payload = get_payload(data_filename, user_session_id, time_cutoff)  # use cache if not live version
        if is_empty:
            raise ValueError(f"No data found for user_session_id {user_session_id} and filename {data_filename}")
        prediction_value = get_prediction_from_sagemaker_uplift_stg2(scoring_artifact.deployment_external_reference, payload)
        return prediction_value
    
    @classmethod
    def make_determination(cls, user_session_id, scoring_layer_id, decision_algorithm_type=DECISION_ALGORITHM_INTENT_AND_UPLIFT):
        cls.logger.info(f"Making uplift determination for user_session_id {user_session_id} and scoring_layer_id {scoring_layer_id}")
        with app.app_context():
            scoring_layer_record = scoring_layer.query.filter_by(uuid=scoring_layer_id).first()
            user_session_record = user_session.query.filter_by(uuid=user_session_id).first()
            scoring_layer_thresholds_record = scoring_layer_thresholds.query.filter_by(
                scoring_layer_id=scoring_layer_id, 
                store_id=user_session_record.store_id,
                active=True
            ).order_by(scoring_layer_thresholds.time.desc()).first()

            if scoring_layer_thresholds_record is None:
                raise ValueError(f"No active thresholds found for scoring_layer_id {scoring_layer_id} and store_id {user_session_record.store_id}")
            
            # get scoring artifacts
            ctl_scoring_artifact_id = scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_CTL]
            trt_scoring_artifact_id = scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_TRT]
            propensity_scoring_artifact_id = scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY]
            scoring_artifact_stage2_ctl_record = scoring_artifact.query.filter_by(uuid=ctl_scoring_artifact_id).first()
            scoring_artifact_stage2_trt_record = scoring_artifact.query.filter_by(uuid=trt_scoring_artifact_id).first()
            scoring_artifact_propensity_record = scoring_artifact.query.filter_by(uuid=propensity_scoring_artifact_id).first()
            # get predictions CTL
            prediction_value_stage2_ctl = cls.get_prediction_from_uplift_trt_artifact(
                scoring_artifact_stage2_ctl_record, user_session_id, scoring_layer_record.scoring_time_cutoff
            )
            # trt
            prediction_value_stage2_trt = cls.get_prediction_from_uplift_trt_artifact(
                scoring_artifact_stage2_trt_record, user_session_id, scoring_layer_record.scoring_time_cutoff
            )
            # propensity
            prediction_value_propensity = cls.get_prediction_from_artifact(
                scoring_artifact_propensity_record, user_session_id, scoring_layer_record.scoring_time_cutoff
            )
            # calculate uplift
            prediction_value = (
                prediction_value_propensity * prediction_value_stage2_trt
                + (1 - prediction_value_propensity) * prediction_value_stage2_ctl
            )

            # determine uplift
            prediction_determination = LOW_UPLIFT_LABEL
            uplift_threshold = scoring_layer_thresholds_record.scoring_artifact_thresholds[decision_algorithm_type][DECISION_ALGORITHM_THRESHOLD_UPLIFT]
            
            if uplift_threshold == -1:
                prediction_determination = INCONCLUSIVE_INTENT_LABEL
            elif prediction_value > uplift_threshold:
                prediction_determination = HIGH_UPLIFT_LABEL

            determination_scores = {
                DETERMINATION_TYPE_UPLIFT: prediction_value,
                SCORING_ARTIFACT_TYPE_UPLIFT_CTL: prediction_value_stage2_ctl,
                SCORING_ARTIFACT_TYPE_UPLIFT_TRT: prediction_value_stage2_trt,
                SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY: prediction_value_propensity,
            }
            # persist determination
            scoring_layer_determination_record = scoring_layer_determination(
                time=int(time.time()),
                scoring_layer_id=scoring_layer_id,
                scoring_layer_thresholds_id=scoring_layer_thresholds_record.uuid,
                user_session_id=user_session_id,
                scoring_layer_scores=determination_scores,
                determination=prediction_determination,
                scoring_algorithm_version=cls.algorithm_version,
            )
            db_session.add(scoring_layer_determination_record)
            db_session.commit()
            return prediction_value, prediction_determination, scoring_layer_determination_record.uuid

class CappedUpliftScoringLayerLogic(ScoringLogic):
    
    algorithm_version = "v1"

    @staticmethod
    def get_prediction_from_uplift_trt_artifact(scoring_artifact, user_session_id, time_cutoff):
        data_filename = scoring_artifact.artifact_metadata[PIPELINE_FILE_KEYNAME]
        is_empty, payload = get_payload(data_filename, user_session_id, time_cutoff)  # use cache if not live version
        if is_empty:
            raise ValueError(f"No data found for user_session_id {user_session_id} and filename {data_filename}")
        prediction_value = get_prediction_from_sagemaker_uplift_stg2(scoring_artifact.deployment_external_reference, payload)
        return prediction_value
    
    @classmethod
    def get_prediction_from_intent_artifact(cls, scoring_artifact, scoring_layer_thresholds_record, user_session_id, time_cutoff):
        # get prediction
        prediction_value = cls.get_prediction_from_artifact(
            scoring_artifact, user_session_id, time_cutoff
        )
        # determine intent
        prediction_determination = HIGH_INTENT_LABEL
        low_med_threshold = scoring_layer_thresholds_record.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_AND_UPLIFT][DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED]
        
        if low_med_threshold == -1:
            prediction_determination = INCONCLUSIVE_INTENT_LABEL
        elif prediction_value < low_med_threshold:
            prediction_determination = LOW_INTENT_LABEL
        elif (
            DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH
            in scoring_layer_thresholds_record.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_AND_UPLIFT].keys()
            and prediction_value
            < scoring_layer_thresholds_record.scoring_artifact_thresholds[DECISION_ALGORITHM_INTENT_AND_UPLIFT][DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH]
        ):
            prediction_determination = MEDIUM_INTENT_LABEL
        return prediction_value, prediction_determination

    
    @classmethod
    def make_determination(cls, user_session_id, scoring_layer_id, decision_algorithm_type=DECISION_ALGORITHM_INTENT_AND_UPLIFT):
        cls.logger.info(f"Making uplift determination for user_session_id {user_session_id} and scoring_layer_id {scoring_layer_id}")
        with app.app_context():
            scoring_layer_record = scoring_layer.query.filter_by(uuid=scoring_layer_id).first()
            user_session_record = user_session.query.filter_by(uuid=user_session_id).first()
            scoring_layer_thresholds_record = scoring_layer_thresholds.query.filter_by(
                scoring_layer_id=scoring_layer_id, 
                store_id=user_session_record.store_id,
                active=True
            ).order_by(scoring_layer_thresholds.time.desc()).first()

            if scoring_layer_thresholds_record is None:
                raise ValueError(f"No active thresholds found for scoring_layer_id {scoring_layer_id} and store_id {user_session_record.store_id}")
            
            #get intent artifact
            intent_scoring_artifact_id = scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_INTENT]
            intent_score_artifact = scoring_artifact.query.filter_by(uuid=intent_scoring_artifact_id).first()
            # get prediction
            prediction_value_intent, prediction_determination_intent = cls.get_prediction_from_intent_artifact(
                intent_score_artifact, scoring_layer_thresholds_record, user_session_id, scoring_layer_record.scoring_time_cutoff
            )
            prediction_determination = prediction_determination_intent
            prediction_value = prediction_value_intent
            determination_scores = {
                DETERMINATION_TYPE_INTENT: prediction_value_intent,
            }
            #only do uplift for medium and low intent, or inconclusive intent (will be ignored later)
            if prediction_determination_intent != HIGH_INTENT_LABEL:
                # get scoring artifacts
                ctl_scoring_artifact_id = scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_CTL]
                trt_scoring_artifact_id = scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_TRT]
                propensity_scoring_artifact_id = scoring_layer_record.scoring_artifacts[SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY]
                scoring_artifact_stage2_ctl_record = scoring_artifact.query.filter_by(uuid=ctl_scoring_artifact_id).first()
                scoring_artifact_stage2_trt_record = scoring_artifact.query.filter_by(uuid=trt_scoring_artifact_id).first()
                scoring_artifact_propensity_record = scoring_artifact.query.filter_by(uuid=propensity_scoring_artifact_id).first()
                # get predictions CTL
                prediction_value_stage2_ctl = cls.get_prediction_from_uplift_trt_artifact(
                    scoring_artifact_stage2_ctl_record, user_session_id, scoring_layer_record.scoring_time_cutoff
                )
                # trt
                prediction_value_stage2_trt = cls.get_prediction_from_uplift_trt_artifact(
                    scoring_artifact_stage2_trt_record, user_session_id, scoring_layer_record.scoring_time_cutoff
                )
                # propensity
                prediction_value_propensity = cls.get_prediction_from_artifact(
                    scoring_artifact_propensity_record, user_session_id, scoring_layer_record.scoring_time_cutoff
                )
                # calculate uplift
                prediction_value = (
                    prediction_value_propensity * prediction_value_stage2_trt
                    + (1 - prediction_value_propensity) * prediction_value_stage2_ctl
                )
                # determine uplift
                prediction_determination = LOW_UPLIFT_LABEL
                uplift_threshold = scoring_layer_thresholds_record.scoring_artifact_thresholds[decision_algorithm_type][DECISION_ALGORITHM_THRESHOLD_UPLIFT]
                
                #if intent is inconclusive, don't change the determination
                if prediction_determination_intent != INCONCLUSIVE_INTENT_LABEL:
                    if uplift_threshold == -1:
                        prediction_determination = INCONCLUSIVE_INTENT_LABEL
                    elif prediction_value > uplift_threshold:
                        prediction_determination = HIGH_UPLIFT_LABEL

                determination_scores.update({
                    DETERMINATION_TYPE_UPLIFT: prediction_value,
                    SCORING_ARTIFACT_TYPE_UPLIFT_CTL: prediction_value_stage2_ctl,
                    SCORING_ARTIFACT_TYPE_UPLIFT_TRT: prediction_value_stage2_trt,
                    SCORING_ARTIFACT_TYPE_UPLIFT_PROPENSITY: prediction_value_propensity,
                })
            # persist determination
            scoring_layer_determination_record = scoring_layer_determination(
                time=int(time.time()),
                scoring_layer_id=scoring_layer_id,
                scoring_layer_thresholds_id=scoring_layer_thresholds_record.uuid,
                user_session_id=user_session_id,
                scoring_layer_scores=determination_scores,
                determination=prediction_determination,
                scoring_algorithm_version=cls.algorithm_version,
            )
            db_session.add(scoring_layer_determination_record)
            db_session.commit()
            return prediction_value, prediction_determination, scoring_layer_determination_record.uuid
