import time
from app import db_session
from db import (
    scoring_layer,
    scoring_layer_thresholds,
    intervention_type,
    scoring_layer_determination,
    store_url,
    store,
    store_intervention_association,
    store_intervention_event,
    user_session
)
from constants import (
    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
    DECISION_ALGORITHM_INTENT_ONLY,
    DECISION_ALGORITHM_INTENT_AND_UPLIFT,
    DECISION_ALGORITHM_UPLIFT_ONLY,
    DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED,
    DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH,
    DECISION_ALGORITHM_THRESHOLD_UPLIFT,
    STORE_STATUS_UNINSTALLED,
    INTERVENTION_TYPE_SAVINGS_NUDGE
)
from . import intervention_orchestration
from sqlalchemy import and_, func
from datetime import datetime, timedelta
import numpy as np
from utils import get_generic_logger
from task_queue import ASYNC_DATA_PROCESSING_QUEUE
import uuid

logger = get_generic_logger(__name__)

def schedule_next_threshold_optimization(store_id):
    """
    Schedules the next threshold optimization run for a store.
    Will run 6 hours from now.
    
    Args:
        store_id: UUID of the store to schedule optimization for
    """
    scheduled_time = datetime.now() + timedelta(hours=6)
    
    # Schedule the task
    ASYNC_DATA_PROCESSING_QUEUE.enqueue_at(
        scheduled_time,
        update_intent_thresholds_based_on_show_rate_optimization,
        args=(store_id,),
        job_id=f"optimize_thresholds_{store_id}",
        job_timeout=300  # 5 minute timeout
    )
    
    logger.info(
        f"Scheduled next threshold optimization for store {store_id} at {scheduled_time}"
    )

def setup_store_intervention_defaults(store_record):
    """
    Sets up default intervention associations and scoring thresholds for a newly created store.
    
    For new stores, we want to:
    1. Create an intent-based discount intervention association with 100% holdout
    2. Set all scoring thresholds to -1 to mark determinations as inconclusive
    3. Schedule a task to optimize thresholds after 6 hours of data collection
    
    This ensures new stores start in a safe state where:
    - All users are in holdout (no discounts shown)
    - All scoring determinations are marked inconclusive
    - Thresholds are automatically optimized once sufficient data is collected
    
    Args:
        store_record: The newly created store record
    """
    # Create intervention association with 100% holdout
    intervention_type_record = intervention_type.query.filter_by(
        name=INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
    ).first()
    
    if not intervention_type_record:
        intervention_type_record = intervention_orchestration.create_intervention_type(
            INTERVENTION_TYPE_INTENT_BASED_DISCOUNT
        )
    
    store_intervention = intervention_orchestration.create_store_intervention_association(
        store_record,
        intervention_type_record
    )
    # Set 100% holdout via parameters
    store_intervention.parameters = {'holdout_percentage': 0.5, "anti_holdout_percentage": 0.05}
    db_session.commit()
    # do the same for the savings nudge
    intervention_type_record = intervention_type.query.filter_by(
        name=INTERVENTION_TYPE_SAVINGS_NUDGE
    ).first()
    if not intervention_type_record:
        intervention_type_record = intervention_orchestration.create_intervention_type(
            INTERVENTION_TYPE_SAVINGS_NUDGE
        )
    store_intervention = intervention_orchestration.create_store_intervention_association(
        store_record,
        intervention_type_record
    )
    store_intervention.parameters = {'holdout_percentage': 0.2}
    db_session.commit()
    # Create scoring thresholds for all active scoring layers
    setup_scoring_thresholds_for_store(store_record)
    
    # Schedule first threshold optimization
    schedule_next_threshold_optimization(store_record.uuid)

def setup_scoring_thresholds_for_store(store_record):
    """
    Creates scoring thresholds for all active scoring layers for a given store.
    
    Sets all thresholds to -1 to mark determinations as inconclusive. This is done for:
    - Intent-only scoring layers
    - Intent-and-uplift scoring layers  
    - Uplift-only scoring layers
    
    Only processes non-store-specific scoring layers that are active and running predictions.
    
    Args:
        store_record: The store record to create thresholds for
    """
    # Get all active, non-store-specific scoring layers
    active_scoring_layers = scoring_layer.query.filter_by(
        active=True,
        run_predictions=True,
        store_specific=False
    ).all()
    
    for layer in active_scoring_layers:
        # Set any existing thresholds to inactive
        scoring_layer_thresholds.query.filter_by(
            scoring_layer_id=layer.uuid,
            store_id=store_record.uuid,
            active=True
        ).update({"active": False})
        db_session.commit()
        
        # Create new thresholds based on layer type
        thresholds = {}
        if layer.scoring_algorithm_type == DECISION_ALGORITHM_INTENT_ONLY:
            thresholds = {
                DECISION_ALGORITHM_INTENT_ONLY: {
                    DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED: -1,
                    DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH: -1
                }
            }
        elif layer.scoring_algorithm_type == DECISION_ALGORITHM_INTENT_AND_UPLIFT:
            thresholds = {
                DECISION_ALGORITHM_INTENT_AND_UPLIFT: {
                    DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED: -1,
                    DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH: -1,
                    DECISION_ALGORITHM_THRESHOLD_UPLIFT: -1
                }
            }
        elif layer.scoring_algorithm_type == DECISION_ALGORITHM_UPLIFT_ONLY:
            thresholds = {
                DECISION_ALGORITHM_UPLIFT_ONLY: {
                    DECISION_ALGORITHM_THRESHOLD_UPLIFT: -1
                }
            }
        
        # Create threshold record
        if thresholds:
            threshold_record = scoring_layer_thresholds(
                scoring_layer_id=layer.uuid,
                store_id=store_record.uuid,
                time=time.time(),
                active=True,
                scoring_artifact_thresholds=thresholds
            )
            db_session.add(threshold_record)
            db_session.commit()

def update_intent_thresholds_based_on_show_rate_optimization(store_id):
    """
    Updates scoring thresholds for intent-only models based on historical scoring data.
    Skips processing and breaks optimization cycle if store has been uninstalled 
    (no active store_urls).
    
    Args:
        store_id: UUID of the store to update thresholds for
    
    Returns:
        tuple: (updates_made: dict, needs_more_data: bool)
        - updates_made: Dictionary of {layer_id: new_threshold}
        - needs_more_data: Boolean indicating if any layers need more data collection
    """
    try:
        # Check if store has been uninstalled
        store_url_exists = store_url.query.filter_by(store_id=store_id).first()
        store_record = store.query.filter_by(uuid=store_id).first()

        if not store_url_exists or store_record is None or store_record.status == STORE_STATUS_UNINSTALLED:
            logger.info(f"Store {store_id} has been uninstalled, breaking optimization cycle")
            return {}, False

        

        # Get qualifying scoring layers
        active_layers = scoring_layer.query.filter(
            and_(
                scoring_layer.active == True,
                scoring_layer.run_predictions == True,
                scoring_layer.live_decisioning == True,
                scoring_layer.store_specific == False,
                scoring_layer.scoring_algorithm_type == DECISION_ALGORITHM_INTENT_ONLY
            )
        ).all()
        
        if not active_layers:
            logger.info(f"No qualifying scoring layers found for store {store_id}")
            return {}, False
            
        thirty_days_ago = int((datetime.now() - timedelta(days=30)).timestamp())
        updates_made = {}
        needs_more_data = False  # Track if any layer needs more data
        
        for layer in active_layers:
            # Get current active threshold for this layer
            current_threshold = scoring_layer_thresholds.query.filter_by(
                scoring_layer_id=layer.uuid,
                store_id=store_id,
                active=True
            ).first()
            
            # Check if threshold qualifies for update
            should_update = False
            if current_threshold:
                threshold_value = current_threshold.scoring_artifact_thresholds.get(
                    DECISION_ALGORITHM_INTENT_ONLY, {}
                ).get(DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED)
                
                if (current_threshold.threshold_methodology == 'show_30_percent' or 
                    threshold_value == -1):
                    should_update = True
                else:
                    logger.info(
                        f"Skipping layer {layer.uuid} - current threshold uses different methodology: "
                        f"{current_threshold.threshold_methodology}"
                    )
                    continue
            else:
                should_update = True
            
            if not should_update:
                continue
                
            # Get historical determinations
            determinations = scoring_layer_determination.query\
                .join(user_session, scoring_layer_determination.user_session_id == user_session.uuid)\
                .filter(
                    and_(
                        scoring_layer_determination.scoring_layer_id == layer.uuid,
                        user_session.store_id == store_id,
                        scoring_layer_determination.time >= thirty_days_ago
                    )
                ).all()
            
            if not determinations:
                logger.info(f"No determinations found for layer {layer.uuid}")
                needs_more_data = True
                continue
                
            # Check if we have enough determinations
            if len(determinations) < 100:
                logger.info(f"Insufficient determinations ({len(determinations)}) for layer {layer.uuid}. Need at least 100.")
                needs_more_data = True
                continue
                
            # Extract intent scores
            intent_scores = [
                det.scoring_layer_scores.get('intent', None) 
                for det in determinations 
                if det.scoring_layer_scores and 'intent' in det.scoring_layer_scores
            ]
            
            if not intent_scores:
                logger.info(f"No valid intent scores found for layer {layer.uuid}")
                needs_more_data = True
                continue
                
            # Check if we have enough valid scores
            if len(intent_scores) < 100:
                logger.info(f"Insufficient valid scores ({len(intent_scores)}) for layer {layer.uuid}. Need at least 100.")
                needs_more_data = True
                continue
                
            # Calculate 30th percentile
            threshold = float(np.percentile(intent_scores, 30))
            logger.info(f"Calculated threshold {threshold} for layer {layer.uuid}")
            
            # Deactivate current thresholds
            if current_threshold:
                current_threshold.active = False
                db_session.commit()
            
            # Create new threshold record
            new_threshold = scoring_layer_thresholds(
                scoring_layer_id=layer.uuid,
                store_id=store_id,
                time=time.time(),
                active=True,
                scoring_artifact_thresholds={
                    DECISION_ALGORITHM_INTENT_ONLY: {
                        DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED: threshold                    
                    }
                },
                threshold_methodology="show_30_percent"
            )
            db_session.add(new_threshold)
            db_session.commit()
            
            updates_made[layer.uuid] = threshold
            logger.info(f"Updated thresholds for layer {layer.uuid} with value {threshold}")
            
        # Schedule next optimization if updates were made OR if we need more data
        if updates_made or needs_more_data:
            schedule_next_threshold_optimization(store_id)
            if updates_made:
                logger.info(f"Updates made for store {store_id}, scheduled next optimization")
            else:
                logger.info(f"No updates made but layers need more data for store {store_id}, scheduled next optimization")
        else:
            logger.info(f"No updates needed and no layers pending data collection for store {store_id}, stopping optimization cycle")
        
        return updates_made, needs_more_data
        
    except Exception as e:
        logger.error(f"Error updating intent thresholds: {str(e)}")
        db_session.rollback()
        raise 

def deactivate_store_settings(store_id):
    """
    Deactivates all store-specific settings when a store uninstalls the app.
    This includes:
    1. All store intervention associations (with event logging)
    2. All scoring layer thresholds
    
    Args:
        store_id: UUID of the store being uninstalled
        
    Returns:
        tuple: (success: bool, error_message: str or None)
    """
    try:
        current_time = int(time.time())
        
        # Deactivate all store intervention associations
        store_interventions = store_intervention_association.query.filter_by(
            store_id=store_id,
            active=True
        ).all()
        
        for intervention in store_interventions:
            intervention.active = False
            
            # Log deactivation event
            event = store_intervention_event(
                uuid=str(uuid.uuid4()),
                time=current_time,
                store_intervention_association_id=intervention.uuid,
                event='set_to_inactive'
            )
            db_session.add(event)
        
        # Deactivate all scoring layer thresholds
        store_thresholds = scoring_layer_thresholds.query.filter_by(
            store_id=store_id,
            active=True
        ).all()
        
        for threshold in store_thresholds:
            threshold.active = False
            
        db_session.commit()
        
        logger.info(
            f"Deactivated {len(store_interventions)} interventions and "
            f"{len(store_thresholds)} thresholds for store {store_id}"
        )
        
        return True, None
        
    except Exception as e:
        error_msg = f"Error deactivating store settings: {str(e)}"
        logger.error(error_msg)
        db_session.rollback()
        return False, error_msg 