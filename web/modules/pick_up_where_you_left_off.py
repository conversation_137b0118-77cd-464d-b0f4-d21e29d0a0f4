from flask import request, jsonify
from datetime import datetime, timedelta
from pytz import UTC

from db import page_view, user_session, db

# interaction metrics

def get_latest_items_handler():
    previous_session_converted = was_previous_session_converted()
    
    if not previous_session_converted:
        items = get_latest_items()
    else:
        items = []
        
    return jsonify({
        "items": items,
        "success": True
    })
    
def was_previous_session_converted():
    session_cookie = request.args.get("session_cookie")
    customer_cookie = request.args.get("customer_cookie")
    
    user_session_record = user_session.query.filter(
        db.and_(
            user_session.customer_cookie == customer_cookie,
            user_session.session_cookie != session_cookie,
        )
    ).order_by(user_session.time.desc()).first()
    
    if not user_session_record:
        return False
    
    return user_session_record.conversion

def get_latest_items():
    customer_cookie = request.args.get("customer_cookie")
    seven_days_ago = datetime.now(UTC) - timedelta(days=7)

    page_views = (page_view.query
        .join(user_session, page_view.user_session_id == user_session.uuid)
        .filter(user_session.customer_cookie == customer_cookie)
        .filter(user_session.conversion is not True)
        .filter(page_view.page.like('%/products/%'))
        .filter(db.func.to_timestamp(page_view.time) >= seven_days_ago)
        .with_entities(page_view.page, db.func.max(page_view.time).label('latest_time'))
        .group_by(page_view.page)
        .order_by(db.func.max(page_view.time).desc())
        .all())

    # Extract base product slugs without URL parameters
    all_products = [pv[0].split('/products/')[-1].split('?')[0] for pv in page_views]
    # Get unique list of products, preserve list order
    unique_products = list(dict.fromkeys(all_products))
    # Return first 3
    return unique_products[:3]

