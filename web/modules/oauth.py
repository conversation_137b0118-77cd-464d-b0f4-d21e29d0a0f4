from flask import request, redirect, abort, current_app, jsonify
from db import store, store_url, webhook_request, intervention_type, scoring_layer, scoring_layer_thresholds, onboarding_details
from . import intervention_orchestration
from app import app, db_session
from config import APP_ENVIRONMENT
from helpers import create_webhook
from constants import (
    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
    DECISION_ALGORITHM_INTENT_ONLY,
    DECISION_ALGORITHM_INTENT_AND_UPLIFT,
    DECISION_ALGORITHM_UPLIFT_ONLY,
    DECISION_ALGORITHM_THRESHOLD_INTENT_LOW_MED,
    DECISION_ALGORITHM_THRESHOLD_INTENT_MED_HIGH,
    DECISION_ALGORITHM_THRESHOLD_UPLIFT,
    STORE_STATUS_UNINSTALLED,
    STORE_STATUS_ACTIVE
)
from modules.scoring_threshold_use_cases import setup_store_intervention_defaults, deactivate_store_settings

import json
from pyactiveresource.connection import ServerError, UnauthorizedAccess
import shopify
import time
import uuid
import requests

OAUTH_SCOPES = [
    "read_products",
    "write_products",
    "read_orders",
    "write_orders",
    "read_price_rules",
    "write_price_rules",
    "read_discounts",
    "write_discounts",
    "read_themes",
    "read_checkouts",
    "write_checkouts"
]

def app_install_handler():
    shop_url = request.args.get('shop')
    if not shop_url:
        return 'No shop found'
    
    shopify.Session.setup(
        api_key=app.config['SHOPIFY_API_KEY'],
        secret=app.config['SHOPIFY_SHARED_SECRET']
    )
    # URL must be format SHOP_NAME.myshopify.com
    session = shopify.Session(shop_url, app.config['SHOPIFY_LEGACY_API_VERSION'])

    url_root = request.url_root
    if APP_ENVIRONMENT != "local":
        url_root = url_root.replace("http://", "https://")
    
    permission_url = session.create_permission_url(
        OAUTH_SCOPES,
        url_root + "install_finalize"
    )

    return redirect(permission_url)

#def post_install_account_setup_handler():
def install_finalize_handler():
    """Extract store info from Shopify JWT and return shop details"""
    auth_header = request.headers.get("Authorization")
    token = request.args.get('id_token')

    if not auth_header and not token:
        return jsonify({"error": "Missing Authorization Header"}), 401

    if auth_header:
        token = auth_header.replace("Bearer ", "")

    shop_url = request.args.get("shop")
    if not shop_url:
        return jsonify({"error": "Missing Shop Url Param"}), 401
    
    response = requests.post(f'https://{shop_url}/admin/oauth/access_token', data={
        "client_id": app.config['SHOPIFY_API_KEY'],
        "client_secret": app.config['SHOPIFY_SHARED_SECRET'],
        "grant_type": "urn:ietf:params:oauth:grant-type:token-exchange",
        "subject_token": token,
        "subject_token_type": "urn:ietf:params:oauth:token-type:id_token",
        "requested_token_type": "urn:shopify:params:oauth:token-type:offline-access-token"
    })
    if response.status_code != 200:
         return jsonify({"error": "error"})

    data = response.json()

    installer_response = requests.post(f'https://{shop_url}/admin/oauth/access_token', data={
        "client_id": app.config['SHOPIFY_API_KEY'],
        "client_secret": app.config['SHOPIFY_SHARED_SECRET'],
        "grant_type": "urn:ietf:params:oauth:grant-type:token-exchange",
        "subject_token": token,
        "subject_token_type": "urn:ietf:params:oauth:token-type:id_token",
        "requested_token_type": "urn:shopify:params:oauth:token-type:online-access-token"
    })

    installer_data = installer_response.json()

    shopify.Session.setup(
        api_key=app.config['SHOPIFY_API_KEY'],
        secret=app.config['SHOPIFY_SHARED_SECRET']
    )
    session = shopify.Session(shop_url, app.config['SHOPIFY_LEGACY_API_VERSION'], data.get('access_token'))
    
    shopify.ShopifyResource.activate_session(session)
    shop = shopify.Shop.current()
    
    # Get merchant location data
    us_only = True
    shipping_zones = shopify.ShippingZone.find()
    for zone in shipping_zones:
        if not us_only:
            break
        
        for country in zone.countries:
            if country.name != "United States":
                us_only = False
                break

    locations = shopify.Location.find()
    for location in locations:
        if location.country != "United States":
            us_only = False
            break
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is not None:
        new_store = store.query.filter_by(uuid=store_url_record.store_id).first()
    else:
        new_store = store()
        new_store.uuid = str(uuid.uuid4())
        new_store.time = time.time()
        db_session.add(new_store)

        new_store_url = store_url()
        new_store_url.uuid = str(uuid.uuid4())
        new_store_url.time = time.time()
        new_store_url.default = True
        new_store_url.store_id = new_store.uuid
        new_store_url.url = shop_url
        db_session.add(new_store_url)
        
        

    new_store.access_token = data.get('access_token')
    new_store.email = shop.email
    new_store.shop_owner = shop.shop_owner
    new_store.name = shop.name
    new_store.address1 = shop.address1
    new_store.address2 = shop.address2
    new_store.city = shop.city
    new_store.state = shop.province
    new_store.zip = shop.zip
    new_store.status = STORE_STATUS_ACTIVE
    new_store.vandra_admin_show_discount = True
    new_store.show_discount=False
    db_session.commit()
    # Set up intervention defaults for new stores
    setup_store_intervention_defaults(new_store)

    # Register our webhooks
    check_registered_webhooks(new_store)

    # Create onboarding
    new_onboarding_details = onboarding_details()
    new_onboarding_details.uuid = str(uuid.uuid4())
    new_onboarding_details.store_url = shop_url
    new_onboarding_details.installer_first_name = installer_data.get('associated_user').get('first_name')
    new_onboarding_details.installer_last_name = installer_data.get('associated_user').get('last_name')
    new_onboarding_details.installer_email = installer_data.get('associated_user').get('email')
    db_session.add(new_onboarding_details)

    new_store.onboarding_details = new_onboarding_details.uuid
    db_session.commit()

    return jsonify({"details": new_onboarding_details.to_dict()})

def check_registered_webhooks(store_record):
    if store_record is None:
        return False

    # Don't check webhooks more than once an hour
    if store_record.last_webhook_check is not None and store_record.last_webhook_check > time.time() - 3600:
        return False
    
    if store_record.access_token in [None, ""]:
        return False
    
    shopify.Session.setup(
        api_key=app.config['SHOPIFY_API_KEY'],
        secret=app.config['SHOPIFY_SHARED_SECRET']
    )
    store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
        order_by(store_url.default.desc()).first()
    if store_url_record is None:
        return False

    session = shopify.Session(store_url_record.url, app.config['SHOPIFY_LEGACY_API_VERSION'], store_record.access_token)
    shopify.ShopifyResource.activate_session(session)
    
    # Shopify API occassionally has 405 or 401 errors we need to deal with
    try:
        webhooks = shopify.Webhook.find()
    except (ServerError, UnauthorizedAccess) as e:
        return False
    
    topics = [x.topic for x in webhooks]
    url_root = request.url_root
    if APP_ENVIRONMENT != "local":
        url_root = url_root.replace("http://", "https://")
    if "app/uninstalled" not in topics:
        try:
            create_webhook(shopify, url_root + "app_uninstalled", "app/uninstalled")
        except Exception as e:
            current_app.logger.error(e)
    if "orders/create" not in topics:
        try:
            create_webhook(shopify, url_root + "new_order_webhook", "orders/create")
        except Exception as e:
            current_app.logger.error(e)
    if "carts/update" not in topics:
        try:
            create_webhook(shopify, url_root + "record_cart_change", "carts/update")
        except Exception as e:
            current_app.logger.error(e)
    if "checkouts/create" not in topics:
        try:
            create_webhook(shopify, url_root + "record_checkout_create", "checkouts/create")
        except Exception as e:
            current_app.logger.error(e)
    if "checkouts/update" not in topics:
        try:
            create_webhook(shopify, url_root + "record_checkout_update", "checkouts/update")
        except Exception as e:
            current_app.logger.error(e)
    
    store_record.last_webhook_check = time.time()
    db_session.commit()

def check_access_scopes(store_record):
    if store_record is None:
        return False

    shopify.Session.setup(
        api_key=app.config['SHOPIFY_API_KEY'],
        secret=app.config['SHOPIFY_SHARED_SECRET']
    )
    store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
        order_by(store_url.default.desc()).first()
    if store_url_record is None:
        return False

    session = shopify.Session(store_url_record.url, app.config['SHOPIFY_LEGACY_API_VERSION'], store_record.access_token)
    shopify.ShopifyResource.activate_session(session)
    scope_set = {x.handle for x in shopify.AccessScope.find()}
    url_root = request.url_root
    if APP_ENVIRONMENT != "local":
        url_root = url_root.replace("http://", "https://")
    for scope in OAUTH_SCOPES:
        if scope not in scope_set:
            return session.create_permission_url(
                OAUTH_SCOPES,
                url_root + "install_finalize"
            )
            '''return session.create_permission_url(
                OAUTH_SCOPES,
                "https://" + store_url_record.url + "/admin/apps/" + app.config["SHOPIFY_API_KEY"]
            )'''
    
    return None

def app_uninstalled_handler():
    # https://shopify.dev/docs/admin-api/rest/reference/events/webhook?api[version]=2020-04

    from helpers import process_webhook_request
    event, shop_url = process_webhook_request()

    if not event:
        return abort(401)
    
    request_data = request.get_json()
    
    new_webhook_request = webhook_request()
    new_webhook_request.uuid = str(uuid.uuid4())
    new_webhook_request.time = time.time()
    new_webhook_request.event = event
    new_webhook_request.shop_url = shop_url
    new_webhook_request.data = json.dumps(request_data)
    db_session.add(new_webhook_request)
    db_session.commit()

    shopify_domain = request_data["domain"]
    current_app.logger.info(f"Shopify domain {shopify_domain} uninstalled")

    store_url_record = store_url.query.filter_by(url=shopify_domain).first()
    if store_url_record is not None:
        store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
        if store_record is not None:
            store_record.active_store = False
            store_record.charge_id = None
            store_record.has_first_visit = False
            store_record.subscription_plan = None
            store_record.access_token = None
            store_record.last_webhook_check = None
            store_record.status = STORE_STATUS_UNINSTALLED
            details = onboarding_details.query.filter_by(uuid=store_record.onboarding_details).first()
            details.deleted = True
            store_record.onboarding_details = None
            # Deactivate all store settings before removing store URL
            success, error = deactivate_store_settings(store_record.uuid)
            if not success:
                current_app.logger.error(f"Error deactivating store settings: {error}")
    db_session.commit()

    return "Okay"