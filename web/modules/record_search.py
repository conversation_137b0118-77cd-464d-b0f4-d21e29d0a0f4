from flask import request
import time
import uuid
from db import user_session, page_view, search
from app import db_session

def get_element_from_data_string(element):
    val = request.form.get(element)
    if val in ["null", "unknown", "void", "undefined"]:
        val = None
    return val

def record_search_handler():
    session_cookie = request.form.get("session_cookie")
    
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return "error"
    
    page_view_record = page_view.query.filter_by(user_session_id=user_session_record.uuid).\
        filter_by(front_end_id=request.form.get("page_view_id")).first()
    if page_view_record is None:
        return "error"
    
    # Let's add a search
    search_record = search()
    search_record.uuid = str(uuid.uuid4())
    search_record.time = time.time()
    search_record.page_view_id = page_view_record.uuid
    search_record.search_query = get_element_from_data_string("search_query")
    
    db_session.add(search_record)
    db_session.commit()
    
    return "okay"