from flask import request, abort
from app import db_session
from db import user_session, cart, cart_token
from helpers import process_webhook_request
from modules.discount_code_use_cases import record_discount_code_usage
from utils import get_generic_logger
import uuid
import time

logger = get_generic_logger(__name__)

def clean_data(data):
    if data in ["null", "unknown", "void", "undefined"]:
        data = None
    return data

def calculate_items(items):
    """ item_count: webhook.line_items[i].quantity """
    """ items_subtotal_price: original_total_price - total_discount """
    """ original_total_price: webhook.line_items[i].original_price * webhook.line_items[i].quantity """
    """ total_discount: webhook.line_items[i].total_discount """
    """ total_price: webhook.line_items[i].price * webhook.line_items[i].quantity """
    """ total_weight: webhook.line_items[i].grams """
    item_count = 0
    items_subtotal_price = 0
    original_total_price = 0
    total_discount = 0
    total_price = 0
    total_weight = 0
    for item in items:
        try:
            quantity = int(clean_data(item["quantity"]))
            item_count += quantity
        except:
            quantity = None
        try:
            original_total_price += float(clean_data(item["original_price"])) * quantity
        except:
            original_total_price = None
        try:
            total_discount += float(clean_data(item["total_discount"]))
        except:
            total_discount = None
        try:
            total_price += float(clean_data(item["price"])) * quantity
        except:
            total_price = None
        try:
            total_weight += float(clean_data(item["grams"]))
        except:
            total_weight = None
    try:
        items_subtotal_price = original_total_price - total_discount
    except:
        items_subtotal_price = None
    return item_count, items_subtotal_price, original_total_price, total_discount, total_price, total_weight

def get_cart_change_type(webhook_cart_token, item_count):
    prev_cart_record = cart.query.filter_by(cart_token=webhook_cart_token).order_by(cart.time.desc()).first()
    if prev_cart_record is None:
        return "create"
    else:
        prev_cart_item_count = prev_cart_record.item_count
        if prev_cart_item_count < item_count:
            return "add"
        elif prev_cart_item_count > item_count:
            if item_count == 0:
                return "clear"
            else:
                return "remove"
        elif prev_cart_item_count == item_count:
            return "none"
        else:
            return "error"

def record_cart_change_handler():
    event, shop_url = process_webhook_request()
    if not event:
        return abort(401)
    request_data = request.get_json()

    try:
        webhook_cart_token = clean_data(request_data["token"])
    except:
        webhook_cart_token = None
    
    user_session_record = user_session.query.filter(user_session.cart_tokens.any(cart_token.cart_token == webhook_cart_token)).first()
    if user_session_record is None:
        return 'okay'
    
    try:
        cart_items = clean_data(request_data["line_items"])
        item_count, items_subtotal_price, original_total_price, total_discount, total_price, total_weight = calculate_items(cart_items)

        # Add discount code tracking
        try:
            discount_codes = set()  # Use set for unique codes
            for item in cart_items:
                if item.get('discounts'):
                    for discount in item['discounts']:
                        if discount.get('title'):  # Get the discount code from the title field
                            discount_codes.add(discount['title'])
            
            # Record each unique discount code
            for discount_code in discount_codes:
                record_discount_code_usage(
                    user_session_record=user_session_record,
                    discount_code=discount_code,
                    page_view_id=None,  # No page view ID in webhook context
                    detection_source=event
                )
        except Exception as e:
            logger.error(f"Error processing cart discount codes: {str(e)}")
            # Continue with cart change handling even if discount processing fails
            
    except:
        cart_items = None
        item_count = None
        items_subtotal_price = None
        original_total_price = None
        total_discount = None
        total_price = None
        total_weight = None

    try:
        currency = clean_data(request_data["line_items"][0]["original_line_price_set"]["shop_money"]["currency_code"])
    except:
        currency = None

    try:
        note = clean_data(request_data["note"])
    except:
        note = None

    # Let's add a cart_change
    cart_change_record = cart()
    cart_change_record.uuid = str(uuid.uuid4())
    cart_change_record.time = time.time()
    cart_change_record.user_session_id = user_session_record.uuid
    cart_change_record.cart_token = webhook_cart_token
    cart_change_record.cart_change_type = get_cart_change_type(webhook_cart_token, item_count)
    cart_change_record.cart_items = cart_items
    cart_change_record.currency = currency
    cart_change_record.item_count = item_count
    cart_change_record.items_subtotal_price = items_subtotal_price
    cart_change_record.note = note
    cart_change_record.original_total_price = original_total_price
    cart_change_record.total_discount = total_discount
    cart_change_record.total_price = total_price
    cart_change_record.total_weight = total_weight
    db_session.add(cart_change_record)
    db_session.commit()
    
    return 'okay'