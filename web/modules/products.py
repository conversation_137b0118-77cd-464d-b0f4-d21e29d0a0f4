from flask import request, jsonify
from app import app
from db import store_url, store
from ephemeral_store import EphemeralStore
import requests
import jwt


def get_product_is_bestseller():
    shopify_url = request.args.get("shopify_url")
    product_id = request.args.get("product_id")

    store_url_record = store_url.query.filter_by(url=shopify_url).first()


    # look up cached product with their purchase amounts in our redis cache
    cached_product_ids = EphemeralStore.find_key(key=store_url_record.store_id)
    
    if cached_product_ids and product_id in cached_product_ids:
        return jsonify({"product_id": product_id, "is_bestseller": True})

    return jsonify({"product_id": product_id, "is_bestseller": False})

def get_products_by_ids():
    """
    Fetch product titles by a list of product IDs (comma-separated in ?ids=...)
    Returns: [{id: <product_id>, title: <product_title>}]
    """
    ids_param = request.args.get('ids')
    if not ids_param:
        return jsonify({'error': 'Missing ids parameter'}), 400
    id_list = [str(i).strip() for i in ids_param.split(',') if i.strip()]
    if not id_list:
        return jsonify({'error': 'No valid product IDs provided'}), 400

    # Get store context from token (reuse logic from merchant_search_products_handler)
    id_token = request.args.get('id_token')
    try:
        decoded = jwt.decode(id_token, options={"verify_signature": False})
        dest = decoded.get('dest', '')
        if dest.startswith('https://'):
            shop_url = dest.split('https://')[1]
            shop_name = shop_url.split('.')[0]
        else:
            shop_name = dest.split('/')[-1]
        if '.' in shop_name:
            shop_name = shop_name.split('.')[0]
    except Exception as e:
        return jsonify({"error": "Invalid token", "details": str(e)}), 401
    shop_url_record = store_url.query.filter_by(url=f"{shop_name}.myshopify.com").first()
    if shop_url_record is None:
        alt_shop_url_record = store_url.query.filter_by(url=shop_name).first()
        if alt_shop_url_record:
            shop_url_record = alt_shop_url_record
        else:
            return jsonify({"error": "Store not found", "attempted_url": f"{shop_name}.myshopify.com"}), 404
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return jsonify({"error": "Store not configured", "store_id": shop_url_record.store_id}), 404
    if not shop_record.access_token:
        return jsonify({"error": "Store has no access token"}), 401

    # Prepare Shopify GraphQL query for nodes
    api_version = app.config.get("SHOPIFY_LEGACY_API_VERSION", "2023-04")
    graphql_url = f"https://{shop_url_record.url}/admin/api/{api_version}/graphql.json"
    headers = {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shop_record.access_token
    }
    # Build GIDs
    gids = [f"gid://shopify/Product/{pid}" for pid in id_list]
    query = '''
    query getProducts($ids: [ID!]!) {
        nodes(ids: $ids) {
            ... on Product { id title }
        }
    }
    '''
    variables = {"ids": gids}
    payload = {"query": query, "variables": variables}
    response = requests.post(graphql_url, json=payload, headers=headers)
    if response.status_code != 200:
        return jsonify({"error": "Shopify API error", "status": response.status_code, "body": response.text}), response.status_code
    data = response.json()
    products = []
    for node in data.get('data', {}).get('nodes', []):
        if node and node.get('id') and node.get('title'):
            # Extract numeric ID from GID
            pid = node['id'].split('/')[-1]
            products.append({'id': pid, 'title': node['title']})
    return jsonify({'products': products})

