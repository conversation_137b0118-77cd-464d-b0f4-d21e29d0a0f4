import time
from flask import request, jsonify
from db import session_consent_event, store_url
from app import db_session
from utils import get_generic_logger

logger = get_generic_logger(__name__)

def standardize_consent(consent):
    if consent is None:
        return None
    consent = str(consent).lower()
    if consent in ["true", "yes", "1"]:
        return "true"
    elif consent in ["false", "no", "0"]:
        return "false"
    return consent

def record_consent_handler():
    payload = request.get_json()
    consent = dict(payload['consent'])
    store_url_record = store_url.query.filter_by(url=payload.get("shopify_url","").lower()).first()
    if store_url_record is None:
        return jsonify({"error": "Store not found", "message": "Could not find store record"}), 404
    if payload["consent_state"] == "initial":
        marketing_consent = consent['marketing'] or None
        analytics_consent = consent['analytics'] or None
        sale_of_data_consent = consent['sale_of_data'] or None
        preferences_consent = consent['preferences'] or None
    else:
        marketing_consent = consent.get('marketingAllowed')
        analytics_consent = consent.get('analyticsAllowed')
        sale_of_data_consent = consent.get('saleOfDataAllowed')
        preferences_consent = consent.get('preferencesAllowed')

    if payload["consent_state"] == "initial":
        existing_session_consent_event = session_consent_event.query.filter_by(
            session_cookie=payload.get("session_cookie"),
            customer_cookie=payload.get("customer_cookie"),
            store_id=str(store_url_record.parent.uuid),
            consent_state='initial'
        ).first()
        if existing_session_consent_event is not None:
            return jsonify({"success": "Skipping duplicate record", "message": "A record with the same session and customer cookie already exists"}), 200    
    session_consent_event_record = session_consent_event(
        time = time.time(),
        session_cookie = payload.get("session_cookie"),
        customer_cookie = payload.get("customer_cookie"),
        consent_state = payload.get("consent_state"),
        marketing_consent = standardize_consent(marketing_consent),
        analytics_consent = standardize_consent(analytics_consent),
        sale_of_data_consent = standardize_consent(sale_of_data_consent),
        preferences_consent = standardize_consent(preferences_consent),
        store_id = str(store_url_record.parent.uuid)
    )
    db_session.add(session_consent_event_record)
    db_session.commit()
    return jsonify({"success": True}), 200
    
    
