from app import app, db_session
from db import daily_stat, discount_code, exclude_tag, merchant_engagement, model_state, order, store, store_discount, store_url, user_session, onboarding_details, subscription_plans, intervention_type, store_intervention_association, metabase_analytics, exclude_url, discount_collection, admin_user
from config import APP_ENVIRONMENT
from modules.oauth import check_access_scopes, check_registered_webhooks, install_finalize_handler
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from constants import DEFAULT_HOLDOUT_PERC, DEFAULT_ANTI_HOLDOUT_PERC
from . import intervention_orchestration
from modules.base_nudge import get_default_holdout_percentage
from .intervention_orchestration import get_store_intervention_association
from communications import Notifier
from graphql.collections_queries import ALL_COLLECTIONS_QUERY
import dateutil.tz
from dateutil import parser
from flask import request, jsonify
from helpers import create_discount, send_mail
import json
from pyactiveresource.connection import ServerError, UnauthorizedAccess
import re
import shopify
from sqlalchemy import func
from threading import Thread
import time
import uuid
import base64
import requests
import jwt

from utils import get_generic_logger
logger = get_generic_logger(__name__)


def validate_admin_permissions_for_emulation():
    """
    Helper function to validate admin permissions during emulation mode.
    This prevents privilege escalation by ensuring the original admin still has valid permissions.
    """
    shopify_session = getattr(request, 'shopify_session', None)
    if not shopify_session:
        return False
    
    # If not in emulation mode, skip this check
    if not shopify_session.get('emulated'):
        return True
    
    # Get the original admin email
    original_admin_email = shopify_session.get('original_admin_email')
    if not original_admin_email:
        return False
    
    # Check if original admin still has super admin privileges
    admin_user_record = admin_user.query.filter(
        func.lower(admin_user.email) == original_admin_email.lower(),
        admin_user.super_admin == True
    ).first()
    
    return admin_user_record is not None


UTC_EASTERN_TIME_ZONE_OFFSET = 18000
MIN_DATE_TIMESTAMP = 1688399197

def get_csv(store_record, min_time, max_time, email):
    with app.app_context():
        # Rows
        daily_stats = []

        cur = min_time
        next = cur + (24 * 60 * 60)
        today = datetime.now(dateutil.tz.gettz('UTC')).replace(hour=0, minute=0, second=0, microsecond=0).astimezone(dateutil.tz.tzutc())
        while cur <= max_time and cur <= today.timestamp():
            stats = {}

            midday = (cur + next) / 2
            base_query_daily = daily_stat.query.\
                filter_by(store_id=store_record.uuid).\
                filter(daily_stat.start_time <= midday).\
                filter(daily_stat.end_time >= midday)
            
            stats["vandra_date_daily"] = datetime.fromtimestamp(cur).strftime("%Y-%m-%d")
            
            first_result = base_query_daily.first()
            if first_result:
                vandra_revenue_daily = first_result.vandra_conversions_dollars or 0
                stats["vandra_revenue_daily"] = "${:0,.2f}".format(float(vandra_revenue_daily))

                vandra_orders_daily = order.query.filter_by(store_id=store_record.uuid).\
                    filter(order.created_at >= cur).\
                    filter(order.created_at < next).\
                    filter(order.discount_code != None).\
                    filter(order.discount_code != "").count()
                stats["vandra_orders_daily"] = str(int(vandra_orders_daily))

                vandra_actionable_sessions_daily = first_result.analytics_actionable_audience_sessions or 0
                stats["vandra_actionable_sessions_daily"] = str(int(vandra_actionable_sessions_daily))

                vandra_popups_shown_daily = first_result.popups_shown or 0
                vandra_total_sessions_daily = first_result.analytics_total_audience_sessions or 0
                vandra_show_rate_daily = float(vandra_popups_shown_daily) / float(vandra_total_sessions_daily) if vandra_total_sessions_daily > 0 else 0
                stats["vandra_show_rate_daily"] = "{:0,.2f}%".format(float(vandra_show_rate_daily))

                vandra_applied_sessions_daily = first_result.popups_applied or 0
                vandra_show_to_apply_rate_daily =  float(vandra_applied_sessions_daily) / float(vandra_popups_shown_daily) if vandra_popups_shown_daily > 0 else 0
                stats["vandra_show_to_apply_rate_daily"] = "{:0,.2f}%".format(float(vandra_show_to_apply_rate_daily))

                vandra_conversion_sessions_daily = first_result.vandra_conversions or 0
                vandra_apply_to_convert_rate_daily = float(vandra_conversion_sessions_daily) / float(vandra_applied_sessions_daily) if vandra_applied_sessions_daily > 0 else 0
                stats["vandra_apply_to_convert_rate_daily"] = "{:0,.2f}%".format(float(vandra_apply_to_convert_rate_daily))

                vandra_commission_daily = float(vandra_revenue_daily) * float(store_record.commission_rate)
                stats["vandra_commission_daily"] = "${:0,.2f}".format(float(vandra_commission_daily))
            else:
                stats["vandra_revenue_daily"] = None
                stats["vandra_orders_daily"] = None
                stats["vandra_actionable_sessions_daily"] = None
                stats["vandra_show_rate_daily"] = None
                stats["vandra_show_to_apply_rate_daily"] = None
                stats["vandra_apply_to_convert_rate_daily"] = None
                stats["vandra_commission_daily"] = None

            daily_stats.append(stats)

            cur = next
            next = cur + (24 * 60 * 60)

        csv = [",".join([
                "Date",
                "Revenue",
                "Vandra Orders",
                "Actionable Sessions",
                "Show Rate",
                "Show-to-Apply Rate",
                "Apply-to-Convert Rate",
                "Commission"
        ])]
        for daily_stat in daily_stats:
            csv_row = ",".join([
                daily_stat["vandra_date_daily"],
                daily_stat["vandra_revenue_daily"],
                daily_stat["vandra_orders_daily"],
                daily_stat["vandra_actionable_sessions_daily"],
                daily_stat["vandra_show_rate_daily"],
                daily_stat["vandra_show_to_apply_rate_daily"],
                daily_stat["vandra_apply_to_convert_rate_daily"],
                daily_stat["vandra_commission_daily"]
            ])
            csv.append(csv_row)
        csv = "\n".join(csv)

        if email:
            subject = "Vandra Analytics Export: " + store_record.name
            body = "Please view the attachment below."
            encoded_csv = base64.b64encode(csv.encode("ascii"))
            attachment = { "filename": "Vandra.csv",
                            "contentType": "text/csv",
                            "data": encoded_csv }
            send_mail(subject, body, to=[store_record.email], attachment=attachment)
            return
        else:
            return csv


def initialize_store_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    charge_id = request.args.get("charge_id")

    shop_url_record = store_url.query.filter_by(url=shop_url).first()
    if shop_url_record is None:
        return install_finalize_handler()
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return install_finalize_handler()

    # App was previously integrated but has been uninstalled
    if shop_record.access_token is None:
        return install_finalize_handler()

    if charge_id:
        shop_record.charge_id = charge_id
        db_session.commit()

    details = onboarding_details.query.filter_by(uuid=shop_record.onboarding_details).first()

    return_details = details.to_dict()
    return_details['confirmed_charge'] = bool(shop_record.charge_id)

    if details:
        return jsonify({"details": return_details})
    else:
        return jsonify({"error": "details not found"})

def merchant_confirm_billing_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    shop_url_record = store_url.query.filter_by(url=shop_url).first()
    if shop_url_record is None:
        return jsonify({"error": "error"})
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return jsonify({"error": "error"})

    now = datetime.now()
    one_month_future = now + relativedelta(days=30)

    details = onboarding_details.query.filter_by(uuid=shop_record.onboarding_details).first()
    details.billing_orders_selected = request.form.get('billing_orders_selected')
    details.billing_plan_selected = request.form.get('billing_plan_selected')
    details.billing_confirmed = time.time()
    details.billing_trial_end = time.mktime(one_month_future.timetuple())

    subscription_plan = subscription_plans.query.order_by(subscription_plans.created_at.desc()).first()

    shop_record.subscription_plan = subscription_plan.uuid
    db_session.commit()

    shop_name = shop_url.split(".")[0]
    # Billing
    redirect_url = f'https://admin.shopify.com/store/{shop_name}/apps/' + app.config['SHOPIFY_API_KEY']
    recurring_charge_dict = {
        "recurring_application_charge": {
            "name": "Vandra Usage Billing",
            "return_url": redirect_url,
            "price": 0,
            "capped_amount": 2000,
            "trial_days": 30,
            "terms": "Charge fluctuates based on number of orders.",
        }
    }
    if "vandra" in shop_url.split(".")[0]:
        recurring_charge_dict["recurring_application_charge"]["test"] = True

    charge_response = requests.post(
        f"https://{shop_url}/admin/api/2024-10/recurring_application_charges.json",
        data=json.dumps(recurring_charge_dict),
        headers={
            "X-Shopify-Access-Token": shop_record.access_token,
            "Content-Type": "application/json",
        }
    )

    charge = charge_response.json()

    if charge.get('recurring_application_charge').get('confirmation_url'):
        return jsonify({ "redirect_url": charge.get('recurring_application_charge').get('confirmation_url')})


def merchant_fetch_pricing_plan_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    shop_url_record = store_url.query.filter_by(url=shop_url).first()
    if shop_url_record is None:
        return jsonify({"error": "error"})
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return jsonify({"error": "error"})
    
    subscription_plan = subscription_plans.query.order_by(subscription_plans.created_at.desc()).first()

    if subscription_plan:
        return jsonify({"pricing_details": subscription_plan.pricing_details})
    return jsonify({"error": "error"})

def merchant_update_onboarding_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    shop_url_record = store_url.query.filter_by(url=shop_url).first()
    if shop_url_record is None:
        return jsonify({"error": "error"})
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return jsonify({"error": "error"})

    updateField = request.form.get('field')
    value = request.form.get('value')

    if value is None:
        value = time.time()

    details = onboarding_details.query.filter_by(uuid=shop_record.onboarding_details).first()
    setattr(details, updateField, value)
    db_session.commit()

    return jsonify({ "okay": True })

def merchant_fetch_details():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    shop_url_record = store_url.query.filter_by(url=shop_url).first()
    if shop_url_record is None:
        return jsonify({"error": "error"})
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return jsonify({"error": "error"})
    
    details = onboarding_details.query.filter_by(uuid=shop_record.onboarding_details).first()

    return jsonify({
        "firstName": details.installer_first_name,
        "lastName": details.installer_last_name,
        "email": details.installer_email,
        "role": details.installer_role
    })


def merchant_update_details():
    shop_url = request.shopify_session.get('dest').split('https://')[1]

    shop_url_record = store_url.query.filter_by(url=shop_url).first()
    if shop_url_record is None:
        return jsonify({"error": "error"})
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return jsonify({"error": "error"})

    details = onboarding_details.query.filter_by(uuid=shop_record.onboarding_details).first()

    details.installer_first_name = request.form.get('first_name')
    details.installer_last_name = request.form.get('last_name')
    details.installer_email = request.form.get('email')
    details.installer_role = request.form.get('role')
    details.details_confirmed = time.time()

    db_session.commit()

    return jsonify({"okay": True})

def merchant_onboarding_script_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]

    shop_url_record = store_url.query.filter_by(url=shop_url).first()
    if shop_url_record is None:
        return jsonify({"error": "error"})
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return jsonify({"error": "error"})
    
    session = shopify.Session(shop_url, app.config["SHOPIFY_LEGACY_API_VERSION"], shop_record.access_token)
    shopify.ShopifyResource.activate_session(session)

    # Check for Vandra app ID (13652151254090710087) in settings and ensure app is enabled
    # Updated Vandra app ID (5666136905621767119)
    try:
        asset = shopify.Asset.find("config/settings_data.json")
        value = json.loads(asset.value)
        # If the current value is a string and equals "Default", the script is not installed
        if isinstance(value["current"], str) and value["current"] == "Default":
            return jsonify({"okay": False})
        if "blocks" not in value["current"]:
            return jsonify({"okay": False})
        blocks = value["current"]["blocks"]
        if "13652151254090710087" in blocks and blocks["13652151254090710087"]["disabled"] == False:
            return jsonify({"okay": True})
        elif app.config["SHOPIFY_VANDRA_THEME_EXTENSION_BLOCK_ID"] in blocks and blocks[app.config["SHOPIFY_VANDRA_THEME_EXTENSION_BLOCK_ID"]]["disabled"] == False:
            return jsonify({"okay": True})
        # checking from id not block id per 
        # https://shopify.dev/docs/apps/build/online-store/theme-app-extensions/configuration#detecting-app-embed-blocks
        elif app.config["SHOPIFY_VANDRA_THEME_EXTENSION_ID"]:
            for __, block in blocks.items():
                if app.config["SHOPIFY_VANDRA_THEME_EXTENSION_ID"] in block["type"]:
                    if block["disabled"] == False:
                        return jsonify({"okay": True})
                    return jsonify({"okay": False})
            return jsonify({"okay": False})
        else:
            return jsonify({"okay": False})
    except (ServerError, UnauthorizedAccess) as e:
        return jsonify({"error": "error"})

def merchant_intervention_types_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]

    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    intervention_types = intervention_type.query.order_by(intervention_type.time.asc())
    types = []
    for x in intervention_types:
        types.append({
            "id": x.uuid,
            "title": x.title
        })

    return jsonify({"types": types})

# Social media functions moved to social_media_nudge_use_cases.py

def merchant_nudge_requests_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]

    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    available_nudges = intervention_type.query.filter(intervention_type.active == True, intervention_type.name != "intent_based_discount").all()

    # Fetch the MOST RECENT association for each intervention type for the store
    from sqlalchemy import func, distinct
    
    # First get the most recent association ID for each intervention type
    subquery = store_intervention_association.query.with_entities(
        store_intervention_association.intervention_type_id,
        func.max(store_intervention_association.time).label('max_created_at')
    ).filter(
        store_intervention_association.store_id == store_record.uuid
    ).group_by(
        store_intervention_association.intervention_type_id
    ).subquery()
    
    # Then join to get the full association records
    all_associations = store_intervention_association.query.join(
        intervention_type, store_intervention_association.intervention_type_id == intervention_type.uuid
    ).join(
        subquery, 
        (store_intervention_association.intervention_type_id == subquery.c.intervention_type_id) & 
        (store_intervention_association.time == subquery.c.max_created_at)
    ).filter(
        store_intervention_association.store_id == store_record.uuid,
        intervention_type.name != "intent_based_discount"
    ).all()

    return_data = {}
    # Initialize with all available nudges (title, description, default inactive/empty params)
    for x in available_nudges:
        return_data[x.name] = {
            "title": x.title,
            "description": x.description,
            "active": False, # Default to inactive
            "parameters": {} # Default to empty parameters
        }
        
        # Always add default preview video for social_media_content
        if x.name == "social_media_content":
            from .social_media_nudge_use_cases import get_default_preview_video
            return_data[x.name]['parameters'].update(get_default_preview_video())
        
    # Populate active status and parameters from ALL fetched associations
    for assoc in all_associations:
        if assoc.intervention_type.name in return_data: # Check if it's an available nudge
            return_data[assoc.intervention_type.name]['active'] = assoc.active
            return_data[assoc.intervention_type.name]['parameters'] = assoc.parameters or {} # Use stored params or empty dict
            
            # Add extra parameters for social_media_content
            if assoc.intervention_type.name == "social_media_content":
                from .social_media_nudge_use_cases import get_social_media_content_parameters
                extra_params = get_social_media_content_parameters(assoc)
                return_data[assoc.intervention_type.name]['parameters'].update(extra_params)

    return jsonify({"requests": return_data})


def merchant_request_nudge_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]

    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    intervention_type_record = intervention_type.query.filter_by(name=request.form.get('id')).first()

    if intervention_type_record is None:
         return jsonify({"error": "nudge type not found"})

    previous_request = store_intervention_association.query.filter_by(store_id=store_record.uuid, intervention_type_id=intervention_type_record.uuid, active=True).first()

    if previous_request is not None:
        return jsonify({"error": "already requested"})

    store_intervention = intervention_orchestration.create_store_intervention_association(
        store_record,
        intervention_type_record
    )
    # Set 100% holdout via parameters
    store_intervention.parameters = {'holdout_percentage': 1.0, "anti_holdout_percentage": 0.0, "tag" : "requested_access"}
    db_session.commit()

    available_nudges = intervention_type.query.filter(intervention_type.active == True, intervention_type.name != "intent_based_discount").all()

    requested = store_intervention_association.query.join(
        intervention_type, store_intervention_association.intervention_type_id == intervention_type.uuid
    ).filter(store_intervention_association.store_id == store_record.uuid, intervention_type.name != "intent_based_discount").all()

    return_data = {}
    for x in available_nudges:
        return_data[x.name] = {
            "title": x.title,
            "description": x.description
        }
    for x in requested:
        return_data[x.intervention_type.name]['active'] = x.active
        return_data[x.intervention_type.name]['parameters'] = x.parameters

    return jsonify({"requests": return_data})

def merchant_dashboard_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "dashboard_view"
    db_session.add(new_engagement)
    db_session.commit()

    redirect_url = check_access_scopes(store_record)
    if redirect_url is not None:
        return jsonify({"redirect_url": redirect_url})
    
    session = shopify.Session(shop_url, app.config["SHOPIFY_LEGACY_API_VERSION"], store_record.access_token)
    shopify.ShopifyResource.activate_session(session)

    # Check if we have a discount code set up. If not redirect to onboarding
    discount_code_record = discount_code.query.filter_by(store_id=store_record.uuid).first()
    if discount_code_record is None:
        return jsonify({
            "session_count": 0,
            "shown_count": 0,
            "order_count": 0,
            "first_visit": True,
            "redirect_to_onboarding": True
        })
    
    session_count = user_session.query.\
        filter_by(store_id=store_record.uuid).\
        filter_by(vandra_opportunity=True).\
        filter((user_session.holdout == False) | (user_session.holdout == None)).\
        count()
    shown_count = user_session.query.\
        filter_by(store_id=store_record.uuid).\
        filter_by(vandra_shown=True).\
        count()
    order_count = user_session.query.\
        filter_by(store_id=store_record.uuid).\
        filter_by(vandra_conversion=True).\
        count()
    
    first_visit = not store_record.had_first_visit
    if first_visit:
        store_record.had_first_visit = True
        db_session.commit()
    
    discount_enabled = store_record.show_discount or store_record.vandra_admin_show_discount

    shop_name = store_record.name
    if shop_name is None:
        # Shopify API occassionally has 405 or 401 errors we need to deal with
        try:
            shop = shopify.Shop.current()
            shop_name = shop.name
            store_record.name = shop_name
        except (ServerError, UnauthorizedAccess) as e:
            shop_name = shop_url.split(".")[0]
        db_session.commit()

    return jsonify({
        "shop_name": shop_name,
        "session_count": session_count,
        "shown_count": shown_count,
        "order_count": order_count,
        "first_visit": first_visit,
        "redirect_to_onboarding": False,
        "discount_enabled": discount_enabled,
        "show_discount": store_record.show_discount,
        "vandra_admin_show_discount": store_record.vandra_admin_show_discount
    })

def merchant_dashboard_sales_handler():
    args_dict = request.args.to_dict()
    min_time = args_dict.pop("min_time")
    max_time = args_dict.pop("max_time")

    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    try:
        min_time = parser.parse(min_time).timestamp()
        max_time = parser.parse(max_time).timestamp()
    except:
        return jsonify({"error": "error"})

    vandra_conversion_value = user_session.query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter_by(store_id=store_record.uuid).\
        filter(user_session.time >= min_time).\
        filter(user_session.time <= max_time).\
        filter_by(vandra_conversion=True).first().conversion_value
    
    if vandra_conversion_value is None:
        vandra_conversion_value = 0
    
    return jsonify({ "sales": vandra_conversion_value })

def merchant_analytics_test_mode_verification_handler():
    
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    
    if store_url_record.url == "vandra-test.myshopify.com":
        return jsonify({ "test_mode": True })
    else:
        return jsonify({ "test_mode": False })

def merchant_analytics_test_stores_handler():
    store_records = store.query.filter(store.had_first_visit==True,store.last_order_sync>0).all()
    payload = []
    for store_record in store_records:
        url = None if not store_record.store_urls else  store_record.store_urls[0].url
        if url:
            payload.append({"name" : store_record.name, "url" : store_record.store_urls[0].url})
    return jsonify({ "test_stores": payload })

def merchant_analytics_handler():
    args_dict = request.args.to_dict()
    test_store = args_dict.pop("test_store") if "test_store" in args_dict else None
    min_time = args_dict.pop("min_time")
    max_time = args_dict.pop("max_time")

    shop_url = request.shopify_session.get('dest').split('https://')[1]

    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})

    store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
        order_by(store_url.default.desc()).first()
    if store_url_record is None:
        return jsonify({"data": {"error": "error"}})
    
    if store_url_record.url == "vandra-test.myshopify.com":
        test_store_url_record = store_url.query.filter_by(url=test_store).first()
        store_record = test_store_url_record.parent
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "analytics_view"
    db_session.add(new_engagement)
    db_session.commit()

    first_user_session = user_session.query.\
        filter_by(store_id=store_record.uuid).\
        order_by(user_session.time).first()
    if first_user_session is None:
        return jsonify({"data": {"error": "error"}})
    first_user_session_min_time = first_user_session.time
    first_user_session_min_time_datetime = datetime.fromtimestamp(first_user_session_min_time).strftime("%Y-%m-%d")
    first_user_session_min_time_timestamp = parser.parse(first_user_session_min_time_datetime).timestamp() + UTC_EASTERN_TIME_ZONE_OFFSET
    
    try:
        min_time = parser.parse(min_time).timestamp()
        if(min_time < first_user_session_min_time_timestamp):
            min_time = first_user_session_min_time_timestamp
        max_time = parser.parse(max_time).timestamp()
        max_time_next_datetime = datetime.fromtimestamp(max_time) + timedelta(days=1)
        max_time_next = max_time_next_datetime.timestamp()
    except:
        return jsonify({"error": "error"})
    
    if min_time < MIN_DATE_TIMESTAMP:
        return jsonify({"error": "error"})

    # Overview stats
    overview_stats = {}

    # min_time and max_time timestamps are in midnight UTC
    # daily_stat.start_time and daily_stat.end_time timestamps are in midnight ET (which can either be GMT-0400 or GMT-0500 depending on the time of year)
    # Midnight UTC timestamp < midnight ET timestamp

    vandra_sale_result = daily_stat.query.with_entities(func.sum(daily_stat.vandra_conversions_dollars).label("vandra_sales")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    #check results aren't empty before attempting to access the list
    vandra_sales = 0 if vandra_sale_result is None else vandra_sale_result[0] or 0

    overview_stats["vandra_sales"] = float(vandra_sales)

    vandra_orders = order.query.filter_by(store_id=store_record.uuid).\
        filter(order.created_at >= min_time).\
        filter(order.created_at < max_time_next).\
        filter(order.discount_code != None).\
        filter(order.discount_code != "").count()
    overview_stats["vandra_orders"] = int(vandra_orders)

    # Segmentation stats
    segmentation_stats = {}

    # Total audience
    vandra_total_sessions_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_total_audience_sessions).label("vandra_total_sessions")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    
    vandra_total_sessions = 0 if vandra_total_sessions_result is None else vandra_total_sessions_result[0] or 0
    segmentation_stats["vandra_total_sessions"] = int(vandra_total_sessions)

    vandra_total_orders = vandra_orders
    segmentation_stats["vandra_total_orders"] = int(vandra_total_orders)

    total_conversions_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_total_audience_conversions).label("total_conversions")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()

    total_conversions = 0 if total_conversions_result is None else total_conversions_result[0]
    

    vandra_total_conversion_rate = float(total_conversions) / float(vandra_total_sessions) if vandra_total_sessions > 0 else 0
    segmentation_stats["vandra_total_conversion_rate"] = float(vandra_total_conversion_rate)

    vandra_total_revenue_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_total_audience_revenue).label("vandra_total_revenue")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    vandra_total_revenue = 0 if vandra_total_revenue_result is None else vandra_total_revenue_result[0] or 0

    segmentation_stats["vandra_total_revenue"] = float(vandra_total_revenue)

    # Protected audience
    vandra_protected_sessions_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_protected_audience_sessions).label("vandra_protected_sessions")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    vandra_protected_sessions = 0 if vandra_protected_sessions_result is None else vandra_protected_sessions_result[0] or 0

    segmentation_stats["vandra_protected_sessions"] = int(vandra_protected_sessions)

    protected_conversions_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_protected_audience_conversions).label("protected_conversions")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    protected_conversions = 0 if protected_conversions_result is None else protected_conversions_result[0]

    vandra_protected_conversion_rate = float(protected_conversions) / float(vandra_protected_sessions) if vandra_protected_sessions > 0 else 0
    segmentation_stats["vandra_protected_conversion_rate"] = float(vandra_protected_conversion_rate)

    vandra_protected_revenue_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_protected_audience_revenue).label("vandra_protected_revenue")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    vandra_protected_revenue = 0 if vandra_protected_revenue_result is None else vandra_protected_revenue_result[0] or 0
    segmentation_stats["vandra_protected_revenue"] = float(vandra_protected_revenue)

    # Actionable audience
    vandra_actionable_sessions_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_actionable_audience_sessions).label("vandra_actionable_sessions")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    vandra_actionable_sessions = 0 if vandra_actionable_sessions_result is None else vandra_actionable_sessions_result[0]
    segmentation_stats["vandra_actionable_sessions"] = int(vandra_actionable_sessions)

    vandra_actionable_orders = vandra_orders
    segmentation_stats["vandra_actionable_orders"] = int(vandra_actionable_orders)

    actionable_conversions_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_actionable_audience_conversions).label("actionable_conversions")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    actionable_conversions = 0 if actionable_conversions_result is None else actionable_conversions_result[0] or 0
    
    vandra_actionable_conversion_rate = float(actionable_conversions) / float(vandra_actionable_sessions) if vandra_actionable_sessions > 0 else 0
    segmentation_stats["vandra_actionable_conversion_rate"] = float(vandra_actionable_conversion_rate)

    vandra_actionable_revenue_result = daily_stat.query.with_entities(func.sum(daily_stat.analytics_actionable_audience_revenue).label("vandra_actionable_revenue")).\
        filter_by(store_id=store_record.uuid).\
        filter(daily_stat.start_time >= min_time).\
        filter(daily_stat.end_time <= max_time_next).first()
    vandra_actionable_revenue = 0 if vandra_actionable_revenue_result is None else vandra_actionable_revenue_result[0] or 0
    segmentation_stats["vandra_actionable_revenue"] = float(vandra_actionable_revenue)

    # Overview stats
    vandra_lift_denominator = float(vandra_total_revenue) - float(vandra_sales)
    vandra_lift = float(vandra_sales) / vandra_lift_denominator if vandra_lift_denominator > 0 else 0
    overview_stats["vandra_lift"] = float(vandra_lift)

    # Daily stats
    daily_stats = []

    cur = min_time
    next = cur + (24 * 60 * 60)
    today = datetime.now(dateutil.tz.gettz('UTC')).replace(hour=0, minute=0, second=0, microsecond=0).astimezone(dateutil.tz.tzutc())
    while cur <= max_time and cur <= today.timestamp():
        stats = {}

        midday = (cur + next) / 2
        base_query_daily = daily_stat.query.\
            filter_by(store_id=store_record.uuid).\
            filter(daily_stat.start_time <= midday).\
            filter(daily_stat.end_time >= midday)
        
        stats["vandra_date_daily"] = datetime.fromtimestamp(cur).strftime("%Y-%m-%d")
        
        first_result = base_query_daily.first()
        if first_result:
            vandra_revenue_daily = first_result.vandra_conversions_dollars or 0
            stats["vandra_revenue_daily"] = float(vandra_revenue_daily)

            vandra_orders_daily = order.query.filter_by(store_id=store_record.uuid).\
                filter(order.created_at >= cur).\
                filter(order.created_at < next).\
                filter(order.discount_code != None).\
                filter(order.discount_code != "").count()
            stats["vandra_orders_daily"] = int(vandra_orders_daily)

            vandra_actionable_sessions_daily = first_result.analytics_actionable_audience_sessions or 0
            stats["vandra_actionable_sessions_daily"] = int(vandra_actionable_sessions_daily)

            vandra_popups_shown_daily = first_result.popups_shown or 0
            vandra_total_sessions_daily = first_result.analytics_total_audience_sessions or 0
            vandra_show_rate_daily = float(vandra_popups_shown_daily) / float(vandra_total_sessions_daily) if vandra_total_sessions_daily > 0 else 0
            stats["vandra_show_rate_daily"] = float(vandra_show_rate_daily)

            vandra_applied_sessions_daily = first_result.popups_applied or 0
            vandra_show_to_apply_rate_daily =  float(vandra_applied_sessions_daily) / float(vandra_popups_shown_daily) if vandra_popups_shown_daily > 0 else 0
            stats["vandra_show_to_apply_rate_daily"] = float(vandra_show_to_apply_rate_daily)

            vandra_conversion_sessions_daily = first_result.vandra_conversions or 0
            vandra_apply_to_convert_rate_daily = float(vandra_conversion_sessions_daily) / float(vandra_applied_sessions_daily) if vandra_applied_sessions_daily > 0 else 0
            stats["vandra_apply_to_convert_rate_daily"] = float(vandra_apply_to_convert_rate_daily)

            vandra_commission_daily = float(vandra_revenue_daily) * float(store_record.commission_rate)
            stats["vandra_commission_daily"] = float(vandra_commission_daily)
        else:
            stats["vandra_revenue_daily"] = None
            stats["vandra_orders_daily"] = None
            stats["vandra_actionable_sessions_daily"] = None
            stats["vandra_show_rate_daily"] = None
            stats["vandra_show_to_apply_rate_daily"] = None
            stats["vandra_apply_to_convert_rate_daily"] = None
            stats["vandra_commission_daily"] = None

        daily_stats.append(stats)

        cur = next
        next = cur + (24 * 60 * 60)
    
    return jsonify({
        "overview_stats": overview_stats,
        "segmentation_stats": segmentation_stats,
        "daily_stats": daily_stats
    })

def merchant_analytics_export_handler():
    args_dict = request.args.to_dict()
    min_time = args_dict.pop("min_time")
    max_time = args_dict.pop("max_time")
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "analytics_export"
    db_session.add(new_engagement)
    db_session.commit()

    first_user_session = user_session.query.\
        filter_by(store_id=store_record.uuid).\
        order_by(user_session.time).first()
    first_user_session_min_time = first_user_session.time
    first_user_session_min_time_datetime = datetime.fromtimestamp(first_user_session_min_time).strftime("%Y-%m-%d")
    first_user_session_min_time_timestamp = parser.parse(first_user_session_min_time_datetime).timestamp() + UTC_EASTERN_TIME_ZONE_OFFSET

    email_export = False
    try:
        min_time = parser.parse(min_time).timestamp()
        if(min_time < first_user_session_min_time_timestamp):
            min_time = first_user_session_min_time_timestamp
        max_time = parser.parse(max_time).timestamp()
        difference_seconds = abs(max_time - min_time)
        # 30 day limit for CSV export, otherwise email export
        if difference_seconds > 2592000:
            email_export = True
    except:
        return jsonify({"error": "error"})
    
    if email_export:
        thread = Thread(target=get_csv, args=(store_record, min_time, max_time, True))
        thread.start()
        email = store_record.email
        if email is None:
            return jsonify({
                "email": "error"
            })
        else:
            return jsonify({
                "email": email
            })
    else:
        csv = get_csv(store_record, min_time, max_time, False)
        return jsonify({
            "csv": csv
        })

def merchant_settings_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
        order_by(store_url.default.desc()).first()
    if store_url_record is None:
        return jsonify({"data": {"error": "error"}})
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "settings_view"
    db_session.add(new_engagement)
    db_session.commit()
    
    max_discount = store_record.max_discount or 0

    discount_variants = []
    # Get all the values we need to create a new discount for
    discount_rate_query = store_discount.query.filter_by(store_id=store_record.uuid, active=True).all()
    for discount_rate in discount_rate_query:
        # add discount rate and prefix to the list, default to the store record prefix
        discount_variants.append({
            "discount_value": discount_rate.discount_value,
            "discount_prefix": discount_rate.discount_prefix or store_record.discount_prefix
        })
    
    if len(discount_variants) == 0:
        discount_variants.append({"discount_value": store_record.max_discount, "discount_prefix": store_record.discount_prefix})

    discount_ends_at_time = 0
    discount_code_records = discount_code.query.filter_by(store_id=store_record.uuid, expired=False).all()
    if len(discount_code_records) > 0:
        discount_ends_at_time = discount_code_records[0].ends_at_time
    # Get list of URLs that we want to exclude from displaying the popup
    exclude_urls_query = exclude_url.query.filter_by(store_id=store_record.uuid).all()
    exclude_urls = [x.url for x in exclude_urls_query]

    discount_collection_query = discount_collection.query.filter_by(store_id=store_record.uuid).all()
    discount_collections = [x.collection_id for x in discount_collection_query]

    combines_with_dict = {
        "orderDiscounts": False,
        "productDiscounts": False,
        "shippingDiscounts": True,
    }
    holdout_perc = DEFAULT_HOLDOUT_PERC
    anti_holdout_perc = DEFAULT_ANTI_HOLDOUT_PERC
    # Get the intervention type record
    intervention_type_record = intervention_type.query.filter_by(name='intent_based_discount').first()
    # Get current association record
    association_record = get_store_intervention_association(
        store_record, 'intent_based_discount', only_active=False
    )
    if association_record is not None:
        # Get the parameters from the association record
        parameters = association_record.parameters or {}
        # Check if the discount code is set in the parameters
        combines_with_dict.update(parameters.get("combinesWith", {})) 
        holdout_perc = parameters.get('holdout_percentage', DEFAULT_HOLDOUT_PERC)
        anti_holdout_perc = parameters.get('anti_holdout_percentage', DEFAULT_ANTI_HOLDOUT_PERC)

    collections = get_collections(store_record, store_url_record)

    return jsonify({
        "title": intervention_type_record.title,
        "description": intervention_type_record.description,
        "url": store_url_record.url,
        "show_discount": store_record.show_discount,
        "max_discount": max_discount,
        "show_discount_when_logged_in": store_record.show_discount_when_logged_in,
        "show_discount_to_previous_customers": store_record.show_discount_to_previous_customers,
        "commission_rate": store_record.commission_rate * 100,
        "popup_primary_color": store_record.popup_primary_color,
        "popup_discount_code_prefix": store_record.discount_prefix,
        "popup_bg_color": store_record.popup_bg_color,
        "popup_font": store_record.popup_font,
        "popup_text_header": store_record.popup_text_header,
        "popup_text_body": store_record.popup_text_body,
        "popup_text_button": store_record.popup_text_button,
        "popup_text_button_close": store_record.popup_text_button_close,
        "popup_text_success": store_record.popup_text_success,
        "minimized_text_header": store_record.minimized_text_header,
        "auto_apply_text_body": store_record.auto_apply_text_body,
        "popup_image_url": store_record.popup_image_url,
        "exclude_urls": exclude_urls,
        "hide_minimized_popup": store_record.hide_minimized_popup,
        "use_meta_ad_pixel": store_record.use_meta_ad_pixel,
        "discount_one_time": store_record.discount_one_time,
        "discount_subscription": store_record.discount_subscription,
        "discount_variants": discount_variants,
        "discount_ends_at_time": discount_ends_at_time,
        "discount_collections": discount_collections,
        "discount_combines_with": combines_with_dict,
        "holdout": holdout_perc,
        "anti_holdout_percentage": anti_holdout_perc,
        "collections": collections,
    })

def merchant_discount_nudge_activate_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "discount_nudge_activate"
    db_session.add(new_engagement)
    db_session.commit()

    session = shopify.Session(shop_url, app.config['SHOPIFY_LEGACY_API_VERSION'], store_record.access_token)
    shopify.ShopifyResource.activate_session(session)

    data = request.get_json()
    if data.get("show_discount") == "true" or data.get("show_discount") == True:
        store_record.show_discount = True
    else:
        store_record.show_discount = False
    
    db_session.commit()
    return jsonify({"data": {"success": True}})

def merchant_discount_nudge_save_settings_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "discount_nudge_settings_save"
    db_session.add(new_engagement)
    db_session.commit()

    session = shopify.Session(shop_url, app.config['SHOPIFY_LEGACY_API_VERSION'], store_record.access_token)
    shopify.ShopifyResource.activate_session(session)

    data = request.get_json()

    discount_one_time = False
    if data.get("discount_one_time") == True:
        discount_one_time = True
    discount_subscription = False
    if data.get("discount_subscription") == True:
        discount_subscription = True
    
    change_discount = False
    if store_record.discount_one_time != discount_one_time or store_record.discount_subscription != discount_subscription:
        logger.info("Discount type changed due to subscription type")
        change_discount = True
    
    store_record.show_discount_when_logged_in = True if data.get("show_discount_when_logged_in") == True else False
    store_record.show_discount_to_previous_customers = True if data.get("show_discount_to_previous_customers") == True else False
    store_record.popup_primary_color = data.get("popup_primary_color")
    store_record.popup_bg_color = data.get("popup_bg_color")
    store_record.popup_font = data.get("popup_font")
    store_record.popup_text_header = data.get("popup_text_header")
    store_record.popup_text_body = data.get("popup_text_body")
    store_record.popup_text_button = data.get("popup_text_button")
    # store_record.popup_text_button_close = data.get("popup_text_button_close")
    store_record.popup_text_success = data.get("popup_text_success")
    store_record.auto_apply_text_body = data.get("auto_apply_text_body")
    store_record.popup_image_url = data.get("popup_image_url")
    store_record.minimized_text_header = data.get("minimized_text_header")
    store_record.discount_one_time = discount_one_time
    store_record.discount_subscription = discount_subscription
    store_record.hide_minimized_popup = True if data.get("hide_minimized_popup") == True else False

    # Update discount prefix and expire all existing discounts if necessary
    old_max_discount = store_record.max_discount
    old_discount_prefix = store_record.discount_prefix
    store_record.discount_prefix = data.get("discount_prefix")
    store_record.max_discount = data.get("max_discount")
    
    # discount_collections
    delete_collection_id_list = []
    discount_collections_list = [x.replace("\r", "") for x in data.get("discount_collections")]
    discount_collection_query = discount_collection.query.filter_by(store_id=store_record.uuid).all()
    for existing_collection in discount_collection_query:
        if existing_collection.collection_id not in discount_collections_list:
            delete_collection_id_list.append(existing_collection.uuid)
        else:
            discount_collections_list.remove(existing_collection.collection_id)
    
    if len(delete_collection_id_list) > 0 or len(discount_collections_list) > 0:
        logger.info("Discount collection changed")
        change_discount = True

    discount_collection.query.filter_by(store_id=store_record.uuid).\
        filter(discount_collection.uuid.in_(delete_collection_id_list)).delete()
    
    # Add the new discount_collections
    add_collection_list = []
    for new_collection in discount_collections_list:
        if new_collection == "":
            continue
        
        new_discount_collection = discount_collection()
        new_discount_collection.uuid = str(uuid.uuid4())
        new_discount_collection.time = time.time()
        new_discount_collection.collection_id = new_collection
        new_discount_collection.store_id = store_record.uuid
        add_collection_list.append(new_discount_collection)
    db_session.bulk_save_objects(add_collection_list)
    
    # exclude URLs
    delete_url_id_list = []
    exclude_urls_list = [x.replace("\r", "") for x in data.get("exclude_urls")]
    exclude_url_query = exclude_url.query.filter_by(store_id=store_record.uuid).all()
    for existing_url in exclude_url_query:
        if existing_url.url not in exclude_urls_list:
            delete_url_id_list.append(existing_url.uuid)
        else:
            exclude_urls_list.remove(existing_url.url)
    
    # Delete the exclude_urls that aren't to be used anymore
    exclude_url.query.filter_by(store_id=store_record.uuid).\
        filter(exclude_url.uuid.in_(delete_url_id_list)).delete()
    
    # Add the new exclude_urls
    add_url_list = []
    for new_url in exclude_urls_list:
        if new_url == "":
            continue
        
        new_exclude_url = exclude_url()
        new_exclude_url.uuid = str(uuid.uuid4())
        new_exclude_url.time = time.time()
        new_exclude_url.url = new_url
        new_exclude_url.store_id = store_record.uuid
        add_url_list.append(new_exclude_url)
    db_session.bulk_save_objects(add_url_list)

    discount_variants = data.get("discount_variants", [])
    discount_values = [int(x.get("discount_value")) for x in discount_variants]
    discount_prefixes = [x.get("discount_prefix") for x in discount_variants]

    store_discount.query.filter_by(store_id=store_record.uuid).\
        filter(store_discount.discount_value.notin_(discount_values)).update({"active": False})
    store_discount.query.filter_by(store_id=store_record.uuid).\
        filter(store_discount.discount_prefix.notin_(discount_prefixes)).update({"active": False})
    for v in discount_variants:
        discount_value, discount_prefix = int(v.get("discount_value")), v.get("discount_prefix")
        if discount_value is None:
            continue
        if discount_prefix is None:
            discount_prefix = store_record.discount_prefix + "-" + str(discount_value)
        # Check if the discount already exists
        existing_discount = store_discount.query.filter_by(store_id=store_record.uuid, discount_value=discount_value, discount_prefix=discount_prefix, active=True).first()
        if existing_discount is None:
            logger.info("Creating new discount")
            change_discount = True
            new_discount = store_discount()
            new_discount.uuid = str(uuid.uuid4())
            new_discount.time = time.time()
            new_discount.store_id = store_record.uuid
            new_discount.discount_value = discount_value
            new_discount.discount_prefix = discount_prefix
            db_session.add(new_discount)
        
    # Get the intervention type record
    intervention_type_record = intervention_type.query.filter_by(name='intent_based_discount').first()
    
    # Get current association record
    association_record = get_store_intervention_association(
        store_record, 'intent_based_discount', only_active=False
    )

    # If no association record exists, create one
    if association_record is None:
        association_record = store_intervention_association(
            uuid=str(uuid.uuid4()),
            time=time.time(),
            intervention_type_id=intervention_type_record.uuid,
            store_id=store_record.uuid,
            active=True,
            parameters={}
        )
        db_session.add(association_record)

    # Get current parameters or initialize empty dict
    current_parameters = association_record.parameters or {}
    
    new_parameters = {}
    new_parameters["holdout_percentage"] = data.get("holdout", DEFAULT_HOLDOUT_PERC)
    new_parameters["anti_holdout_percentage"] = data.get("anti_holdout_percentage", DEFAULT_ANTI_HOLDOUT_PERC)
    combines_with_dict = {
        "orderDiscounts": False,
        "productDiscounts": False,
        "shippingDiscounts": True,
    }
    old_combines_with_dict = combines_with_dict.copy()
    old_combines_with_dict.update(current_parameters.get("combinesWith", {})) 
    combines_with_dict.update(data.get("discount_combines_with", {}))
    if (old_combines_with_dict != combines_with_dict):
        logger.info("Combines with changed")
        change_discount = True
    new_parameters["combinesWith"] = combines_with_dict

    # Update current parameters with new parameters
    current_parameters.update(new_parameters)

    # Save updated parameters back to the association record
    association_record.parameters = current_parameters

    if int(old_max_discount) != int(store_record.max_discount) or old_discount_prefix != store_record.discount_prefix:
        logger.info(f"Discount value or prefix changed from {old_max_discount} to {store_record.max_discount} or {old_discount_prefix} to {store_record.discount_prefix}")
        change_discount = True
    
    if change_discount:
        db_session.commit()
        create_discount(store_record)

    try:
        db_session.commit()
    except Exception as e:
        db_session.rollback()
        logger.error(f"Database commit failed: {e}", exc_info=True)
        return jsonify({"error": "Server Error", "message": "Failed to save parameters"}), 500

    return jsonify({"data": {"success": True}})

def merchant_discount_settings_save_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "settings_save"
    db_session.add(new_engagement)
    db_session.commit()

    session = shopify.Session(shop_url, app.config['SHOPIFY_LEGACY_API_VERSION'], store_record.access_token)
    shopify.ShopifyResource.activate_session(session)
    
    old_discount = store_record.max_discount
    
    if request.form.get("show_discount") == "true":
        store_record.show_discount = True
    elif request.form.get("show_discount") in [None, "false"]:
        store_record.show_discount = False
    
    new_state_switch = model_state()
    new_state_switch.uuid = str(uuid.uuid4())
    new_state_switch.time = time.time()
    new_state_switch.store_id = store_record.uuid
    new_state_switch.merchant_enabled = store_record.show_discount
    new_state_switch.vandra_admin_enabled = store_record.vandra_admin_show_discount
    db_session.add(new_state_switch)
    db_session.commit()
    
    try:
        max_discount_tmp = int(round(float(request.form.get("max_discount")), 0))
        if max_discount_tmp >= 0 and max_discount_tmp <= 100:
            store_record.max_discount = max_discount_tmp
    except ValueError:
        pass
    
    if store_record.max_discount != old_discount:
        current_discounts = store_discount.query.filter_by(store_id=store_record.uuid).\
                filter(store_discount.discount_value > store_record.max_discount).all()
        for current_discount in current_discounts:
            current_discount.active = False
        
        create_discount(store_record)
    
    if request.form.get("show_discount_when_logged_in") == "true":
        store_record.show_discount_when_logged_in = True
    elif request.form.get("show_discount_when_logged_in") in [None, "false"]:
        store_record.show_discount_when_logged_in = False
    
    db_session.commit()

    return jsonify({"okay": True})

def merchant_widget_settings_save_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "widget_settings_save"
    db_session.add(new_engagement)
    db_session.commit()

    session = shopify.Session(shop_url, app.config['SHOPIFY_LEGACY_API_VERSION'], store_record.access_token)
    shopify.ShopifyResource.activate_session(session)

    store_record.popup_font = request.form.get("popup_font")

    old_discount_prefix = store_record.discount_prefix
    store_record.discount_prefix = request.form.get("popup_discount_code_prefix")

    # Check if we need to create a new discount code and/or turn off an old one
    if store_record.discount_prefix != old_discount_prefix:
        create_discount(store_record)

    popup_primary_color = request.form.get("popup_primary_color")
    if len(popup_primary_color) > 0 and popup_primary_color[0] == '#':
        popup_primary_color = popup_primary_color[1:]
    if re.search(r'^(?:[0-9a-fA-F]{3}){1,2}$', popup_primary_color):
        store_record.popup_primary_color = popup_primary_color

    popup_bg_color = request.form.get("popup_bg_color")
    if len(popup_bg_color) > 0 and popup_bg_color[0] == '#':
        popup_bg_color = popup_bg_color[1:]
    if re.search(r'^(?:[0-9a-fA-F]{3}){1,2}$', popup_bg_color):
        store_record.popup_bg_color = popup_bg_color
    
    db_session.commit()

    return jsonify({"okay": True})

def merchant_contact_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})
    
    new_engagement = merchant_engagement()
    new_engagement.uuid = str(uuid.uuid4())
    new_engagement.time = time.time()
    new_engagement.store_id = store_record.uuid
    new_engagement.engagement = "email_send"
    db_session.add(new_engagement)
    db_session.commit()
    
    subject = "Vandra Contact: " + request.form.get("name", "")
    body = "From: " + request.form.get("email") + "\n\n" + request.form.get("message")
    
    send_mail(subject, body, to=["<EMAIL>"])

    return jsonify({"okay": True})

def metabase_iframe_handler():
    # Check if we're in emulation mode
    shopify_session = getattr(request, 'shopify_session', {})
    if shopify_session.get('emulated'):
        # In emulation mode, validate admin permissions
        if not validate_admin_permissions_for_emulation():
            logger.error("Admin permissions validation failed during emulation")
            return jsonify({"error": "Admin permissions validation failed during emulation"}), 403
    
    shop_url = request.shopify_session.get('dest').split('https://')[1]

    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        logger.error(f"Store URL record not found for: {shop_url}")
        return jsonify({"error": "Store not found"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        logger.error(f"Store record not found for store_id: {store_url_record.store_id}")
        return jsonify({"error": "Store record not found"})

    intervention_type_name = request.args.get("intervention_type_name")
    store_id_override = request.args.get("store_id_override")
    emulate_store = request.args.get("emulate_store")
    
    # Handle both store_id_override and emulate_store parameters
    if emulate_store and APP_ENVIRONMENT != "production":
        store_id = emulate_store
        logger.info(f"Using emulated store ID: {emulate_store}")
    elif store_id_override and APP_ENVIRONMENT != "production":
        store_id = store_id_override
        logger.info(f"Using store ID override: {store_id_override}")
    else:
        store_id = store_record.uuid
        logger.info(f"Using default store ID: {store_record.uuid}")
    
    intervention = intervention_type.query.filter_by(name=intervention_type_name).first()
    if intervention_type_name is None or intervention is None:
        logger.error(f"Intervention type not found: {intervention_type_name}")
        return jsonify({"error": "Must provide intervention type"})

    metabase_association = metabase_analytics.query.filter_by(intervention_type_id=intervention.uuid).first()
    if metabase_association is None:
        return jsonify({"error": "Could not find metabase association"})

    today = datetime.today()

    first_day = today - timedelta(days=7)
    first_day_str = first_day.strftime('%Y-%m-%d')
    today_str = today.strftime('%Y-%m-%d')

    METABASE_INSTANCE_URL = "https://vandra.metabaseapp.com"
    payload = {
        "resource": {"dashboard": metabase_association.dashboard_id},
        "params": {
            "store_id": store_id,
            "personalization_nudge_type": intervention.name
        },
        "exp": round(time.time()) + (60 * 10)
    }


    token = jwt.encode(
        payload,
        app.config['METABASE_EMBEDDING_SECRET_KEY'],
        algorithm="HS256"
    )

    iframeUrl = f"{METABASE_INSTANCE_URL}/embed/dashboard/{token}?start_time={first_day_str}&end_time={today_str}#bordered=true&titled=true&downloads=true"

    logger.info(f"Generated analytics iframe URL for store {store_id}, intervention {intervention_type_name}")
    return jsonify({"url": iframeUrl})

def merchant_update_nudge_parameters_handler():
    """
    Handler for updating store-level intervention parameters from the merchant admin panel.
    Uses Shopify session authentication.
    """
    shop_url = None
    try:
        shop_url = request.shopify_session.get('dest').split('https://')[1]
    except Exception as e:
        return jsonify({"error": "Authentication Error", "message": "Could not identify shop."}), 401

    if not shop_url:
        return jsonify({"error": "Authentication Error", "message": "Shop URL not found in session."}), 401

    # === Get data from JSON request body ===
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Bad Request", "message": "No JSON data received"}), 400

        intervention_type_name = data.get("intervention_type_name")
        new_parameters = data.get("parameters", {}) # Get parameters from the 'parameters' key
        active_state = data.get("active")

        if not intervention_type_name:
            return jsonify({"error": "Bad Request", "message": "intervention_type_name is required"}), 400

    except Exception as e:
        return jsonify({"error": "Bad Request", "message": "Invalid JSON format"}), 400

    # === Get store_record based on shop_url ===
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "Not Found", "message": f"Store not found for {shop_url}"}), 404
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "Server Error", "message": "Store data inconsistency"}), 500
    # === End Store Record Retrieval ===

    # Use the helper from social_media_nudge_use_cases.py to ensure association exists
    from .social_media_nudge_use_cases import ensure_store_intervention_association
    association_record, err = ensure_store_intervention_association(
        store_record, intervention_type_name, active_state=active_state
    )
    if not association_record:
        return (
            jsonify(
                {
                    "error": "Not Found",
                    "message": f"Could not find intervention type {intervention_type_name}",
                }
            ),
            404,
        )

    # Get current parameters or initialize empty dict
    current_parameters = association_record.parameters or {}
    current_parameters.update(new_parameters)

    # Save updated parameters back to the association record
    association_record.parameters = current_parameters
    #if active is not None, update the active state
    if active_state is not None:
        association_record.active = active_state

    try:
        db_session.commit()
        return jsonify({"data": {"success": True, "parameters": current_parameters}})
    except Exception as e:
        db_session.rollback()
        return jsonify({"error": "Server Error", "message": "Failed to save parameters"}), 500

    # Return success and the final merged parameters
    return jsonify({"data": {"success": True, "parameters": current_parameters}})

def get_collections(store_record, store_url_record):
    access_token = store_record.access_token
    if not access_token:
        return jsonify({"error": "No access token found"})
    # Prepare GraphQL request
    headers = {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': access_token
    }
    
    data = {
        "query": ALL_COLLECTIONS_QUERY,
    }

    # Make request to Shopify using configured API version
    api_version = app.config.get('SHOPIFY_GRAPHQL_API_VERSION', '2025-01')
    url = f"https://{store_url_record.url}/admin/api/{api_version}/graphql.json"

    response = requests.post(
        url,
        json=data,
        headers=headers
    )

    if not response.ok:
        response_json = response.json()
        errors = response_json.get('errors', [])
        error_messages = '; '.join([error.get('message', 'Unknown error') for error in errors])
        error_msg = f"Shopify API error {response.status_code}: {error_messages}"
        logger.error(error_msg)
        return jsonify({"error": error_msg})

    shopify_data = response.json()

    # Check if we got any data back
    if not shopify_data.get('data'):
        logger.error("No data returned from Shopify")
        return jsonify({"error": "No data returned from Shopify"})

    collections_node = shopify_data.get('data', {}).get('collections').get('edges', [])
    if not collections_node:
        logger.error("No collections found in Shopify response")
        return jsonify({"error": "No collections found in Shopify response"})
    
    collections = [{"label": x.get('node').get('title'), "value": x.get('node').get('id').split('/')[-1]} for x in collections_node]

    return collections

def admin_check_status_handler():
    """Check if current user is a super admin based on Shopify session email."""
    try:
        # Get the email from the Shopify session
        shopify_session = getattr(request, 'shopify_session', None)
        if not shopify_session:
            return jsonify({"is_admin": False, "error": "No shopify session"})
        
        # Check if we're in emulation mode - use preserved admin email
        if shopify_session.get('emulated') and shopify_session.get('original_admin_email'):
            user_email = shopify_session.get('original_admin_email')
            
            # Validate emulation session hasn't expired (2 hours timeout)
            emulation_start = shopify_session.get('emulation_start_time', 0)
            if time.time() - emulation_start > 7200:  # 2 hours in seconds
                return jsonify({"is_admin": False, "error": "Emulation session expired"})
            
            # Validate original session is still valid by checking if original admin still exists
            admin_user_record = admin_user.query.filter(
                func.lower(admin_user.email) == user_email.lower(),
                admin_user.super_admin == True
            ).first()
            
            if not admin_user_record:
                return jsonify({"is_admin": False, "error": "Original admin no longer has super admin privileges"})
                
            return jsonify({
                "is_admin": True,
                "email": user_email,
                "emulation_mode": True
            })
        
        # Normal authentication flow - try to extract email from the Shopify session
        user_email = None
        
        # Method 1: Try to get from session destination (shop owner email)
        dest = shopify_session.get('dest', '')
        if dest:
            try:
                # Extract shop domain from dest URL
                shop_domain = dest.replace('https://', '').replace('http://', '')
                
                # Get store record to find the shop owner email
                shop_url_record = store_url.query.filter_by(url=shop_domain).first()
                if shop_url_record:
                    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
                    if shop_record and shop_record.email:
                        user_email = shop_record.email
            except Exception as e:
                logger.error(f"Error extracting email from shop record: {e}")
        
        # Method 2: Try to decode JWT token if we couldn't get email from store record
        if not user_email:
            try:
                # Get the session token from headers
                auth_header = request.headers.get('Authorization')
                if auth_header and auth_header.startswith('Bearer '):
                    token = auth_header.split(' ')[1]
                    
                    # Decode the JWT payload (second part after splitting by '.')
                    parts = token.split('.')
                    if len(parts) == 3:
                        # Decode the payload (add padding if needed)
                        payload = parts[1]
                        # Add padding if needed for base64 decoding
                        payload += '=' * (4 - len(payload) % 4)
                        decoded_payload = base64.b64decode(payload)
                        session_data = json.loads(decoded_payload)
                        
                        # Try different fields that might contain email
                        user_email = (session_data.get('email') or 
                                    session_data.get('sub') or 
                                    session_data.get('user_email') or
                                    session_data.get('dest', '').replace('https://', ''))
                        
            except Exception as e:
                logger.error(f"Error decoding JWT token: {e}")
        
        if not user_email:
            return jsonify({"is_admin": False, "error": "No email found in session"})
        
        # Check if this email exists in admin_user table with super_admin = True
        admin_user_record = admin_user.query.filter(
            func.lower(admin_user.email) == user_email.lower(),
            admin_user.super_admin == True
        ).first()
        
        is_super_admin = admin_user_record is not None
        
        return jsonify({
            "is_admin": is_super_admin,
            "email": user_email if is_super_admin else None,
            "emulation_mode": False
        })
        
    except Exception as e:
        logger.error(f"Error in admin_check_status_handler: {e}")
        return jsonify({"is_admin": False, "error": str(e)})

def admin_available_stores_handler():
    """Get list of available stores for admin emulation."""
    try:
        # Additional validation for emulation mode
        if not validate_admin_permissions_for_emulation():
            return jsonify({"error": "Admin permissions validation failed during emulation"}), 403
        
        # First check if user is super admin
        auth_check = admin_check_status_handler()
        auth_data = json.loads(auth_check.get_data())
        
        if not auth_data.get('is_admin', False):
            return jsonify({"error": "Unauthorized - Super admin required"}), 403
        
        # Get current store from Shopify session
        current_store_url = None
        current_store_uuid = None
        current_store_name = None
        
        try:
            shop_url = request.shopify_session.get('dest').split('https://')[1]
            current_store_url_record = store_url.query.filter_by(url=shop_url).first()
            if current_store_url_record:
                current_store_record = store.query.filter_by(uuid=current_store_url_record.store_id).first()
                if current_store_record:
                    current_store_url = shop_url
                    current_store_uuid = current_store_record.uuid
                    current_store_name = current_store_record.name or "Unnamed Store"
        except Exception as e:
            logger.error(f"Error identifying current store: {e}")
        
        # Get all stores with their URLs, excluding the current store
        stores_query = db_session.query(store, store_url).join(
            store_url, store.uuid == store_url.store_id
        ).filter(
            store_url.default == True
        ).order_by(store.name).all()
        
        stores_list = []
        for store_record, store_url_record in stores_query:
            # Exclude current store from the list
            if current_store_uuid and store_record.uuid == current_store_uuid:
                continue
                
            stores_list.append({
                "uuid": store_record.uuid,
                "name": store_record.name or "Unnamed Store",
                "url": store_url_record.url,
                "active": store_record.show_discount,
                "test_store": store_record.test_store
            })
        
        return jsonify({
            "stores": stores_list,
            "current_store": {
                "uuid": current_store_uuid,
                "name": current_store_name,
                "url": current_store_url
            } if current_store_uuid else None
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    
def send_support_email_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"})

    support_message = request.form.get('support_message')
    body = f"New support request from store {store_record.name}, submitted by {store_record.email}.\n\n{support_message}"
    

    Notifier.send_raw('Vandra Support Request',body,['<EMAIL>'])

    return jsonify({"success": True})
