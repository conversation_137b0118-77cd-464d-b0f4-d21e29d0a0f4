import os
import sys
sys.path.append(os.getcwd().replace("/web", "") + "/data_science")

try:
    from data_science.prepare_data import prep_data_for_model
except ModuleNotFoundError as _:
    from prepare_data import prep_data_for_model

from app import app

import json
import time
from botocore.exceptions import ClientError
import os.path
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

import boto3
import pandas as pd
from io import StringIO
from ephemeral_store import EphemeralStore
from datetime import timedelta

from utils import get_generic_logger
logger = get_generic_logger(__name__)

def get_user_session_id_from_payload(payload):
    return [value for field, value in zip(payload["fields"], payload["rows"][0]) if field in 'user_session_id'][0]



def get_prediction_from_sagemaker(deployment_id, payload):
    start_time = time.time()
    user_session_id = get_user_session_id_from_payload(payload)
    logger.info(f"Starting get_prediction_from_sagemaker() for user_session_id {user_session_id} and SageMaker deployment_id {deployment_id}")
    
    AWS_REGION = "us-east-2"
    COLS_TO_CONVERT = ['mobile', 'is_multitab_session', 'ad_google', 'ad_facebook', 'ad_tiktok', 'ad_bing', 'ad_doubleclick']
    csv_buffer = StringIO()
    
    (pd.DataFrame(payload['rows'], columns=payload['fields'])
        .drop(columns=['conversion'])
        .pipe(lambda df: df.assign(**{col: df[col].astype(bool) for col in COLS_TO_CONVERT if col in df.columns}))
        .to_csv(csv_buffer, index=False, header=False)
    )
    
    payload_sm = csv_buffer.getvalue()
    
    sagemaker_runtime = boto3.Session(aws_access_key_id=app.config["SES_ACCESS_KEY"], aws_secret_access_key=app.config["SES_SECRET"], region_name=AWS_REGION).client('sagemaker-runtime')
    logger.info(f"--- seconds to prep data for inference for user_session_id {user_session_id} and SageMaker deployment_id {deployment_id} --- {(time.time() - start_time)}")
    response = sagemaker_runtime.invoke_endpoint(
        EndpointName=f'{deployment_id}-endpoint',
        ContentType='text/csv',
        Body=payload_sm
    )
    
    result = json.loads(response['Body'].read().decode())
    
    if isinstance(result, list): # Note 2024-10-30: Results from deployments that include the new calibration postprocessing models will be single-element lists
        result = result[0]
    
    logger.info(f"--- seconds to predict for user_session_id {user_session_id} and SageMaker deployment_id {deployment_id} --- {(time.time() - start_time)}")
    return result

def get_prediction_from_sagemaker_uplift_stg2(deployment_id, payload):
    start_time = time.time()
    user_session_id = get_user_session_id_from_payload(payload)
    logger.info(f"Starting get_prediction_from_sagemaker() for user_session_id {user_session_id} and SageMaker deployment_id {deployment_id}")
    
    AWS_REGION = "us-east-2"
    COLS_TO_CONVERT = ['mobile', 'is_multitab_session', 'ad_google', 'ad_facebook', 'ad_tiktok', 'ad_bing', 'ad_doubleclick']
    csv_buffer = StringIO()
    
    (pd.DataFrame(payload['rows'], columns=payload['fields'])
        .pipe(lambda df: df.assign(**{col: df[col].astype(bool) for col in COLS_TO_CONVERT if col in df.columns}))
        .to_csv(csv_buffer, index=False, header=False)
    )
    
    payload_sm = csv_buffer.getvalue()
    
    sagemaker_runtime = boto3.Session(aws_access_key_id=app.config["SES_ACCESS_KEY"], aws_secret_access_key=app.config["SES_SECRET"], region_name=AWS_REGION).client('sagemaker-runtime')
    logger.info(f"--- seconds to prep data for inference for user_session_id {user_session_id} and SageMaker deployment_id {deployment_id} --- {(time.time() - start_time)}")
    response = sagemaker_runtime.invoke_endpoint(
        EndpointName=f'{deployment_id}-endpoint',
        ContentType='text/csv',
        Body=payload_sm
    )
    
    result = json.loads(response['Body'].read().decode())
    
    if isinstance(result, list): # Note 2024-10-30: Results from deployments that include the new calibration postprocessing models will be single-element lists
        result = result[0]
    
    logger.info(f"--- seconds to predict for user_session_id {user_session_id} and SageMaker deployment_id {deployment_id} --- {(time.time() - start_time)}")
    return result

def get_payload(filename, session_id, time_cutoff, use_cache=True):
    cache_id = f"{session_id}_{filename}_{time_cutoff}"
    if use_cache:
        start_time = time.time()
        cached_payload = EphemeralStore.find_key(key=cache_id)
        if cached_payload:
            logger.info(f"Data cached for {session_id} and {filename}")
            cached_payload = json.loads(cached_payload)
            logger.info(f"--- seconds to cache data for user_session_id {session_id} and filename {filename} --- {(time.time() - start_time)}")
            return cached_payload['is_empty'], cached_payload['payload']
    #else continue and load
    prepped_data = prep_data_for_model[filename](train_or_predict="PREDICT", time_cutoff=time_cutoff, predict_session_id=session_id)
    
    if isinstance(prepped_data, (list, tuple)):
        joined_table, _ = prepped_data # Starting with 2023082901.py, min_user_session_time_processed is also returned for use in the iterative feat eng (unused in this file)
    else:
        joined_table = prepped_data # In feat eng scripts prior to 2023082901.py, only joined_table is returned
    
    is_empty= False
    payload = {}
    if joined_table is False or len(joined_table) == 0: # Switched to using empty DataFrames instead of False during the change to iterative feat eng (2023082901.py).
        is_empty = True
    else:
        payload = dict(
            fields=list(joined_table.columns),
            rows=(
                joined_table.iloc[[0]]
                # For H2O-trained models, sends boolean features as floats to H2O MLOps in order to prevent some intermittent H2O issues. However, does not do this with scikit-learn models.
                .pipe(lambda df_: df_.astype({'mobile': 'float'}) if 'mobile' in df_.columns else df_)
                .pipe(lambda df_: df_.astype({'is_multitab_session': 'float'}) if 'is_multitab_session' in df_.columns else df_)
                .pipe(lambda df_: df_.astype({'ad_google': 'float'}) if 'ad_google' in df_.columns else df_)
                .pipe(lambda df_: df_.astype({'ad_facebook': 'float'}) if 'ad_facebook' in df_.columns else df_)
                .pipe(lambda df_: df_.astype({'ad_tiktok': 'float'}) if 'ad_tiktok' in df_.columns else df_)
                .pipe(lambda df_: df_.astype({'ad_bing': 'float'}) if 'ad_bing' in df_.columns else df_)
                .pipe(lambda df_: df_.astype({'ad_doubleclick': 'float'}) if 'ad_doubleclick' in df_.columns else df_)
                .astype(object).fillna('').astype(str).to_dict("split")["data"]
            )
        )
    if use_cache:
        delta = timedelta(minutes=5)
        logger.info(f"Caching data for {session_id} and {filename} with cache id {cache_id}")
        EphemeralStore.add_to_store(key=cache_id, expire_timedelta=delta, data=json.dumps({'is_empty' : is_empty, 'payload' : payload})) 
    return is_empty, payload
