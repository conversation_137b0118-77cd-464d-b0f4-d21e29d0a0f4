from flask import request, redirect, render_template, session, jsonify
from db import (admin_user, admin_user_login_token, click, daily_stat, discount_code,
    discount_collection, exclude_url, logged_error, merchant_engagement, model_decision, model_state,
    order, os_version_time, page_activity, page_focus_change, page_view, store, store_url, usage_charge, user_session,
    user_session_event, webhook_request)
from app import db_session
from helpers import check_admin, check_super_admin, create_discount, send_mail, validate_vandra_admin_credentials
from seed_utilities.threshold_optimization_utility import trigger_threshold_optimization

import bcrypt
from collections import OrderedDict
from datetime import datetime, timedelta
from dateutil import parser
import dateutil.tz
from email.mime.application import MIMEApplication
import random
from sqlalchemy import func
import shopify
import string
import time
import uuid
import base64
from communications import EMAIL_TEMPLATE_USAGE_CHARGE, Notifier


def vandra_admin_login_process_handler():
    input_email = request.form.get('email').lower()
    input_password = request.form.get('password', '')

    if input_email is None:
        return redirect('/vandra_admin')
    
    potential_user = admin_user.query.filter(func.lower(admin_user.email) == input_email).first()
    if potential_user is None:
        return redirect('/vandra_admin')
    
    if not validate_vandra_admin_credentials(input_email, input_password):
        return redirect('/vandra_admin')
    
    session['vandra_admin_email'] = input_email
    session['vandra_admin_token_id'] = ''.join(random.SystemRandom().choice(string.ascii_uppercase + string.ascii_lowercase + string.digits) for _ in range(64))

    new_token = admin_user_login_token()
    new_token.uuid = str(uuid.uuid4())
    new_token.time = time.time()
    new_token.token = session["vandra_admin_token_id"]
    new_token.admin_user_id = potential_user.uuid

    db_session.add(new_token)
    db_session.commit()

    if session.get("post_login_path") not in [None, ''] and 'login' not in session.get("post_login_path"):
        return redirect(session.get("post_login_path"))
    
    return redirect("/vandra_admin")

def vandra_admin_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    live_store_list = store.query.filter_by(test_store=False).\
        filter((store.show_discount == True) & (store.vandra_admin_show_discount == True)).order_by(store.name).all()
    inactive_store_list = store.query.filter_by(test_store=False).\
        filter((store.show_discount == False) | (store.vandra_admin_show_discount == False)).order_by(store.name).all()
    test_store_list = store.query.filter_by(test_store=True).order_by(store.name).all()
    
    store_dict = {}
    for store_record in live_store_list + inactive_store_list + test_store_list:
        store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
            order_by(store_url.default.desc()).first()
        if store_url_record is None:
            continue
        
        store_dict[store_record.uuid] = store_url_record.url
    
    # Collect any usage charges we have teed up
    usage_charge_list = []
    usage_charges = usage_charge.query.filter_by(submitted_to_shopify=False).\
        order_by(usage_charge.time).all()
    for charge in usage_charges:
        # Get sales stats from relevant period
        total_sales = order.query.with_entities(func.sum(order.subtotal_price).label("total_price")).\
            filter_by(store_id=charge.store_id).\
            filter(order.created_at >= charge.start_time).\
            filter(order.created_at <= charge.end_time).first()[0]
        
        start_date = datetime.fromtimestamp(charge.start_time).strftime("%-m/%-d/%Y")
        end_date = datetime.fromtimestamp(charge.end_time).strftime("%-m/%-d/%Y")
        usage_charge_list.append({
            "uuid": charge.uuid,
            "store": store_dict[charge.store_id],
            "date_range": start_date + " - " + end_date,
            "charge": "${:,.2f}".format(charge.price),
            "total_orders": "${:,.2f}".format(total_sales),
            "description": charge.description
        })
    
    return render_template("vandra_admin.html",
        live_store_list=live_store_list,
        inactive_store_list=inactive_store_list,
        test_store_list=test_store_list,
        usage_charge_list=usage_charge_list
    )

def vandra_admin_toggle_merchant_show_discount_handler():
    if not check_super_admin():
        return redirect('/vandra_admin_login')
    
    store_uuid = request.form.get("store_uuid")
    store_record = store.query.filter_by(uuid=store_uuid).first()
    if store_record is None:
        return redirect("/vandra_admin")
    
    store_record.show_discount = not store_record.show_discount
    
    new_state_switch = model_state()
    new_state_switch.uuid = str(uuid.uuid4())
    new_state_switch.time = time.time()
    new_state_switch.store_id = store_record.uuid
    new_state_switch.merchant_enabled = store_record.show_discount
    new_state_switch.vandra_admin_enabled = store_record.vandra_admin_show_discount
    db_session.add(new_state_switch)

    db_session.commit()

    return redirect("/vandra_admin")

def vandra_admin_toggle_admin_show_discount_handler():
    if not check_super_admin():
        return redirect('/vandra_admin_login')
    
    store_uuid = request.form.get("store_uuid")
    store_record = store.query.filter_by(uuid=store_uuid).first()
    if store_record is None:
        return redirect("/vandra_admin")
    
    store_record.vandra_admin_show_discount = not store_record.vandra_admin_show_discount

    new_state_switch = model_state()
    new_state_switch.uuid = str(uuid.uuid4())
    new_state_switch.time = time.time()
    new_state_switch.store_id = store_record.uuid
    new_state_switch.merchant_enabled = store_record.show_discount
    new_state_switch.vandra_admin_enabled = store_record.vandra_admin_show_discount
    db_session.add(new_state_switch)

    db_session.commit()

    return redirect("/vandra_admin")

def vandra_send_usage_charge_order_email(start_time,
                                         end_time,
                                         commission_rate,
                                         total_sales,
                                         price,
                                         description,
                                         email
                                         ):
    data = {
        "start_date" : datetime.utcfromtimestamp(start_time).strftime('%m/%d'),
        "end_date" : datetime.utcfromtimestamp(end_time).strftime('%m/%d'),
        "orders_during_period" : '{:,}'.format(round(total_sales, 0)),
        "vandra_charge" : '{:,}'.format(round(price, 0)),
        "description" : description
    }
    
    
    Notifier.send_from_template(EMAIL_TEMPLATE_USAGE_CHARGE,[{"email": email}],data=data)
    

def vandra_send_email_from_usage_charge(usage_charge_record, email_override=None):
    store_record = usage_charge_record.parent
    target_email = email_override or store_record.email
    
    vandra_send_usage_charge_order_email(usage_charge_record.start_time, 
                                         usage_charge_record.end_time,
                                         store_record.commission_rate,
                                         usage_charge_record.total_sales,
                                         usage_charge_record.price,
                                         usage_charge_record.description,
                                         target_email
                                         )
    
def vandra_admin_submit_charge_handler():

    from app import app
    
    if not check_super_admin():
        return redirect("/vandra_admin_login")
    
    if request.form.get("usage_charge_id") is None:
        return redirect("/vandra_admin")
    
    usage_charge_record = usage_charge.query.filter_by(uuid=request.form.get("usage_charge_id")).first()
    if usage_charge_record is None:
        return redirect("/vandra_admin")
    
    if usage_charge_record.submitted_to_shopify:
        return redirect("/vandra_admin")
    
    store_record = store.query.filter_by(uuid=usage_charge_record.store_id).first()
    if store_record is None:
        return redirect("/vandra_admin")
    
    store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
        order_by(store_url.default.desc()).first()
    if store_url_record is None:
        return redirect("/vandra_admin")
    
    if "vandra" not in store_url_record.url:
        usage_charge_dict = {
            "recurring_application_charge_id": store_record.charge_id,
            "description": usage_charge_record.description,
            "price": float(usage_charge_record.price)
        }
        
        # Submit charge to Shopify
        session = shopify.Session(store_url_record.url, app.config['SHOPIFY_API_VERSION'], store_record.access_token)
        shopify.ShopifyResource.activate_session(session)
        
        charge = shopify.UsageCharge(usage_charge_dict)
        charge.save()
    
    usage_charge_record.submitted_to_shopify = True
    usage_charge_record.submitted_time = time.time()
    db_session.commit()
    
    # Send merchant email about charge
    vandra_send_email_from_usage_charge(usage_charge_record)
    
    return redirect("/vandra_admin")

def vandra_admin_delete_usage_charge_handler():
    if not check_super_admin():
        return redirect('/vandra_admin')
    
    usage_charge_result = usage_charge.query.filter_by(uuid=request.args.get("id")).first()
    if usage_charge_result is None:
        return redirect("/vandra_admin")
    
    db_session.delete(usage_charge_result)
    db_session.commit()
    
    return redirect("/vandra_admin")

def vandra_admin_manage_admins_handler():
    if not check_super_admin():
        return redirect('/vandra_admin')
    
    admin_query = admin_user.query.order_by(admin_user.email).all()
    
    return render_template("vandra_admin_manage_admins.html", admin_list=admin_query)

def vandra_admin_delete_admin_handler():
    if not check_super_admin():
        return redirect('/vandra_admin')
    
    delete_user = admin_user.query.filter_by(uuid=request.args.get("id")).first()
    if delete_user is None:
        return redirect("/vandra_admin_manage_admins")
    db_session.delete(delete_user)
    db_session.commit()
    
    return redirect("/vandra_admin_manage_admins")


def create_admin_user(email, password, super_admin):
    new_admin = admin_user()
    new_admin.uuid = str(uuid.uuid4())
    new_admin.time = time.time()
    new_admin.email = email 
    password_bytes = password.encode('utf-8')
    salt = bcrypt.gensalt()
    new_admin.password = bcrypt.hashpw(password_bytes, salt)
    new_admin.super_admin = super_admin
    
    db_session.add(new_admin)
    db_session.commit()
    
    return new_admin

def generate_password():
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))


def vandra_admin_add_admin_handler():
    if not check_super_admin():
        return redirect('/vandra_admin')
    email = request.form.get("email")
    password = generate_password()
    super_admin = request.form.get("super_admin") is not None
    create_admin_user(email, password, super_admin)
    return render_template("vandra_admin_new_admin.html", email=email, password=password)


def vandra_admin_dashboard_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    original_end_time = time.time()
    end_time = time.time()
    
    store_dict = {}
    store_query = store.query.all()
    for store_record in store_query:
        store_dict[store_record.uuid] = store_record.commission_rate
    
    stat_labels = [
        "opportunities",
        "shown",
        "show_rate",
        "dismissed",
        "applied",
        "show_to_apply",
        "conversions",
        "apply_to_convert",
        "conversions_dollars"
    ]
    
    day_list = []
    stat_list = []
    while end_time > original_end_time - 86400*13:
        start_time = end_time - 86400
        day_list.append(datetime.utcfromtimestamp(start_time).strftime('%m-%d-%Y'))
        if len([x for x in day_list if x != "Week"]) % 7 == 0:
            day_list.append("Week")
        
        opportunities = daily_stat.query.with_entities(func.sum(daily_stat.opportunity_sessions)).\
            filter(daily_stat.end_time >= start_time).\
            filter(daily_stat.end_time < end_time).scalar() or 0
        decisions = daily_stat.query.with_entities(func.sum(daily_stat.opportunity_sessions_with_predictions)).\
            filter(daily_stat.end_time >= start_time).\
            filter(daily_stat.end_time < end_time).scalar() or 0
        shown = daily_stat.query.with_entities(func.sum(daily_stat.popups_shown)).\
            filter(daily_stat.end_time >= start_time).\
            filter(daily_stat.end_time < end_time).scalar() or 0
        dismissed = daily_stat.query.with_entities(func.sum(daily_stat.popups_dismissed)).\
            filter(daily_stat.end_time >= start_time).\
            filter(daily_stat.end_time < end_time).scalar() or 0
        applied = daily_stat.query.with_entities(func.sum(daily_stat.popups_applied)).\
            filter(daily_stat.end_time >= start_time).\
            filter(daily_stat.end_time < end_time).scalar() or 0
        conversions = daily_stat.query.with_entities(func.sum(daily_stat.vandra_conversions)).\
            filter(daily_stat.end_time >= start_time).\
            filter(daily_stat.end_time < end_time).scalar() or 0
        conversions_dollars = daily_stat.query.\
            with_entities(func.sum(daily_stat.vandra_conversions_dollars)).\
            filter(daily_stat.end_time >= start_time).\
            filter(daily_stat.end_time < end_time).scalar() or 0
        
        stat_list.append({
            "summary": False,
            "start_time": start_time,
            "end_time": end_time,
            "opportunities": '{:,}'.format(opportunities),
            "decisions": '{:,}'.format(decisions),
            "shown": '{:,}'.format(shown),
            "show_rate": '{:.2%}'.format(shown/decisions) if opportunities != 0 else "0%",
            "dismissed": '{:,}'.format(dismissed),
            "applied": '{:,}'.format(applied),
            "show_to_apply": '{:.2%}'.format(applied/shown) if shown != 0 else "0%",
            "conversions": '{:,}'.format(conversions),
            "apply_to_convert": '{:.2%}'.format(conversions/applied) if applied != 0 else "0%",
            "conversions_dollars": '${:,.2f}'.format(conversions_dollars)
        })
        
        end_time = start_time
    
    # Add in weekly summaries
    stat_list_final = []
    for i, stat in enumerate(stat_list):
        stat_list_final.append(stat)
        if (i+1) % 7 != 0 or i == 0:
            continue
        
        opportunities = 0
        decisions = 0
        shown = 0
        dismissed = 0
        applied = 0
        conversions = 0
        conversions_dollars = 0
        for j in range(7):
            opportunities += int(stat_list[i-j]["opportunities"].replace(",", ""))
            decisions += int(stat_list[i-j]["decisions"].replace(",", ""))
            shown += int(stat_list[i-j]["shown"].replace(",", ""))
            dismissed += int(stat_list[i-j]["dismissed"].replace(",", ""))
            applied += int(stat_list[i-j]["applied"].replace(",", ""))
            conversions += int(stat_list[i-j]["conversions"].replace(",", ""))
            conversions_dollars += float(stat_list[i-j]["conversions_dollars"].replace("$", "").replace(",", ""))
        show_rate = shown/decisions if opportunities != 0 else 0
        show_to_apply = applied/shown if shown != 0 else 0
        apply_to_convert = conversions/applied if applied != 0 else 0
        
        stat_list_final.append({
            "summary": True,
            "opportunities": '{:,}'.format(opportunities),
            "shown": '{:,}'.format(shown),
            "show_rate": '{:.2%}'.format(show_rate),
            "dismissed": '{:,}'.format(dismissed),
            "applied": '{:,}'.format(applied),
            "show_to_apply": '{:.2%}'.format(show_to_apply),
            "conversions": '{:,}'.format(conversions),
            "apply_to_convert": '{:.2%}'.format(apply_to_convert),
            "conversions_dollars": '${:,.2f}'.format(conversions_dollars)
        })
    
    return render_template("vandra_admin_dashboard.html",
        day_list=day_list, stat_labels=stat_labels, stat_list=stat_list_final)

def vandra_admin_user_session_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    user_sessions = user_session.query.order_by(user_session.time.desc()).limit(100).all()
    
    user_session_uuid = request.form.get("user_session_uuid")
    if user_session_uuid is None:
        return render_template("vandra_admin_user_session.html",
            user_sessions=user_sessions
        )
    
    user_session_record = user_session.query.filter_by(uuid=user_session_uuid).first()
    if user_session_record is None:
        return redirect("/vandra_admin")
    
    user_session_time = user_session_record.time
    events.append({
        "time": user_session_time,
        "session_timestamp": 0,
        "action": "user_session_start"
    })

    events = []
    user_session_event_records = user_session_event.query.filter_by(user_session_id=user_session_record.uuid).order_by(user_session_event.time.desc()).all()
    for user_session_event_record in user_session_event_records:
        events.append({
            "time": user_session_event_record.time,
            "action": user_session_event_record.action
        })
    page_view_records = page_view.query.filter_by(user_session_id=user_session_record.uuid).order_by(page_view.time.desc()).all()
    for page_view_record in page_view_records:
        events.append({
            "time": page_view_record.time,
            "session_timestamp": page_view_record.time - user_session_time,
            "action": "page_view",
            "document_has_focus": page_view_record.document_has_focus,
            "dwell_time": page_view_record.dwell_time,
            "page": page_view_record.page,
            "total_mouse_move": page_view_record.total_mouse_move,
            "total_scroll": page_view_record.total_scroll
        })
        page_focus_change_records = page_focus_change.query.filter_by(page_view_id=page_view_record.uuid).order_by(page_focus_change.time.desc()).all()
        for page_focus_change_record in page_focus_change_records:
            events.append({
                "time": page_focus_change_record.time,
                "session_timestamp": page_focus_change_record.time - user_session_time,
                "action": "page_focus_change",
                "coming_into_focus": page_focus_change_record.coming_into_focus
            })
        click_records = click.query.filter_by(page_view_id=page_view_record.uuid).order_by(click.time.desc()).all()
        for click_record in click_records:
            events.append({
                "time": click_record.time,
                "session_timestamp": click_record.time - user_session_time,
                "action": "click",
                "base_uri": click_record.base_uri,
                "children_count": click_record.children_count,
                "class_name": click_record.class_name,
                "client_width": click_record.client_width,
                "client_height": click_record.client_height,
                "client_top": click_record.client_top,
                "client_left": click_record.client_left,
                "element_id": click_record.element_id,
                "first_child_name": click_record.first_child_name,
                "inner_text": click_record.inner_text,
                "last_child_name": click_record.last_child_name,
                "next_sibling_name": click_record.next_sibling_name,
                "offset_width": click_record.offset_width,
                "offset_height": click_record.offset_height,
                "parent_node_name": click_record.parent_node_name,
                "previous_sibling_name": click_record.previous_sibling_name,
                "style": click_record.style,
                "tag_name": click_record.tag_name
            })
        page_activity_records = page_activity.query.filter_by(page_view_id=page_view_record.uuid).order_by(page_activity.time.desc()).all()
        for page_activity_record in page_activity_records:
            events.append({
                "time": page_activity_record.time,
                "session_timestamp": page_activity_record.time - user_session_time,
                "action": "page_activity",
                "dwell_time": page_activity_record.dwell_time,
                "mouse_move": page_activity_record.mouse_move,
                "scroll": page_activity_record.scroll
            })
    events = sorted(events, key=lambda x: x["time"], reverse=True)

    user_sessions = user_session.query.order_by(user_session.time.desc()).limit(100).all()

    return render_template("vandra_admin_user_session.html",
        user_session_uuid=user_session_uuid,
        user_sessions=user_sessions,
        events=events
    )

def vandra_admin_webhooks_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    # Webhook requests
    requests = webhook_request.query.order_by(webhook_request.time.desc()).limit(100).all()

    return render_template("vandra_admin_webhooks.html", requests=requests)

def vandra_admin_error_log_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    error_query = logged_error.query.order_by(logged_error.time.desc()).limit(100).all()

    error_list = []
    for error_record in error_query:
        error_list.append({
            "date": datetime.fromtimestamp(error_record.time).strftime("%-m/%-d/%Y"),
            "error_text": error_record.error_text,
            "time": datetime.fromtimestamp(error_record.time).strftime("%-H:%M:%S")
        })
    
    return render_template("vandra_admin_error_log.html", error_list=error_list)

def vandra_admin_store_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    store_id = request.args.get("id")
    if store_id is None:
        return redirect('/vandra_admin')

    store_record = store.query.filter_by(uuid=store_id).first()
    if store_record is None:
        return redirect('/vandra_admin')
    
    # Store settings
    store_settings = dict()
    store_settings["maximum_discount"] = store_record.max_discount
    store_settings["commission_rate"] = store_record.commission_rate * 100
    store_settings["show_discount_when_logged_in"] = store_record.show_discount_when_logged_in
    store_settings["show_discount_to_previous_customers"] = store_record.show_discount_to_previous_customers
    store_settings["popup_primary_color"] = store_record.popup_primary_color
    store_settings["popup_bg_color"] = store_record.popup_bg_color
    store_settings["popup_font"] = store_record.popup_font
    store_settings["popup_text_header"] = store_record.popup_text_header
    store_settings["popup_text_body"] = store_record.popup_text_body
    store_settings["popup_text_button"] = store_record.popup_text_button
    store_settings["popup_text_button_close"] = store_record.popup_text_button_close
    store_settings["popup_text_success"] = store_record.popup_text_success
    store_settings["minimized_text_header"] = store_record.minimized_text_header
    store_settings["auto_apply_text_body"] = store_record.auto_apply_text_body
    store_settings["popup_image_url"] = store_record.popup_image_url or ""
    store_settings["discount_prefix"] = store_record.discount_prefix
    store_settings["discount_one_time"] = store_record.discount_one_time
    store_settings["discount_subscription"] = store_record.discount_subscription
    
    # Exclude tags
    # Discount collections
    discount_collection_query = discount_collection.query.filter_by(store_id=store_record.uuid).all()
    store_settings["discount_collections"] = "\n".join([x.collection_id for x in discount_collection_query])
    # Exclude URLs
    exclude_url_query = exclude_url.query.filter_by(store_id=store_record.uuid).all()
    store_settings["exclude_urls"] = "\n".join([x.url for x in exclude_url_query])

    # Default Store URL
    store_url_default = store_url.query.filter_by(store_id=store_record.uuid, default=True).first()
    store_settings["store_url_default"] = store_url_default.url

     # Non-Default Store URLs
    store_urls_non_default = store_url.query.filter_by(store_id=store_record.uuid, default=False).all()
    store_settings["store_urls_non_default"] = "\n".join([x.url for x in store_urls_non_default])
    
    return render_template("vandra_admin_store.html",
        store_id=store_record.uuid,
        store_url=store_url_default,
        store_settings=store_settings
    )

def vandra_admin_store_save_settings_handler():
    if not check_super_admin():
        return redirect('/vandra_admin_login')
    
    store_id = request.args.get("id")
    if store_id is None:
        return redirect('/vandra_admin')

    store_record = store.query.filter_by(uuid=store_id).first()
    if store_record is None:
        return redirect('/vandra_admin')
    
    discount_one_time = False
    if request.form.get("discount_one_time") == "true":
        discount_one_time = True
    discount_subscription = False
    if request.form.get("discount_subscription") == "true":
        discount_subscription = True
    
    change_discount = False
    if store_record.discount_one_time != discount_one_time or store_record.discount_subscription != discount_subscription:
        change_discount = True
    
    new_commission_rate = request.form.get("commission_rate")
    new_commission_rate = float(request.form.get("commission_rate")) / 100
    if new_commission_rate >= 0 and new_commission_rate <= 0.2:
        store_record.commission_rate = new_commission_rate
    
    store_record.show_discount_when_logged_in = True if request.form.get("show_discount_when_logged_in") == "true" else False
    store_record.show_discount_to_previous_customers = True if request.form.get("show_discount_to_previous_customers") == "true" else False
    store_record.popup_primary_color = request.form.get("popup_primary_color")
    store_record.popup_bg_color = request.form.get("popup_bg_color")
    store_record.popup_font = request.form.get("popup_font")
    store_record.popup_text_header = request.form.get("popup_text_header")
    store_record.popup_text_body = request.form.get("popup_text_body")
    store_record.popup_text_button = request.form.get("popup_text_button")
    store_record.popup_text_button_close = request.form.get("popup_text_button_close")
    store_record.popup_text_success = request.form.get("popup_text_success")
    store_record.auto_apply_text_body = request.form.get("auto_apply_text_body")
    store_record.popup_image_url = request.form.get("popup_image_url")
    store_record.minimized_text_header = request.form.get("minimized_text_header")
    store_record.discount_one_time = discount_one_time
    store_record.discount_subscription = discount_subscription

    # Get all new front end experiment ui allocations
    i = 0
    new_front_end_experiment_ui_allocations = {}
    while request.form.get(f"store_front_end_ui_name_{i}") is not None and request.form.get(f"store_front_end_ui_weight_{i}") is not None:
        store_front_end_ui_name = str(request.form.get(f"store_front_end_ui_name_{i}"))
        store_front_end_ui_weight = int(request.form.get(f"store_front_end_ui_weight_{i}"))
        # Ensure weight is greater than 0 and less than or equal to 100
        # Weights do not need to add up to 100 and will be assigned probabilities relative to other front end ui weights in the same front end experiment
        if store_front_end_ui_weight <= 0 or store_front_end_ui_weight > 100:
            return "Please enter a weight greater than 0 AND less than or equal to 100"
        # Accounts for reentries
        if new_front_end_experiment_ui_allocations[store_front_end_ui_name] is None:
            new_front_end_experiment_ui_allocations[store_front_end_ui_name] = store_front_end_ui_weight
        else:
            new_front_end_experiment_ui_allocations[store_front_end_ui_name] += store_front_end_ui_weight
        i += 1

    # Update discount prefix and expire all existing discounts if necessary
    old_max_discount = store_record.max_discount
    old_discount_prefix = store_record.discount_prefix
    store_record.discount_prefix = request.form.get("discount_prefix")
    store_record.max_discount = request.form.get("maximum_discount")
    
    if old_max_discount != store_record.max_discount or old_discount_prefix != store_record.discount_prefix:
        change_discount = True
    
    if change_discount:
        existing_discounts = discount_code.query.filter_by(store_id=store_record.uuid, expired=False).all()
        for existing_discount_record in existing_discounts:
            existing_discount_record.expired = True
        db_session.commit()
        create_discount(store_record)
    
    # discount_collections
    delete_collection_id_list = []
    discount_collections_list = [x.replace("\r", "") for x in request.form.get("discount_collections").split("\n")]
    discount_collection_query = discount_collection.query.filter_by(store_id=store_record.uuid).all()
    for existing_collection in discount_collection_query:
        if existing_collection.collection_id not in discount_collections_list:
            delete_collection_id_list.append(existing_collection.uuid)
        else:
            discount_collections_list.remove(existing_collection.collection_id)
    
    discount_collection.query.filter_by(store_id=store_record.uuid).\
        filter(discount_collection.uuid.in_(delete_collection_id_list)).delete()
    
    # Add the new discount_collections
    add_collection_list = []
    for new_collection in discount_collections_list:
        if new_collection == "":
            continue
        
        new_discount_collection = discount_collection()
        new_discount_collection.uuid = str(uuid.uuid4())
        new_discount_collection.time = time.time()
        new_discount_collection.collection_id = new_collection
        new_discount_collection.store_id = store_record.uuid
        add_collection_list.append(new_discount_collection)
    db_session.bulk_save_objects(add_collection_list)
    
    # exclude URLs
    delete_url_id_list = []
    exclude_urls_list = [x.replace("\r", "") for x in request.form.get("exclude_urls").split("\n")]
    exclude_url_query = exclude_url.query.filter_by(store_id=store_record.uuid).all()
    for existing_url in exclude_url_query:
        if existing_url.url not in exclude_urls_list:
            delete_url_id_list.append(existing_url.uuid)
        else:
            exclude_urls_list.remove(existing_url.url)
    
    # Delete the exclude_urls that aren't to be used anymore
    exclude_url.query.filter_by(store_id=store_record.uuid).\
        filter(exclude_url.uuid.in_(delete_url_id_list)).delete()
    
    # Add the new exclude_urls
    add_url_list = []
    for new_url in exclude_urls_list:
        if new_url == "":
            continue
        
        new_exclude_url = exclude_url()
        new_exclude_url.uuid = str(uuid.uuid4())
        new_exclude_url.time = time.time()
        new_exclude_url.url = new_url
        new_exclude_url.store_id = store_record.uuid
        add_url_list.append(new_exclude_url)
    db_session.bulk_save_objects(add_url_list)

    # Default Store URL
    store_url_default = store_url.query.filter_by(store_id=store_record.uuid, default=True).first()
    store_url_default.url = request.form.get("store_url_default")

    # Non-Default Store URLs
    delete_url_id_list = []
    store_urls_list = [x.replace("\r", "") for x in request.form.get("store_urls_non_default").split("\n")]
    store_url_query = store_url.query.filter_by(store_id=store_record.uuid, default=False).all()
    for existing_url in store_url_query:
        if existing_url.url not in store_urls_list:
            delete_url_id_list.append(existing_url.uuid)
        else:
            store_urls_list.remove(existing_url.url)
    
    # Delete the store_urls that aren't to be used anymore
    store_url.query.filter_by(store_id=store_record.uuid, default=False).\
        filter(store_url.uuid.in_(delete_url_id_list)).delete()
    
    # Add the new store_urls
    add_url_list = []
    for new_url in store_urls_list:
        if new_url == "":
            continue
        
        new_store_url = store_url()
        new_store_url.uuid = str(uuid.uuid4())
        new_store_url.time = time.time()
        new_store_url.default = False
        new_store_url.store_id = store_record.uuid
        new_store_url.url = new_url
        add_url_list.append(new_store_url)
    db_session.bulk_save_objects(add_url_list)

    db_session.commit()
    
    return redirect("/vandra_admin_store?id=" + store_id)

def vandra_admin_store_analytics_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    store_id = request.args.get("id")
    if store_id is None:
        return redirect('/vandra_admin')

    store_record = store.query.filter_by(uuid=store_id).first()
    if store_record is None:
        return redirect('/vandra_admin')
    
    store_url_record = store_url.query.filter_by(store_id=store_record.uuid, default=True).first()
    if store_url_record is None:
        return redirect('/vandra_admin')
    
    try:
        min_time = parser.parse(request.form.get("min_time")).timestamp()
        max_time = parser.parse(request.form.get("max_time")).timestamp()
    except:
        min_time = time.time() - 86400
        max_time = time.time()
    
    min_time_display = datetime.fromtimestamp(min_time).strftime("%-m/%-d/%Y at %H:%M")
    max_time_display = datetime.fromtimestamp(max_time).strftime("%-m/%-d/%Y at %H:%M")
    
    base_query = user_session.query.filter_by(store_id=store_record.uuid).\
        filter(user_session.time >= min_time).\
        filter(user_session.time <= max_time)

    session_count = base_query.count()
    conversion_count = base_query.filter_by(conversion=True).count()
    
    conversion_value = base_query.with_entities(func.sum(user_session.cart_size).label('conversion_value')).\
        filter_by(conversion=True).first().conversion_value
    
    opportunity_count = base_query.filter_by(vandra_opportunity=True).count()
    
    session_decision_count = base_query.filter(user_session.model_decisions.any(model_decision.time > 0)).count()
    
    opportunity_decision_count = base_query.filter_by(vandra_opportunity=True).\
        filter((user_session.holdout == False) | (user_session.holdout == None)).\
        filter(user_session.model_decisions.any(model_decision.time > 0)).count()
    
    opportunity_decision_count_ho = base_query.filter_by(vandra_opportunity=True, holdout=True).\
        filter(user_session.model_decisions.any(model_decision.time > 0)).count()
    
    session_yes_decision_count = base_query.\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).count()
    
    opportunity_yes_decision_count = base_query.filter_by(vandra_opportunity=True).\
        filter((user_session.holdout == False) | (user_session.holdout == None)).\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).count()
    
    opportunity_yes_decision_count_ho = base_query.filter_by(vandra_opportunity=True, holdout=True).\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).count()
    
    session_applied_discount_count = base_query.filter_by(vandra_discount_applied=True).count()
    
    session_dismissed_count = base_query.filter_by(vandra_dismissed=True).count()
    
    vandra_conversion_count = base_query.filter_by(vandra_conversion=True).count()
    
    vandra_conversion_value = base_query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter_by(vandra_conversion=True).first().conversion_value
    
    pre_vandra_conversion_count = base_query.\
        filter(~user_session.model_decisions.any(model_decision.time > 0)).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True).count()
    
    pre_vandra_conversion_count_all = base_query.\
        filter(~user_session.model_decisions.any(model_decision.time > 0)).\
        filter_by(conversion=True).count()
    
    pre_vandra_conversion_value = base_query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(~user_session.model_decisions.any(model_decision.time > 0)).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True).first().conversion_value
    
    pre_vandra_conversion_value_all = base_query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(~user_session.model_decisions.any(model_decision.time > 0)).\
        filter_by(conversion=True).first().conversion_value
    
    no_conversion_count = base_query.\
        filter(user_session.model_decisions.any(model_decision.time > 0)).\
        filter(~user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True).\
        filter((user_session.holdout == False) | (user_session.holdout == None)).count()
    
    no_conversion_count_ho = base_query.\
        filter(user_session.model_decisions.any(model_decision.time > 0)).\
        filter(~user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True, holdout=True).count()
    
    no_conversion_count_all = base_query.\
        filter(user_session.model_decisions.any(model_decision.time > 0)).\
        filter(~user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).count()
    
    no_conversion_value = base_query.\
        with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(user_session.model_decisions.any(model_decision.time > 0)).\
        filter(~user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True).\
        filter((user_session.holdout == False) | (user_session.holdout == None)).first().conversion_value
    
    no_conversion_value_ho = base_query.\
        with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(user_session.model_decisions.any(model_decision.time > 0)).\
        filter(~user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True, holdout=True).first().conversion_value
    
    no_conversion_value_all = base_query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(user_session.model_decisions.any(model_decision.time > 0)).\
        filter(~user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).first().conversion_value
    
    yes_conversion_count = base_query.\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True).\
        filter((user_session.holdout == False) | (user_session.holdout == None)).count()
    
    yes_conversion_count_ho = base_query.\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True, holdout=True).count()
    
    yes_conversion_count_all = base_query.\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).count()
    
    yes_conversion_value = base_query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True).\
        filter((user_session.holdout == False) | (user_session.holdout == None)).first().conversion_value
    
    yes_conversion_value_ho = base_query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).filter_by(vandra_opportunity=True, holdout=True).first().conversion_value
    
    yes_conversion_value_all = base_query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(user_session.model_decisions.any((model_decision.decided_to_show_discount == True) & (model_decision.live_version == True))).\
        filter_by(conversion=True).first().conversion_value
    
    vandra_conversion_count_shown = base_query.filter_by(vandra_shown=True).\
        filter_by(vandra_conversion=True).filter_by(vandra_opportunity=True).count()
    
    vandra_conversion_value_shown = base_query.with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter_by(vandra_shown=True).\
        filter_by(vandra_conversion=True).filter_by(vandra_opportunity=True).first().conversion_value
    
    vandra_conversion_not_shown_list = base_query.filter_by(vandra_shown=False).\
        filter_by(vandra_conversion=True).filter_by(vandra_opportunity=True).all()
    
    vandra_conversion_not_shown_customer_ids = []
    vandra_conversion_count_not_shown = 0
    vandra_conversion_value_not_shown = 0

    for session_record in vandra_conversion_not_shown_list:
        vandra_conversion_count_not_shown += 1
        vandra_conversion_value_not_shown += session_record.cart_size
        if session_record.customer_cookie is not None:
            vandra_conversion_not_shown_customer_ids.append(session_record.customer_cookie)
    
    shown_records = user_session.query.filter_by(store_id=store_record.uuid).\
        filter(user_session.time <= max_time).\
        filter(user_session.customer_cookie.in_(vandra_conversion_not_shown_customer_ids)).\
        filter_by(vandra_shown=True).all()
    for shown_record in shown_records:
        while shown_record.customer_cookie in vandra_conversion_not_shown_customer_ids:
            vandra_conversion_not_shown_customer_ids.remove(shown_record.customer_cookie)

    vandra_conversion_count_not_shown_any_session = base_query.\
        filter(user_session.customer_cookie.in_(vandra_conversion_not_shown_customer_ids)).\
        filter_by(vandra_conversion=True).count()
    
    vandra_conversion_value_not_shown_any_session = base_query.\
        with_entities(func.sum(user_session.cart_size).label("conversion_value")).\
        filter(user_session.customer_cookie.in_(vandra_conversion_not_shown_customer_ids)).\
        filter_by(vandra_conversion=True).first().conversion_value
    
    store_display = dict()
    store_display["id"] = store_record.uuid
    store_display["url"] = store_url_record.url
    store_display["session_count"] = session_count
    store_display["conversion_count"] = conversion_count
    store_display["conversion_value"] = conversion_value or 0
    store_display["conversion_rate"] = round((conversion_count / session_count)*100, 1) if session_count > 0 else 0
    store_display["opportunity_count"] = opportunity_count
    store_display["number_of_decisions"] = session_decision_count
    store_display["number_of_decisions_on_opportunities"] = opportunity_decision_count
    store_display["number_of_decisions_on_opportunities_ho"] = opportunity_decision_count_ho
    store_display["number_of_discounts"] = session_yes_decision_count
    store_display["number_of_discounts_on_opportunities"] = opportunity_yes_decision_count
    store_display["number_of_discounts_on_opportunities_ho"] = opportunity_yes_decision_count_ho
    store_display["number_of_discounts_applied"] = session_applied_discount_count
    store_display["number_of_discounts_dismissed"] = session_dismissed_count
    store_display["vandra_conversion_count"] = vandra_conversion_count
    store_display["vandra_conversion_value"] = vandra_conversion_value or 0
    store_display["vandra_conversion_rate"] = round((vandra_conversion_count / session_yes_decision_count)*100, 1) if session_yes_decision_count > 0 else 0
    store_display["pre_vandra_conversion_count_all"] = pre_vandra_conversion_count_all
    store_display["pre_vandra_conversion_value_all"] = pre_vandra_conversion_value_all or 0
    store_display["no_conversion_count_all"] = no_conversion_count_all
    store_display["no_conversion_value_all"] = no_conversion_value_all or 0
    store_display["yes_conversion_count_all"] = yes_conversion_count_all
    store_display["yes_conversion_value_all"] = yes_conversion_value_all or 0
    store_display["pre_vandra_conversion_count"] = pre_vandra_conversion_count
    store_display["pre_vandra_conversion_value"] = pre_vandra_conversion_value or 0
    store_display["no_conversion_count"] = no_conversion_count
    store_display["no_conversion_count_ho"] = no_conversion_count_ho
    store_display["no_conversion_value"] = no_conversion_value or 0
    store_display["no_conversion_value_ho"] = no_conversion_value_ho or 0
    store_display["yes_conversion_count"] = yes_conversion_count
    store_display["yes_conversion_count_ho"] = yes_conversion_count_ho
    store_display["yes_conversion_value"] = yes_conversion_value or 0
    store_display["yes_conversion_value_ho"] = yes_conversion_value_ho or 0
    store_display["vandra_conversion_count_shown"] = vandra_conversion_count_shown
    store_display["vandra_conversion_value_shown"] = vandra_conversion_value_shown or 0
    store_display["vandra_conversion_count_not_shown"] = vandra_conversion_count_not_shown
    store_display["vandra_conversion_value_not_shown"] = vandra_conversion_value_not_shown or 0
    store_display["vandra_conversion_count_not_shown_any_session"] = vandra_conversion_count_not_shown_any_session
    store_display["vandra_conversion_value_not_shown_any_session"] = vandra_conversion_value_not_shown_any_session or 0
    
    # Engagements
    engagement_dict = OrderedDict()
    engagement_query = merchant_engagement.query.\
        filter_by(store_id=store_record.uuid).\
        filter(merchant_engagement.time > min_time).\
        filter(merchant_engagement.time < max_time).all()
    for engagement_record in engagement_query:
        if engagement_record.engagement not in engagement_dict:
            engagement_dict[engagement_record.engagement] = 0
        engagement_dict[engagement_record.engagement] += 1
    
    engagement_dict_sorted = OrderedDict(sorted(engagement_dict.items(), key=lambda key_value_pair: key_value_pair[1], reverse=True))
    
    return render_template("vandra_admin_store_analytics.html",
        store_display=store_display,
        min_time=min_time_display,
        max_time=max_time_display,
        engagement_dict=engagement_dict_sorted
    )


# shared by vandra_admin_daily_stats and vandra_admin_merchant_stats_over_time
stat_list = [
    "Sessions",
    "Sessions with predictions",
    "Predictions with \"show\" decision",
    "Conversions",
    "Conversions before prediction",
    "Conversions with \"show\" decision",
    "Holdout sessions",
    "Holdouts with \"show\" decision",
    "Holdout conversions",
    "Holdout conversions with \"show\" decision",
    "Opportunity sessions",
    "Opportunity predictions",
    "Opportunity with \"show\" decision",
    "Opportunity conversions",
    "Opportunity conversions before prediction",
    "Opportunity conversions with \"show\" decision",
    "Merchant admin engagements",
    "Popups shown",
    "Popups applied",
    "Popups dismissed",
    "Vandra conversions",
    "Vandra conversions (not shown that session)"
]

def format_percent(num, den):
    if den in [0, None]:
        return " (-)"
    return " (" + str(round((num / den) * 100, 1)) + "%)"

def add_stat_values(add_dict, stat_record):
    add_dict["Sessions"] = str(stat_record.sessions)
    add_dict["Sessions with predictions"] = str(stat_record.sessions_with_predictions) + format_percent(stat_record.sessions_with_predictions, stat_record.sessions)
    add_dict["Predictions with \"show\" decision"] = str(stat_record.sessions_with_show_predictions) + format_percent(stat_record.sessions_with_show_predictions, stat_record.sessions_with_predictions)
    add_dict["Conversions"] = str(stat_record.conversions) + format_percent(stat_record.conversions, stat_record.sessions)
    add_dict["Conversions before prediction"] = str(stat_record.conversions_before_predictions) + format_percent(stat_record.conversions_before_predictions, stat_record.conversions)
    add_dict["Conversions with \"show\" decision"] = str(stat_record.conversions_vandra_show) + format_percent(stat_record.conversions_vandra_show, stat_record.conversions)
    add_dict["Holdout sessions"] = str(stat_record.holdout_sessions) + format_percent(stat_record.holdout_sessions, stat_record.sessions)
    add_dict["Holdouts with \"show\" decision"] = str(stat_record.holdout_sessions_with_show_predictions) + format_percent(stat_record.holdout_sessions_with_show_predictions, stat_record.holdout_sessions)
    add_dict["Holdout conversions"] = str(stat_record.holdout_session_conversions) + format_percent(stat_record.holdout_session_conversions, stat_record.holdout_sessions)
    add_dict["Holdout conversions with \"show\" decision"] = str(stat_record.holdout_session_conversions_vandra_show) + format_percent(stat_record.holdout_session_conversions_vandra_show, stat_record.holdout_session_conversions)
    add_dict["Opportunity sessions"] = str(stat_record.opportunity_sessions) + format_percent(stat_record.opportunity_sessions, stat_record.sessions)
    add_dict["Opportunity predictions"] = str(stat_record.opportunity_sessions_with_predictions) + format_percent(stat_record.opportunity_sessions_with_predictions, stat_record.opportunity_sessions)
    add_dict["Opportunity with \"show\" decision"] = str(stat_record.opportunity_sessions_with_show_predictions) + format_percent(stat_record.opportunity_sessions_with_show_predictions, stat_record.opportunity_sessions_with_predictions)
    add_dict["Opportunity conversions"] = str(stat_record.opportunity_session_conversions) + format_percent(stat_record.opportunity_session_conversions, stat_record.opportunity_sessions)
    add_dict["Opportunity conversions before prediction"] = str(stat_record.opportunity_session_conversions_before_predictions) + format_percent(stat_record.opportunity_session_conversions_before_predictions, stat_record.opportunity_session_conversions)
    add_dict["Opportunity conversions with \"show\" decision"] =  str(stat_record.opportunity_session_conversions_vandra_show) + format_percent(stat_record.opportunity_session_conversions_vandra_show, stat_record.opportunity_session_conversions)
    add_dict["Merchant admin engagements"] = str(stat_record.merchant_admin_engagements)
    add_dict["Popups shown"] = str(stat_record.popups_shown)
    add_dict["Popups applied"] = str(stat_record.popups_applied) + format_percent(stat_record.popups_applied, stat_record.popups_shown)
    add_dict["Popups dismissed"] = str(stat_record.popups_dismissed) + format_percent(stat_record.popups_dismissed, stat_record.popups_shown)
    add_dict["Vandra conversions"] = str(stat_record.vandra_conversions) + format_percent(stat_record.vandra_conversions, stat_record.sessions)
    add_dict["Vandra conversions (not shown that session)"] = str(stat_record.vandra_conversions_not_shown_that_session)
    
    return add_dict

def vandra_admin_daily_stats_handler():
    if not check_admin():
        return redirect('/vandra_admin_login')
    
    # Get dict of merchant names
    store_dict = {}
    store_query = store.query.all()
    for store_record in store_query:
        store_url_record = store_url.query.filter_by(store_id=store_record.uuid, default=True).first()
        store_dict[store_record.uuid] = store_record.name or store_url_record.url
    
    current_day = (datetime
        .now(dateutil.tz.gettz('America/New_York'))
        .replace(hour=0, minute=0, second=0, microsecond=0)
        .astimezone(dateutil.tz.tzutc()))
    previous_day = current_day - timedelta(days=1)
    start_time = previous_day.timestamp()
    
    current_day = current_day - timedelta(seconds=1)
    end_time = current_day.timestamp()
    
    date_string = previous_day.strftime("%Y-%m-%d at %H:%M:%S") + " to " + current_day.strftime("%Y-%m-%d at %H:%M:%S") + " (UTC)"
    
    merchant_list = []
    stat_query = daily_stat.query.filter(daily_stat.start_time == start_time).all()
    for stat_record in stat_query:
        add_merchant = {}
        add_merchant["id"] = stat_record.store_id
        add_merchant["name"] = store_dict[stat_record.store_id]
        add_merchant = add_stat_values(add_merchant, stat_record)
        merchant_list.append(add_merchant)
    
    return render_template("vandra_admin_daily_stats.html",
        date_string=date_string,
        stat_list=stat_list,
        merchant_list=merchant_list
    )

def vandra_admin_merchant_stats_over_time_handler():
    if not check_admin():
        return redirect("/vandra_admin_login")
    
    merchant_record = store.query.filter_by(uuid=request.args.get("id")).first()
    if merchant_record is None:
        return redirect("/vandra_admin_daily_stats")
    
    day_list = []
    stat_query = daily_stat.query.filter(daily_stat.store_id == merchant_record.uuid).\
        order_by(daily_stat.start_time).limit(365).all()
    for stat_record in stat_query:
        add_day = {}
        add_day["day"] = datetime.utcfromtimestamp(stat_record.start_time).strftime("%Y-%m-%d")
        add_day = add_stat_values(add_day, stat_record)
        day_list.append(add_day)
    
    return render_template("vandra_admin_merchant_stats_over_time.html",
        merchant_name=merchant_record.name or merchant_record.url,
        stat_list=stat_list,
        day_list=day_list
    )

def vandra_admin_delete_merchant_pii_handler():
    if not check_super_admin():
        return redirect("/vandra_admin_login")
    
    merchant_record = store.query.filter_by(uuid=request.args.get("id")).first()
    if merchant_record is None:
        return redirect("/vandra_admin")
    
    merchant_record.email = None
    merchant_record.shop_owner = None
    merchant_record.address1 = None
    merchant_record.address2 = None
    merchant_record.city = None
    merchant_record.state = None
    merchant_record.zip = None
    db_session.commit()

    user_session_query = user_session.query.filter_by(store_id=merchant_record.uuid).all()
    for i, user_session_record in enumerate(user_session_query):
        user_session_record.customer_cookie = None
        user_session_record.ip_address = None
        user_session_record.shopify_customer_id = None
        if i % 1000 == 0:
            db_session.commit()
    db_session.commit()

    return "Done"

def vandra_admin_os_version_time_handler():
    if not check_admin():
        return redirect("/vandra_admin_login")
    
    os_version_time_list = []
    os_version_time_query = os_version_time.query.order_by(os_version_time.release_time.desc()).all()
    for os_version_time_result in os_version_time_query:
        os_version_time_list.append({
            "id": os_version_time_result.uuid,
            "os": os_version_time_result.os,
            "os_version": os_version_time_result.os_version,
            "release_date": datetime.utcfromtimestamp(os_version_time_result.release_time).strftime("%b %y")
        })
    
    return render_template("vandra_admin_os_version_time.html",
        os_version_time_list=os_version_time_list
    )

def vandra_admin_os_version_time_add_handler():
    if not check_super_admin():
        return redirect("/vandra_admin_login")
    
    if request.form.get("os") in ["", None] or request.form.get("os_version") in ["", None]:
        return redirect("vandra_admin_os_version_time")
    
    release_time = parser.parse(request.form.get("release_date")).timestamp()
    
    new_os_version_time = os_version_time()
    new_os_version_time.uuid = str(uuid.uuid4())
    new_os_version_time.time = time.time()
    new_os_version_time.os = request.form.get("os")
    new_os_version_time.os_version = request.form.get("os_version")
    new_os_version_time.release_time = release_time
    db_session.add(new_os_version_time)
    db_session.commit()
    
    return redirect("vandra_admin_os_version_time")

def vandra_admin_os_version_time_delete_handler():
    if not check_super_admin():
        return redirect("/vandra_admin_login")
    
    os_version_time_record = os_version_time.query.filter_by(uuid=request.args.get("id")).first()
    if os_version_time_record is None:
        return redirect("vandra_admin_os_version_time")
    
    db_session.delete(os_version_time_record)
    db_session.commit()
    
    return redirect("vandra_admin_os_version_time")

def vandra_admin_store_trigger_calibration_handler():
    """
    Triggers intent threshold calibration for a store from the admin interface
    """
    if not check_super_admin():
        return redirect('/vandra_admin_login')
    
    store_id = request.args.get("id")
    if store_id is None:
        return redirect('/vandra_admin')

    store_record = store.query.filter_by(uuid=store_id).first()
    if store_record is None:
        return redirect('/vandra_admin')
        
    result = trigger_threshold_optimization(store_id)
    
    if result["success"]:
        if result["updates"]:
            message = "Successfully updated thresholds:\n"
            for layer_id, threshold in result["updates"].items():
                message += f"\nLayer {layer_id}: new threshold = {threshold}"
        elif result["needs_more_data"]:
            message = "No updates made - insufficient data. Will retry in 6 hours when more data is available."
        else:
            message = "No updates needed. Store is either using a different methodology or has no qualifying layers."
    else:
        message = f"Error triggering intent calibration: {result.get('error', 'Unknown error')}"
    
    return jsonify({
        "success": result["success"],
        "message": message,
        "details": {
            "updates": result["updates"],
            "needs_more_data": result["needs_more_data"]
        }
    })