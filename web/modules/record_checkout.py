from flask import abort, jsonify, request
from app import db_session
from db import cart_token, checkout_discount, discount_code, store, store_url, user_session, user_session_event, webhook_request
from helpers import process_webhook_request
import time
import uuid
from modules.discount_code_use_cases import record_discount_code_usage
from utils import get_generic_logger

logger = get_generic_logger(__name__)

def get_discount_codes_handler():
    args_dict = request.args.to_dict()
    if args_dict.get("shop_url") is None:
        return jsonify({"error": "error"})
    
    shop_url = args_dict.get("shop_url").lower()
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"status": "okay"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"status": "okay"})

    vandra_discount_code_records = discount_code.query.filter_by(store_id=store_record.uuid, expired=False).\
        filter(discount_code.time >= time.time() - 86400).all()
    vandra_discount_codes = [vandra_discount_code.discount_code_code for vandra_discount_code in vandra_discount_code_records]
    vandra_discount_percentage = (vandra_discount_code_records[0].price_rule_value * -1) if len(vandra_discount_code_records) > 0 else None

    return jsonify({
        "vandra_discount_codes": vandra_discount_codes,
        "vandra_discount_percentage": vandra_discount_percentage
    })

def get_session_handler():
    shop_url = request.args.get("shop_url")
    vandra_session_id = request.args.get("vandra_session_id")
    
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"status": "okay"})
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"status": "okay"})

    vandra_session = user_session.query.filter_by(uuid=vandra_session_id).first()

    return jsonify({
        "vandra_session": vandra_session.uuid if vandra_session is not None else None
    })

def record_checkout_create_handler():
    event, _ = process_webhook_request()
    if not event:
        return abort(401)

    request_data = request.get_json()

    webhook_cart_token = request_data.get("cart_token")
    cart_token_record = cart_token.query.filter_by(cart_token=webhook_cart_token).first()
    user_session_id = cart_token_record.user_session_id if cart_token_record else None
    if user_session_id is None:
        return jsonify({"status": "okay"})

    new_user_session_event = user_session_event()
    new_user_session_event.uuid = str(uuid.uuid4())
    new_user_session_event.time = time.time()
    new_user_session_event.action = "checkout_create"
    new_user_session_event.user_session_id = user_session_id
    db_session.add(new_user_session_event)
    db_session.commit()

    discount_codes = request_data.get("discount_codes", [])
    for discount_code in discount_codes:        
        checkout_discount_record = checkout_discount()
        checkout_discount_record.uuid = str(uuid.uuid4())
        checkout_discount_record.time = time.time()
        checkout_discount_record.discount_code = discount_code.get("code")
        checkout_discount_record.discount_amount = discount_code.get("amount")
        checkout_discount_record.user_session_id = user_session_id
        db_session.add(checkout_discount_record)
        
        # Add discount code tracking
        try:
            if discount_code.get("code"):
                user_session_record = user_session.query.get(user_session_id)
                if user_session_record:
                    record_discount_code_usage(
                        user_session_record=user_session_record,
                        discount_code=discount_code["code"],
                        page_view_id=None,  # No page view ID in webhook context
                        detection_source=event
                    )
        except Exception as e:
            logger.error(f"Error processing checkout discount code: {str(e)}")
            # Continue with checkout handling even if discount tracking fails

    db_session.commit()

    return "okay"

def record_checkout_update_handler():
    event, shop_url = process_webhook_request()
    if not event:
        return abort(401)

    request_data = request.get_json()

    webhook_cart_token = request_data.get("cart_token")
    cart_token_record = cart_token.query.filter_by(cart_token=webhook_cart_token).first()
    user_session_id = cart_token_record.user_session_id if cart_token_record else None
    if user_session_id is None:
        return jsonify({"status": "okay"})

    new_user_session_event = user_session_event()
    new_user_session_event.uuid = str(uuid.uuid4())
    new_user_session_event.time = time.time()
    new_user_session_event.action = "checkout_update"
    new_user_session_event.user_session_id = user_session_id
    db_session.add(new_user_session_event)
    db_session.commit()

    # Add discount code tracking
    discount_codes = request_data.get("discount_codes", [])
    for discount_code in discount_codes:        
        # Add discount code tracking
        try:
            if discount_code.get("code"):
                user_session_record = user_session.query.get(user_session_id)
                if user_session_record:
                    record_discount_code_usage(
                        user_session_record=user_session_record,
                        discount_code=discount_code["code"],
                        page_view_id=None,  # No page view ID in webhook context
                        detection_source=event
                    )
        except Exception as e:
            logger.error(f"Error processing checkout discount code: {str(e)}")
            # Continue with checkout handling even if discount tracking fails

    return "okay"