from app import db_session
from db import user_session_nudge_parameters
import time
import numpy as np
import random

def truncated_exponential_assignment(scale, min_value, max_value):
    # Calculate CDF values at min_value and max_value
    cdf_min = 1 - np.exp(-min_value / scale)
    cdf_max = 1 - np.exp(-max_value / scale)
    seeded_random = random.Random()
    uniform_assignment = seeded_random.uniform(cdf_min, cdf_max) #number between the CDF values of the cutoffs
    scaled_assignment = -scale * np.log(1 - uniform_assignment)
    scaled_delay = scaled_assignment - min_value #shift distribution to a 0 starting point, as the assignment is based on delay)
    return round(scaled_delay)


def assign_nudge_delay(user_session_record):
    #distribution parameters
    scale = 1 / 0.004603
    min_value = 71
    max_value = 500
    #assignment
    delay = truncated_exponential_assignment(scale, min_value, max_value)
    nudge_params = user_session_nudge_parameters(
        user_session_id=user_session_record.uuid,
        time=time.time(),
        trigger_type="time",
        trigger_delay=delay #modify to use actual assignment logic
    )
    db_session.add(nudge_params)
    db_session.commit()

def assign_nudge_delay_v2(user_session_record):
    #distribution parameters
    delay = random.choices(population=[0,110], weights=[0.3,0.7],k=1)[0]
    nudge_params = user_session_nudge_parameters(
        user_session_id=user_session_record.uuid,
        time=time.time(),
        trigger_type="time",
        trigger_delay=delay #modify to use actual assignment logic
    )
    db_session.add(nudge_params)
    db_session.commit()

'''
    Assigns the nudge params for ACTION_BASED_SHOW for the user session
    We do our assignment by selecting from a list of possible action delay pairs. These pairs are based on the stats given in the link below and fall in the 80th percentile
    https://uploads.linear.app/ede3e0fc-a6f0-4c75-9847-4fc496629dad/bcdff64c-0609-4c16-aff0-6de8efbdff94/961e4115-29be-41a5-b53b-793d8e0aee94

'''
def assign_show_action_and_delay(user_session_record):
    # possible actions to watch
    actions = [
        {"action": "click", "delay": 48},
        {"action" : "scroll", "delay": 18},
        {"action": "mousemove", "delay": 46},
        {"action": "focus", "delay": 231},
        {"action": "keypress", "delay": 65},
        {"action": "page_visit", "delay": 78},
        {"action": "any", "delay": 0}
    ]
    
    result = random.choice(actions)

    nudge_params = user_session_nudge_parameters(
        user_session_id=user_session_record.uuid,
        time=time.time(),
        trigger_type=result["action"],
        trigger_delay=result["delay"]
    )
    db_session.add(nudge_params)
    db_session.commit()



def assign_show_action_and_delay_v2(user_session_record):
    # possible actions to watch
    actions = [
        {"action": "any", "delay": 0},
        {"action": "engagement_action", "delay": 60}
    ]
    
    result = random.choice(actions)

    nudge_params = user_session_nudge_parameters(
        user_session_id=user_session_record.uuid,
        time=time.time(),
        trigger_type=result["action"],
        trigger_delay=result["delay"]
    )
    db_session.add(nudge_params)
    db_session.commit()


def assign_returning_messaging(user_session_record):
    trigger_type = random.choice(["returning", "control"])
    trigger_state = "situational_returning_visitor"
    nudge_params = user_session_nudge_parameters(
        user_session_id=user_session_record.uuid,
        time=time.time(),
        trigger_state=trigger_state,
        trigger_type=trigger_type
    )
    db_session.add(nudge_params)
    db_session.commit()
    return trigger_type, trigger_state

def create_nudge_params(user_session_record):
    nudge_params = user_session_nudge_parameters(
        user_session_id=user_session_record.uuid,
        time=time.time()
    )
    db_session.add(nudge_params)
    db_session.commit()