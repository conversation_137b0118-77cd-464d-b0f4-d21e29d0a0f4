import jwt
import re
import boto3
import os
import requests
import time
import uuid
from urllib.parse import urlparse, unquote
from botocore.exceptions import ClientError
import shopify
from flask import request, jsonify

from app import app, db_session
from db import store, store_url, intervention_type, store_intervention_association
from database_models import store_intervention_association_assets

from utils import get_generic_logger
logger = get_generic_logger(__name__)


def get_default_preview_video():
    """
    Helper function to retrieve the default preview video URL from app config.
    Returns a dictionary with default_preview_video or empty dict if not configured.
    """
    if app.config["DEFAULT_PREVIEW_VIDEO_URL"]:
        return {"default_preview_video": app.config["DEFAULT_PREVIEW_VIDEO_URL"]}
    return {}


def get_social_media_content_parameters(assoc):
    """
    Returns a dict of extra parameters for social_media_content nudges, including default_preview_video and assets list.
    Now also returns product_name for each asset using a single Shopify GraphQL call.
    Handles both product_id and variant_id in asset metadata.
    Adds 'filename' for each asset based on asset_url.
    """
    import requests
    params = {}
    
    # Add default preview video parameter
    params.update(get_default_preview_video())

    # Fetch all product_video assets for this store/intervention association
    assets = (
        store_intervention_association_assets.query
        .join(
            store_intervention_association,
            store_intervention_association_assets.store_intervention_association_id == store_intervention_association.uuid
        )
        .filter(
            store_intervention_association.store_id == assoc.store_id,
            store_intervention_association.intervention_type_id == assoc.intervention_type_id,
            store_intervention_association_assets.asset_type == "product_video"
        )
        .all()
    )
    asset_list = []
    gid_to_asset_indices = {}  # gid -> list of asset indices
    gids = []
    for idx, asset in enumerate(assets):
        metadata = asset.asset_metadata or {}
        product_id = metadata.get('product_id')
        variant_id = metadata.get('variant_id')
        original_filename = metadata.get('original_filename')
        gid = None
        if variant_id is not None:
            try:
                variant_id_str = str(int(variant_id))
                gid = f"gid://shopify/ProductVariant/{variant_id_str}"
            except Exception:
                gid = None
        elif product_id is not None:
            try:
                product_id_str = str(int(product_id))
                gid = f"gid://shopify/Product/{product_id_str}"
            except Exception:
                gid = None
        if gid:
            gids.append(gid)
            gid_to_asset_indices.setdefault(gid, []).append(idx)
        # Extract filename from asset_url
        filename = None
        if asset.asset_url:
            try:
                parsed_url = urlparse(asset.asset_url)
                filename = os.path.basename(parsed_url.path)
                filename = unquote(filename)
            except Exception:
                filename = None
        # Use original_filename if present, else fallback to filename from URL
        asset_list.append({
            'asset_type': asset.asset_type,
            'asset_key': asset.asset_key,
            'asset_url': asset.asset_url,
            'product_id': product_id,
            'variant_id': variant_id,
            'filename': original_filename if original_filename else filename,
        })

    # Map from asset index to product_name
    asset_index_to_product_name = {}
    # Look up store context
    store_record = store.query.filter_by(uuid=assoc.store_id).first()
    if store_record:
        # Get the default store_url for this store
        store_url_record = store_url.query.filter_by(store_id=store_record.uuid).order_by(store_url.default.desc()).first()
    if gids:
        if store_url_record:
            shop_url = store_url_record.url
            access_token = store_record.access_token
            api_version = app.config.get("SHOPIFY_LEGACY_API_VERSION", "2023-04")
            try:
                graphql_url = f"https://{shop_url}/admin/api/{api_version}/graphql.json"
                headers = {
                    'Content-Type': 'application/json',
                    'X-Shopify-Access-Token': access_token
                }
                # Build GraphQL query for nodes
                query = '''
                query getProducts($ids: [ID!]!) {
                    nodes(ids: $ids) {
                        ... on Product { id title }
                        ... on ProductVariant { id product { id title } }
                    }
                }
                '''
                variables = {"ids": gids}
                payload = {"query": query, "variables": variables}
                response = requests.post(graphql_url, json=payload, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    for node in data.get('data', {}).get('nodes', []):
                        if not node or not node.get('id'):
                            continue
                        gid = node['id']
                        if 'title' in node and 'product' not in node:
                            product_name = node['title']
                        elif 'product' in node and node['product'] and 'title' in node['product']:
                            product_name = node['product']['title']
                        else:
                            product_name = None
                        for idx in gid_to_asset_indices.get(gid, []):
                            asset_index_to_product_name[idx] = product_name
                    # else: log error if needed
                else:
                    logger.error(f"Shopify GraphQL API error: {response.status_code} {response.text}")
            except Exception as e:
                logger.error(f"Error fetching product names from Shopify: {e}")

    # Find all products
    try:
        product_options = []
        access_token = store_record.access_token
        if not access_token:
            return jsonify({"error": "No access token found"})
        # Prepare GraphQL request
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': access_token
        }
        
        data = {
            "query": """
                query {
                    products(first: 250) {
                        edges {
                            node {
                                id
                                title
                                handle
                            }
                            cursor
                        }
                        pageInfo {
                            hasNextPage
                        }
                    }
                }
            """
        }

        # Make request to Shopify using configured API version
        api_version = app.config.get('SHOPIFY_GRAPHQL_API_VERSION', '2025-01')
        url = f"https://{store_url_record.url}/admin/api/{api_version}/graphql.json"

        response = requests.post(
            url,
            json=data,
            headers=headers
        )

        if not response.ok:
            response_json = response.json()
            errors = response_json.get('errors', [])
            error_messages = '; '.join([error.get('message', 'Unknown error') for error in errors])
            error_msg = f"Shopify API error {response.status_code}: {error_messages}"
            logger.error(error_msg)
            return jsonify({"error": error_msg})

        shopify_data = response.json()

        new_products = shopify_data.get('data').get('products').get('edges')
        product_options = product_options + [{ "value": x.get("node")["id"], "label": x.get("node")["title"] } for x in new_products]
        while shopify_data.get('data').get('products').get('pageInfo').get('hasNextPage'):
            data = {
                "query": f"""
                    query {{
                        products(first: 250, after: "{new_products[-1]["cursor"]}") {{
                            edges {{
                                node {{
                                    id
                                    title
                                    handle
                                }}
                                cursor
                            }}
                            pageInfo {{
                                hasNextPage
                            }}
                        }}
                    }}
                """
            }
            response = requests.post(
                url,
                json=data,
                headers=headers
            )

            if not response.ok:
                response_json = response.json()
                errors = response_json.get('errors', [])
                error_messages = '; '.join([error.get('message', 'Unknown error') for error in errors])
                error_msg = f"Shopify API error {response.status_code}: {error_messages}"
                logger.error(error_msg)
                return jsonify({"error": error_msg})

            shopify_data = response.json()
            new_products = shopify_data.get('data').get('products').get('edges')
            product_options = product_options + [{ "value": x.get("node")["id"], "label": x.get("node")["title"] } for x in new_products]
    except Exception as e:
        logger.error(f"Error fetching product names from Shopify: {e}")

    params['product_options'] = product_options

    # Fill in product_name for each asset
    for idx, asset in enumerate(asset_list):
        asset['product_name'] = asset_index_to_product_name.get(idx)

    params['assets'] = asset_list
    return params


def merchant_search_products_handler():
    """Handle product search requests from the frontend"""
    id_token = request.args.get('id_token')
    search_query = request.args.get('query')
    
    try:
        decoded = jwt.decode(id_token, options={"verify_signature": False})
        # Get the shop name from the token
        dest = decoded.get('dest', '')
        # Extract shop name from dest URL
        if dest.startswith('https://'):
            shop_url = dest.split('https://')[1]
            shop_name = shop_url.split('.')[0]
        else:
            shop_name = dest.split('/')[-1]
        # If shop_name is still a complete URL, try to parse it
        if '.' in shop_name:
            shop_name = shop_name.split('.')[0]
    except Exception as e:
        return jsonify({"error": "Invalid token", "details": str(e)}), 401
    
    # Find the store record
    shop_url_record = store_url.query.filter_by(url=f"{shop_name}.myshopify.com").first()
    if shop_url_record is None:
        # Try alternative lookup without .myshopify.com
        alt_shop_url_record = store_url.query.filter_by(url=shop_name).first()
        if alt_shop_url_record:
            shop_url_record = alt_shop_url_record
        else:
            return jsonify({"error": "Store not found", "attempted_url": f"{shop_name}.myshopify.com"}), 404
        
    shop_record = store.query.filter_by(uuid=shop_url_record.store_id).first()
    if shop_record is None:
        return jsonify({"error": "Store not configured", "store_id": shop_url_record.store_id}), 404
    
    if not shop_record.access_token:
        return jsonify({"error": "Store has no access token"}), 401
    
    # Initialize the Shopify API
    session_url = f"{shop_name}.myshopify.com"
    shop_session = shopify.Session(session_url, app.config.get("SHOPIFY_LEGACY_API_VERSION", '2023-04'), shop_record.access_token)
    shopify.ShopifyResource.activate_session(shop_session)
    
    try:
        # Prepare headers for Shopify API calls
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': shop_record.access_token
        }
        # Validate the access token with a simple shop.json request
        version = app.config.get('SHOPIFY_LEGACY_API_VERSION', '2023-04')
        # Build the GraphQL endpoint URL
        graphql_url = f"https://{session_url}/admin/api/{version}/graphql.json"
        
        # Define the GraphQL query string
        query = """
        query getProducts($first: Int!, $query: String, $sortKey: ProductSortKeys) {
          products(first: $first, query: $query, sortKey: $sortKey) {
            edges {
              node {
                id
                title
              }
            }
          }
        }
        """
        
        # Payload for GraphQL query - if search_query is empty or None, sort by TITLE
        variables = {
            "first": 50,
            "sortKey": "TITLE"
        }
        
        # If search query is provided, use it in the query parameter
        if search_query and search_query.strip():
            variables["query"] = f"title:*{search_query}*"
        
        # Also test using the session JWT as a Bearer token
        session_headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {id_token}'
        }
        try:
            requests.post(graphql_url, json={"query": query, "variables": variables}, headers=session_headers)
        except Exception as session_e:
            return jsonify({
                "error": "Exception during session-token GraphQL request",
                "details": str(session_e)
            }), 500

        payload = {
            'query': query,
            'variables': variables
        }
        http_response = requests.post(graphql_url, json=payload, headers=headers)
        
        if http_response.status_code != 200:
            return jsonify({
                "error": "Error from Shopify API",
                "status": http_response.status_code,
                "body": http_response.text
            }), http_response.status_code
        
        result = http_response.json()
        
        if 'errors' in result:
            return jsonify({"error": "Error from Shopify API", "graphql_errors": result['errors']}), 500
        
        # Transform data to the format expected by the frontend
        products = []
        for edge in result['data']['products']['edges']:
            products.append({
                "id": edge['node']['id'],
                "title": edge['node']['title']
            })
        
        return jsonify({"products": products})
    except Exception as e:
        # Error handling unchanged
        error_type = type(e).__name__
        error_msg = str(e)
        error_details = {}
        if hasattr(e, 'response'):
            if hasattr(e.response, 'status_code'):
                error_details['status_code'] = e.response.status_code
            if hasattr(e.response, 'text'):
                error_details['response_text'] = e.response.text
            if hasattr(e.response, 'headers'):
                error_details['headers'] = dict(e.response.headers)
        return jsonify({
            "error": f"Error searching products: {error_type}: {error_msg}",
            "error_type": error_type,
            "details": error_details
        }), 500
    finally:
        shopify.ShopifyResource.clear_session()


def get_product_id_and_handle_from_gid(shop_url, access_token, gid, api_version):
    """
    Given a Shopify GID (product or variant), return (product_id, handle).
    Raises Exception if not found or on error.
    """
    import requests
    from urllib3.exceptions import InsecureRequestWarning
    
    # Suppress only the single warning from urllib3 needed
    requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    
    is_variant = 'ProductVariant' in gid
    
    # Prepare the GraphQL query
    if is_variant:
        query = '''
        query($id: ID!) {
          node(id: $id) {
            ... on ProductVariant {
              id
              product {
                id
                handle
              }
            }
          }
        }
        '''
    else:
        query = '''
        query($id: ID!) {
          node(id: $id) {
            ... on Product {
              id
              handle
            }
          }
        }
        '''
    
    variables = {"id": gid}
    payload = {
        'query': query,
        'variables': variables
    }
    
    # Prepare headers for Shopify API calls
    headers = {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': access_token
    }
    
    # Build the GraphQL endpoint URL
    graphql_url = f"https://{shop_url}/admin/api/{api_version}/graphql.json"
    
    try:
        # First try with normal verification
        http_response = requests.post(graphql_url, json=payload, headers=headers)
    except requests.exceptions.SSLError as ssl_err:
        # If SSL verification fails, retry with verify=False
        logger.warning(f"SSL certificate verification failed for {shop_url}, retrying with verify=False: {str(ssl_err)}")
        http_response = requests.post(graphql_url, json=payload, headers=headers, verify=False)
    
    if http_response.status_code != 200:
        raise Exception(f"GraphQL query failed with status {http_response.status_code}: {http_response.text}")
    
    result = http_response.json()
    
    if 'errors' in result:
        raise Exception(f"GraphQL query returned errors: {result['errors']}")
    
    data = result
    
    if is_variant:
        node = data.get('data', {}).get('node', {})
        if not node or not node.get('product'):
            raise Exception("Could not find product for variant")
        product_id = node['product']['id']
        handle = node['product']['handle']
    else:
        node = data.get('data', {}).get('node', {})
        if not node or not node.get('handle'):
            raise Exception("Could not find product handle")
        product_id = node['id']
        handle = node['handle']
    
    return product_id, handle


def ensure_store_intervention_association(store_record, intervention_type_name, active_state=None, parameters=None):
    """
    Ensure a store_intervention_association exists for the given store and intervention type name.
    If not, create one. Optionally set active_state and parameters if creating.
    Returns the association record.
    """
    intervention_type_record = intervention_type.query.filter_by(name=intervention_type_name).first()
    if not intervention_type_record:
        return None, f"Intervention type not found: {intervention_type_name}"
    
    # First, check for any existing association for this store and intervention type, regardless of active state
    association = store_intervention_association.query.filter_by(
        store_id=store_record.uuid,
        intervention_type_id=intervention_type_record.uuid
    ).order_by(store_intervention_association.time.desc()).first()
    
    if association:
        # Update the active state if provided and different
        if active_state is not None and association.active != active_state:
            association.active = active_state
            association.time = time.time()  # Update the timestamp
            db_session.commit()
    else:
        # Create a new association only if none exists
        association = store_intervention_association(
            uuid=str(uuid.uuid4()),
            time=time.time(),
            intervention_type_id=intervention_type_record.uuid,
            store_id=store_record.uuid,
            active=active_state if active_state is not None else False,
            parameters=parameters or {}
        )
        db_session.add(association)
        db_session.commit()
    
    return association, None


def get_next_file_number(assets, key_prefix, pattern_string):
    """
    Helper function to find the highest suffix number for files with a given prefix.
    Returns the next number to use (max + 1).
    """
    max_n = 0
    pattern = re.compile(pattern_string)
    for asset in assets:
        if asset.asset_url:
            match = pattern.search(asset.asset_url)
            if match:
                # If there's a suffix, get its value
                if match.group(1):
                    n = int(match.group(1))
                    if n > max_n:
                        max_n = n
                # If no suffix and max_n is still 0, set it to 1
                elif max_n == 0:
                    max_n = 1
    return max_n + 1 if max_n > 0 else 0


def merchant_get_social_video_upload_presigned_url_handler():
    """
    Generate a presigned URL for uploading social media content video, using Shopify product/variant GID or as a general fallback video.
    The uploaded file will be named after the product handle or fallback_N.mp4.
    Returns: { url, product_id, variant_id, handle, filename }
    """
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error"}), 400
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error"}), 400

    # Use the helper to ensure association exists
    association, err = ensure_store_intervention_association(store_record, "social_media_content")
    if not association:
        return jsonify({"error": err or "Intervention association not found"}), 400

    shopify_gid = request.args.get("shopify_gid")
    folder = re.sub(r'[^a-zA-Z0-9.\-]', '_', store_url_record.url.strip().lower())

    product_id_numeric = None
    variant_id_numeric = None

    try:
        # Get all video assets for this store for checking existing files
        assets = (
            store_intervention_association_assets.query
            .join(
                store_intervention_association,
                store_intervention_association_assets.store_intervention_association_id == store_intervention_association.uuid
            )
            .filter(
                store_intervention_association.store_id == store_record.uuid,
                store_intervention_association_assets.asset_type == 'product_video'
            )
            .all()
        )

        if not shopify_gid:
            # General fallback video logic - changed to use underscore
            fallback_assets = [a for a in assets if a.asset_key == 'fallback']
            next_n = get_next_file_number(fallback_assets, 'fallback', r'fallback_(\d+)\.mp4$')
            
            # If no existing files were found, start with 1
            if next_n == 0:
                next_n = 1
                
            filename = f"fallback_{next_n}.mp4"
            key = f"{folder}/{filename}"
            product_id = None
            handle = None
        else:
            # Product/variant logic
            try:
                product_id, handle = get_product_id_and_handle_from_gid(
                    shop_url,
                    store_record.access_token,
                    shopify_gid,
                    app.config.get("SHOPIFY_LEGACY_API_VERSION", '2023-04')
                )
            except Exception as e:
                return jsonify({"error": str(e)}), 500
            
            # Check if files with this handle already exist
            base_handle = handle
            handle_pattern = re.escape(base_handle) + r'(?:_(\d+))?\.mp4$'
            
            next_n = get_next_file_number(assets, base_handle, handle_pattern)
            
            # Determine filename based on existence of previous files
            if next_n > 0:
                filename = f"{base_handle}_{next_n}.mp4"
            else:
                filename = base_handle if base_handle.endswith('.mp4') else f"{base_handle}.mp4"
                
            key = f"{folder}/{filename}"
            
            # Extract product and variant IDs
            product_id_match = re.search(r'Product/(\d+)', product_id or '')
            if product_id_match:
                product_id_numeric = product_id_match.group(1)
            if 'ProductVariant' in shopify_gid:
                variant_id_match = re.search(r'ProductVariant/(\d+)', shopify_gid)
                if variant_id_match:
                    variant_id_numeric = variant_id_match.group(1)
            else:
                variant_id_numeric = None

        # Ensure key always ends with .mp4
        if not key.endswith('.mp4'):
            key += '.mp4'

        # Optionally, get Content-Type from request (if passed)
        content_type = request.args.get('content_type')
        if not content_type:
            content_type = 'video/mp4'  # Default for video

        s3_client = boto3.client("s3",
                    aws_access_key_id=app.config["SES_ACCESS_KEY"],
                    aws_secret_access_key=app.config["SES_SECRET"],
                    region_name="eu-north-1")
        formatted_bucket_name = "vandra-social-media-assets"
        client_method = "put_object"
        method_parameters = {"Bucket": formatted_bucket_name, "Key": key, "ContentType": content_type}
        url = s3_client.generate_presigned_url(
            ClientMethod=client_method, 
            Params=method_parameters, 
            ExpiresIn=60
        )
        response_payload = {
            "url": url,
            "product_id": product_id_numeric,
            "variant_id": variant_id_numeric,
            "handle": handle,
            "filename": filename
        }
        return jsonify(response_payload), 200
    except ClientError as err:
        return jsonify({"error": "Failed to generate upload URL"}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500


def merchant_add_social_video_asset_handler():
    shop_url = request.shopify_session.get('dest').split('https://')[1]
    store_url_record = store_url.query.filter_by(url=shop_url).first()
    if store_url_record is None:
        return jsonify({"error": "error: store_url_record not found"}), 400
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"error": "error: store_record not found"}), 400

    data = request.get_json()
    product_id = data.get('product_id')
    variant_id = data.get('variant_id')
    asset_url = data.get('asset_url')
    is_fallback = data.get('is_fallback', False)
    original_filename = data.get('original_filename')  # <-- NEW: get original filename from request

    if not asset_url:
        return jsonify({"error": "Missing asset_url"}), 400

    # Find the intervention type for social media content
    intervention_type_record = intervention_type.query.filter_by(name="social_media_content").first()
    if not intervention_type_record:
        return jsonify({"error": "Intervention type not found"}), 400

    # Find the active store_intervention_association
    association = store_intervention_association.query.filter_by(
        store_id=store_record.uuid,
        intervention_type_id=intervention_type_record.uuid,
    ).order_by(store_intervention_association.time.desc()).first()
    if not association:
        return jsonify({"error": "No store_intervention_association found"}), 400

    # Compose asset_key and asset_metadata
    if is_fallback or not product_id:
        asset_key = 'fallback'
        asset_metadata = {}
    else:
        asset_key = f"{product_id}-{variant_id}" if variant_id else f"{product_id}-"
        asset_metadata = {"product_id": product_id}
        if variant_id:
            asset_metadata["variant_id"] = variant_id
    # Add original filename to asset_metadata if present
    if original_filename:
        asset_metadata["original_filename"] = original_filename

    # Create the asset row
    new_asset = store_intervention_association_assets(
        store_intervention_association_id=association.uuid,
        asset_key=asset_key,
        asset_url=asset_url,
        asset_type="product_video",
        asset_metadata=asset_metadata
    )
    db_session.add(new_asset)
    db_session.commit()

    # Enqueue background job to process the uploaded video asset
    from task_queue import ASYNC_DATA_PROCESSING_QUEUE
    ASYNC_DATA_PROCESSING_QUEUE.enqueue('task_queue_workers.async_data_processing_worker.process_social_video_asset', asset_url, new_asset.uuid)

    return jsonify({"success": True, "uuid": new_asset.uuid})


def merchant_delete_social_video_asset_handler():
    try:
        shop_url = request.shopify_session.get('dest').split('https://')[1]
        store_url_record = store_url.query.filter_by(url=shop_url).first()
        if store_url_record is None:
            return jsonify({"error": "error: store_url_record not found"}), 400
        store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
        if store_record is None:
            return jsonify({"error": "error: store_record not found"}), 400

        data = request.get_json()
        asset_uuid = data.get('asset_uuid')
        asset_url = data.get('asset_url')
        if not asset_uuid and not asset_url:
            return jsonify({"error": "Missing asset_uuid or asset_url"}), 400

        # Find the asset row
        asset = None
        if asset_uuid:
            asset = store_intervention_association_assets.query.filter_by(uuid=asset_uuid).first()
        elif asset_url:
            asset = store_intervention_association_assets.query.filter_by(asset_url=asset_url).first()
        if not asset:
            return jsonify({"error": "Asset not found"}), 404

        # Delete from S3
        s3_url = asset.asset_url
        parsed = urlparse(s3_url)
        bucket = parsed.netloc.split('.')[0]  # e.g. vandra-social-media-assets
        key = parsed.path.lstrip('/')
        s3_client = boto3.client("s3",
            aws_access_key_id=app.config["SES_ACCESS_KEY"],
            aws_secret_access_key=app.config["SES_SECRET"],
            region_name="eu-north-1")
        try:
            s3_client.delete_object(Bucket=bucket, Key=key)
        except Exception as e:
            # Log but continue to delete DB row
            logger.error(f"Failed to delete S3 object: {bucket}/{key}: {e}")

        # Delete from DB
        db_session.delete(asset)
        db_session.commit()
        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"Error in merchant_delete_social_video_asset_handler: {e}")
        db_session.rollback()
        return jsonify({"error": str(e)}), 500 