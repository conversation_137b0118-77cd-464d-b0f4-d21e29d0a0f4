from flask import request
import time
import uuid
from db import user_session, page_view, click
from app import db_session

def get_element_from_data_string(element):
    val = request.form.get(element)
    if val in ["null", "unknown", "void", "undefined"]:
        val = None
    return val

def record_click_handler():
    session_cookie = request.form.get("session_cookie")

    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return "error"
    page_view_record = page_view.query.filter_by(user_session_id=user_session_record.uuid).\
        filter_by(front_end_id=request.form.get("page_view_id")).first()
    if page_view_record is None:
        return "error"

    int_var_dict = dict()
    int_var_list = ["offsetWidth", "offsetHeight", "clientWidth", "clientHeight", "clientTop", "clientLeft", "childrenCount"]
    for each_var in int_var_list:
        try:
            int_var_dict[each_var] = int(get_element_from_data_string(each_var))
        except:
            int_var_dict[each_var] = None

    # Let's add a click
    click_record = click()
    click_record.uuid = str(uuid.uuid4())
    click_record.time = time.time()
    click_record.page_view_id = page_view_record.uuid
    click_record.tag_name = get_element_from_data_string("tagName")
    click_record.base_uri = get_element_from_data_string("baseURI")
    click_record.class_name = get_element_from_data_string("className")
    click_record.element_id = get_element_from_data_string("id")
    click_record.style = get_element_from_data_string("style")
    click_record.inner_text = get_element_from_data_string("innerText")
    click_record.offset_width = int_var_dict["offsetWidth"]
    click_record.offset_height = int_var_dict["offsetHeight"]
    click_record.client_width = int_var_dict["clientWidth"]
    click_record.client_height = int_var_dict["clientHeight"]
    click_record.client_top = int_var_dict["clientTop"]
    click_record.client_left = int_var_dict["clientLeft"]
    click_record.parent_node_name = get_element_from_data_string("parentNodeName")
    click_record.next_sibling_name = get_element_from_data_string("nextSiblingName")
    click_record.previous_sibling_name = get_element_from_data_string("previousSiblingName")
    click_record.children_count = int_var_dict["childrenCount"]
    click_record.first_child_name = get_element_from_data_string("firstChildName")
    click_record.last_child_name = get_element_from_data_string("lastChildName")

    db_session.add(click_record)
    db_session.commit()

    return "okay"