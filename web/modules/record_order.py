from flask import request, abort
from db import user_session, discount_code, webhook_request, store, store_url, cart_token
from app import db_session
from helpers import process_webhook_request, send_mail
import json
import requests
import time
import datetime
import uuid
import config
from utils import get_generic_logger
from sqlalchemy import func
from db import page_view

logger = get_generic_logger(__name__)


def add_tags_to_order(store_record, store_url_record, order_id):
    # Add <PERSON>dra tag to the order
    graphql_url = "https://" + store_url_record.url + "/admin/api/" + config.SHOPIFY_LEGACY_API_VERSION + "/graphql.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": store_record.access_token
    }
    query = '''mutation tagsAdd($id: ID!, $tags: [String!]!) {
        tagsAdd(id: $id, tags: $tags) {
            node {
                id
            }
            userErrors {
                field
                message
            }
        }
    }'''
    variables = {
        "id": "gid://shopify/Order/" + str(order_id),
        "tags": [
            "Vandra"
        ]
    }

    requests.post(graphql_url, headers=headers, json={'query': query, 'variables': variables})


def associate_session_and_order(session_id,request_data, shop_url):
    #use appropriate timestamp, order date from the webhook
    created_at_timestamp = datetime.datetime.strptime(request_data["created_at"], "%Y-%m-%dT%H:%M:%S%z") 
    wb_time = round(time.mktime(created_at_timestamp.timetuple()))
        
    existing_session = user_session.query.filter(user_session.uuid==session_id).first()
    if existing_session is None:
        return "okay"

    # Get the latest activity time for this session
    try:
        latest_activity = page_view.query.with_entities(
            func.max(page_view.time + func.coalesce(page_view.dwell_time, 0))
        ).filter(
            page_view.user_session_id == existing_session.uuid
        ).scalar() or existing_session.time
    except Exception as e:
        logger.exception(f"Error getting latest activity for session {existing_session.uuid}: {str(e)}")
        latest_activity = existing_session.time
    
    # Check if the latest activity was within the last 24 hours
    is_attributable = latest_activity > wb_time - 86400
    
    existing_session.order_id = request_data["id"]
    if is_attributable:
        existing_session.conversion = True
        existing_session.cart_size = request_data["subtotal_price"]
    db_session.commit()
    
    if is_attributable:
        store_url_record = store_url.query.filter_by(url=shop_url).first()
        if store_url_record is None:
            return "okay"
        store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
        if store_record is None:
            return "okay"

        store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
            order_by(store_url.default.desc()).first()
        if store_url_record is None:
            return "okay"

        for each_discount in request_data["discount_codes"]:
            existing_session.conversion_discount_code = each_discount["code"]
            existing_session.conversion_discount_amount = each_discount["amount"]
            
            check_discount_code = discount_code.query.filter_by(discount_code_code=each_discount["code"]).filter_by(store_id=store_record.uuid).first()
            if check_discount_code is None:
                continue
            
            # alert if discounting is off
            if not store_record.show_discount or not store_record.vandra_admin_show_discount:
                logger.exception(f"Alert - Conversion for inactive merchant {store_record.uuid}")
                
            existing_session.vandra_conversion = True
            db_session.commit()

            add_tags_to_order(store_record, store_url_record, request_data["id"])
            break

        db_session.commit()

    return 'okay'



def associate_session_and_order_from_shopify_object(session_id,order_object, shop_url):
    request_data = {
        "created_at" : order_object.created_at,
        "subtotal_price": order_object.subtotal_price,
        "id" : order_object.id,
        "discount_codes" : [{"code": x.code, "amount" : x.amount} for x in order_object.discount_codes]
    }
    #reuse existing function
    associate_session_and_order(session_id,request_data, shop_url)


def record_order_handler():

    event, shop_url = process_webhook_request()
    if not event:
        return abort(401)

    request_data = request.get_json()

    # Record the details of the webhook request
    new_webhook_request = webhook_request()
    new_webhook_request.uuid = str(uuid.uuid4())
    new_webhook_request.time = time.time()
    new_webhook_request.event = event
    new_webhook_request.shop_url = shop_url
    new_webhook_request.data = json.dumps(request_data)
    db_session.add(new_webhook_request)
    db_session.commit()

    # See if we got a Vandra session ID from the order
    session_id = None
    for each_attribute in request_data["note_attributes"]:
        if each_attribute["name"] == "__vandra_session":
            session_id = each_attribute["value"]
            break
    if session_id is None:
        return "okay"

    return associate_session_and_order(session_id, request_data, shop_url)