import os
import sys
sys.path.append(os.getcwd().replace("/web", "") + "/data_science")

try:
    from data_science.prepare_data import prep_data_for_model
except ModuleNotFoundError as _:
    from prepare_data import prep_data_for_model

from flask import request
from db import db, discount_code, \
    store, store_url, user_session, front_end_ui
from app import db_session, app

import json
import random
import requests
import time
import uuid
from .decisioning_algorithms import IntentDecisioningLogic
from task_queue import LOADED_MODELS
from rq import Retry
import boto3
from botocore.exceptions import ClientError
import os.path
from .predictor_helper import get_prediction_from_sagemaker, get_payload, get_user_session_id_from_payload
from constants import ( DEFAULT_ANTI_HOLDOUT_HOLDOUT_PERC, 
                       ANTI_HOLDOUT_LABEL, 
                       MEDIUM_INTENT_LABEL,
                       LOW_INTENT_LABEL, 
                       HIGH_INTENT_LABEL, 
                       FORCE_DEFAULT_RIGHT_ANTI_HOLDOUT,
                       INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                       INTERVENTION_DECISION_SHOW,
                       INTERVENTION_DECISION_NO_SHOW)
from .intervention_orchestration import (get_intent_score_for_intervention,
                                         get_session_intervention_record)

import pandas as pd
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

from io import StringIO
from ephemeral_store import EphemeralStore
from datetime import timedelta

from utils import get_generic_logger, unique_id
logger = get_generic_logger(__name__)


def download_file_from_s3(filename, location='../data_science/model_object_files'):
    s3_client = boto3.client("s3",
                aws_access_key_id=app.config["SES_ACCESS_KEY"],
                aws_secret_access_key=app.config["SES_SECRET"],
                region_name="us-east-2"
    )
    
    with open(f"{location}/{filename}", 'wb') as f:
        s3_client.download_fileobj("vandra-model-objects", filename, f)


def get_model_decision_handler():
    session_cookie = request.form.get("session_cookie")
    
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return "error"
    
    base_url = request.form.get("shopify_url")
    if base_url in [None, ""]:
        base_url = request.form.get("page", "").\
            replace("http://", "").\
            replace("https://", "").\
            replace("www.", "").split("/")[0].lower()
    
    store_url_record = store_url.query.filter_by(url=base_url).first()
    if store_url_record is None:
        return "error"
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return "error"
    
    # TODO - use the actual session discount code
    discount_code_record = discount_code.query.filter_by(store_id=store_record.uuid).\
        order_by(discount_code.time.desc()).first()
    if discount_code_record is None:
        return "error"
    
    # Get all the models so we can find the ones we want
    current_time_cutoff = int(request.form.get("model_time_threshold"))
        
    #enqueue new layer for intent decisioning
    _, live_job = enqueue_scoring_layer_decisioning(user_session_record, current_time_cutoff)
    
    #wait until the live job is done
    if live_job:
        result = live_job.latest_result(timeout=60)  # Will wait a maximum of 60 secs, otherwise error out
        if not result or result.type != result.Type.SUCCESSFUL:
            #if just delayed, cancel
            if not result:
                live_job.cancel()
            raise Exception(f"Inference error for session {user_session_record.uuid}")

    session_intervention_record = get_session_intervention_record(user_session_record, INTERVENTION_TYPE_INTENT_BASED_DISCOUNT)
    live_show_discount = session_intervention_record.decision == INTERVENTION_DECISION_SHOW
    session_in_holdout = session_intervention_record.holdout
    probability_predicted = get_intent_score_for_intervention(session_intervention_record)

    if request.form.get("show_popup") == "false":
        return json.dumps({"status": "do_not_show", "prediction": probability_predicted})
    
    if live_show_discount and session_in_holdout == False:
        return json.dumps({"status": "show", "prediction": probability_predicted})
    
    return json.dumps({"status": "do_not_show", "prediction": probability_predicted})

def get_prediction_value_from_prediction_h2o(prediction):
    prediction_index = None
    for i, field in enumerate(prediction["fields"]):
        if field == "conversion.1" or field == "quality":
            prediction_index = i
            break
    if prediction_index is None:
        raise Exception("Inference could not find prediction index")
    return float(prediction["score"][0][prediction_index])



def enqueue_scoring_layer_decisioning(session_record, current_time_cutoff):
    intent_decisioning_logic = IntentDecisioningLogic(INTERVENTION_TYPE_INTENT_BASED_DISCOUNT)
    has_live_decision, live_job = intent_decisioning_logic.decide(
        session_record,
        current_time_cutoff,
        )
        
    if has_live_decision:
        logger.info(
            f"Scheduled job {live_job.id} to run scoring layer decisioning jobs for session {session_record.uuid}."
        )
    return has_live_decision, live_job