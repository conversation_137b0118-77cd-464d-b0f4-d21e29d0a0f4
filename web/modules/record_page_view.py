from flask import request, jsonify
from app import db_session
from db import (cart, discount_code, exclude_url,
    front_end_ui, front_end_experiment, front_end_experiment_store_association,
    front_end_experiment_ui_allocation,
    page_view, product, store, store_url,scoring_layer,
    user_session, user_session_event, ip2location)
from dictionary import DEVICES_VIEWPORT_DICT, DEVICES_PRICE_DICT
from helpers import create_discount, send_mail
import ipaddress
from modules.predictor import enqueue_scoring_layer_decisioning
from modules.oauth import check_registered_webhooks
import random
from random import randint
import time
import uuid
from rq import Retry

from .experiment_assignment import assign_nudge_delay, assign_nudge_delay_v2, assign_returning_messaging, assign_show_action_and_delay_v2, create_nudge_params
from utils import is_valid_uuid
from utils import get_generic_logger, unique_id
from constants import (
    DEFAULT_ANTI_HOLDOUT_HOLDOUT_PERC, ANTI_HOLDOUT_LABEL, LOW_INTENT_LABEL, 
    HIGH_INTENT_LABEL, FORCE_DEFAULT_RIGHT_ANTI_HOLDOUT,
    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
    INTERVENTION_STATUS_DISMISSED,
    INTERVENTION_STATUS_SHOWN,
    INTERVENTION_STATUS_ACTIONED,
    INTERVENTION_DECISION_SHOW
    )
from .intervention_orchestration import (assign_session_intervention, 
                                        get_session_intervention_record,
                                        update_session_intervention_status,
                                        update_session_intervention_opportunity,
                                        get_intervention_record_determination_time,
                                        capture_session_intervention_event)

logger = get_generic_logger(__name__)

def get_devices(screen_width, screen_height, device_os, device_os_version):
    """Accounts for 1. the effect device rotation has on window.screen object, which exhibits
    inconsistent behavior across operating systems and 2. inconsistencies across online sources
    regarding the ordering of width and height dimensions"""
    longer_length = max(int(screen_width), int(screen_height))
    shorter_length = min(int(screen_width), int(screen_height))
    device_os_formatted = "".join(device_os.split()).lower()
    if "windows" in device_os_formatted:
        device_os_formatted = "windows"
    device_key = str(longer_length) + ";" + str(shorter_length) + ";" + device_os_formatted

    os_versions = None
    if device_key in DEVICES_VIEWPORT_DICT:
        os_versions = DEVICES_VIEWPORT_DICT[device_key]

    """ If a mapping exists for device_key (screen width, screen_height, and device_os) in DEVICES_VIEWPORT_DICT,
    then an object that maps operating system versions to devices is returned. This block compares device_os_version
    to each operating system version returned. If device_os_version is equal to or newer than an operating system version,
    the associated devices are added to the device string. This is because a device's operating system can be updated to
    a newer version than the one it was initialized with. Because operating system versions can come in different formats
    (ex: the operating system version for Apple iPhone 7, iOS 10.0.1, has more specificity (3 levels) than that of Apple
    iPhone SE (2022), iOS 15.4 (2 levels)), the algorithm compares each level of specificity of the operating system versions
    until 1. there is a mismatch in versions, at which point a decision can be made on whether device_os_version matches the 
    associated devices OR 2. there are no more levels of specificity for one or both of the operating system versions being
    compared. If there are no more levels of specificity for both of the compared operating system versions, they are equal and thus
    considered a match. If there are no more levels of specificity for only the DEVICES_VIEWPORT_DICT operating system version,
    it can be assumed that device_os_version is a match, since, for example, v10.0.X is equal to or newer than v10.0. On the other hand,
    if there are no more levels of specificity for only device_os_version, it can be assumed that any next level of specificity
    is no greater than 0. Therefore, if any next level of specificity for the DEVICES_VIEWPORT_DICT operating system version is
    greater than 0, device_os_version is not a match. Outputs a string with all matched devices separated with a `;` delimiter. """
    devices = ""
    if os_versions is not None:
        for key, value in os_versions.items():
            """ Devices with a key of 0 have inconsistent operating system data in user-agent (ex: MacBooks, iPads) """
            if key == "0":
                if len(devices) > 0:
                    devices += "; "
                devices += value
            else:
                device_os_version = str(device_os_version)
                device_os_version_arr = device_os_version.split(".") if "." in device_os_version else device_os_version.split("_") if "_" in device_os_version else [device_os_version]
                if device_os_version_arr is not None:
                    os_version_arr = key.split(".")
                    match = True
                    equal = True
                    for i in range(0, min(len(device_os_version_arr), len(os_version_arr))):
                        if device_os_version_arr[i] > os_version_arr[i]:
                            equal = False
                            break
                        elif device_os_version_arr[i] < os_version_arr[i]:
                            match = False
                            equal = False
                            break
                    if equal:
                        if len(os_version_arr) > len(device_os_version_arr):
                            i = len(device_os_version_arr)
                            while i < len(os_version_arr):
                                if int(os_version_arr[i]) > 0:
                                    match = False
                                i += 1
                    if match:
                        if len(devices) > 0:
                            devices += "; "
                        devices += value

    return devices

def get_prices(devices):
    prices = ""
    if devices not in [None, ""]:
        for device in devices.split(";"):
            device_formatted = device.strip()
            if device_formatted in DEVICES_PRICE_DICT:
                if len(prices) > 0:
                    prices += "; "
                prices += str(DEVICES_PRICE_DICT[device_formatted])

    return prices


MAX_DECISION_DELAY_SECONDS = 20
def record_page_view_handler():
    session_cookie = request.form.get("session_cookie")
    customer_cookie = request.form.get("customer_cookie")

    local_time = request.form.get("local_datetime")
    if local_time in ["null", "unknown", "void", "undefined"]:
        local_time = None
    local_timezone = ""
    if local_time is not None and len(local_time) > 6:
        local_timezone = local_time[-6:]

    # Let's see if we already have a session
    new_session = False
    new_customer = False
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    customer_session_record = user_session.query.filter_by(customer_cookie=customer_cookie).first()
    delay_assignment = None
    if customer_session_record is None:
        new_customer = True
    if user_session_record is None:
        # No existing session. Let's make one
        new_session = True

        data_inputs = [
            "ad_bing",
            "ad_doubleclick",
            "ad_facebook",
            "ad_google",
            "ad_tiktok",
            "browser",
            "browser_version",
            "customer_cookie",
            "device_memory",
            "language",
            "mobile",
            "os",
            "os_version",
            "page",
            "referrer",
            "screen_width",
            "screen_height",
            "shopify_url",
            "store_front_end_ui_id",
            "store_front_end_experiment_id",
            "utm_campaign",
            "utm_content",
            "utm_medium",
            "utm_source",
            "utm_term"
        ]

        data_dict = dict()
        for each_input in data_inputs:
            data_dict[each_input] = request.form.get(each_input)
            if data_dict[each_input] in ["null", "unknown", "void", "undefined"]:
                data_dict[each_input] = None
            if each_input[:3] == "ad_":
                if data_dict[each_input] == "true":
                    data_dict[each_input] = True
                else:
                    data_dict[each_input] = False

        if data_dict["shopify_url"] is None:
            data_dict["store_id"] = None
        else:
            try:
                store_url_record = store_url.query.filter_by(url=data_dict["shopify_url"].lower()).first()
                if store_url_record is None:
                    return jsonify({"status": "error" }), 400
                
                store_rec = store.query.filter_by(uuid=store_url_record.store_id).first()
                if store_rec is None:
                    return jsonify({"status": "error" }), 400
                
                data_dict["store_id"] = store_rec.uuid
                
                # See if we have a store-specific experiment
                store_front_end_experiment = front_end_experiment.query.filter_by(active=True, store_specific=True).\
                    filter(front_end_experiment.store_associations.any(front_end_experiment_store_association.store_id == data_dict["store_id"])).\
                    order_by(front_end_experiment.time.desc()).first()
                
                if store_front_end_experiment is None:
                    # Get front end experiment
                    store_front_end_experiment = front_end_experiment.query.filter_by(active=True, store_specific=False).\
                        order_by(front_end_experiment.time.desc()).first()
                
                data_dict["store_front_end_experiment_id"] = store_front_end_experiment.uuid
                
                # Get front end UI configurations
                store_front_end_experiment_ui_allocations = front_end_experiment_ui_allocation.query.\
                    filter_by(front_end_experiment_id=data_dict["store_front_end_experiment_id"]).all()
                
                start_probability = 0 # Noninclusive
                end_probability = 1 # Inclusive
                probabilities = {}
                for store_front_end_experiment_ui_allocation in store_front_end_experiment_ui_allocations:
                    store_front_end_ui = front_end_ui.query.filter_by(uuid=store_front_end_experiment_ui_allocation.front_end_ui_id).first()
                    store_front_end_ui_id = str(store_front_end_ui.uuid)
                    store_front_end_ui_weight = int(store_front_end_experiment_ui_allocation.weight)
                    end_probability = start_probability + store_front_end_ui_weight
                    probabilities[f"{start_probability}:{end_probability}"] = store_front_end_ui_id
                    start_probability = end_probability
                rand = randint(1, end_probability)
                
                for probability_range, front_end_ui_id in probabilities.items():
                    probability_range_arr = probability_range.split(":")
                    start_probability = int(probability_range_arr[0])
                    end_probability = int(probability_range_arr[1])
                    if rand > start_probability and rand <= end_probability:
                        selected_front_end_ui_id = front_end_ui_id
                
                try:
                    select_front_end_ui = front_end_ui.query.filter_by(uuid=selected_front_end_ui_id).first()
                except:
                    send_mail("Vandra error on record_page_view", "Experiment maybe set up incorrectly")
                    raise
                
                data_dict["store_front_end_ui_id"] = select_front_end_ui.uuid

                if (data_dict["store_front_end_ui_id"] == "IMAGE" or data_dict["store_front_end_ui_id"] == "MODAL") and store_rec.popup_image_url is None:
                    data_dict["store_front_end_ui_id"] = front_end_ui.query.filter_by(default=True).first().uuid
                #assign parameters for frontend ui assigned
                if select_front_end_ui.name == "NUDGE_DELAY":
                    delay_assignment = "NUDGE_DELAY"
                elif select_front_end_ui.name == "NUDGE_DELAY_V2":
                    delay_assignment = "NUDGE_DELAY_V2"
                elif select_front_end_ui.name == "ACTION_BASED_SHOW":
                    delay_assignment = "ACTION_BASED_SHOW"

            except Exception as e:
                data_dict["store_front_end_experiment_id"] = None
                default_front_end_ui = front_end_ui.query.filter_by(default=True).first()
                data_dict["store_front_end_ui_id"] = default_front_end_ui.uuid
                #assign parameters for frontend ui assigned
                if default_front_end_ui.name == "NUDGE_DELAY":
                    delay_assignment = "NUDGE_DELAY"
                elif default_front_end_ui.name == "NUDGE_DELAY_V2":
                    delay_assignment = "NUDGE_DELAY_V2"
                elif default_front_end_ui.name == "ACTION_BASED_SHOW":
                    delay_assignment = "ACTION_BASED_SHOW"
        
        if data_dict["mobile"] == "false":
            data_dict["mobile"] = False
        elif data_dict["mobile"] == "true":
            data_dict["mobile"] = True
        
        # Device information
        try:
            data_dict["device"] = get_devices(data_dict["screen_width"], data_dict["screen_height"], data_dict["os"], data_dict["os_version"])
        except:
            data_dict["device"] = None
        
        try:
            data_dict["price"] = get_prices(data_dict["device"])
        except:
            data_dict["price"] = None
        
        data_dict["ip_address"] = request.remote_addr
        
        # IP address information
        data_dict["country_name"] = None
        data_dict["region_name"] = None
        data_dict["zip_code"] = None
        data_dict["latitude"] = None
        data_dict["longitude"] = None

        ip_address_decimal = int(ipaddress.ip_address(data_dict["ip_address"]))
        ip2location_record = ip2location.query.\
            filter(ip2location.ip_from <= ip_address_decimal).\
            filter(ip2location.ip_to >= ip_address_decimal).first()
        if ip2location_record is not None:
            data_dict["country_name"] = ip2location_record.country_name
            data_dict["region_name"] = ip2location_record.region_name
            data_dict["zip_code"] = ip2location_record.zip_code
            data_dict["latitude"] = ip2location_record.latitude
            data_dict["longitude"] = ip2location_record.longitude

        data_dict["domain"] = (data_dict["page"] or "").replace("http://", "")
        data_dict["domain"] = data_dict["domain"].replace("https://", "")
        data_dict["domain"] = data_dict["domain"].replace("www.", "")
        #truncate due to column limitations
        data_dict["domain"] = data_dict["domain"].split("/")[0][:250]
        data_dict["utm_campaign"] = data_dict["utm_campaign"][:250] if data_dict["utm_campaign"] else data_dict["utm_campaign"]
        data_dict["utm_content"] = data_dict["utm_content"][:250] if data_dict["utm_content"] else data_dict["utm_content"]
        data_dict["utm_medium"] = data_dict["utm_medium"][:250] if data_dict["utm_medium"] else data_dict["utm_medium"]
        data_dict["utm_source"] = data_dict["utm_source"][:250] if data_dict["utm_source"] else data_dict["utm_source"]
        data_dict["utm_term"] = data_dict["utm_term"][:250] if data_dict["utm_term"] else data_dict["utm_term"]


        if data_dict["referrer"] is not None:
            data_dict["referrer"] = data_dict["referrer"][:999]
        if data_dict["browser"] is not None:
            data_dict["browser"] = data_dict["browser"][:99]
        
        # smallint database type won't allow too-large numbers
        try:
            data_dict["screen_width"] = min(data_dict["screen_width"], 32767)
        except TypeError:
            pass
        try:
            data_dict["screen_height"] = min(data_dict["screen_height"], 32767)
        except TypeError:
            pass

        user_session_record = user_session()
        user_session_record.uuid = str(uuid.uuid4())
        user_session_record.time = time.time()
        user_session_record.store_id = data_dict["store_id"]
        user_session_record.ad_bing = data_dict["ad_bing"]
        user_session_record.ad_doubleclick = data_dict["ad_doubleclick"]
        user_session_record.ad_facebook = data_dict["ad_facebook"]
        user_session_record.ad_google = data_dict["ad_google"]
        user_session_record.ad_tiktok = data_dict["ad_tiktok"]
        user_session_record.browser = data_dict["browser"]
        user_session_record.browser_version = data_dict["browser_version"]
        user_session_record.country_name = data_dict["country_name"]
        user_session_record.customer_cookie = data_dict["customer_cookie"]
        user_session_record.device = data_dict["device"]
        user_session_record.device_memory = data_dict["device_memory"]
        user_session_record.domain = data_dict["domain"]
        user_session_record.front_end_ui_id = data_dict["store_front_end_ui_id"]
        user_session_record.front_end_experiment_id = data_dict["store_front_end_experiment_id"]
        user_session_record.ip_address = data_dict["ip_address"]
        user_session_record.language = data_dict["language"]
        user_session_record.latitude = data_dict["latitude"]
        user_session_record.local_time = local_time
        user_session_record.local_timezone = local_timezone
        user_session_record.longitude = data_dict["longitude"]
        user_session_record.mobile = data_dict["mobile"]
        user_session_record.os = data_dict["os"]
        user_session_record.os_version = data_dict["os_version"]
        user_session_record.page = (data_dict["page"] or "")[:999]
        user_session_record.price = data_dict["price"]
        user_session_record.referrer = data_dict["referrer"]
        user_session_record.region_name = data_dict["region_name"]
        user_session_record.screen_height = min(int(data_dict["screen_height"] or 0), 32000) or None
        user_session_record.screen_width = min(int(data_dict["screen_width"] or 0), 32000) or None
        user_session_record.session_cookie = session_cookie
        user_session_record.utm_campaign = data_dict["utm_campaign"]
        user_session_record.utm_content = data_dict["utm_content"]
        user_session_record.utm_medium = data_dict["utm_medium"]
        user_session_record.utm_source = data_dict["utm_source"]
        user_session_record.utm_term = data_dict["utm_term"]
        user_session_record.vandra_opportunity = True
        user_session_record.zip_code = data_dict["zip_code"]

        db_session.add(user_session_record)
        db_session.commit()
        
        if delay_assignment == "NUDGE_DELAY":
            assign_nudge_delay(user_session_record) 
        elif delay_assignment == "NUDGE_DELAY_V2":
            assign_nudge_delay_v2(user_session_record) 
        elif delay_assignment == "ACTION_BASED_SHOW":
            assign_show_action_and_delay_v2(user_session_record)
        
        #capture session intervention assignment
        assign_session_intervention(user_session_record, 
                                    INTERVENTION_TYPE_INTENT_BASED_DISCOUNT)



    # See if this is a spam bot. End processing if so
    if not new_session:
        page_view_count = page_view.query.filter_by(user_session_id=user_session_record.uuid).count()
        if page_view_count > 1000:
            return jsonify({"status": "error", "spam": True}), 400
    
    document_has_focus = None
    if request.form.get("document_has_focus") == "true":
        document_has_focus = True
    elif request.form.get("document_has_focus") == "false":
        document_has_focus = False

    # Let's add a page_view
    page_view_record = page_view()
    page_view_record.uuid = str(uuid.uuid4())
    page_view_record.time = time.time()
    page_view_record.local_time = local_time
    page_view_record.local_timezone = local_timezone
    page_view_record.front_end_id = request.form.get("page_view_id")
    page_view_record.page = request.form.get("page", "")[:999]
    page_view_record.document_has_focus = document_has_focus
    page_view_record.user_session_id = user_session_record.uuid

    db_session.add(page_view_record)
    db_session.commit()

    # Figure out if this is the first page view after applying the discount
    # Show success toast if so
    show_applied_toast = False
    if user_session_record.vandra_discount_applied and not user_session_record.vandra_discount_toasted:
        show_applied_toast = True
        user_session_record.vandra_discount_toasted = True
        db_session.commit()

    # Get the store record so we can send back the discount code
    base_url = request.form.get("shopify_url")
    if base_url in [None, ""]:
        if request.form.get("page") in [None, ""]:
            return jsonify({"status": "error", "session_id": user_session_record.uuid}), 400
        base_url = request.form.get("page").\
            replace("http://", "").\
            replace("https://", "").\
            replace("www.", "").split("/")[0].lower()
    
    store_url_record = store_url.query.filter_by(url=base_url).first()
    if store_url_record is None:
        return jsonify({"status": "error", "session_id": user_session_record.uuid}), 400
    store_record = store.query.filter_by(uuid=store_url_record.store_id).first()
    if store_record is None:
        return jsonify({"status": "error", "session_id": user_session_record.uuid}), 400
    
    # Figure out if it's been enough time to display the discount yet
    scoring_layer_records = scoring_layer.query.filter_by(run_predictions=True).\
        order_by(scoring_layer.scoring_time_cutoff).all()
    current_time_cutoff = None
    
    scoring_layer_time_thresholds = []
    for scoring_layer_record in scoring_layer_records:
        time_cutoff = scoring_layer_record.scoring_time_cutoff
        if time_cutoff not in scoring_layer_time_thresholds:
            scoring_layer_time_thresholds.append(time_cutoff)
        if time.time() - user_session_record.time >= time_cutoff:
            current_time_cutoff = time_cutoff
    
    # Make sure the merchant has all of the webhooks they need
    check_registered_webhooks(store_record)
    
    # Either get the discount code assigned or choose a new one to assign
    discount_code_record = None
    if user_session_record.vandra_discount_code_assigned not in [None, ""]:
        discount_code_record = discount_code.query.filter_by(store_id=store_record.uuid).\
            filter_by(discount_code_code=user_session_record.vandra_discount_code_assigned).first()
    
    if discount_code_record is None:
        discount_code_list = discount_code.query.filter_by(store_id=store_record.uuid, expired=False).\
            filter(discount_code.time >= time.time() - 86400).all()
        try:
            discount_code_record = random.choice(discount_code_list)
        except IndexError:
            discount_code_record = None

        # If the code doesn't exist or is over a day old let's make a new one to prevent coupon scrapers
        if discount_code_record is None:
            new_discount_codes = create_discount(store_record)
            if new_discount_codes is False:
                return jsonify({"status": "error", "session_id": user_session_record.uuid, "info": "No discount code available"}), 400
            if len(new_discount_codes) > 0:
                discount_code_record = random.choice(new_discount_codes)
        
        if discount_code_record is None:
            return jsonify({"status": "error", "session_id": user_session_record.uuid, "info": "No discount code available"}), 400
    
    session_intervention_record = get_session_intervention_record(user_session_record, INTERVENTION_TYPE_INTENT_BASED_DISCOUNT)
    if session_intervention_record.decision is None and current_time_cutoff is not None:
        #enqueue new layer for intent decisioning
        _, live_job = enqueue_scoring_layer_decisioning(user_session_record, current_time_cutoff)
        
        #wait until the live job is done
        if live_job:
            result = live_job.latest_result(timeout=60)  # Will wait a maximum of 60 secs, otherwise error out
            if not result or result.type != result.Type.SUCCESSFUL:
                #if just delayed, cancel
                if not result:
                    live_job.cancel()
                raise Exception(f"Inference error for session {user_session_record.uuid}")

        db_session.refresh(session_intervention_record) # = get_session_intervention_record(user_session_record, INTERVENTION_TYPE_INTENT_BASED_DISCOUNT)

    if user_session_record.vandra_discount_code_assigned is None:
        user_session_record.vandra_discount_code_assigned = discount_code_record.discount_code_code
        user_session_record.vandra_discount_rate_assigned = discount_code_record.price_rule_value # discount_code_record stores rates as negatives
        db_session.commit()
    
        
    show_discount = session_intervention_record.decision == INTERVENTION_DECISION_SHOW
    session_in_holdout = session_intervention_record.holdout
    time_since_start_of_session = time.time() - user_session_record.time
    
    # Get list of URLs that we want to exclude from displaying the popup
    exclude_urls_query = exclude_url.query.filter_by(store_id=store_record.uuid).all()
    exclude_urls = [x.url for x in exclude_urls_query]
     
    popup_text_header = store_record.popup_text_header
    popup_text_body = store_record.popup_text_body
    messaging_type = None
    messaging_state = None
    is_returning = user_is_returning(user_session_record=user_session_record, new_session=new_session)

    if user_session_record.front_end_ui and user_session_record.front_end_ui.name == "MESSAGING":
        if new_session:
            if is_returning:
                messaging_type, messaging_state = assign_returning_messaging(user_session_record=user_session_record)
            else:
                create_nudge_params(user_session_record)
        else:
            #this should only happen if there was an issue with the session
            if not user_session_record.user_session_nudge_parameters:
                create_nudge_params(user_session_record)
            #this is the regular logic to reuse existing params
            messaging_type = user_session_record.user_session_nudge_parameters.trigger_type
            messaging_state = user_session_record.user_session_nudge_parameters.trigger_state

    return_dict = {
        "session_id": user_session_record.uuid,
        "code": discount_code_record.discount_code_code,
        "discount_amount": (-1*discount_code_record.price_rule_value),
        "discount_ends_at_time": discount_code_record.ends_at_time,
        "popup_primary_color": store_record.popup_primary_color,
        "popup_bg_color": store_record.popup_bg_color,  
        "popup_font": store_record.popup_font,
        "popup_text_header": popup_text_header,
        "popup_text_body": popup_text_body,
        "popup_text_button": store_record.popup_text_button,
        "popup_text_button_close": store_record.popup_text_button_close,
        "popup_text_success": store_record.popup_text_success,
        "minimized_text_header": store_record.minimized_text_header,
        "auto_apply_text_body": store_record.auto_apply_text_body,
        "popup_image_url": store_record.popup_image_url,
        "time_since_start_of_session": time_since_start_of_session,
        "model_time_thresholds": scoring_layer_time_thresholds,
        "exclude_urls": exclude_urls,
        "page_view_uuid": page_view_record.uuid,
        "hide_minimized_popup": store_record.hide_minimized_popup,
        "use_meta_ad_pixel": store_record.use_meta_ad_pixel,
        "front_end_ui_name": user_session_record.front_end_ui.name,
        "messaging_type": messaging_type,
        "messaging_state": messaging_state
    }

    if new_session:
        return_dict["new_session"] = new_session
    if new_customer:
        return_dict["new_customer"] = new_customer

    # Obey merchant admin setting if they don't want to show the popup
    if not store_record.show_discount or not store_record.vandra_admin_show_discount:
        user_session_record.vandra_opportunity = False
        db_session.commit()
        update_session_intervention_opportunity(user_session_record, 
                                        INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                        False,
                                        "discount_disabled"
                                    )
        return_dict["status"] = "disabled"
        return jsonify(return_dict)
    
    # User is logged in and we don't want to show to them
    if request.form.get("logged_in") == "true" and not store_record.show_discount_when_logged_in:
        user_session_record.vandra_opportunity = False
        db_session.commit()
        update_session_intervention_opportunity(user_session_record, 
                                        INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                        False,
                                        "logged_in"
                                    )
        return_dict["status"] = "logged_in"
        return jsonify(return_dict)
    
    # User is not from a paid channel but we only want to give discounts to paid
    usr = user_session_record
    is_paid = usr.ad_bing or usr.ad_doubleclick or usr.ad_facebook or usr.ad_google or usr.ad_tiktok
    if store_record.show_discount_only_to_paid and not is_paid:
        user_session_record.vandra_opportunity = False
        db_session.commit()
        update_session_intervention_opportunity(user_session_record, 
                                        INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                        False,
                                        "not_paid_traffic"
                                    )
        return_dict["status"] = "not_paid"
        return jsonify(return_dict)
    
    # Session has the "no_vandra" cookie set so we shouldn't show the popup
    if request.form.get("no_vandra") == "true":
        user_session_record.no_vandra_cookie = True
        user_session_record.vandra_opportunity = False
        db_session.commit()
        update_session_intervention_opportunity(user_session_record, 
                                        INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                        False,
                                        "no_vandra_cookie_present"
                                    )
        return_dict["status"] = "no_vandra"
        return jsonify(return_dict)
    
    # User has had a conversion previously and we don't want to show them
    if not store_record.show_discount_to_previous_customers:
        previous_conversion = user_session.query.\
                filter((user_session.customer_cookie == user_session_record.customer_cookie) & (user_session.customer_cookie != None)).\
                filter((user_session.conversion == True) | (user_session.order_id != None) ).order_by(user_session.time).first()        
        if previous_conversion is not None:
            if previous_conversion.uuid != user_session_record.uuid:
                user_session_record.vandra_opportunity = False
                db_session.commit()
                update_session_intervention_opportunity(user_session_record, 
                                        INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                        False,
                                        "previous_customer"
                                    )
            return_dict["status"] = "previous_customer"
            return jsonify(return_dict)
    
    #up to this point, we've cleared all cases where we can set vandra_opportunity to false
        

    if session_in_holdout:
        return_dict["status"] = "holdout"
        return jsonify(return_dict)

    # User dismissed the popup. Don't show it again
    if user_session_record.vandra_dismissed:
        return_dict["status"] = "dismissed"
        return jsonify(return_dict)

    # Discount already applied, stop showing the popup
    if user_session_record.vandra_discount_applied:
        return_dict["status"] = "already_applied"
        return_dict["show_applied_toast"] = show_applied_toast
        return jsonify(return_dict)
    
    vandra_discount_codes = set(discount_code.discount_code_code for discount_code in discount_code.query.filter_by(store_id=store_record.uuid).all())
    # User previously applied discount without converting in a previous session
    previous_apply = user_session.query.\
        filter(user_session.uuid != user_session_record.uuid).\
        filter(user_session.time > time.time() - 86400).\
        filter(user_session.store_id == user_session_record.store_id).\
        filter(user_session.vandra_discount_applied == True, user_session.vandra_discount_code_assigned.in_(vandra_discount_codes)).\
        filter((user_session.customer_cookie == user_session_record.customer_cookie) & (user_session.customer_cookie != None)).\
        filter_by(conversion=False).first()
    if previous_apply is not None:
        return_dict["status"] = "return"
        return jsonify(return_dict)

    # Always show the popup for our test store
    if "vandra-dev.myshopify.com" in base_url:
        show_discount = True
    
    if show_discount and not user_session_record.vandra_dismissed and not user_session_record.vandra_discount_applied:
        return_dict["status"] = "show"
        return_dict["show_applied_toast"] = show_applied_toast
        return jsonify(return_dict)
    
    return_dict["status"] = "do_not_show"
    return_dict["show_applied_toast"] = show_applied_toast
    return jsonify(return_dict)

def get_popup_status_handler():
    session_cookie = request.args.get("session_cookie")
    
    # Let's see if we have a session
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return jsonify({"status": "okay"})
    
    store_record = store.query.filter_by(uuid=user_session_record.store_id).first()
    if store_record is None:
        return jsonify({"status": "okay"})
    
    cart_record = cart.query.filter_by(user_session_id=user_session_record.uuid).order_by(cart.time.desc()).first()
    
    vandra_ui_version = front_end_ui.query.filter_by(uuid=user_session_record.front_end_ui_id).first()
    if vandra_ui_version is None:
        vandra_ui_version = front_end_ui.query.filter_by(default=True).first()

    vandra_discount_applied = str(user_session_record.vandra_discount_applied)
    
    cart_product_id_list = []
    if cart_record is not None:
        cart_items = cart_record.cart_items
        for cart_item in cart_items:
            cart_product_id_list.append(str(cart_item["product_id"]))

    vandra_countdown_deadline = user_session_record.countdown_deadline

    renudge = get_renudge_type(user_session_record)
    vandra_renudge_type = renudge["vandra_renudge_type"]
    vandra_can_be_renudged_time = renudge["vandra_can_be_renudged_time"]
    #extra framework to pass nudge parameters
    nudge_parameters = {}
    #for nudge delay, pass nudge parameter
    if vandra_ui_version.name in ["NUDGE_DELAY", "NUDGE_DELAY_V2"]:
        nudge_parameters["trigger_delay"] = user_session_record.user_session_nudge_parameters.trigger_delay
    
    if vandra_ui_version.name in ["ACTION_BASED_SHOW"]:
        nudge_parameters["trigger_delay"] = user_session_record.user_session_nudge_parameters.trigger_delay
        nudge_parameters["trigger_type"] = user_session_record.user_session_nudge_parameters.trigger_type

    #check if had had live decision
    session_intervention_record = get_session_intervention_record(user_session_record, INTERVENTION_TYPE_INTENT_BASED_DISCOUNT)
    determination_time = get_intervention_record_determination_time(session_intervention_record)
    
    if determination_time is not None:
        nudge_parameters["trigger_decisioning_time"] = determination_time

    return jsonify({
        "vandra_ui_version_name": vandra_ui_version.name,
        "vandra_ui_version_filename": vandra_ui_version.filename,
        "vandra_discount_applied": vandra_discount_applied,
        "vandra_countdown_deadline": vandra_countdown_deadline,
        "vandra_renudge_type": vandra_renudge_type,
        "vandra_can_be_renudged_time": vandra_can_be_renudged_time,
        "vandra_nudge_parameters" : nudge_parameters,
        "hide_minimized_popup": store_record.hide_minimized_popup
    })


def get_renudge_status_handler():
    if request.args.get("session_cookie") is None:
        return "okay"
    session_cookie = request.args.get("session_cookie")

    # Let's see if we already have a session
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return "okay"

    renudge_status = get_renudge_status(user_session_record)

    return jsonify({
        "vandra_renudge_status": renudge_status
    })

def get_renudge_status(user_session_record):
    postapply_renudge_cartadd_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="postapply_renudge_cartadd").first()
    postapply_renudge_expires_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="postapply_renudge_expires").first()
    postdismiss_renudge_cartadd_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="postdismiss_renudge_cartadd").first()
    postdismiss_renudge_expires_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="postdismiss_renudge_expires").first()

    vandra_renudge_status = "not_done"
    if postapply_renudge_cartadd_event is not None or postapply_renudge_expires_event is not None:
        vandra_renudge_status = "postapply_done"
    elif postdismiss_renudge_cartadd_event is not None or postdismiss_renudge_expires_event is not None:
        vandra_renudge_status = "postdismiss_done"

    return vandra_renudge_status

def get_renudge_type(user_session_record):
    discount_applied_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="discount_applied").first()
    popup_dismissed_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="popup_dismissed").first()
    postapply_renudge_cartadd_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="postapply_renudge_cartadd").first()
    postapply_renudge_expires_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="postapply_renudge_expires").first()
    postdismiss_renudge_cartadd_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="postdismiss_renudge_cartadd").first()
    postdismiss_renudge_expires_event = user_session_event.query.\
        filter_by(user_session_id=user_session_record.uuid).\
        filter_by(action="postdismiss_renudge_expires").first()

    vandra_renudge_type = None
    vandra_can_be_renudged_time = None
    if discount_applied_event is not None:
        if postapply_renudge_cartadd_event is None and postapply_renudge_expires_event is None:
            vandra_renudge_type = "postapply"
            vandra_can_be_renudged_time = discount_applied_event.time + 20
        else:
            vandra_renudge_type = "postapply_done"
    elif discount_applied_event is None and popup_dismissed_event is not None:
        if postdismiss_renudge_cartadd_event is None and postdismiss_renudge_expires_event is None:
            vandra_renudge_type = "postdismiss"
            vandra_can_be_renudged_time = popup_dismissed_event.time + 30
        else:
            vandra_renudge_type = "postdismiss_done"

    return {
        "vandra_renudge_type": vandra_renudge_type,
        "vandra_can_be_renudged_time": vandra_can_be_renudged_time
    }

def record_countdown_deadline_handler():
    session_cookie = request.form.get("session_cookie")
    deadline = request.form.get("deadline")

    # Let's see if we already have a session
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return 'okay'

    user_session_record.countdown_deadline = deadline
    db_session.commit()

    return 'okay'


def record_discount_applied_handler():
    session_cookie = request.form.get("session_cookie")
    page_view_id = request.form.get("page_view_id")
    dwell_time = request.form.get("dwell_time")
    focused_dwell_time = request.form.get("focused_dwell_time")
    # Let's see if we already have a session
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return 'okay'

    new_user_session_event = user_session_event()
    new_user_session_event.uuid = str(uuid.uuid4())
    new_user_session_event.time = time.time()
    new_user_session_event.action = "discount_applied"
    new_user_session_event.user_session_id = user_session_record.uuid
    new_user_session_event.dwell_time = dwell_time
    new_user_session_event.focused_dwell_time = focused_dwell_time
    new_user_session_event.page_view_id = page_view_id
    db_session.add(new_user_session_event)
    db_session.commit()

    user_session_record.vandra_discount_applied = True
    update_session_intervention_status(user_session_record, 
                                           INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                           INTERVENTION_STATUS_ACTIONED
                                           )
    capture_session_intervention_event(user_session_record, 
                                       INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                       "discount_applied",
                                       dwell_time,
                                       focused_dwell_time,
                                        page_view_id
                                    )

    db_session.commit()

    return 'okay'

def record_ineligible_product_handler():
    session_cookie = request.form.get("session_cookie")
    page_view_id = request.form.get("page_view_id")
    dwell_time = request.form.get("dwell_time")
    focused_dwell_time = request.form.get("focused_dwell_time")

    page_view_id = page_view_id if is_valid_uuid(page_view_id) else None
    # Let's see if we already have a session
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return 'okay'
    
    new_user_session_event = user_session_event()
    new_user_session_event.uuid = str(uuid.uuid4())
    new_user_session_event.time = time.time()
    new_user_session_event.action = "cartadd_ineligible"
    new_user_session_event.user_session_id = user_session_record.uuid
    new_user_session_event.dwell_time = dwell_time
    new_user_session_event.focused_dwell_time = focused_dwell_time
    new_user_session_event.page_view_id = page_view_id
    db_session.add(new_user_session_event)
    db_session.commit()

    return 'okay'

def record_popup_dismissed_handler():
    session_cookie = request.form.get("session_cookie")
    page_view_id = request.form.get("page_view_id")
    dwell_time = request.form.get("dwell_time")
    focused_dwell_time = request.form.get("focused_dwell_time")
    is_backfill_call = request.form.get("is_backfill_call") == "true" # This is a boolean that indicates if the base nudge event was triggered and we can skip the backfill
    # Let's see if we already have a session
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return 'okay'
    
    new_user_session_event = user_session_event()
    new_user_session_event.uuid = str(uuid.uuid4())
    new_user_session_event.time = time.time()
    new_user_session_event.action = "popup_dismissed"
    new_user_session_event.user_session_id = user_session_record.uuid
    new_user_session_event.dwell_time = dwell_time
    new_user_session_event.focused_dwell_time = focused_dwell_time
    new_user_session_event.page_view_id = page_view_id
    db_session.add(new_user_session_event)
    db_session.commit()
    
    vandra_discount_applied = user_session_record.vandra_discount_applied

    if vandra_discount_applied is not True:
        user_session_record.vandra_dismissed = True
        db_session.commit()
        if not is_backfill_call:
            update_session_intervention_status(user_session_record, 
                                            INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                            INTERVENTION_STATUS_DISMISSED
                                            )
    
    if not is_backfill_call:
        capture_session_intervention_event(user_session_record,
                                            INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                            "popup_dismissed",
                                            dwell_time,
                                            focused_dwell_time,
                                            page_view_id
                                        )
                                        
    return 'okay'

def record_popup_shown_handler():
    session_cookie = request.form.get("session_cookie")
    page_view_id = request.form.get("page_view_id")
    page_view_id = page_view_id if is_valid_uuid(page_view_id) else None
    dwell_time = request.form.get("dwell_time")
    focused_dwell_time = request.form.get("focused_dwell_time")
    is_backfill_call = request.form.get("is_backfill_call") == "true" # This is a boolean that indicates if the base nudge event was triggered and we can skip the backfill
    # Let's see if we already have a session
    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return 'okay'
    
    new_user_session_event = user_session_event()
    new_user_session_event.uuid = str(uuid.uuid4())
    new_user_session_event.time = time.time()
    new_user_session_event.action = "popup_shown"
    new_user_session_event.user_session_id = user_session_record.uuid
    new_user_session_event.dwell_time = dwell_time
    new_user_session_event.focused_dwell_time = focused_dwell_time
    new_user_session_event.page_view_id = page_view_id
    db_session.add(new_user_session_event)
    db_session.commit()
    #update vandra_shown
    user_session_record.vandra_shown = True
    db_session.commit()
    if not is_backfill_call:
        update_session_intervention_status(user_session_record, 
                                            INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                            INTERVENTION_STATUS_SHOWN
                                            )
        capture_session_intervention_event(user_session_record,
                                            INTERVENTION_TYPE_INTENT_BASED_DISCOUNT,
                                            "popup_shown",
                                            dwell_time,
                                            focused_dwell_time,
                                            page_view_id
                                        )
        
    return 'okay'

def user_is_returning(user_session_record, new_session):
    from ephemeral_store import EphemeralStore
    from datetime import timedelta

    customer_cookie = user_session_record.customer_cookie
    session_cookie = user_session_record.session_cookie

    value = EphemeralStore.find_key(key=customer_cookie)
    if value is not None:
        if new_session:
            #update cache with new session id and set to expire 24 hrs from now
            EphemeralStore.add_to_store(key=customer_cookie, expire_timedelta=timedelta(hours=24), data=session_cookie)
        return True

    EphemeralStore.add_to_store(key=customer_cookie, expire_timedelta=timedelta(hours=24), data=session_cookie)

    return False