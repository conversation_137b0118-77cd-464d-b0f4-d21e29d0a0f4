import time
from datetime import datetime
from db import session_discount_code, user_session, store_url
from app import db_session, app
from utils import get_generic_logger
import requests
from graphql.discount_code_queries import DISCOUNT_CODE_QUERY
from task_queue import ASYNC_DATA_PROCESSING_QUEUE
from sqlalchemy import func

logger = get_generic_logger(__name__)

def parse_iso_datetime(iso_string):
    """Convert ISO 8601 datetime string to Unix timestamp"""
    try:
        if not iso_string:
            return None
        dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
        return int(dt.timestamp())
    except Exception as e:
        logger.error(f"Error parsing datetime {iso_string}: {str(e)}")
        return None

def determine_discount_method(discount_data):
    """
    Determine the discount method from Shopify's response
    Returns: 'percentage', 'amount', 'bxgy', or 'free_shipping'
    """
    try:
        typename = discount_data.get('__typename')
        if typename == 'DiscountCodeBasic':
            value = discount_data.get('customerGets', {}).get('value', {})
            # Check if percentage exists directly
            if 'percentage' in value:
                return 'percentage'
            # Check if amount exists in nested structure
            elif value.get('amount'):
                return 'amount'
        elif typename == 'DiscountCodeBxgy':
            return 'bxgy'
        elif typename == 'DiscountCodeFreeShipping':
            return 'free_shipping'
        return None
    except Exception as e:
        logger.error(f"Error determining discount method: {str(e)}")
        return None

def extract_discount_value(discount_data):
    """
    Extract the discount value (percentage or amount) from Shopify's response
    Returns: float value or None
    """
    try:
        typename = discount_data.get('__typename')
        if typename == 'DiscountCodeBasic':
            value = discount_data.get('customerGets', {}).get('value', {})
            # Get percentage directly from value object
            if 'percentage' in value:
                return float(value.get('percentage', 0))
            # Get amount from nested amount object
            elif value.get('amount'):
                return float(value.get('amount', {}).get('amount', 0))
        return None
    except Exception as e:
        logger.error(f"Error extracting discount value: {str(e)}")
        return None

def check_restrictions(discount_data):
    """
    Check if any restrictions apply to the discount
    Returns: boolean
    """
    try:
        typename = discount_data.get('__typename')
        
        # Check minimum requirements
        if typename == 'DiscountCodeBasic':
            min_req = discount_data.get('minimumRequirement', {})
            if min_req:
                return True
                
        # Check customer selection restrictions
        customer_selection = discount_data.get('customerSelection', {})
        if customer_selection and not customer_selection.get('allCustomers'):
            return True
            
        # Check BXGY restrictions
        if typename == 'DiscountCodeBxgy':
            customer_buys = discount_data.get('customerBuys', {}).get('items', {})
            if not customer_buys.get('allItems'):
                return True
                
        # Check product/collection restrictions
        items = discount_data.get('customerGets', {}).get('items', {})
        if items and not items.get('allItems'):
            return True
            
        return False
    except Exception as e:
        logger.error(f"Error checking restrictions: {str(e)}")
        return True  # Default to True if we can't determine

def check_combines_with_order_discounts(discount_data):
    """
    Check if discount can be combined with order discounts
    Returns: boolean or None if not applicable
    """
    try:
        combines_with = discount_data.get('combinesWith', {})
        if combines_with:
            return combines_with.get('orderDiscounts', False)
        return None
    except Exception as e:
        logger.error(f"Error checking combines with order discounts: {str(e)}")
        return None

def get_discount_code_details_from_shopify(session_discount_code_record_id):
    """
    Retrieves details about a discount code from Shopify and stores them in metadata.
    
    Args:
        session_discount_code_record_id: The UUID of the discount code record
    
    Returns:
        tuple: (success: bool, error_message: str or None)
    """
    try:
        # Get the discount code record
        discount_code_record = session_discount_code.query.filter_by(uuid=session_discount_code_record_id).first()
        if not discount_code_record:
            logger.info(f"Discount code record not found: {session_discount_code_record_id}")
            return False, "Discount code record not found"

        # Get store access token and URL
        store_record = discount_code_record.user_session.parent
        access_token = store_record.access_token
        if not access_token:
            logger.info(f"No access token found for discount code record: {session_discount_code_record_id}")
            return False, "No access token found"

        # Get store URL from store_url table
        store_url_record = store_url.query.filter_by(store_id=store_record.uuid).\
            order_by(store_url.default.desc()).first()
        if store_url_record is None:
            logger.info(f"No store URL found for discount code record: {session_discount_code_record_id}")
            return False, "No store URL found"

        # Prepare GraphQL request
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': access_token
        }
        
        variables = {
            "code": discount_code_record.discount_code_value
        }
        
        data = {
            "query": DISCOUNT_CODE_QUERY,
            "variables": variables
        }

        # Make request to Shopify using configured API version
        api_version = app.config.get('SHOPIFY_GRAPHQL_API_VERSION', '2025-01')
        url = f"https://{store_url_record.url}/admin/api/{api_version}/graphql.json"

        
        response = requests.post(
            url,
            json=data,
            headers=headers
        )

        if not response.ok:
            response_json = response.json()
            errors = response_json.get('errors', [])
            error_messages = '; '.join([error.get('message', 'Unknown error') for error in errors])
            error_msg = f"Shopify API error {response.status_code}: {error_messages}"
            logger.error(error_msg)
            return False, error_msg

        shopify_data = response.json()

        # Check if we got any data back
        if not shopify_data.get('data'):
            logger.error("No data returned from Shopify")
            return False, "No data returned from Shopify"

        code_discount_node = shopify_data.get('data', {}).get('codeDiscountNodeByCode')
        if not code_discount_node:
            return False, "Discount code not found in Shopify"

        discount_data = code_discount_node.get('codeDiscount')
        if not discount_data:
            return False, "No discount data in response"

        # Update discount details
        method = determine_discount_method(discount_data)
        value = extract_discount_value(discount_data)
                
        discount_code_record.discount_method = method
        discount_code_record.discount_value = value
        discount_code_record.restrictions_apply = check_restrictions(discount_data)
        discount_code_record.starts_at = parse_iso_datetime(discount_data.get('startsAt'))
        discount_code_record.ends_at = parse_iso_datetime(discount_data.get('endsAt'))
        discount_code_record.combines_with_order_discounts = check_combines_with_order_discounts(discount_data)
        
        # Update metadata even if discount not found
        metadata = discount_code_record.discount_code_metadata or {}
        metadata['shopify_discount_details'] = shopify_data.get('data', {})
        metadata['shopify_fetch_time'] = int(time.time())
        
        discount_code_record.discount_code_metadata = metadata
        db_session.commit()

        return True, None

    except Exception as e:
        error_msg = f"Error fetching discount code details: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Stack trace: ", exc_info=True)  # Add stack trace
        return False, error_msg

def record_discount_code_usage(user_session_record, discount_code, page_view_id, detection_source='cookie'):
    """
    Records or updates a discount code usage for a user session.
    
    Args:
        user_session_record: The user session record object
        discount_code: The discount code being used
        page_view_id: The current page view ID
        detection_source: Where the code was detected (cookie/cart/checkout)
    
    Returns:
        tuple: (success: bool, error_message: str or None)
    """
    try:
        current_time = int(time.time())
        
        # Check if we already have this discount code for this session
        existing_discount = session_discount_code.query.filter_by(
            user_session_id=user_session_record.uuid,
            discount_code_value=discount_code
        ).first()

        # Create detection history entry
        detection_entry = {
            'page_view_id': page_view_id,
            'detection_source': detection_source,
            'time': current_time
        }

        if existing_discount:
            # Update last detected location and time
            existing_discount.last_detected_in = detection_source
            existing_discount.last_detected_time = current_time
            existing_discount.time = current_time
            
            # Append to existing metadata history
            metadata = existing_discount.discount_code_metadata or {}
            history = metadata.get('detection_history', [])
            history.append(detection_entry)
            metadata['detection_history'] = history
            existing_discount.discount_code_metadata = metadata
            
            db_session.commit()
        else:
            # Create new record with both first and last detection times
            new_discount = session_discount_code(
                time=current_time,
                user_session_id=user_session_record.uuid,
                discount_code_value=discount_code,
                first_detected_in=detection_source,
                last_detected_in=detection_source,
                first_detected_time=current_time,
                last_detected_time=current_time,
                discount_code_metadata={
                    'detection_history': [detection_entry]
                }
            )
            db_session.add(new_discount)
            db_session.commit()

            try:
                # Enqueue async task to fetch discount details from Shopify
                ASYNC_DATA_PROCESSING_QUEUE.enqueue(
                    get_discount_code_details_from_shopify,
                    args=(new_discount.uuid,),
                    job_id=f"fetch_discount_details_{new_discount.uuid}",
                    job_timeout=120
                )
            except Exception as e:
                logger.error(f"Error enqueuing discount code details fetch: {str(e)}")

        return True, None

    except Exception as e:
        error_msg = f"Error recording discount code usage: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

def handle_record_discount_code(session_cookie, discount_code, page_view_id, detection_source):
    """
    Handler logic for recording a discount code, separated from the route to avoid circular imports.
    """
    try:
        if not discount_code:
            return False, "Missing discount code", 400

        # Get user session
        user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
        if not user_session_record:
            return False, "Session not found", 404

        success, error = record_discount_code_usage(
            user_session_record, 
            discount_code,
            page_view_id,
            detection_source
        )

        if not success:
            return False, error, 500

        return True, None, 200

    except Exception as e:
        error_msg = f"Error in discount code endpoint: {str(e)}"
        logger.error(error_msg)
        return False, "Internal server error", 500

def has_competitive_active_discount(user_session_record):
    """
    Check if there's an active percentage discount that's at least 90% of the session's assigned discount
    
    Args:
        user_session_record: user_session model instance
        
    Returns:
        bool: True if a similar active discount exists
    """
    try:
        # If no discount was assigned to this session, return False
        if user_session_record.vandra_discount_rate_assigned is None:
            return False
        
        #convert assigned rate to a positive float
        assigned_rate = -round(float(user_session_record.vandra_discount_rate_assigned)/100.0,2)
        competitive_rate = round(assigned_rate * 0.9,2) # 90% of assigned rate
        #log competitive rate
        #determine current unix time
        current_time = int(time.time())
        # Query for active percentage discounts for this store
        competitive_discount = session_discount_code.query.filter(
            #session id filter
            session_discount_code.user_session_id == user_session_record.uuid,
            #discount value filter
            session_discount_code.discount_value >= competitive_rate,
            # expires soon or doesn't expire (NULL ends_at means no expiration)
            (session_discount_code.ends_at >= current_time + 1800) | (session_discount_code.ends_at == None),
            #restrictions apply filter
            session_discount_code.restrictions_apply == False,
            #discount method filter
            session_discount_code.discount_method == 'percentage',
            #code isn't the same as the assigned code
            session_discount_code.discount_code_value != user_session_record.vandra_discount_code_assigned
        ).first()
        
        return competitive_discount is not None
        
    except Exception as e:
        logger.exception(f"Error checking for competitive discounts for session {user_session_record.uuid}: {str(e)}")
        return False 