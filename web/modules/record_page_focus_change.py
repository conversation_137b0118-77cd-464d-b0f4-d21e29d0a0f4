from flask import request
import time
import uuid
from db import user_session, page_view, page_focus_change
from app import db_session

def record_page_focus_change_handler():
    session_cookie = request.form.get("session_cookie")

    user_session_record = user_session.query.filter_by(session_cookie=session_cookie).first()
    if user_session_record is None:
        return "error"
    page_view_record = page_view.query.filter_by(user_session_id=user_session_record.uuid).\
        filter_by(front_end_id=request.form.get("page_view_id")).first()
    if page_view_record is None:
        return "error"

    focus_change_type = request.form.get("focus_change_type")

    # Let's add a page_focus_change
    page_focus_change_record = page_focus_change()
    page_focus_change_record.uuid = str(uuid.uuid4())
    page_focus_change_record.time = time.time()
    page_focus_change_record.page_view_id = page_view_record.uuid
    if focus_change_type == 'focus':
        page_focus_change_record.coming_into_focus = True
    elif focus_change_type == 'blur':
        page_focus_change_record.coming_into_focus = False
    else:
        page_focus_change_record.coming_into_focus = None

    db_session.add(page_focus_change_record)
    db_session.commit()

    return "okay"