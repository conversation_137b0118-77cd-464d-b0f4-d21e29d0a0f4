import React from 'react';
import { AppProvider } from '@shopify/polaris';
import '@shopify/polaris/build/esm/styles.css';
import '../src/index.css';
import { createAppBridgeContext } from './shopify-app-bridge-mock';

/** @type { import('@storybook/react').Preview } */
const preview = {
  decorators: [
    (Story) => (
      <AppProvider i18n={{}}>
        <div style={{ height: '100vh', width: '100%' }}>
          <Story />
        </div>
      </AppProvider>
    ),
    (Story) => createAppBridgeContext(Story),
  ],
  parameters: {
    layout: 'fullscreen',
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;