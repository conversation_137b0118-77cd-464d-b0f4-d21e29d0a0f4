import React from 'react';
import { useAppBridge } from './shopify-app-bridge-mock';

// Mock Modal component
export const Modal = ({ children, ...props }) => {
  return (
    <div 
      style={{ 
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
        zIndex: 1000,
        maxWidth: '90%',
        maxHeight: '90%',
        overflow: 'auto'
      }}
    >
      <div style={{ marginBottom: '10px', display: 'flex', justifyContent: 'space-between' }}>
        <h3 style={{ margin: 0 }}>{props.title || 'Modal Title'}</h3>
        <button 
          onClick={props.onClose} 
          style={{ 
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ✕
        </button>
      </div>
      <div>
        {children}
      </div>
    </div>
  );
};

// Re-export the useAppBridge hook
export { useAppBridge };

// Mock other components as needed
export const Toast = ({ children, ...props }) => {
  return (
    <div 
      style={{ 
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        backgroundColor: props.isError ? '#FEE' : '#EFE',
        padding: '10px 20px',
        borderRadius: '4px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        zIndex: 1000
      }}
    >
      {children}
    </div>
  );
};

// Add more mock components as needed 