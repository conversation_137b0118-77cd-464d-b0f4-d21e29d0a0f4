/** @type { import('@storybook/react-webpack5').StorybookConfig } */
const path = require('path');

const config = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-essentials',
    '@storybook/preset-create-react-app',
    '@chromatic-com/storybook',
    '@storybook/addon-interactions',
  ],
  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },
  staticDirs: ['../public'],
  webpackFinal: async config => {
    // Add alias for @shopify/app-bridge-react to use our mock
    config.resolve.alias = {
      ...config.resolve.alias,
      '@shopify/app-bridge-react': path.resolve(__dirname, './shopify-app-bridge-react-mock.js'),
    };
    return config;
  },
  docs: {
    autodocs: true,
  },
};
export default config;
