import React, { createContext, useContext } from 'react';

// Create a mock AppBridge context
const AppBridgeContext = createContext(null);

// Mock AppBridge object with common methods
const mockAppBridge = {
  // Basic app properties
  apiKey: 'test-api-key',
  host: 'test-host.myshopify.com',
  
  // Common actions
  dispatch: (action) => {
    console.log('AppBridge action dispatched:', action);
    return Promise.resolve();
  },
  
  // Mock modal functionality
  Modal: {
    create: (options) => ({
      dispatch: (action) => {
        console.log('Modal action dispatched:', action);
        return Promise.resolve();
      },
      set: (options) => {
        console.log('Modal options set:', options);
        return Promise.resolve();
      },
      subscribe: (callback) => {
        console.log('Modal subscription added');
        return () => console.log('Modal subscription removed');
      }
    })
  },
  
  // Mock Toast functionality
  Toast: {
    create: (app, options) => ({
      dispatch: (action) => {
        console.log('Toast action dispatched:', action);
        return Promise.resolve();
      }
    })
  },
  
  // Mock other common App Bridge features as needed
  // Add more mock implementations as your components require them
};

// Provider component to wrap stories with AppBridge context
export function AppBridgeProvider({ children }) {
  return (
    <AppBridgeContext.Provider value={mockAppBridge}>
      {children}
    </AppBridgeContext.Provider>
  );
}

// Hook to use AppBridge in components
export function useAppBridge() {
  const appBridge = useContext(AppBridgeContext);
  if (!appBridge) {
    console.warn('No AppBridge context found. Make sure to wrap your component with AppBridgeProvider.');
    return mockAppBridge;
  }
  return appBridge;
}

// Helper to create AppBridge context for Storybook
export function createAppBridgeContext(Story) {
  return (
    <AppBridgeProvider>
      <Story />
    </AppBridgeProvider>
  );
}

// Mock for the useAppBridge hook from @shopify/app-bridge-react
export const mockUseAppBridge = () => mockAppBridge; 