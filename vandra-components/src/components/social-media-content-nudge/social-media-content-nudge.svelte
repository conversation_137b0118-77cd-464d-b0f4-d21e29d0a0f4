<script lang="ts">
  import { apiUrl } from "../../lib/store";
  import { isMobile } from "../../lib/utils";
  import { onMount } from "svelte";
  import minimizeIcon from "../../assets/SocialMediaContentNudge/minimize-icon.svg";
  import closeIcon from "../../assets/SocialMediaContentNudge/close-icon.svg";
  import soundOnIcon from "../../assets/SocialMediaContentNudge/sound-on-icon.svg";
  import soundOffIcon from "../../assets/SocialMediaContentNudge/sound-off-icon.svg";
  import playIcon from "../../assets/SocialMediaContentNudge/play-icon.svg";
  import unmuteIcon from "../../assets/SocialMediaContentNudge/unmute-icon.svg";
  import loadingIcon from "../../assets/SocialMediaContentNudge/loading-icon.svg";
  import { buildRequest } from "../../lib/api/utils";
  import { Button } from "../../lib/components/ui/button";
  import { vandraGetCookie } from "$lib/utils";

  const INTERVENTION_TYPE = "social_media_content";

  export let productData: {
    productHandle: string;
    productId: string;
    variantId: string;
  };
  export let videoUrl: string | Promise<string>;
  export let videoSelectionMethod: string;
  export let pageViewId: string;
  export let dwellTimeStartCounter: number;
  export let isPreview: boolean = false;
  export let animate: boolean = true;
  let isExpanded: boolean = false;

  let show = true;
  let isMuted = true;
  let isPlaying = true;
  let hasUnmuted = false;
  let videoElement: HTMLVideoElement;
  let showUnmuteHint = false;
  let unmuteHintTimeout: NodeJS.Timeout;
  let isLoading = true;

  // Check if we're in preview mode with animation disabled
  $: isPreviewNoAnimate = isPreview && !animate;
  
  // Determine position and animation classes
  $: positionClass = isPreviewNoAnimate ? "va-relative" : "va-fixed";
  $: animationClass = !isPreviewNoAnimate && animate ? "va-animate-slide-up" : "";
  
  // Determine container styles
  $: containerStyle = isPreviewNoAnimate ? "width: 100%; height: 100%; max-height: 100%;" : "";
  
  // Determine container classes
  $: containerClasses = isExpanded
    ? isPreviewNoAnimate
      ? "va-inset-0"
      : isPreview
        ? "va-inset-0 va-flex va-justify-center va-items-center"
        : "va-inset-0 sm:va-inset-auto sm:va-bottom-4 sm:va-right-4"
    : "va-bottom-4 va-right-4";
  
  // Determine video container classes
  $: videoContainerClasses = isExpanded
    ? isPreviewNoAnimate
      ? "va-w-full va-h-full va-rounded-[10px] va-max-h-full"
      : "sm:va-w-[368px] sm:va-h-[654px] va-w-full va-h-full sm:va-rounded-[10px]"
    : "va-w-[106px] va-h-[188px] va-rounded-[5px]";
    
  // Determine loading container classes
  $: loadingContainerClasses = isExpanded
    ? isPreviewNoAnimate
      ? "va-w-full va-h-full"
      : "sm:va-w-[368px] sm:va-h-[654px] va-w-full va-h-full"
    : "va-w-[106px] va-h-[188px]";
  
  // Background opacity for buttons
  $: bgOpacity = isPreviewNoAnimate ? "#00000080" : "rgba(0, 0, 0, 0.2)";

  // Subscribe to the apiUrl store
  let url = "";
  $: url = $apiUrl;

  let session_cookie = "";

  let resolvedVideoUrl: string = "";

  $: {
    if (videoUrl instanceof Promise) {
      videoUrl.then((url) => {
        resolvedVideoUrl = url;
      });
    } else {
      resolvedVideoUrl = videoUrl;
    }
  }

  // Export function to update props without remounting
  export function updateProps(newProps: Record<string, any>) {
    if (newProps.productData !== undefined) productData = newProps.productData;
    if (newProps.videoUrl !== undefined) {
      videoUrl = newProps.videoUrl;
      if (videoUrl instanceof Promise) {
        videoUrl.then((url) => {
          resolvedVideoUrl = url;
        });
      } else {
        resolvedVideoUrl = videoUrl;
      }
    }
    if (newProps.videoSelectionMethod !== undefined)
      videoSelectionMethod = newProps.videoSelectionMethod;
    if (newProps.pageViewId !== undefined) pageViewId = newProps.pageViewId;
    if (newProps.dwellTimeStartCounter !== undefined)
      dwellTimeStartCounter = newProps.dwellTimeStartCounter;
    if (newProps.isPreview !== undefined) isPreview = newProps.isPreview;
    if (newProps.animate !== undefined) animate = newProps.animate;
    if (newProps.isExpanded !== undefined) isExpanded = newProps.isExpanded;
  }

  function handleClickOutside(event: MouseEvent) {
    if (isExpanded && !event.composedPath().includes(videoElement)) {
      isExpanded = false;
      if (!isPreview) {
        recordEngagement("minimized");
      }
    }
  }

  // Generic function to record engagement events
  const recordEngagement = async (
    action: string,
    isAutomatic: boolean = false
  ) => {
    // Skip API calls when in preview mode
    if (isPreview) return;

    await buildRequest({
      url,
      path: `/base_nudge/record/${action}`,
      method: "POST",
      queryParams: undefined,
      bodyParams: {
        session_cookie,
        intervention_type: INTERVENTION_TYPE,
        page_view_id: pageViewId,
        dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
        metadata: JSON.stringify({
          product: productData,
          is_automatic: isAutomatic,
          video_url: resolvedVideoUrl,
          video_selection_method: videoSelectionMethod,
        }),
      },
    });
  };

  function toggleExpand() {
    if (isPreviewNoAnimate) return;

    isExpanded = !isExpanded;
    if (isExpanded) {
      // Record expanded event
      if (!isPreview) {
        recordEngagement("expanded");
      }

      // Restart video when expanded
      if (videoElement) {
        videoElement.currentTime = 0;
        videoElement.play();
        isPlaying = true;
      }
      // Show unmute hint only if video is muted and hasn't been unmuted yet
      if (isMuted && !hasUnmuted) {
        showUnmuteHint = true;
        // Clear any existing timeout
        if (unmuteHintTimeout) clearTimeout(unmuteHintTimeout);
        // Set new timeout
        unmuteHintTimeout = setTimeout(() => {
          showUnmuteHint = false;
        }, 5000);
      } else {
        showUnmuteHint = false;
      }
    } else {
      // Record minimized event
      if (!isPreview) {
        recordEngagement("minimized");
      }
    }
  }

  function toggleMute() {
    if (videoElement) {
      isMuted = !isMuted;
      videoElement.muted = isMuted;

      // Record mute/unmute event
      if (!isPreview) {
        recordEngagement(isMuted ? "muted" : "unmuted");
      }

      // Hide unmute hint when user toggles mute
      showUnmuteHint = false;
      if (unmuteHintTimeout) {
        clearTimeout(unmuteHintTimeout);
      }
    }

    hasUnmuted = true;
  }

  function togglePlay() {
    if (videoElement) {
      isPlaying = !isPlaying;
      if (isPlaying) {
        videoElement.play();
        if (!isPreview) {
          recordEngagement("played");
        }
      } else {
        videoElement.pause();
        if (!isPreview) {
          recordEngagement("paused");
        }
      }
    }
  }

  function handleVideoLoaded() {
    isLoading = false;
    // Record initial play event when video autoloads, but mark it as automatic
    if (isPlaying && !isPreview) {
      recordEngagement("played", true);
    }
  }

  function handleInteraction(e: MouseEvent) {
    e.stopPropagation();
    if (!isExpanded) {
      toggleExpand();
    } else if (isExpanded && isMuted && !hasUnmuted) {
      toggleMute();
    } else {
      togglePlay();
    }
  }

  onMount(() => {
    session_cookie = vandraGetCookie("vandra_session_cookie") || "";

    // Add click outside listener
    document.addEventListener("click", handleClickOutside);

    // Skip API calls when in preview mode
    if (!isPreview) {
      recordEngagement("shown", true);
    }

    return () => {
      document.removeEventListener("click", handleClickOutside);
      if (unmuteHintTimeout) clearTimeout(unmuteHintTimeout);
    };
  });

  const recordDismissed = async () => {
    if (!isPreview) {
      recordEngagement("dismissed", false);
    }
  };
</script>

{#if show}
  <div
    class={`${positionClass} va-z-max ${animationClass} ${containerClasses}`}
    style={containerStyle}
  >
    <div
      class={`va-flex va-flex-col va-overflow-hidden va-shadow-lg ${videoContainerClasses}`}
    >
      <!-- Video container -->
      <button
        type="button"
        class="va-relative va-cursor-pointer va-border-0 va-p-0 va-bg-transparent va-block va-h-full va-w-full"
        style="line-height: 0;"
        disabled={isLoading}
        on:click={handleInteraction}
        aria-label={isExpanded
          ? isMuted
            ? "Unmute video"
            : "Play/pause video"
          : "Expand video"}
      >
        {#if isLoading}
          <div
            class={`va-bg-gray-100 va-flex va-items-center va-justify-center ${loadingContainerClasses}`}
          >
            <img
              src={loadingIcon}
              alt="Loading Icon"
              class="va-w-10 va-h-10 va-loading-spin"
            />
          </div>
        {/if}

        <video
          bind:this={videoElement}
          src={resolvedVideoUrl}
          autoplay
          loop
          muted={isMuted}
          playsinline
          class="va-w-full va-h-full va-object-cover"
          on:loadeddata={handleVideoLoaded}
          style={isLoading ? "display: none;" : ""}
        ></video>

        {#if showUnmuteHint && isExpanded && isMuted}
          <div
            class="va-absolute va-bottom-20 va-w-full va-flex va-justify-center"
          >
            <div
              class="va-text-base va-font-bold va-bg-black va-bg-opacity-50 va-text-white va-px-4 va-py-3 va-rounded-[10px] va-whitespace-nowrap va-uppercase va-flex va-items-center va-gap-2 va-animate-fade-out"
            >
              <img src={`${unmuteIcon}`} alt="unmuteIcon" class="va-m-0" />
              <span>{isMobile() ? "Tap" : "Click"} to unmute</span>
            </div>
          </div>
        {/if}

        {#if isExpanded}
          <!-- Minimize button -->
          <Button
            variant="link"
            on:click={(e) => {
              e.stopPropagation();
              if (!isPreviewNoAnimate) {
                toggleExpand();
              }
            }}
            class={`${isPreview ? 'va-absolute' : 'va-fixed'} va-w-10 va-h-10 va-top-4 va-left-4 va-rounded-full va-border-none va-flex va-items-center va-justify-center va-cursor-pointer`}
            style={`background-color: ${bgOpacity}; backdrop-filter: blur(1px);`}
            aria-label="Minimize video"
          >
            <img
              src={`${minimizeIcon}`}
              alt="minimizeIcon"
              class={`va-m-0 va-w-6 va-h-6`}
            />
          </Button>

          <!-- Sound button -->
          <Button
            variant="link"
            on:click={(e) => {
              e.stopPropagation();
              toggleMute();
            }}
            class={`${isPreview ? 'va-absolute' : 'va-fixed'} va-w-10 va-h-10 va-bottom-4 va-right-4 va-rounded-full va-border-none va-flex va-items-center va-justify-center va-cursor-pointer`}
            style={`background-color: ${bgOpacity}; backdrop-filter: blur(1px);`}
            aria-label={isMuted ? "Unmute video" : "Mute video"}
          >
            <img
              src={`${isMuted ? soundOffIcon : soundOnIcon}`}
              alt="soundIcon"
              class="va-m-0"
            />
          </Button>

          <!-- Play button when paused -->
          {#if !isPlaying}
            <Button
              variant="link"
              on:click={(e) => {
                e.stopPropagation();
                togglePlay();
              }}
              class={`va-absolute va-w-${isPreviewNoAnimate ? '16' : '10'} va-h-${isPreviewNoAnimate ? '16' : '10'} va-border-none va-rounded-full va-flex va-items-center va-justify-center va-top-1/2 va-left-1/2 ${isPreviewNoAnimate ? 'va-transform -va-translate-x-1/2 -va-translate-y-1/2' : '-translate-x-1/2 -translate-y-1/2'} va-cursor-pointer`}
              style={`background-color: ${bgOpacity}; backdrop-filter: blur(1px);`}
              aria-label="Play video"
            >
              <img
                src={`${playIcon}`}
                alt="playIcon"
                class="va-m-0 va-w-6 va-h-6"
              />
            </Button>
          {/if}
        {/if}

        <!-- Close button -->
        <Button
          variant="link"
          on:click={(e) => {
            e.stopPropagation();
            if (!isPreview) {
              recordDismissed();
              show = false;
            }
          }}
          class={`${
            isExpanded
              ? "va-absolute va-w-10 va-h-10 va-top-4 va-right-4"
              : "va-fixed va-w-4 va-h-4 va-top-[-5px] va-right-[-5px]"
          } va-rounded-full va-border-none va-flex va-items-center va-justify-center va-cursor-pointer va-p-0`}
          style={`background-color: ${isExpanded ? bgOpacity : "rgba(0, 0, 0, 0.8)"}; backdrop-filter: blur(1px);`}
          aria-label="Close video"
        >
          <img
            src={`${closeIcon}`}
            alt="closebuttonIcon"
            class={`va-m-0 ${isExpanded ? 'va-w-6 va-h-6' : 'va-w-[9px] va-h-[9px]'}`}
          />
        </Button>
      </button>
    </div>
  </div>
{/if}

<style>
  @keyframes slideUp {
    0% {
      transform: translateY(100%);
    }
    100% {
      transform: translateY(0);
    }
  }

  .va-animate-slide-up {
    animation: slideUp 0.3s ease-out forwards !important;
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }

  .va-animate-fade-out {
    animation: fadeOut 5s ease-out forwards;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  :global(.va-loading-spin) {
    transform-origin: center;
    animation: spin 0.7s linear infinite;
  }
</style>
