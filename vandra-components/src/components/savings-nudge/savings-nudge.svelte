<script lang="ts">
  import { onMount } from "svelte";
  import { buildRequest } from "../../lib/api/utils";
  import closeIcon from "../../assets/closeIcon.svg";
  import { Button } from "../../lib/components/ui/button";
  import {
    apiUrl,
    store_total_savings,
    store_savings_show,
    store_savings_mounted,
    store_savings_threshold,
  } from "../../lib/store";
  import { vandraGetCookie, vandraSetCookie, formatPrice } from "$lib/utils";
  import { INTERVENTION_TYPE_NAMES } from "../../constants";
  import type { CartItem } from "../../lib/types";

  export let tabColor = "#2B3336";
  export let tabFontColor = "#FFFFFF";
  export let tabText = "Savings: {total_savings}";
  export let primaryColor = "#232323";
  export let primaryFontColor = "#232323";
  export let backgroundColor = "#FFFFFF";
  export let font = "Inter";
  export let expandedHeadline = "Lucky you… {total_savings} in savings!";
  export let expandedBody =
    "When you're ready, head to checkout and lock in these savings.";
  export let buttonText = "Checkout";
  export let buttonDestination = "/checkout";
  export let positioning = "Right"; // or Left
  export let anchor = "Top"; // or Bottom
  export let distanceFromAnchor = 30;

  export let total_savings = "";
  export let pageViewId: string;
  export let dwellTimeStartCounter = Date.now();
  export let isPreview: boolean = false;
  export let animate: boolean = true;

  let minimized = true;
  let hiding = false;

  let session_cookie = "";
  let shown = false;

  // Subscribe to the apiUrl store
  let url = "";
  $: $apiUrl, (url = $apiUrl);

  // Only update total_savings from the store if not in preview mode
  $: if (!isPreview && $store_total_savings) {
    total_savings = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format($store_total_savings);
  }

  // Compute animation classes based on animate prop and positioning
  $: slideAnimationClass = animate
    ? positioning === "Left"
      ? "slide_to_right_animation"
      : "slide_to_left_animation"
    : "";

  // Compute position class based on preview settings
  $: positioningClass = positioning === "Left" ? "va-left-0" : "va-right-0";

  // Export function to update props without remounting
  export function updateProps(newProps: Record<string, any>) {
    if (newProps.tabColor !== undefined) tabColor = newProps.tabColor;
    if (newProps.tabFontColor !== undefined)
      tabFontColor = newProps.tabFontColor;
    if (newProps.tabText !== undefined) tabText = newProps.tabText;
    if (newProps.primaryColor !== undefined)
      primaryColor = newProps.primaryColor;
    if (newProps.primaryFontColor !== undefined)
      primaryFontColor = newProps.primaryFontColor;
    if (newProps.backgroundColor !== undefined)
      backgroundColor = newProps.backgroundColor;
    if (newProps.font !== undefined) font = newProps.font;
    if (newProps.expandedHeadline !== undefined)
      expandedHeadline = newProps.expandedHeadline;
    if (newProps.expandedBody !== undefined)
      expandedBody = newProps.expandedBody;
    if (newProps.buttonText !== undefined) buttonText = newProps.buttonText;
    if (newProps.buttonDestination !== undefined)
      buttonDestination = newProps.buttonDestination;
    if (newProps.positioning !== undefined) positioning = newProps.positioning;
    if (newProps.anchor !== undefined) anchor = newProps.anchor;
    if (newProps.distanceFromAnchor !== undefined)
      distanceFromAnchor = newProps.distanceFromAnchor;
    if (newProps.total_savings !== undefined)
      total_savings = newProps.total_savings;
    if (newProps.pageViewId !== undefined) pageViewId = newProps.pageViewId;
    if (newProps.isPreview !== undefined) isPreview = newProps.isPreview;
    if (newProps.animate !== undefined) animate = newProps.animate;
  }

  onMount(() => {
    session_cookie = vandraGetCookie("vandra_session_cookie") || "";
    // store_show_savings.set(true);
    dwellTimeStartCounter = Date.now();
    store_savings_mounted.set(true);

    // If in preview mode, we want to show the component right away
    if (isPreview) {
      store_savings_show.set(true);
      // Keep minimized state regardless of animation setting
      // (removed auto-expansion logic that was causing the issue)
    }

    store_savings_show.subscribe((value) => {
      if (value && $store_total_savings >= $store_savings_threshold) {
        if (!shown) {
          // Only record the action if it hasn't been shown before
          recordAction("shown");
          shown = true;
        }
      } else {
        shown = false;
      }
    });
    store_total_savings.subscribe((value) => {
      if (value >= $store_savings_threshold && $store_savings_show) {
        if (!shown) {
          // Only record the action if it hasn't been shown before
          recordAction("shown");
          shown = true;
        }
      } else {
        shown = false;
      }
    });
    return () => store_savings_mounted.set(false);
  });

  const recordAction = (
    action: "shown" | "minimized" | "expanded" | "dismissed"
  ) => {
    // Skip API calls when in preview mode
    if (isPreview) return;

    buildRequest({
      url,
      path: `/base_nudge/record/${action}`,
      method: "POST",
      queryParams: undefined,
      bodyParams: {
        session_cookie: session_cookie,
        intervention_type: INTERVENTION_TYPE_NAMES.SAVINGS_NUDGE,
        page_view_id: pageViewId,
        dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
        metadata: JSON.stringify({
          total_savings: total_savings,
          page: window.location.href,
        }),
      },
    }).catch((err) => {
      throw new Error(err.message);
    });
  };
  const recordNudgeCTAClicked = async () => {
    // Skip API calls when in preview mode
    if (isPreview) {
      window.location.href = buttonDestination;
      return;
    }

    const res = (await buildRequest({
      url,
      path: "/base_nudge/record/cta",
      method: "POST",
      queryParams: undefined,
      bodyParams: {
        session_cookie: session_cookie,
        intervention_type: INTERVENTION_TYPE_NAMES.SAVINGS_NUDGE,
        page_view_id: pageViewId,
        dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
        metadata: JSON.stringify({
          total_savings: total_savings,
          destination: buttonDestination,
          page: window.location.href,
        }),
      },
    })) as any;
    if (res.errors) {
      throw new Error(res.errors.message);
    }
    window.location.href = buttonDestination;
  };

  const toggleVisibility = () => {
    hiding = true;
    setTimeout(() => {
      minimized = !minimized;
      setTimeout(() => {
        hiding = false;
        if (!$store_savings_show) return;
        if (minimized) {
          recordAction("minimized");
        } else {
          recordAction("expanded");
        }
      }, 200);
    }, 500);
  };
  const recordDismissed = async (e: Event) => {
    if (isPreview) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    setTimeout(() => {
      store_savings_show.set(false);
      vandraSetCookie("vandra_savings_nudge_dismissed", "true", 0.04);
    }, 500);
    recordAction("dismissed");
  };
</script>

{#if $store_savings_show && $store_total_savings >= $store_savings_threshold}
  <div
    class="va-fixed va-z-max {positioningClass} {slideAnimationClass}"
    style={`font-family: ${font}, serif; ${anchor == "Bottom" ? "bottom" : "top"}: clamp(50px, ${distanceFromAnchor}vh, calc(100vh - 200px));`}
  >
    <div
      class="{isPreview && !minimized
        ? 'va-w-[calc(100%-20px)]'
        : 'va-w-[375px]'} {isPreview && !minimized
        ? 'va-mx-[10px]'
        : positioning == 'Left'
          ? 'va-ml-[10px]'
          : 'va-mr-[10px]'} va-border-[#dfd9fd] va-rounded-l-[4px] va-shadow-[0px_2.5px_15px_rgba(0,0,0,0.12)] va-transition va-duration-500 va-max-w-full sm:va-max-w-[375px]"
      style={`background-color: ${backgroundColor}; ${minimized ? "display: none;" : ""} ${hiding ? `transform:translateX(${positioning == "Left" ? "-" : ""}100%); ` : "transform:translateX(0px);"}`}
    >
      <div class="va-absolute va-top-3 va-right-5">
        <Button
          class="va-p-1 va-bg-transparent va-border-0 va-cursor-pointer"
          variant="link"
          on:click={toggleVisibility}
        >
          <img src={`${closeIcon}`} alt="closebuttonIcon" />
        </Button>
      </div>
      <div
        class="va-p-[18px] va-flex va-flex-col va-justify-center va-items-start"
        style={`color: ${primaryColor};`}
      >
        <div
          class="va-w-full va-pr-8 va-text-lg va-leading-[22px] va-font-bold va-line-clamp-no-ellipsis-1"
          style={`color: ${primaryFontColor};`}
        >
          {expandedHeadline.replaceAll("{total_savings}", total_savings)}
        </div>
        <div
          class="va-w-full va-text-sm va-leading-[17px] va-font-normal va-mt-2 va-line-clamp-no-ellipsis-2"
          style={`color: ${primaryFontColor};`}
        >
          {expandedBody.replaceAll("{total_savings}", total_savings)}
        </div>
        <svelte-css-wrapper
          style="display: contents; --color: {backgroundColor}; --bg-color: {primaryColor};"
        >
          <button
            class="va-flex va-self-center va-items-center va-mt-4 va-w-full va-h-11 va-border-none va-justify-center va-text-lg va-font-semibold va-cursor-pointer va-rounded-[4px]"
            style="font-family: {font}, serif;"
            on:click={recordNudgeCTAClicked}
            disabled={isPreview}
          >
            {buttonText}
          </button>
        </svelte-css-wrapper>
      </div>
    </div>
    <button
      class={`va-flex va-gap-2 va-items-center va-w-[30px] va-py-4 va-rounded-r-[10px] va-border-none va-text-center va-text-sm va-font-bold [writing-mode:vertical-lr] va-cursor-pointer va-transition va-duration-500`}
      style="background-color: {tabColor}; color: {tabFontColor} !important; {minimized
        ? 'display: flex;'
        : 'display: none;'} 
    {hiding
        ? `transform:${positioning == 'Left' ? 'translateX(-100%)' : 'translateX(100%) rotate(180deg)'}`
        : `transform:translateX(0px) ${positioning == 'Left' ? '' : 'rotate(180deg)'}`}"
      on:click={toggleVisibility}
    >
      {#if positioning == "Left"}
        <Button
          class="va-p-0 va-bg-transparent va-border-0 va-cursor-pointer [filter:brightness(3)]"
          variant="link"
          on:click={recordDismissed}
        >
          <img
            width="14"
            height="14"
            src={`${closeIcon}`}
            alt="closebuttonIcon"
          />
        </Button>
      {/if}
      {tabText.replaceAll("{total_savings}", total_savings)}
      {#if positioning !== "Left"}
        <Button
          class="va-p-0 va-bg-transparent va-border-0 va-cursor-pointer [filter:brightness(3)]"
          variant="link"
          on:click={recordDismissed}
        >
          <img
            width="14"
            height="14"
            src={`${closeIcon}`}
            alt="closebuttonIcon"
          />
        </Button>
      {/if}
    </button>
  </div>
{/if}

<style>
  button {
    background-color: var(--bg-color, #232323);
    color: var(--color, #ffffff);
  }
  @keyframes slideToLeft {
    0% {
      transform: translateX(100%); /* Start off-screen */
    }
    100% {
      transform: translateX(0); /* End at the original position */
    }
  }

  @keyframes slideToRight {
    0% {
      transform: translateX(-100%); /* Start off-screen */
    }
    100% {
      transform: translateX(0); /* End at the original position */
    }
  }

  .slide_to_left_animation {
    animation: slideToLeft 0.3s ease-in-out forwards; /* Define the animation */
  }

  .slide_to_right_animation {
    animation: slideToRight 0.3s ease-in-out forwards; /* Define the animation */
  }
</style>
