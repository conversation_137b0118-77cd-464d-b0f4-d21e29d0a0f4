<script>
    import CheckMark from '../../assets/checkMark.svg';

    export let font = "Inter";
    export let tabColor = "#0000AA";
    export let tabFontColor = "#FFFFFF";
    export let buttonText = "Apply Discount";
    export let successButtonText = "Applied";

    /**
     * @type {() => void}
     */
     export let onClick;
     /**
     * @type {() => void}
     */
      export let postClick;
    let status = "applying"; // idle, applying, post
    let position = 0;
    function handleClick() {
        position = 100;
        onClick();
        setTimeout(() => {
            status = "post";
            position = -100;
            setTimeout(() => {
                position = 0;
                setTimeout(() => {
                    status = "applying";
                    postClick();
                }, 800);
            }, 100);
        }, 500);
    }
</script>
<button class="va-flex va-self-center va-overflow-hidden va-items-center va-mt-2 va-w-full va-h-11 va-border-none va-justify-center va-text-lg va-font-semibold va-cursor-pointer va-rounded-[4px]" style={`background-color: ${tabColor}; color: ${tabFontColor};`} on:click="{handleClick}">
    {#if status == "applying" }
    <span class="va-w-full va-transition va-duration-500 va-text-center" style="transform: translateX({position}%);font-family: {font}, Arial;">
        {buttonText}
    </span>
    {/if}
    {#if status === "post"}
        <span class="va-w-full va-justify-center va-flex va-gap-2 va-transition va-duration-500 va-text-center" style="transform: translateX({position}%);font-family: {font}, Arial;">
            <img src={`${CheckMark}`} alt="closebuttonIcon" /> {successButtonText}
        </span>
    {/if}
</button>