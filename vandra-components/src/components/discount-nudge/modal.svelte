<script lang="ts">
    import { onMount } from "svelte";
    import { buildRequest } from "../../lib/api/utils";
    import closeIcon from "../../assets/closeIcon.svg";
    import { Button } from "../../lib/components/ui/button";
    import { apiUrl, store_savings_show, store_total_savings } from "../../lib/store";
    import { vandraGetCookie, vandraSetCookie, formatPrice } from "$lib/utils";
    import { INTERVENTION_TYPE_NAMES } from "../../constants";
    import Coupon from "../../assets/coupon.svg"
    import ApplyButton from "./applyButton.svelte";

    export let tabColor = "#0000AA";
    export let tabFontColor = "#FFFFFF";

    export let primaryColor = "#232323";
    export let backgroundColor = "#FFFFFF";
    export let font = "Inter";
    export let expandedHeadline = "Get {discount}% off now";
    export let expandedBody = "Valid for one day only. Don't miss out!";
    export let buttonText = "Apply Discount";
    export let successButtonText = "Applied";
    
    export let discount_rate = 0;
    export let discount_code = '';

    export let recordAction = (action : "shown" | "minimized" | "expanded" | "dismissed") => {}
    export let recordNudgeCTAClicked = () => {}
    export let showNudge = (e: boolean) => {}

    let copied = false;
    let show = true;

    let applyButtonParams;
    $: applyButtonParams = {
        font,
        tabColor,
        tabFontColor,
        buttonText,
        successButtonText,
        onClick: recordNudgeCTAClicked,
        postClick: hide
    }
    const dismiss = () => {
        recordAction("dismissed");
        hide();
    }
    const hide = () => {
        show = false;
        setTimeout(() => {
            showNudge(false);
        }, 300);
    }
    const onCopyClicked = () => {
        navigator.clipboard.writeText(discount_code);
        copied = true;
    }
</script>

<div class="nudge_container va-fixed va-z-max va-w-full va-h-full va-bg-black/50  va-transition {!show ? "va-opacity-0":""}" style="font-family: {font}, Arial;">
    <div class="va-flex va-justify-center va-items-center va-w-full va-h-full">
        <div class=" va-relative va-flex va-py-6 va-px-12 va-justify-center va-items-end va-w-[450px] va-h-[216px] va-border-[#dfd9fd] va-rounded-[10px] va-shadow-[0px_2.5px_15px_rgba(0,0,0,0.12)]" style="background-color: {backgroundColor};">
            <div class="va-absolute va-top-4 va-right-4">
                <Button class="va-p-1 va-bg-transparent va-border-0 va-cursor-pointer" variant="link" on:click={dismiss}>
                    <img src={`${closeIcon}`} alt="closebuttonIcon" />
                </Button>
            </div>
            <div class="va-w-80 va-flex va-flex-col va-justify-center va-items-center" style={`color: ${primaryColor};`}>
                <div class="va-text-2xl va-leading-[22px] va-font-bold va-uppercase">
                    {expandedHeadline.replaceAll('{discount}', discount_rate.toString())}
                </div>
                <div class="va-mb-4 va-text-base va-leading-[17px] va-font-normal va-mt-2">
                    {expandedBody}
                </div>
                <div class="va-flex va-content-start va-items-center va-gap-2 va-border-2 va-rounded-[4px] va-w-full va-border-dotted va-border-[#777777] va-p-[10px]">
                    <img src={`${Coupon}`} alt="couponbuttonIcon" />
                    <span class=" va-text-xs">{discount_code}</span>
                    <!-- svelte-ignore a11y_click_events_have_key_events, a11y_no_static_element_interactions (because of reasons) -->
                    <span class="va-ml-auto va-underline va-cursor-pointer va-text-base va-leading-[17px] va-font-normal" on:click={onCopyClicked}>{copied ? "copied" : "copy"}</span>
                </div>
                <ApplyButton {...applyButtonParams} />
            </div>
        </div>
    </div>
</div>


<style>
</style>