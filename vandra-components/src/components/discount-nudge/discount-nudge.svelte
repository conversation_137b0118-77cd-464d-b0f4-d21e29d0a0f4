<script lang="ts">
    import { onMount } from "svelte";
    import { buildRequest } from "../../lib/api/utils";
    import { apiUrl, store_savings_show } from "../../lib/store";
    import { vandraGetCookie, vandraSetCookie, formatPrice, unix_to_gmt_time } from "$lib/utils";
    import { action_types, engagement_action_types, INTERVENTION_TYPE_NAMES, UI_VERSION_NAMES, WIDGET_TYPES } from "../../constants";
    import Default from "./default.svelte";
    import Modal from "./modal.svelte";
    import Auto from "./auto.svelte";
    import Reminder from "./reminder.svelte";
    import '../../components.css';

    export let show = false;
    export let isPreview: boolean = false;
    export let widget_type : "POPUP" | "MINIMIZED" | "RETURN" = "POPUP";

    export let tabText = "Get {discount}% off now";
    export let primaryColor = "#0000FF";
    export let backgroundColor = "#FFFFFF";
    export let font = "Source Sans Pro";
    export let expandedHeadline = "Get {discount}% off now";
    export let expandedHeadlineAuto = "Get {discount}% off now";
    export let expandedBody = "Valid for one day only. Don't miss out!";
    export let buttonText = "Apply Discount";
    export let successButtonText = "Applied";
    export let tabDisabled = false;

    export let discount_code = "";
    export let discount_applied = false;
    export let discount_rate : number;
    export let discount_ends_at_time : number;
    // DEFAULT_RIGHT, DEFAULT_LEFT, DEFAULT_RIGHT_NEW, NO_X, IMAGE, 
    // MODAL, AUTO_APPLY, COUNTDOWN, RENUDGE, RENUDGE_WITH_EXPIRES, NUDGE_DELAY, NUDGE_DELAY_V2, ACTION_BASED_SHOW, MESSAGING, Reminder Nudge
    export let front_end_ui_name = UI_VERSION_NAMES.DEFAULT_RIGHT;
    export let popup_image_url = "";
    export let pageViewId = "";
    export let dwellTimeStartCounter = Date.now();
    export let use_meta_ad_pixel = false;
    export let vandra_fire_meta_pixel: (eventName: string, eventData: Record<string, any>) => void;
    export let captureDiscountCodeApplication: (discountCode: string, event: string) => void | undefined;
    export let isMobile = false;
    let session_cookie = "";
    let customer_cookie = "";
    
    let parameters;
    $: parameters = {
        isPreview,
        widget_type,
        tabText,
        tabColor: primaryColor,
        primaryColor: "#000000",
        backgroundColor,
        font,
        expandedHeadline,
        expandedHeadlineAuto,
        expandedBody,
        buttonText,
        successButtonText,
        discount_code,
        discount_rate,
        popup_image_url,
        discount_ends_at_time,
        session_cookie,
        customer_cookie,
    }
    // Subscribe to the $apiUrl store
    let url = "";
    $: $apiUrl, (url = $apiUrl);
    var vandra_url = new URL(
        window.location.href
    );
    
    export let ui_version_test = ""; // ACTION_BASED_SHOW
    export let test_action_trigger = "click";
    export let test_action_delay = 5;
    
    function get_dwell_time() {
        const dwell_time_counter = (Date.now() - dwellTimeStartCounter) / 1000;
        return dwell_time_counter;
    };

    function vandra_update_nudge_parameters(trigger_state: string, trigger_action: string, trigger_type: string) {
        fetch($apiUrl + "/update_nudge_parameters", {
            method: "POST",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            body: new URLSearchParams({
                session_cookie: session_cookie,
                trigger_state: trigger_state,
                trigger_action: trigger_action,
                trigger_type: trigger_type
            })
        });
    }

    function showNudge(isShow = true) {
        if (isPreview) return;
        show = isShow;
        store_savings_show.set(!isShow);
        if (!isShow) return;
        recordAction("shown");
        fetch(`${$apiUrl}/record_popup_shown`, { // Record function from old vandra-main for disount popup, not sure if needed
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            },
            body: new URLSearchParams({
                session_cookie: session_cookie,
                page_view_id: pageViewId,
                dwell_time: get_dwell_time().toString(),
                is_backfill_call: "true"
            })
        });
        if (use_meta_ad_pixel) {
            vandra_fire_meta_pixel('VandraPopupShown', {
                'customer_cookie': customer_cookie,
                'session_cookie': session_cookie,
                'popup_shown': show,
                'discount_code': discount_code,
                'discount_offer': discount_rate,
                'discount_expiration': unix_to_gmt_time(discount_ends_at_time)
            })
        }
        if (captureDiscountCodeApplication) {
            captureDiscountCodeApplication(discount_code,"vandra_shown");
        }
    }

    // Export function to update props without remounting
    export function updateProps(newProps: Record<string, any>) {
        isPreview = newProps.isPreview ?? isPreview;
        front_end_ui_name = newProps.front_end_ui_name ?? front_end_ui_name;
        widget_type = newProps.widget_type ?? widget_type;
        tabText = newProps.tabText ?? tabText;
        primaryColor = newProps.primaryColor ?? primaryColor;
        backgroundColor = newProps.backgroundColor ?? backgroundColor;
        font = newProps.font ?? font;
        expandedHeadline = newProps.expandedHeadline ?? expandedHeadline;
        expandedHeadlineAuto = newProps.expandedHeadlineAuto ?? expandedHeadlineAuto;
        expandedBody = newProps.expandedBody ?? expandedBody;
        buttonText = newProps.buttonText ?? buttonText;
        successButtonText = newProps.successButtonText ?? successButtonText;
        discount_code = newProps.discount_code ?? discount_code;
        discount_rate = newProps.discount_rate ?? discount_rate;
        popup_image_url = newProps.popup_image_url ?? popup_image_url;
        discount_ends_at_time = newProps.discount_ends_at_time ?? discount_ends_at_time;
        tabDisabled = newProps.tabDisabled ?? tabDisabled;
    }

    onMount(async () => {

        // Skip API calls and parameter fetching when in preview mode
        if (isPreview) {
            show = true;
            return;
        }

        session_cookie = vandraGetCookie("vandra_session_cookie") || "";
        customer_cookie = vandraGetCookie("vandra_customer_cookie") || "";
        
        const get_popup_status_response = await fetch(`${$apiUrl}/get_popup_status?` + new URLSearchParams({
            session_cookie: session_cookie
        }), {
            method: "GET",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            },
            signal: AbortSignal.timeout(10000)
        });
        
        const response_json = await get_popup_status_response.json();
        if (!response_json.hasOwnProperty("vandra_ui_version_name")) {
            return false;
        }
        const {
            vandra_ui_version_name: ui_version_name,
            vandra_ui_version_filename: ui_version_filename,
            vandra_discount_applied: _discount_applied,
            vandra_countdown_deadline: countdown_deadline,
            vandra_renudge_type,
            vandra_can_be_renudged_time,
            vandra_nudge_parameters,
            hide_minimized_popup
        } = response_json;
        tabDisabled = hide_minimized_popup;
        if (ui_version_name) front_end_ui_name = ui_version_name;
        if (ui_version_test) front_end_ui_name = ui_version_test;
        discount_applied = _discount_applied == 'True' ? true: false;
        if (discount_applied && widget_type != WIDGET_TYPES.RETURN) return;
        let nudge_delay = 0;
        let action_based_show = false;
        if ([UI_VERSION_NAMES.NUDGE_DELAY, UI_VERSION_NAMES.NUDGE_DELAY_V2].includes(front_end_ui_name)) {
            const time_since_decisioning = Math.max(0,Date.now() - vandra_nudge_parameters.trigger_decisioning_time*1000);
            //delay relative to how much time has elapsed   
            nudge_delay =  Math.max(vandra_nudge_parameters.trigger_delay - time_since_decisioning/1000,0);
        } else if (front_end_ui_name == UI_VERSION_NAMES.ACTION_BASED_SHOW) {
            // Setup our initial values. Also accounting for if doing manual testing
            const vandra_test_action_trigger = vandra_url.searchParams.get("vandra_test_action_trigger") || test_action_trigger;
            let vandra_test_action_delay = Number(vandra_url.searchParams.get("vandra_test_action_delay")) || test_action_delay;
        
            vandra_test_action_delay = vandra_test_action_delay ? vandra_test_action_delay : 30;  // Check if present if not default to 30 for testing

            let assigned_trigger_type = ui_version_test ? vandra_test_action_trigger : vandra_nudge_parameters.trigger_type;

            let time_since_decisioning = Math.max(0, Date.now() - vandra_nudge_parameters.trigger_decisioning_time*1000);
            
            let action_nudge_delay = ui_version_test ? vandra_test_action_delay : Math.max(vandra_nudge_parameters.trigger_delay - time_since_decisioning/1000,0);
            const delay_cookie = Number(vandraGetCookie("vandra_nudge_delay"));
            if (ui_version_test === "ACTION_BASED_SHOW" && delay_cookie) {
                action_nudge_delay = delay_cookie
            }
            // End setup of initial values

            action_based_show = true;
            let istimeoutExpired = action_nudge_delay === 0 ? true : false;
            let actionOccurred = vandraGetCookie("vandra_action_occurred")
    
            const timeoutId = setTimeout(() => {
                actionOccurred = vandraGetCookie("vandra_action_occurred")
                if (!actionOccurred) {
                    if (assigned_trigger_type === "engagement_action") {
                        //force trigger
                        handlePopup(true, "timeout");
                    } else {
                        // no action was taken during our action timer so fallback to random action trigger
                        addAnyActionTrigger(true);
                    }
                }
            }, action_nudge_delay * 1000);

            const handlePopup = (isFallback: boolean, actionTrigger: string) => {
                if (!show) { 
                    // to avoid different labels on the backend for mobile vs desktop we reassign touchmove to mousemove
                    actionTrigger = actionTrigger === "touchmove" ? "mousemove" : actionTrigger;
                    vandra_update_nudge_parameters(isFallback ? "fallback": "assigned", actionTrigger, "")
                    actionOccurred = vandraGetCookie("vandra_action_occurred")
                    if (!actionOccurred) {
                        showNudge();
                        vandraSetCookie("vandra_action_occurred", "true", 0.04);
                    }
                }
                clearTimeout(timeoutId)
            }

            const addAnyActionTrigger = (isFallback: boolean) => {
                for (let i = 0; i < action_types.length; i++) {
                    let trigger_type = action_types[i];
                    // exclude page_visit we can't set listeners
                    // Note there is an experimental option https://developer.mozilla.org/en-US/docs/Web/API/Navigation/navigate_event
                    if (trigger_type === 'page_visit') return;

                    const eventHandler = () => handlePopup(isFallback, trigger_type)
                    if (trigger_type === 'click') {
                        document.addEventListener(trigger_type, eventHandler);
                    } else {
                        window.addEventListener(trigger_type, eventHandler);
                    }
                }
            }

            const addAnyEngagementActionTrigger = () => {
                for (let i = 0; i < engagement_action_types.length; i++) {
                    let trigger_type = engagement_action_types[i];

                    const eventHandler = () => handlePopup(false, trigger_type)
                    if (trigger_type === 'click') {
                        document.addEventListener(trigger_type, eventHandler);
                    } else {
                        window.addEventListener(trigger_type, eventHandler);
                    }
                }
            }

            if (ui_version_test === "ACTION_BASED_SHOW" && !assigned_trigger_type) {
                assigned_trigger_type = vandraGetCookie("vandra_trigger_type")
            }

            if (actionOccurred) {
                showNudge();
            } else {
                // create listeners for the appropriate default events from the session nudge parameters
                if (assigned_trigger_type === "page_visit") {
                    const visit = vandraGetCookie("vandra_non_initial_page_visit");
                
                    if (visit) {
                        if (istimeoutExpired) {
                            handlePopup(true, assigned_trigger_type)
                        } else {
                            handlePopup(false, assigned_trigger_type)
                        }
                    }

                    vandraSetCookie("vandra_non_initial_page_visit", "true", 30);

                } else if (assigned_trigger_type === "any") {
                    addAnyActionTrigger(false)
                } else if (assigned_trigger_type === "engagement_action") {
                    addAnyEngagementActionTrigger()
                } else if (assigned_trigger_type === "click") {
                    document.addEventListener(assigned_trigger_type, () => {
                        handlePopup(false, assigned_trigger_type)
                    })
                } else {
                    if (assigned_trigger_type === "mousemove" && isMobile) {
                        assigned_trigger_type = "touchmove"
                    }
                    window.addEventListener(assigned_trigger_type, () => {
                        handlePopup(false, assigned_trigger_type)
                    })
                }
            }
            if (ui_version_test === "ACTION_BASED_SHOW") {
                vandraSetCookie("vandra_trigger_type", assigned_trigger_type, 30);
                vandraSetCookie("vandra_nudge_delay", action_nudge_delay.toString(), 30);
            }
        }
        // add nudge delay as needed
        if (!action_based_show) {
            setTimeout(() => {
                showNudge();
            }, nudge_delay * 1000);
        }
    });
    const recordAction = (action : "shown" | "minimized" | "expanded" | "dismissed") => {
        if (isPreview) return;
        buildRequest({
            url,
            path: `/base_nudge/record/${action}`,
            method: "POST",
            queryParams: undefined,
            bodyParams: {
                session_cookie: session_cookie,
                intervention_type: INTERVENTION_TYPE_NAMES.DISCOUNT_NUDGE,
                page_view_id: pageViewId,
                dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
                metadata: JSON.stringify({
                    page: window.location.href,
                    ui_version: front_end_ui_name,
                })
            },
        }).catch((err) => {
            throw new Error(err.message);
        });
        if (action == "dismissed") {
            fetch(`${$apiUrl}/record_popup_dismissed`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                body: new URLSearchParams({
                    session_cookie: session_cookie,
                    page_view_id: pageViewId,
                    dwell_time: get_dwell_time().toString(),
                    is_backfill_call: "true"
                })
            });
        }
        if (use_meta_ad_pixel && vandra_fire_meta_pixel) {
            const eventName = `VandraPopup${action.charAt(0).toUpperCase() + action.slice(1)}`;
            vandra_fire_meta_pixel(eventName, {
                'customer_cookie': customer_cookie,
                'session_cookie': session_cookie,
                'discount_applied': discount_applied,
                'discount_code': discount_code,
                'discount_offer': discount_rate,
                'discount_expiration': unix_to_gmt_time(discount_ends_at_time)
            })
        }
    }
    async function recordNudgeCTAClicked () {
        if (isPreview) return;
        const res = await buildRequest({
            url,
            path: "/base_nudge/record/cta",
            method: "POST",
            queryParams: undefined,
            bodyParams: {
                session_cookie: session_cookie,
                intervention_type: INTERVENTION_TYPE_NAMES.DISCOUNT_NUDGE,
                page_view_id: pageViewId,
                dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
                metadata: JSON.stringify({
                    page: window.location.href,
                    ui_version: front_end_ui_name,
                })
            },
        }) as any;
        if (res.errors) {
            throw new Error(res.errors.message);
        }
        /* Apply discount through Shopify */
        fetch(`/discount/${discount_code}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            }
        });

        
        /* Record discount application to Vandra server */
        fetch(`${url}/record_discount_applied`, {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            },
            body: new URLSearchParams({
                session_cookie: session_cookie,
                page_view_id: pageViewId,
                dwell_time: get_dwell_time().toString()
            })
        });

        if (use_meta_ad_pixel && vandra_fire_meta_pixel) {
            vandra_fire_meta_pixel('VandraPopupApplied', {
                'customer_cookie': customer_cookie,
                'session_cookie': session_cookie,
                'discount_applied': discount_applied,
                'discount_code': discount_code,
                'discount_offer': discount_rate,
                'discount_expiration': unix_to_gmt_time(discount_ends_at_time)
            })
        }
    }
    

</script>
{#if show}
    {#if widget_type != "RETURN"}
        {#if [UI_VERSION_NAMES.DEFAULT_RIGHT, UI_VERSION_NAMES.NUDGE_DELAY, UI_VERSION_NAMES.NUDGE_DELAY_V2, UI_VERSION_NAMES.ACTION_BASED_SHOW].includes(front_end_ui_name)}
            <Default recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} tabDisabled={tabDisabled} {...parameters}/>
        {/if}
        {#if front_end_ui_name === UI_VERSION_NAMES.DEFAULT_LEFT}
            <Default recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} positioning="Left" tabDisabled={tabDisabled} {...parameters}/>
        {/if}
        {#if front_end_ui_name === UI_VERSION_NAMES.DEFAULT_RIGHT_NEW}
            <Default recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} type="new" tabDisabled={tabDisabled} {...parameters}/>
        {/if}
        {#if front_end_ui_name === UI_VERSION_NAMES.NO_X}
            <Default recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} no_X tabDisabled={tabDisabled} {...parameters}/>
        {/if}
        {#if front_end_ui_name === UI_VERSION_NAMES.IMAGE}
            <Default recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} type="image" tabDisabled={tabDisabled} {...parameters}/>
        {/if}
        {#if front_end_ui_name === UI_VERSION_NAMES.COUNTDOWN}
            <Default recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} type="default" countdown tabDisabled={tabDisabled} {...parameters}/>
        {/if}
        {#if front_end_ui_name === UI_VERSION_NAMES.MODAL}
            <Modal recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} {...parameters}/>
        {/if}
        {#if front_end_ui_name === UI_VERSION_NAMES.AUTO_APPLY}
            <Auto recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} {...parameters}/>
        {/if}
    {/if}
    {#if widget_type === "RETURN"}
        <Reminder recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} showNudge={showNudge} {...parameters}/>
        <!-- <Reminder recordAction={recordAction} recordNudgeCTAClicked={recordNudgeCTAClicked} {...parameters} withExpires/> -->
    {/if}
{/if}