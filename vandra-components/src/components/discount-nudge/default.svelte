<script lang="ts">
    import { onMount } from "svelte";
    import { buildRequest } from "../../lib/api/utils";
    import closeIcon from "../../assets/closeIcon.svg";
    import { Button } from "../../lib/components/ui/button";
    import { apiUrl, store_total_savings, store_savings_threshold, store_savings_show, store_savings_mounted } from "../../lib/store";
    import { vandraGetCookie, vandraSetCookie, formatPrice } from "$lib/utils";
    import { INTERVENTION_TYPE_NAMES } from "../../constants";
    import Coupon from "../../assets/coupon.svg"
    import ApplyButton from "./applyButton.svelte";

    export let isPreview: boolean = false;
    export let tabFontColor = "#FFFFFF";
    export let tabDisabled = false;
    export let tabText = "Get {discount}% off now";
    export let primaryColor = "#0000AA";
    export let tabColor = "#0000AA";
    export let backgroundColor = "#FFFFFF";
    export let font = "Inter";
    export let expandedHeadline = "Get {discount}% off now";
    export let expandedBody = "Valid for one day only. Don't miss out!";
    export let buttonText = "Apply Discount";
    export let successButtonText = "Applied";
    export let positioning = "Right"; // or Left
    export let anchor = "Top"; // or Bottom
    export let distanceFromAnchor = 15;
    export let popup_image_url = '';

    export let discount_rate = 0;
    export let discount_code = '';
    export let discount_ends_at_time = 0;
    export let no_X = false;
    export let countdown = false;
    export let type : "default" | "new" | "image" = "default";

    export let recordAction = (action : "shown" | "minimized" | "expanded" | "dismissed") => {}
    export let recordNudgeCTAClicked = () => {}
    export let showNudge = (e: boolean) => {}

    export let session_cookie;
    export let widget_type;

    let minimized: boolean;
    $: minimized = widget_type == "MINIMIZED";
    let hiding = false;
    let copied = false;
    let remainingTime = 0;
    let h = 0, m = 0, s = 0;

    let applyButtonParams;
    $: applyButtonParams = {
        font,
        tabColor,
        tabFontColor,
        buttonText,
        successButtonText,
        onClick: recordNudgeCTAClicked,
        postClick: hide
    }
    onMount(() => {
        if (countdown) {
            const now = new Date().getTime();
            const midnight = new Date(new Date().setHours(24, 0, 0, 0)).getTime();
            let time = midnight - now;
            let deadline = midnight;
            if(time <= 3600000) {
                time = 3600000;
                deadline = now + time;
            }
            if (discount_ends_at_time) deadline = discount_ends_at_time * 1000
            const interval = setInterval(() => {
                remainingTime = deadline - Date.now();
                h = Math.floor(remainingTime / 3600000);
                m = Math.floor((remainingTime % 3600000) / 60000);
                s = Math.floor((remainingTime % 60000) / 1000);
                if (remainingTime <= 1000) {
                    clearInterval(interval);
                    hide();
                }
            }, 1000);
            fetch(`${apiUrl}/record_countdown_deadline`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                body: new URLSearchParams({
                    session_cookie: session_cookie,
                    deadline: deadline.toString()
                })
            });
        }
        store_savings_show.subscribe(value => {
            if (value && $store_savings_mounted && minimized) {
                hide();
            }
        });
        store_savings_mounted.subscribe(value => {
            if (value && $store_savings_show && minimized) {
                hide();
            }
        })
    })

    const toggleVisibility = () => {
        hiding = true;
        setTimeout(() => {
            minimized = !minimized;
            if (minimized) {
                recordAction("dismissed");
            } else {
                recordAction("expanded");
            }
            if (minimized) {
                store_savings_show.set(true);
                if ($store_savings_mounted) {
                    showNudge(false);
                    return;
                }
            }
            setTimeout(() => {
                hiding = false;
            }, 200);
        }, 500);
    }
    const hide = () => {
        if (isPreview) return;
        hiding = true;
        setTimeout(() => {
            showNudge(false);
        }, 500);
    }
    const onCopyClicked = () => {
        navigator.clipboard.writeText(discount_code);
        copied = true;
    }
</script>

<div class="nudge_container va-fixed va-z-max {positioning == "Left" ? "va-left-0 slide_to_right_animation" : "va-right-0 slide_to_left_animation"}"
    style={`font-family: ${font}, Arial; ${anchor == "Bottom" ? 'bottom' : 'top'}: clamp(50px, ${distanceFromAnchor}vh, calc(100vh - 200px));`}
>
    <div class="va-flex va-p-2 va-justify-center va-items-end {type == "image" ? "va-w-[420px] va-h-[480px]": "va-w-[360px]"} {positioning == "Left" ? "va-ml-[10px]" : "va-mr-[10px]"} va-border-[#dfd9fd] va-rounded-[10px] va-shadow-[0px_2.5px_15px_rgba(0,0,0,0.12)] va-transition va-duration-500" 
        style="{type == "image" ? `background-image: url(${popup_image_url})`:`background-color: ${backgroundColor}`}; {minimized ? 'display: none;' : ''} {hiding ? `transform:translateX(${positioning == "Left" ? "-" : ""}110%); ` : 'transform:translateX(0px);'}"
    >
        {#if !no_X}
        <div class="va-absolute va-top-4 va-right-4">
            <Button class="va-p-1 va-bg-transparent va-border-0 va-cursor-pointer" variant="link" on:click={toggleVisibility}>
                <img class="{type == "image" ? "va-mix-blend-plus-lighter": ""}" src={`${closeIcon}`} alt="closebuttonIcon" />
            </Button>
        </div>
        {/if}
        {#if type == "default"}
            <div class="va-p-[10px] va-w-full va-flex va-flex-col va-justify-center va-items-start">
                {#if countdown}
                    <span class="va-text-base va-font-bold va-mb-2" style="color: {tabColor};">
                        {h.toString().padStart(2, '0')}:{m.toString().padStart(2, '0')}:{s.toString().padStart(2, '0')} LEFT
                    </span>
                {/if}
                <div class="va-w-full va-pr-4 va-text-2xl va-leading-[22px] va-font-bold va-uppercase [word-break:break-word]">
                    {expandedHeadline.replaceAll('{discount}', discount_rate.toString())}
                </div>
                <div class="va-w-full va-mb-5 va-text-base va-leading-[17px] va-font-normal va-mt-2 [word-break:break-word]">
                    {expandedBody}
                </div>
                <div class="va-flex va-content-start va-items-center va-gap-2 va-border-2 va-rounded-[4px] va-w-full va-border-dotted va-border-[#777777] va-p-2">
                    <img src={`${Coupon}`} alt="CouponbuttonIcon" />
                    <span class=" va-text-xs">{discount_code}</span>
                    <!-- svelte-ignore a11y_click_events_have_key_events, a11y_no_static_element_interactions (because of reasons) -->
                    <span class="va-ml-auto va-underline va-cursor-pointer va-text-base va-font-normal" on:click={onCopyClicked}>{copied ? "copied" : "copy"}</span>
                </div>
                <ApplyButton {...applyButtonParams} />
            </div>
        {/if}
        {#if type == "new"}
            <div class="va-py-3 va-pr-5 va-flex va-border-2 va-border-dashed va-items-center" style={`color: ${primaryColor}; border-color: ${tabColor}`}>
                <div class="va-flex va-justify-center va-items-center va-w-[82px] va-h-[82px] va-mx-5 va-rounded-full" style="background-color: {tabColor};">
                    <span class="va-font-bold va-text-xl va-text-center" style="color: {tabFontColor};">{discount_rate}% OFF</span>
                </div>
                <div class="va-flex va-flex-col va-justify-center va-items-start">
                    <div class="va-w-full va-text-lg va-leading-[22px] va-font-bold va-uppercase [word-break:break-word]">
                        {expandedHeadline.replaceAll('{discount_rate}', discount_rate.toString())}
                    </div>
                    <div class="va-w-full va-mb-2 va-text-xs va-font-normal va-mt-1 [word-break:break-word]">
                        {expandedBody}
                    </div>
                    <ApplyButton {...applyButtonParams} />
                </div>
            </div>
        {/if}
        {#if type == "image"}
            <div class="va-p-[10px] va-flex va-flex-col va-justify-center va-items-start va-mb-0" style={`color: ${primaryColor};`}>
                <div class="va-w-full va-text-2xl va-text-center va-leading-[22px] va-font-bold va-uppercase [word-break:break-word]">
                    {expandedHeadline.replaceAll('{discount}', discount_rate.toString())}
                </div>
                <div class="va-w-full va-mb-5 va-text-base va-text-center va-leading-[17px] va-font-normal va-mt-2 [word-break:break-word]">
                    {expandedBody}
                </div>
                <div class="va-flex va-content-start va-items-center va-gap-2 va-border-2 va-rounded-[4px] va-w-full va-border-dotted va-border-[#777777] va-p-2">
                    <img src={`${Coupon}`} alt="closebuttonIcon" />
                    <span class=" va-text-xs">{discount_code}</span>
                    <!-- svelte-ignore a11y_click_events_have_key_events, a11y_no_static_element_interactions (because of reasons) -->
                    <span class="va-ml-auto va-underline va-cursor-pointer va-text-base va-font-normal" on:click={onCopyClicked}>{copied ? "copied" : "copy"}</span>
                </div>
                <ApplyButton {...applyButtonParams} />
            </div>
        {/if}
    </div>
    {#if !tabDisabled}
        <button class={`va-flex va-gap-2 va-items-center va-w-[30px] va-py-4 va-rounded-r-[10px] va-border-none va-text-center va-text-sm va-font-bold [writing-mode:vertical-lr] va-cursor-pointer va-transition va-duration-500 va-uppercase`}
    style="background-color: {tabColor}; color: {tabFontColor} !important; {minimized ? 'display: flex;' : 'display: none;'} 
    {hiding ? `transform:${positioning == "Left" ? "translateX(-100%)" : "translateX(100%) rotate(180deg)"}` : `transform:translateX(0px) ${positioning == "Left" ? "" : "rotate(180deg)"}`}" on:click={toggleVisibility}>
            {tabText.replaceAll('{discount}', discount_rate.toString())}
        </button>
    {/if}
</div>


<style>
</style>