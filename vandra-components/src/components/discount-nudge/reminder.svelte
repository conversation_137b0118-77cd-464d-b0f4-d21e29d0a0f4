<script lang="ts">
    import { onMount } from "svelte";
    import { buildRequest } from "../../lib/api/utils";
    import closeIcon from "../../assets/closeIcon.svg";
    import { Button } from "../../lib/components/ui/button";

    export let tabColor = "#0000AA";
    export let tabFontColor = "#FFFFFF";
    export let primaryColor = "#232323";
    export let backgroundColor = "#FFFFFF";
    export let font = "Inter";
    export let expandedHeadlineReturn = "Reminder: You get {discount}% off your total order!";
    export let expandedBodyReturn = "Your discount code is already applied to your cart!";
    export let buttonTextReturn = "Proceed to Checkout";
    export let positioning = "Right"; // or Left
    export let anchor = "Top"; // or Bottom
    export let distanceFromAnchor = 15;

    export let discount_rate = 0;
    // svelte-ignore export_let_unused
    export let discount_code = '';
    export let discount_ends_at_time = 0;
    export let withExpires = false;

    export let recordAction = (action : "shown" | "minimized" | "expanded" | "dismissed") => {}
    export let recordNudgeCTAClicked = async () => {}
    export let showNudge = (e: boolean) => {}

    let hiding = false;

    onMount(() => {
        if (withExpires) {
            let formattedExpiryDate = undefined;
            let expiryMessage = undefined;
            if(discount_ends_at_time) {
                let expiryDate = new Date(discount_ends_at_time * 1000);
                formattedExpiryDate = (expiryDate.getMonth() + 1) +
                    '/' + expiryDate.getDate() +
                    '/' + expiryDate.getFullYear() +
                    ' at ' + expiryDate.toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
            }
            if(formattedExpiryDate) {
                expiryMessage = "Your unique discount code expires on " + formattedExpiryDate;
            } else {
                expiryMessage = "Your unique discount code expires soon!";
            }
            expandedHeadlineReturn = "EXPIRES SOON !!";
            expandedBodyReturn = expiryMessage;
        }
    })
    const dismiss = () => {
        recordAction("dismissed");
        hide();
    }
    const hide = () => {
        hiding = true;
        setTimeout(() => {
            showNudge(false);
        }, 500);
    }
    async function handleCheckoutClicked() {
        await recordNudgeCTAClicked();
        window.location.href = '/checkout';

    }
</script>

<div class="nudge_container va-fixed va-z-max {positioning == "Left" ? "va-left-0 slide_to_right_animation" : "va-right-0 slide_to_left_animation"}"
    style={`font-family: ${font}, Arial; ${anchor == "Bottom" ? 'bottom' : 'top'}: clamp(50px, ${distanceFromAnchor}vh, calc(100vh - 200px));`}
>
    <div class="va-flex va-p-2 va-justify-center va-items-end va-w-[360px] {positioning == "Left" ? "va-ml-[10px]" : "va-mr-[10px]"} va-border-[#dfd9fd] va-rounded-[10px] va-shadow-[0px_2.5px_15px_rgba(0,0,0,0.12)] va-transition va-duration-500" 
        style="background-color: {backgroundColor}; {hiding ? `transform:translateX(${positioning == "Left" ? "-" : ""}110%); ` : 'transform:translateX(0px);'}"
    >
        <div class="va-absolute va-top-4 va-right-4">
            <Button class="va-p-1 va-bg-transparent va-border-0 va-cursor-pointer" variant="link" on:click={dismiss}>
                <img src={`${closeIcon}`} alt="closebuttonIcon" />
            </Button>
        </div>
        <div class="va-p-[10px] va-flex va-flex-col va-justify-center va-items-start" style={`color: ${primaryColor};`}>
            <div class="va-pr-5 va-text-2xl va-leading-[26px] va-font-bold va-uppercase">
                {expandedHeadlineReturn.replaceAll('{discount}', discount_rate.toString())}
            </div>
            <div class="va-w-full va-text-base va-leading-[17px] va-font-normal va-mt-1">
                {expandedBodyReturn}
            </div>
            <button class="va-flex va-justify-center va-items-center va-mt-3 va-w-full va-h-11 va-border-none va-text-lg va-font-semibold va-cursor-pointer va-rounded-[4px] va-no-underline" style={`background-color: ${tabColor}; color: ${tabFontColor};`} on:click={handleCheckoutClicked} >
                {buttonTextReturn}
            </button>
        </div>
    </div>
</div>


<style>
</style>