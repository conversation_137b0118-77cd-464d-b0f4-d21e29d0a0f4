<script lang="ts">
    import { onMount } from "svelte";
    import closeIcon from "../../assets/closeIcon.svg";
    import { Button } from "../../lib/components/ui/button";
    import { store_savings_show } from "$lib/store";

    export let tabColor = "#0000AA";
    export let tabFontColor = "#FFFFFF";
    export let primaryColor = "#232323";
    export let backgroundColor = "#FFFFFF";
    export let font = "Inter";
    export let expandedHeadlineAuto = "{discount}% OFF, ON US!\n IT'S ALREADY IN YOUR CART";
    export let expandedBody = "A discount is waiting in your cart. It's only good for today so don't miss out.";
    export let positioning = "Right"; // or Left
    export let anchor = "Top"; // or Bottom
    export let distanceFromAnchor = 15;

    export let discount_rate = 0;
    // svelte-ignore export_let_unused
    export let discount_code = '';
    
    export let recordAction = (action : "shown" | "minimized" | "expanded" | "dismissed") => {}
    export let recordNudgeCTAClicked = () => {}
    export let showNudge = (e: boolean) => {}

    let hiding = false;

    onMount(() => {
        recordNudgeCTAClicked();
    })

    const dismiss = () => {
        recordAction("dismissed");
        hide();
    }
    const hide = () => {
        hiding = true;
        setTimeout(() => {
            showNudge(false);
        }, 500);
    }
</script>

<div class="nudge_container va-fixed va-z-max {positioning == "Left" ? "va-left-0 slide_to_right_animation" : "va-right-0 slide_to_left_animation"}"
    style={`font-family: ${font}, Arial; ${anchor == "Bottom" ? 'bottom' : 'top'}: clamp(50px, ${distanceFromAnchor}vh, calc(100vh - 200px));`}
>
    <div class="va-flex va-p-2 va-justify-center va-items-end va-w-[400px] {positioning == "Left" ? "va-ml-[10px]" : "va-mr-[10px]"} va-border-[#dfd9fd] va-rounded-[10px] va-shadow-[0px_2.5px_15px_rgba(0,0,0,0.12)] va-transition va-duration-500" 
        style="background-color: {backgroundColor}; {hiding ? `transform:translateX(${positioning == "Left" ? "-" : ""}110%); ` : 'transform:translateX(0px);'}"
    >
        <div class="va-absolute va-top-4 va-right-4">
            <Button class="va-p-1 va-bg-transparent va-border-0 va-cursor-pointer" variant="link" on:click={dismiss}>
                <img src={`${closeIcon}`} alt="closebuttonIcon" />
            </Button>
        </div>
        <div class="va-py-3 va-pr-5 va-flex va-border-2 va-border-dashed va-items-center" style={`color: ${primaryColor}; border-color: ${tabColor}`}>
            <div class="va-flex va-justify-center va-items-center va-w-[136px] va-h-[82px] va-mx-5 va-rounded-full" style="background-color: {tabColor};">
                <span class="va-font-bold va-text-xl va-text-center" style="color: {tabFontColor};">{discount_rate}% OFF</span>
            </div>
            <div class="va-flex va-flex-col va-justify-center va-items-start">
                <div class=" va-pr-3 va-text-lg va-leading-[22px] va-font-bold va-uppercase va-whitespace-pre-line">
                    {expandedHeadlineAuto.replaceAll('{discount}', discount_rate.toString())}
                </div>
                <div class="va-w-full va-mb-2 va-text-xs va-font-normal va-mt-1">
                    {expandedBody}
                </div>
            </div>
        </div>
    </div>
</div>


<style>
</style>