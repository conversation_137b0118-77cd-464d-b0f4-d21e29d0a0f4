<script>
    import { onMount } from "svelte";

    export let right;
    export let animate = true;

    let animationClass = "";

    onMount(() => {
        if (right && animate) {
            animationClass = "slide_in_right_to_left_animation";
        }
    });
</script>

{#if right}
    <div
        class={`va-fixed va-top-[12%] lg:va-top-[16%] va-z-[999999] va-h-auto va-w-auto va-shadow-none ${animate ? 'va-right-[10px] va-transform va-translate-x-[100%]' : 'va-left-1/2 va-transform -va-translate-x-1/2'} ${animationClass}`}
    >
        <slot></slot>
    </div>
{/if}

<style>
    @keyframes slideInRightToLeft {
        0% {
            transform: translateX(100%); /* Start off-screen */
        }
        100% {
            transform: translateX(0); /* End at the original position */
        }
    }

    .slide_in_right_to_left_animation {
        animation: slideInRightToLeft 0.3s ease-in-out forwards; /* Define the animation */
    }
</style>
