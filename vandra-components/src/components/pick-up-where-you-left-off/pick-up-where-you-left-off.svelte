<script lang="ts">
  import { apiUrl } from "../../lib/store";
  import { onMount } from "svelte";
  import closeIcon from "../../assets/closeIcon.svg";
  import { buildRequest } from "../../lib/api/utils";
  import { Button } from "../../lib/components/ui/button";
  import { vandraGetCookie, formatPrice } from "$lib/utils";
  import type { PickUpWhereYouLeftOffItem } from "../../lib/types";
  import { INTERVENTION_TYPE_NAMES } from "../../constants";

  let show = true;
  export let primaryColor: string;
  export let backgroundColor: string;
  export let itemBackgroundColor: string;
  export let fontName: string;
  export let headlineLine1: string = "Welcome back!";
  export let headlineLine2: string = "Pick up where you left off:";
  export let isPreview: boolean = false;
  export let animate: boolean = true;

  // Compute animation class based on animate prop
  $: animationClass = animate ? "va-animate-slide-up" : "";
  
  // Compute shadow style based on isPreview prop
  $: shadowStyle = isPreview
    ? "filter: drop-shadow(1px 1px 5px rgba(0, 0, 0, 0.25));"
    : "filter: drop-shadow(10px 10px 50px rgba(0, 0, 0, 0.25));";

  // Compute position style based on isPreview prop
  $: positionStyle = isPreview && !animate
    ? "position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
    : "position: fixed; bottom: 0; left: 4;";

  // Compute positioning class based on screen size
  $: responsiveWidthClass = isPreview
    ? "va-w-full md:va-w-[318px] md:va-left-4"
    : "va-w-full md:va-w-[318px] md:va-left-4";

  export let items: PickUpWhereYouLeftOffItem[] = [];

  // Subscribe to the apiUrl store
  let url = "";
  $: url = $apiUrl;

  let session_cookie = "";
  export let dwellTimeStartCounter: number;
  export let pageViewId: string;

  // Export function to update props without remounting
  export function updateProps(newProps: Record<string, any>) {
    if (newProps.primaryColor !== undefined) primaryColor = newProps.primaryColor;
    if (newProps.backgroundColor !== undefined) backgroundColor = newProps.backgroundColor;
    if (newProps.itemBackgroundColor !== undefined) itemBackgroundColor = newProps.itemBackgroundColor;
    if (newProps.fontName !== undefined) fontName = newProps.fontName;
    if (newProps.headlineLine1 !== undefined) headlineLine1 = newProps.headlineLine1;
    if (newProps.headlineLine2 !== undefined) headlineLine2 = newProps.headlineLine2;
    if (newProps.isPreview !== undefined) isPreview = newProps.isPreview;
    if (newProps.animate !== undefined) animate = newProps.animate;
    if (newProps.items !== undefined) items = newProps.items;
    if (newProps.dwellTimeStartCounter !== undefined) dwellTimeStartCounter = newProps.dwellTimeStartCounter;
    if (newProps.pageViewId !== undefined) pageViewId = newProps.pageViewId;
  }

  function getFontStyle() {
    return fontName ? `font-family: ${fontName} !important;` : "";
  }

  function toMetadataItem(item: PickUpWhereYouLeftOffItem) {
    return {
      productId: item.productId,
      variantId: item.variantId,
      url: item.url,
      title: item.title,
      price: item.price,
      priceCurrency: item.priceCurrency,
    };
  }

  function getMetadata(selectedItem?: PickUpWhereYouLeftOffItem) {
    return {
      selectedItem: selectedItem ? toMetadataItem(selectedItem) : undefined,
      items: items.map((item) => toMetadataItem(item)),
    };
  }

  onMount(() => {
    session_cookie = vandraGetCookie("vandra_session_cookie") || "";

    // Skip API calls when in preview mode
    if (!isPreview) {
      buildRequest({
        url,
        path: "/base_nudge/record/shown",
        method: "POST",
        queryParams: undefined,
        bodyParams: {
          session_cookie,
          intervention_type: INTERVENTION_TYPE_NAMES.PICK_UP_WHERE_YOU_LEFT_OFF,
          metadata: JSON.stringify(getMetadata()),
          page_view_id: pageViewId,
          dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
        },
      }).catch((err: Error) => {
        throw new Error(err.message);
      });
    }
  });

  const recordPickUpWhereYouLeftOffClick = async ({
    url,
    path,
    bodyParams,
    item,
  }: {
    url: string;
    path: string;
    bodyParams: Record<string, unknown>;
    item: PickUpWhereYouLeftOffItem;
  }) => {
    // Skip API calls when in preview mode
    if (isPreview) return;

    const res = (await buildRequest({
      url,
      path,
      method: "POST",
      queryParams: undefined,
      bodyParams,
    })) as any;
    if (res.errors) {
      throw new Error(res.errors.message);
    }
    window.location.href = item.url;
  };

  const recordPickUpWhereYouLeftOffDismissed = async ({
    url,
    path,
    bodyParams,
  }: {
    url: string;
    path: string;
    bodyParams: Record<string, unknown>;
  }) => {
    // Skip API calls when in preview mode
    if (isPreview) return;

    const res = (await buildRequest({
      url,
      path,
      method: "POST",
      queryParams: undefined,
      bodyParams,
    })) as any;
    if (res.errors) {
      throw new Error(res.errors.message);
    }
  };
</script>

{#if show}
  <div
    class={`${responsiveWidthClass} ${animationClass} va-z-max`}
    style={`${positionStyle} ${shadowStyle}`}
  >
    <div
      class="va-flex va-flex-col va-p-8 va-rounded-t-[30px] va-rounded-b-none"
      style={`background-color: ${backgroundColor} !important; ${getFontStyle()}`}
    >
      <div
        class="va-flex va-flex-col va-items-center"
      >
        <div class="va-flex va-flex-row va-w-full va-justify-end">
          <Button
            variant="link"
            on:click={() => {
              recordPickUpWhereYouLeftOffDismissed({
                url,
                path: "/base_nudge/record/dismissed",
                bodyParams: {
                  session_cookie,
                  intervention_type:
                    INTERVENTION_TYPE_NAMES.PICK_UP_WHERE_YOU_LEFT_OFF,
                  metadata: JSON.stringify(getMetadata()),
                  page_view_id: pageViewId,
                  dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
                },
              });
              show = false;
            }}
            class="va-p-0 va-h-fit va-border-none p-0 va-bg-transparent va-cursor-pointer"
            disabled={isPreview}
          >
            <img
              src={`${closeIcon}`}
              alt="closebuttonIcon"
              class="va-w-4 va-m-0"
            />
          </Button>
        </div>
        <div
          class="va-font-bold va-text-xl va-m-0 va-p-0 va-mb-2 va-text-center va-overflow-hidden va-max-w-full"
          style={`color: ${primaryColor} !important; ${getFontStyle()}`}
        >
          {headlineLine1} <br />
          {headlineLine2}
        </div>
        <div
          class="va-flex va-flex-col va-items-center va-w-full va-h-full va-pt-4 sm:va-pt-6 va-gap-6"
        >
          {#each items as item}
            <button
              type="button"
              class="va-flex va-flex-row va-items-center va-gap-4 va-w-full va-bg-white va-rounded-[40px] va-border-none va-p-0 va-cursor-pointer"
              style="background-color: {itemBackgroundColor} !important;"
              on:click={() => {
                recordPickUpWhereYouLeftOffClick({
                  url,
                  path: "/base_nudge/record/cta",
                  bodyParams: {
                    session_cookie,
                    intervention_type:
                      INTERVENTION_TYPE_NAMES.PICK_UP_WHERE_YOU_LEFT_OFF,
                    metadata: JSON.stringify(getMetadata(item)),
                    page_view_id: pageViewId,
                    dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
                  },
                  item: item,
                });
              }}
            >
              <div class="va-w-[70px] va-h-[70px] va-flex-shrink-0">
                <img
                  src={item.image}
                  alt="product"
                  class="va-w-full va-h-full va-object-contain"
                />
              </div>
              <div class="va-flex va-flex-col va-items-start va-gap-2">
                <p
                  class="va-font-bold va-text-base va-p-0 va-m-0 va-text-left va-line-clamp-2 va-overflow-hidden"
                  style={`color: ${primaryColor} !important; ${getFontStyle()}`}
                  title={item.title}
                >
                  {item.title}
                </p>
                <p
                  class="va-font-medium va-text-base va-p-0 va-m-0"
                  style={`color: ${primaryColor} !important; ${getFontStyle()}`}
                >
                  {formatPrice(item.price, item.priceCurrency)}
                </p>
              </div>
            </button>
          {/each}
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  @keyframes slideUp {
    0% {
      transform: translateY(100%);
    }
    100% {
      transform: translateY(0);
    }
  }

  /* Animation class that's applied conditionally */
  .va-animate-slide-up {
    animation: slideUp 0.3s ease-out forwards !important;
  }
</style>
