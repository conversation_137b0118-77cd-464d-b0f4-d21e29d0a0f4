<script lang="ts">
    import closeIcon from "../../assets/closeIcon.svg";
    import { Button } from "../../lib/components/ui/button";

    import { apiUrl } from "../../lib/store";
    import { onMount } from "svelte";
    import { buildRequest } from "../../lib/api/utils";
    import { vandraGetCookie } from "$lib/utils";
    import { INTERVENTION_TYPE_NAMES } from "../../constants";

    export let headlineColor = "#358E7F";
    export let bodyTextColor = "#000000";
    export let backgroundColor = "#FFFFFF";
    export let itemBackgroundColor = "#EDEEF0";
    export let font = "Inter";

    export let headline = ""; // e.g. "Which furry friend are you shopping for?";
    export let subheader = ""; // e.g. "We'll take you to the right spot!";
    export let answerOptions = [] as any;
    export let timeDelay = 5;

    export let pageViewId: string;
    export let isPreview: boolean = false;
    export let animate: boolean = true;
    export let holdout: boolean = false;
    export let suppress_handler;

    // Compute animation class based on animate prop
    $: animationClass = animate ? "slide_to_top_animation" : "";
    
    // Compute translate class based on preview and animation settings
    $: translateClass = (isPreview && !animate) ? "" : "va-translate-y-[100%]";

    // Compute position class based on preview and animation settings
    $: positionClass = (isPreview && !animate) 
        ? "va-fixed va-inset-0 va-m-auto va-flex va-items-center va-justify-center va-z-max" 
        : "va-fixed va-bottom-0 va-left-0 md:va-left-4 va-z-max";

    // Compute width class based on preview and animation settings
    $: widthClass = (isPreview && !animate)
        ? "va-w-[318px]"
        : "va-w-full md:va-w-[318px]";

    // Compute close button position class based on preview and animation settings
    $: closeButtonClass = (isPreview && !animate)
        ? "va-absolute va-top-3 va-right-5 va-z-[60]"
        : "va-absolute va-top-5 va-right-6";

    // Also adjust the container's border radius for preview mode
    $: containerClass = (isPreview && !animate)
        ? "va-w-[318px] va-border va-rounded-[20px] va-shadow-[10px_10px_50px_rgba(0,0,0,0.25)] va-pt-11 va-pb-5 va-px-6 va-relative"
        : "va-border va-rounded-t-[30px] va-shadow-[10px_10px_50px_rgba(0,0,0,0.25)] va-pt-11 va-pb-5 va-px-6 va-relative";

    export let show = false;
    let session_cookie = "";
    let dwellTimeStartCounter = Date.now();
      // Subscribe to the apiUrl store
    let url = "";
    $: $apiUrl, (url = $apiUrl);

    // Export function to update props without remounting
    export function updateProps(newProps: Record<string, any>) {
        if (newProps.headlineColor !== undefined) headlineColor = newProps.headlineColor;
        if (newProps.bodyTextColor !== undefined) bodyTextColor = newProps.bodyTextColor;
        if (newProps.backgroundColor !== undefined) backgroundColor = newProps.backgroundColor;
        if (newProps.itemBackgroundColor !== undefined) itemBackgroundColor = newProps.itemBackgroundColor;
        if (newProps.font !== undefined) font = newProps.font;
        if (newProps.headline !== undefined) headline = newProps.headline;
        if (newProps.subheader !== undefined) subheader = newProps.subheader;
        if (newProps.answerOptions !== undefined) {
            answerOptions = [...newProps.answerOptions];
        }
        if (newProps.timeDelay !== undefined) timeDelay = newProps.timeDelay;
        if (newProps.pageViewId !== undefined) pageViewId = newProps.pageViewId;
        if (newProps.isPreview !== undefined) isPreview = newProps.isPreview;
        if (newProps.animate !== undefined) animate = newProps.animate;
        if (newProps.show !== undefined) show = newProps.show;
    }

    onMount(() => {
        session_cookie = vandraGetCookie("vandra_session_cookie") || "";
        
        // Skip API calls and parameter fetching when in preview mode
        if (isPreview) {
            show = true;
            return;
        }
        
        buildRequest({
            url,
            path: "/base_nudge/parameters",
            method: "GET",
            queryParams: {
                session_cookie: session_cookie,
                intervention_type_name: INTERVENTION_TYPE_NAMES.NAVIGATIONAL_NUDGE,
            },
        }).then((res : any) => {
            if (res.errors) {
                throw new Error(res.errors.message);
            }
            res.data.data.parameters.headlineColor && (headlineColor = res.data.data.parameters.headlineColor);
            res.data.data.parameters.bodyTextColor && (bodyTextColor = res.data.data.parameters.bodyTextColor);
            res.data.data.parameters.backgroundColor && (backgroundColor = res.data.data.parameters.backgroundColor);
            res.data.data.parameters.itemBackgroundColor && (itemBackgroundColor = res.data.data.parameters.itemBackgroundColor);
            res.data.data.parameters.font && (font = res.data.data.parameters.font);
            res.data.data.parameters.headline && (headline = res.data.data.parameters.headline);
            res.data.data.parameters.subheader && (subheader = res.data.data.parameters.subheader);
            res.data.data.parameters.answerOptions && (answerOptions = res.data.data.parameters.answerOptions);
            res.data.data.parameters.timeDelay && (timeDelay = res.data.data.parameters.timeDelay);
            
            return new Promise(resolve => setTimeout(resolve, timeDelay * 1000))
        }).then(() => {
            if (holdout === true) {
                suppress_handler();
                return;
            }
            show = true;
            dwellTimeStartCounter = Date.now();
            
            // Record shown event
            buildRequest({
                url,
                path: "/base_nudge/record/shown",
                method: "POST",
                queryParams: undefined,
                bodyParams: {
                    session_cookie: session_cookie,
                    intervention_type: INTERVENTION_TYPE_NAMES.NAVIGATIONAL_NUDGE,
                    page_view_id: pageViewId,
                    metadata: JSON.stringify({
                        items: answerOptions
                    })
                },
            }).catch((err) => {
                throw new Error(err.message);
            });
        }).catch((err) => {
            throw new Error(err.message);
        });
    });

    const recordNudgeDismissed = async ({ url, path, bodyParams } : {
        url: string;
        path: string;
        bodyParams: Record<string, unknown>;
    }) => {
        // Skip API calls when in preview mode
        if (isPreview) return;
        
        const res = await buildRequest({
            url,
            path,
            method: "POST",
            queryParams: undefined,
            bodyParams,
        }) as any;
        if (res.errors) {
            throw new Error(res.errors.message);
        }
    };
    const recordNudgeCTAClicked = async ({ url, path, bodyParams } : {
        url: string;
        path: string;
        bodyParams: Record<string, unknown>;
    }) => {
        // Skip API calls when in preview mode
        if (isPreview) return;
        
        const res = await buildRequest({
            url,
            path,
            method: "POST",
            queryParams: undefined,
            bodyParams,
        }) as any;
        if (res.errors) {
            throw new Error(res.errors.message);
        }
    };
</script>

{#if show}
    <div class={`${positionClass} ${widthClass} va-transform ${translateClass} va-h-auto va-shadow-none ${animationClass}`}>
        <div
            class={containerClass}
            style="background-color: {backgroundColor}; font-family: {font}, serif;"
        >
            <div class={closeButtonClass}>
                <Button class="va-p-1 va-bg-transparent va-border-0 va-cursor-pointer" variant="link" on:click={() => {
                recordNudgeDismissed({
                    url,
                    path: "/base_nudge/record/dismissed",
                    bodyParams: {
                        session_cookie: session_cookie,
                        intervention_type: INTERVENTION_TYPE_NAMES.NAVIGATIONAL_NUDGE,
                        page_view_id: pageViewId,
                        dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
                        metadata: JSON.stringify({
                            items: answerOptions
                        })
                    },
                  });

                  if (animate) {
                    show = false;
                  }
                  }}>
                    <img src={`${closeIcon}`} alt="closebuttonIcon" class="va-w-4 va-h-4" />
                </Button>
            </div>
            <div
                class="va-text-[20px] va-leading-[24px] va-text-center va-font-bold va-overflow-hidden va-line-clamp-2"
                style="color: {headlineColor}"
            >
                {headline}
            </div>
            <div
                class="va-text-[16px] va-leading-[20px] va-text-center va-font-semibold va-mt-2 va-overflow-hidden va-line-clamp-1"
                style="color: {bodyTextColor}"
            >
                {subheader}
            </div>
            <div class="va-flex va-flex-col va-gap-2 va-mt-5 va-px-1">
                {#each answerOptions as item}
                    <a
                    class="va-h-10 va-text-[16px] va-content-center va-text-center va-font-semibold va-rounded-[10px] va-hover:bg-gray-300 va-decoration-transparent va-flex va-items-center va-justify-center va-overflow-hidden va-whitespace-nowrap va-text-ellipsis va-px-2"
                    style="color: {bodyTextColor}; background-color: {itemBackgroundColor}"
                        href={!isPreview ? item.url : undefined} 
                        on:click={!isPreview ? () => {
                            recordNudgeCTAClicked({
                                url,
                                path: "/base_nudge/record/cta",
                                bodyParams: {
                                    session_cookie: session_cookie,
                                    intervention_type: INTERVENTION_TYPE_NAMES.NAVIGATIONAL_NUDGE,
                                    page_view_id: pageViewId,
                                    dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
                                    metadata: JSON.stringify({
                                        selectedItem: item,
                                        items: answerOptions
                                    })
                                },
                            });
                        } : undefined}
                    >
                        {item.label}
                    </a>
                {/each}
            </div>
        </div>
    </div>
{/if}

<style>
    @keyframes slideToTop {
        0% {
            transform: translateY(100%); /* Start off-screen */
        }
        100% {
            transform: translateY(0); /* End at the original position */
        }
    }

    .slide_to_top_animation {
        animation: slideToTop 0.3s ease-in-out forwards; /* Define the animation */
    }
</style>