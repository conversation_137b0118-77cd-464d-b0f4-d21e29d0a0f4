<script lang="ts">
  import closeIcon from "../../assets/closeIcon.svg";
  import { Button } from "../../lib/components/ui/button";
  import { vandraGetCookie } from "../../lib/utils";

  import { apiUrl } from "../../lib/store";
  import { onMount } from "svelte";
  import DefaultPlacementWrapper from "../design-system/default-placement-wrapper.svelte";
  import { buildRequest } from "../../lib/api/utils";
  import { INTERVENTION_TYPE_NAMES } from "../../constants";
  import { decodeTemplateString } from "../../lib/utils";

  const CART_ABANDONMENT_TYPES = {
    IN_SESSION: "in_session",
    RETURNING: "returning",
  } as const;

  type CartAbandonmentType =
    (typeof CART_ABANDONMENT_TYPES)[keyof typeof CART_ABANDONMENT_TYPES];

  interface RequestParams {
    url: string;
    path: string;
    bodyParams: Record<string, any>;
  }

  export let primaryColor: string;
  export let storeAdImage: string;
  export let hasDiscount: boolean;
  export let discountTotal: number = 0;
  export let cartType: CartAbandonmentType;
  export let dwellTimeStartCounter: number;
  export let pageViewId: string;
  export let inSessionCartAbandonmentHeading1: string = "";
  export let inSessionCartAbandonmentHeading2: string = "";
  export let savingsCartAbandonmentHeading1: string = "";
  export let savingsCartAbandonmentHeading2: string = "";
  export let storeFont: string;
  export let isPreview: boolean = false;
  export let animate: boolean = true;

  // Subscribe to the apiUrl store
  let url = "";
  $: $apiUrl, (url = $apiUrl);

  let session_cookie: string = "";

  const getInterventionType = (
    cart_abandonment_type: CartAbandonmentType
  ): string => {
    return cart_abandonment_type === CART_ABANDONMENT_TYPES.IN_SESSION
      ? INTERVENTION_TYPE_NAMES.CART_ABANDONMENT_IN_SESSION
      : INTERVENTION_TYPE_NAMES.CART_ABANDONMENT_RETURNING;
  };

  // Export function to update props without remounting
  export function updateProps(newProps: Record<string, any>) {
    if (newProps.primaryColor !== undefined)
      primaryColor = newProps.primaryColor;
    if (newProps.storeAdImage !== undefined)
      storeAdImage = newProps.storeAdImage;
    if (newProps.hasDiscount !== undefined) hasDiscount = newProps.hasDiscount;
    if (newProps.discountTotal !== undefined)
      discountTotal = newProps.discountTotal;
    if (newProps.cartType !== undefined) cartType = newProps.cartType;
    if (newProps.dwellTimeStartCounter !== undefined)
      dwellTimeStartCounter = newProps.dwellTimeStartCounter;
    if (newProps.pageViewId !== undefined) pageViewId = newProps.pageViewId;
    if (newProps.inSessionCartAbandonmentHeading1 !== undefined)
      inSessionCartAbandonmentHeading1 =
        newProps.inSessionCartAbandonmentHeading1;
    if (newProps.inSessionCartAbandonmentHeading2 !== undefined)
      inSessionCartAbandonmentHeading2 =
        newProps.inSessionCartAbandonmentHeading2;
    if (newProps.savingsCartAbandonmentHeading1 !== undefined)
      savingsCartAbandonmentHeading1 = newProps.savingsCartAbandonmentHeading1;
    if (newProps.savingsCartAbandonmentHeading2 !== undefined)
      savingsCartAbandonmentHeading2 = newProps.savingsCartAbandonmentHeading2;
    if (newProps.storeFont !== undefined) storeFont = newProps.storeFont;
    if (newProps.isPreview !== undefined) isPreview = newProps.isPreview;
    if (newProps.animate !== undefined) animate = newProps.animate;

    // Decode template strings after update
    if (
      newProps.savingsCartAbandonmentHeading1 !== undefined ||
      newProps.discountTotal !== undefined
    ) {
      savingsCartAbandonmentHeading1 = decodeTemplateString(
        savingsCartAbandonmentHeading1,
        { savings: `${Math.floor(discountTotal / 100)}` }
      );
    }
    if (
      newProps.savingsCartAbandonmentHeading2 !== undefined ||
      newProps.discountTotal !== undefined
    ) {
      savingsCartAbandonmentHeading2 = decodeTemplateString(
        savingsCartAbandonmentHeading2,
        { savings: `${Math.floor(discountTotal / 100)}` }
      );
    }
  }

  onMount(() => {
    const cookieValue = vandraGetCookie("vandra_session_cookie");

    if (!cookieValue && !isPreview) {
      throw new Error("No session cookie found");
    }

    session_cookie = cookieValue || "";

    // Skip API calls when in preview mode
    if (!isPreview) {
      buildRequest({
        url,
        path: "/base_nudge/record/shown",
        method: "POST",
        queryParams: undefined,
        bodyParams: {
          session_cookie: session_cookie,
          intervention_type: getInterventionType(cartType),
          page_view_id: pageViewId,
          dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
        },
      }).catch((err: Error) => {
        throw new Error(err.message);
      });
    }

    // Decode template strings
    savingsCartAbandonmentHeading1 = decodeTemplateString(
      savingsCartAbandonmentHeading1,
      { savings: `${Math.floor(discountTotal / 100)}` }
    );
    savingsCartAbandonmentHeading2 = decodeTemplateString(
      savingsCartAbandonmentHeading2,
      { savings: `${Math.floor(discountTotal / 100)}` }
    );
  });

  // actions
  const recordCartAbandonCTAClick = async ({
    url,
    path,
    bodyParams,
  }: RequestParams) => {
    // Skip API calls when in preview mode
    if (isPreview) return;

    const res = await buildRequest({
      url,
      path,
      method: "POST",
      queryParams: undefined,
      bodyParams,
    });
    if (res && typeof res === "object" && "errors" in res) {
      throw new Error((res.errors as any).message);
    }
  };

  const recordCartDismissed = async ({
    url,
    path,
    bodyParams,
  }: RequestParams) => {
    // Skip API calls when in preview mode
    if (isPreview) return;

    const res = await buildRequest({
      url,
      path,
      method: "POST",
      queryParams: undefined,
      bodyParams,
    });
    if (res && typeof res === "object" && "errors" in res) {
      throw new Error((res.errors as any).message);
    }
  };

  let show = true;
</script>

{#if show}
  <DefaultPlacementWrapper right={true} {animate}>
    <div
      id="cart_abandonment_nudge"
      class="scope_defaults va-flex va-flex-col va-shadow-2xl va-rounded-2xl va-items-center"
      style="border: 1px solid {primaryColor};"
    >
      <!-- use the styling white background color -->
      <div
        class="va-flex va-flex-col va-rounded-2xl va-min-w-[216px] va-max-w-[216px]"
        style={`background-color: #ffffff`}
      >
        <!-- Main content section -->
        <div class="va-flex va-flex-col va-items-center va-mx-6">
          <!-- Close button container -->
          <div class="va-place-self-end va-py-5">
            <Button
              variant="link"
              on:click={() => {
                recordCartDismissed({
                  url,
                  path: "/base_nudge/record/dismissed",
                  bodyParams: {
                    session_cookie: session_cookie,
                    intervention_type: getInterventionType(cartType),
                    page_view_id: pageViewId,
                    dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
                  },
                });
                show = false;
              }}
              class="va-bg-transparent va-p-0 va-m-0 va-border-0 hover:va-bg-transparent va-absolute va-top-4 va-right-4 va-h-fit va-w-fit"
              disabled={isPreview}
            >
              <img
                src={`${closeIcon}`}
                alt="closebuttonIcon"
                class="va-w-4 va-h-4 va-m-0"
              />
            </Button>
          </div>
          <div
            class="va-flex va-flex-col va-items-center va-mx-auto va-leading-none va-text-center"
            style="max-width: 100%;"
          >
            {#if hasDiscount}
              <div
                class="va-text-xl va-font-bold"
                style="color: {primaryColor}; font-family: {storeFont}, Inter; font-style: normal; line-height: 120%; overflow: hidden; max-width: 100%;"
              >
                {savingsCartAbandonmentHeading1 ||
                  `You have $${Math.floor(discountTotal / 100)} in`}
                <br />
                {savingsCartAbandonmentHeading2 || `savings in your cart!`}
              </div>
            {:else}
              <div
                class="va-text-xl va-font-semibold va-leading-6"
                style="color: {primaryColor}; font-family: {storeFont}, Inter; font-style: normal; line-height: 120%; overflow: hidden; max-width: 100%;"
              >
                {inSessionCartAbandonmentHeading1 || "Don't leave your"}
                <br />
                {inSessionCartAbandonmentHeading2 || "cart hanging!"}
              </div>
            {/if}
          </div>
          <div
            class="va-flex va-p-7 va-items-center va-justify-center va-h-full"
          >
            <img
              src={storeAdImage}
              alt="product"
              class="va-max-h-[99px] va-max-w-[144px]"
            />
          </div>
        </div>
        <!-- Checkout button -->
        <div
          class="va-flex va-items-center va-justify-center va-h-full va-pb-5 va-mx-6"
        >
          <Button
            variant="outline"
            class={`va-flex va-items-center va-justify-center va-rounded-full va-border-none va-w-full va-py-2 va-h-[38px] va-cursor-pointer va-bg-transparent`}
            backgroundColor={primaryColor}
            color={"#ffffff"}
            style={`font-family: ${storeFont}, Inter;`}
            disabled={isPreview}
            on:click={() => {
              recordCartAbandonCTAClick({
                url,
                path: "/base_nudge/record/cta",
                bodyParams: {
                  session_cookie: session_cookie,
                  intervention_type: getInterventionType(cartType),
                  page_view_id: pageViewId,
                  dwell_time: (Date.now() - dwellTimeStartCounter) / 1000,
                },
              });
              window.location.href = "/checkout";
            }}
          >
            <div
              class="va-text-base va-button-text va-hover:text-white va-font-bold"
            >
              CHECKOUT
            </div>
          </Button>
        </div>
      </div>
    </div>
  </DefaultPlacementWrapper>
{/if}

<style>
  .scope_defaults {
    line-height: 1.5; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
    -moz-tab-size: 4; /* 3 */
    tab-size: 4; /* 3 */
    -webkit-tap-highlight-color: transparent; /* 7 */
  }
</style>
