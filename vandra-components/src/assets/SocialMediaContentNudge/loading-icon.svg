<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#paint0_angular_355_5269_clip_path)" data-figma-skip-parse="true">
    <g transform="matrix(0 0.02 -0.02 0 20 20)">
      <foreignObject x="-1050" y="-1050" width="2100" height="2100">
        <div xmlns="http://www.w3.org/1999/xhtml"
          style="background:conic-gradient(from 90deg,rgba(133, 108, 248, 1) 0deg,rgba(133, 108, 248, 0) 360deg);height:100%;width:100%;opacity:1">
        </div>
      </foreignObject>
    </g>
  </g>
  <path
    d="M40 20C40 31.0457 31.0457 40 20 40C8.9543 40 0 31.0457 0 20C0 8.9543 8.9543 0 20 0C31.0457 0 40 8.9543 40 20ZM6.64637 20C6.64637 27.375 12.625 33.3536 20 33.3536C27.375 33.3536 33.3536 27.375 33.3536 20C33.3536 12.625 27.375 6.64637 20 6.64637C12.625 6.64637 6.64637 12.625 6.64637 20Z"
    data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.52156865596771240,&#34;g&#34;:0.42352941632270813,&#34;b&#34;:0.97254902124404907,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.52156865596771240,&#34;g&#34;:0.42352941632270813,&#34;b&#34;:0.97254902124404907,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.52156865596771240,&#34;g&#34;:0.42352941632270813,&#34;b&#34;:0.97254902124404907,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.52156865596771240,&#34;g&#34;:0.42352941632270813,&#34;b&#34;:0.97254902124404907,&#34;a&#34;:0.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:2.4492935992912173e-15,&#34;m01&#34;:-40.0,&#34;m02&#34;:40.0,&#34;m10&#34;:40.0,&#34;m11&#34;:2.4492935992912173e-15,&#34;m12&#34;:-2.4492935992912173e-15},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" />
  <circle cx="19.9993" cy="36.6666" r="3.33333" fill="#856CF8" />
  <defs>
    <clipPath id="paint0_angular_355_5269_clip_path">
      <path
        d="M40 20C40 31.0457 31.0457 40 20 40C8.9543 40 0 31.0457 0 20C0 8.9543 8.9543 0 20 0C31.0457 0 40 8.9543 40 20ZM6.64637 20C6.64637 27.375 12.625 33.3536 20 33.3536C27.375 33.3536 33.3536 27.375 33.3536 20C33.3536 12.625 27.375 6.64637 20 6.64637C12.625 6.64637 6.64637 12.625 6.64637 20Z" />
    </clipPath>
  </defs>
</svg>