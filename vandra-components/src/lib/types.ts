export interface PickUpWhereYouLeftOffItem {
  productId: string;
  variantId: string;
  url: string;
  title: string;
  price: number;
  priceCurrency: string;
  image: string;
}

export interface CartItem {
    "quantity": number;
    "variant_id": number;
    "key": string;
    "title": string;
    "original_price": number; // 100x price
    "discounted_price": number; // 100x price
    "total_discount": number; // 100x price
    "discounts": [
      {
        "amount": number; // 100x price
        "title": string;
      }
    ];
    "product_id": number;
    "url": string;
    "image": string;
    "handle": string;
    "product_type": string;
    "product_title": string;
}