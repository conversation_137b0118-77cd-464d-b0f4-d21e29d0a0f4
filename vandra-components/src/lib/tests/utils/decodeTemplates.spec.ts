import { describe, expect, test} from 'vitest';
import { decodeTemplateString } from "../../utils"

describe("decodeTemplates test suite", () => {
    test("discountAmount: takes a template string and decodes it", () => {
        const tempStr = "Here is your {discountAmount} off";
        const replaceWith = 100;
        const value = decodeTemplateString(tempStr, replaceWith)
        expect(value).toEqual("Here is your $100 off");
    })

    test("discountAmount: does not replace the wrong template", () => {
        const tempStr = "Here is your {discountAmounts} off";
        const replaceWith = 100;
        expect(() => decodeTemplateString(tempStr, replaceWith)).toThrowError(/Invalid placeholder detected:/)
    })

    test("discountAmount: does not replace if it is not formated as a template", () => {
        const tempStr = "Here is your discountAmount off";
        const replaceWith = 100;
        expect(decodeTemplateString(tempStr, replaceWith)).toEqual("Here is your discountAmount off")
    })
})