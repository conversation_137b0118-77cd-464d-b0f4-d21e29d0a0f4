import snakecaseKeys from "snakecase-keys";

/**
 * Builds and sends an HTTP request based on the given options.
 *
 * @param {Object} options - The options used to configure the request.
 * @param {string} [options.path] - The path to be appended to the base URL.
 * @param {string} [options.url] - The host URL of the request.
 * @param {string} [options.method] - The HTTP method for the request (e.g., 'GET', 'POST', 'PUT', 'DELETE').
 * @param {Object} [options.queryParams] - An object containing query parameters to be appended to the final URL.
 * @param {Object} [options.bodyParams] - An object containing the body parameters to be sent with the request (for POST/PUT requests).
 *
 * @returns {Promise<Object>} [data, errors] - A promise that resolves to the response object from the HTTP request.
 */
export async function buildRequest(options) {
    const { url, path, method, bodyParams, queryParams } = options
    const finalUrl = new URL(`${url}${path}`)

    if (queryParams) {
        // Snakecase query parameters
        const snakecasedQueryParams = snakecaseKeys({ ...queryParams });

        // Support array values for queryParams such as { foo: [42, 43] }
        for (const [key, val] of Object.entries(snakecasedQueryParams)) {
            if (val) {
                [val].flat().forEach(v => finalUrl.searchParams.append(key, v));
            }
        }
    }

    // Build headers
    const headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    };

    let res;

    try {
        if (bodyParams) {
            res = await fetch(finalUrl.href, {
                method,
                headers,
                body: new URLSearchParams(bodyParams)
            })
        } else {
            res = await fetch(finalUrl.href, {
                method,
                headers
            })
        }
        const isJSONResponse = res?.headers && res.headers?.get("content-type") === "application/json";
        const responseBody = await (isJSONResponse ? res.json() : res.text());
        if (!res.ok) {
            return {
                data: null,
                errors: {
                    code: 'server_error',
                    message: responseBody,
                }
            };
        }

        return {
            data: responseBody,
            errors: null
        }
    } catch (error) {
        return {
            data: null,
            errors: {
                code: 'unexpected_error',
                message: error.message || 'Unexpected error',
            },
        };
    }

}