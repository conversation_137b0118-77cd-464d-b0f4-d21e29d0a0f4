<script lang="ts">
	import { But<PERSON> } from "bits-ui";
	import { buttonVariants } from "./index.js";
	import { cn } from "$lib/utils";
	import type { ClassValue } from "clsx";

	let className: string | undefined = undefined;
	export let variant: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" = "default";
	export let size: "default" | "sm" | "lg" | "icon" = "default";
	export let builders: any[] = [];
	export let color: string = "";
	export let backgroundColor: string = "";
	export { className as class };
</script>

<svelte-css-wrapper style="display: contents; --color: {color}; --bg-color: {backgroundColor};">
	<Button.Root
		{builders}
		class={cn(buttonVariants({ variant, size, className }), "va-p-0")}
		type="button"
		{...$$restProps}
		on:click 
		on:keydown
	>
		<slot />
	</Button.Root>
</svelte-css-wrapper>


<style>
	/* Targeting the dynamic button or link */
	:global([data-button-root]) {
		background-color: var(--bg-color, #232323);
		color: var(--color, #FFFFFF);
	}
</style>
