import { clsx, type ClassArray } from "clsx";
import { twMerge } from "tailwind-merge";;

export function cn(...inputs: ClassArray) {
  return twMerge(clsx(inputs));
}

// Get our session cookie and our customer cookie

export const vandraGetCookie = (name: string) => {
  try {
    let name_eq = name + "=";
    let ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) == ' ') c = c.substring(1, c.length);
      if (c.indexOf(name_eq) == 0) return c.substring(name_eq.length, c.length);
    }
    return null;
  } catch (ErrorEvent) {
    throw (ErrorEvent);
  }
}

export function vandraSetCookie(name: string, value: string, days: number) {
  try {
    let date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    const expires = "expires=" + date.toUTCString();
    document.cookie = name + "=" + value + "; " + expires + "; path=/";
  } catch (ErrorEvent) {
    throw (ErrorEvent);
  }
}

/** 
* A utility function for replacing template variables in strings.
* @param input the string to replace values in
* @param replacements object containing key-value pairs for replacements
*/
export const decodeTemplateString = (input: string, replacements: Record<string, string | number>): string => {
  let result = input;
  
  // Loop through all replacement entries
  Object.entries(replacements).forEach(([key, value]) => {
    // Create the placeholder pattern (e.g. {discountAmount})
    const placeholder = `{${key}}`;
    
    // Replace all occurrences of this placeholder
    result = result.replace(new RegExp(placeholder, 'g'), String(value));
  });
  
  return result;
}

export function formatPrice(price: number, currency: string): string {
  const language = navigator.language || "en-US";
  const formattedPrice = new Intl.NumberFormat(language, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);

  // Remove the currency code (e.g., "US") from the formatted price
  return formattedPrice.replace(/[A-Z]{1,2}\$/, "$");
}

export function unix_to_gmt_time(unixtime: number) {
  // Create a Date object from the timestamp (in milliseconds)
  const date = new Date(unixtime * 1000);

  // Get the GMT date and time string
  return date.toUTCString();
}
export const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}