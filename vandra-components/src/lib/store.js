// src/stores.js
import { writable } from 'svelte/store';


// Initial value, could be overridden later
let vandra_api_base_url = "https://app.vandra.ai";

if (typeof window !== 'undefined') {
    if (window.location.hostname.includes("127.0.0.1") || window.location.hostname.includes("localhost")) {
        vandra_api_base_url = "http://localhost:8080";
    } else if (window.location.hostname.includes("vandra-blue.myshopify.com")) {
        vandra_api_base_url = "https://staging-blue.vandra.ai";
    } else if (window.location.hostname.includes("vandra-green.myshopify.com")) {
        vandra_api_base_url = "https://staging-green.vandra.ai";
    } else if (window.location.hostname.includes("vandra-red.myshopify.com")) {
        vandra_api_base_url = "https://staging-red.vandra.ai";
    } else if (window.location.hostname.includes("vandra-orange.myshopify.com")) {
        vandra_api_base_url = "https://staging-orange.vandra.ai";
    } else if (window.location.hostname.includes("vandra-") && window.location.hostname.includes(".myshopify.com")) {
        vandra_api_base_url = "https://staging.vandra.ai";
    }
}

export const apiUrl = writable(vandra_api_base_url);
export const store_total_savings = writable(0);
export const store_savings_threshold = writable(0);
export const store_savings_show = writable(true);
export const store_savings_mounted = writable(false);
