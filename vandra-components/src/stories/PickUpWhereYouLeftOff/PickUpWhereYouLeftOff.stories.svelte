<script lang="ts" module>
  import { defineMeta } from "@storybook/addon-svelte-csf";
  import PickUpWhereYouLeftOff from "../../components/pick-up-where-you-left-off/pick-up-where-you-left-off.svelte";
  import product1 from "../../assets/PickupWhereYouLeftOff/product1.png";
  import product2 from "../../assets/PickupWhereYouLeftOff/product2.png";
  import product3 from "../../assets/PickupWhereYouLeftOff/product3.png";
  import type { PickUpWhereYouLeftOffItem } from "../../lib/types";
  
  // More on how to set up stories at: https://storybook.js.org/docs/writing-stories
  const { Story } = defineMeta({
    title: "Example/PickUpWhereYouLeftOff",
    component: PickUpWhereYouLeftOff,
    tags: ["autodocs"],
    argTypes: {
      primaryColor: { control: "color" },
      backgroundColor: { control: "color" },
      items: { control: "object" },
      isPreview: { 
        control: "boolean", 
        description: "Set to true to avoid making network requests in Storybook" 
      },
      animate: { 
        control: "boolean", 
        description: "Whether to show the animation effect" 
      }
    },
    parameters: {
      layout: "fullscreen",
      controls: {
        exclude: ["items"],
      },
      backgrounds: {
        default: "Gray",
        values: [
          {
            name: "Gray",
            value: "#CCC",
          },
        ],
      },
    },
  });
  
  const primaryColor: string = "#8F0067";
  const backgroundColor: string = "#FFFFFF";
  const items: PickUpWhereYouLeftOffItem[] = [
    {
      productId: "10001",
      variantId: "20001",
      url: "https://www.google.com",
      image: product1,
      title: "Women's Multi",
      price: 13.99,
      priceCurrency: "USD",
    },
    {
      productId: "10002",
      variantId: "20002",
      url: "https://www.google.com",
      image: product2,
      title: "Men's Multi",
      price: 13.99,
      priceCurrency: "USD",
    },
    {
      productId: "10003",
      variantId: "20003",
      url: "https://www.google.com",
      image: product3,
      title: "Probiotic - Mango",
      price: 13.99,
      priceCurrency: "USD",
    },
  ];
</script>

<!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->

<Story
  name="One Item"
  args={{
    primaryColor,
    backgroundColor,
    items: items.slice(0, 1),
    isPreview: true,
    animate: true,
    dwellTimeStartCounter: Date.now(),
    pageViewId: "test-page-view-id"
  }}
  parameters={{
    controls: {
      include: ["primaryColor", "backgroundColor", "isPreview", "animate"],
    },
  }}
/>
<Story
  name="Two Items"
  args={{
    primaryColor,
    backgroundColor,
    items: items.slice(0, 2),
    isPreview: true,
    animate: true,
    dwellTimeStartCounter: Date.now(),
    pageViewId: "test-page-view-id"
  }}
  parameters={{
    controls: {
      include: ["primaryColor", "backgroundColor", "isPreview", "animate"],
    },
  }}
/>
<Story
  name="Three Items"
  args={{
    primaryColor,
    backgroundColor,
    items: items.slice(0, 3),
    isPreview: true,
    animate: true,
    dwellTimeStartCounter: Date.now(),
    pageViewId: "test-page-view-id"
  }}
  parameters={{
    controls: {
      include: ["primaryColor", "backgroundColor", "isPreview", "animate"],
    },
  }}
/>
