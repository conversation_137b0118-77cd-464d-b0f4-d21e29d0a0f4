<script lang="ts" module>
  import { defineMeta } from "@storybook/addon-svelte-csf";
  import SocialMediaContentNudge from "../../components/social-media-content-nudge/social-media-content-nudge.svelte";
  import sampleVideo from "../../assets/SocialMediaContentNudge/sample-video.mp4";
  
  const { Story } = defineMeta({
    title: "Example/SocialMediaContentNudge",
    component: SocialMediaContentNudge,
    tags: ["autodocs"],
    argTypes: {
      videoUrl: { control: "text" },
      isPreview: { 
        control: "boolean", 
        description: "Set to true to avoid making network requests in Storybook" 
      },
      animate: { 
        control: "boolean", 
        description: "Whether to show the animation effect" 
      }
    },
    parameters: {
      layout: "fullscreen",
      backgrounds: {
        default: "Light",
        values: [
          {
            name: "Light",
            value: "#FFFFFF",
          },
          {
            name: "Dark",
            value: "#333333",
          },
        ],
      },
    },
  });

  const primaryColor: string = "#8F0067";
  // You'll need to add a sample video to your assets folder
  const videoUrl: string = sampleVideo;

  // New delayed video URL that resolves after 2 seconds
  const delayedVideoUrl: Promise<string> = new Promise((resolve) => {
    setTimeout(() => {
      resolve(sampleVideo);
    }, 3000);
  });
</script>

<!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->
<Story
  name="Default"
  args={{
    videoUrl,
    pageViewId: "test-page-view-id",
    dwellTimeStartCounter: Date.now(),
    isPreview: true,
    animate: true,
  }}
  parameters={{
    controls: {
      include: ["videoUrl", "isPreview", "animate"],
    },
  }}
/>

<!-- Added a new Story variation "Delayed" to test a delay in video loading -->
<Story
  name="Delayed"
  args={{
    videoUrl: delayedVideoUrl,
    pageViewId: "test-page-view-id-delayed",
    dwellTimeStartCounter: Date.now(),
    isPreview: true,
    animate: true,
  }}
  parameters={{
    controls: {
      include: ["videoUrl", "isPreview", "animate"],
    },
  }}
/>
