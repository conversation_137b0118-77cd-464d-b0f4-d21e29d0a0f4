<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import DiscountNudge from '../../components/discount-nudge/discount-nudge.svelte';
    import { fn } from '@storybook/test';
    import svelte from "../../assets/svelte.svg"
  
    // More on how to set up stories at: https://storybook.js.org/docs/writing-stories
    const { Story } = defineMeta({
      title: 'Example/DiscountNudge',
      component: DiscountNudge,
      tags: ['autodocs'],
      argTypes: {
      },
      parameters: {
        layout: 'fullscreen',
        backgrounds: {
            default: 'Gray',
            values: [
                // 👇 Add a new value
                { name: '<PERSON>', value: '#CCC' },
            ],
        },
      },
      
    });
  </script>
  
  <!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->
  <Story name="DEFAULT_RIGHT" args={{
    isPreview: true,
    show: true,
    front_end_ui_name: 'DEFAULT_RIGHT',
    discount_rate: 20,
    discount_code: 'TEST-59788',
  }} />
  <Story name="DEFAULT_LEFT" args={{
    show: true,
    front_end_ui_name: 'DEFAULT_LEFT',
    discount_rate: 20,
    discount_code: 'TEST-59788',
  }} />
  <Story name="DEFAULT_RIGHT_NEW" args={{
    show: true,
    front_end_ui_name: 'DEFAULT_RIGHT_NEW',
    discount_rate: 20,
    discount_code: 'TEST-59788',
  }} />
  <Story name="NO_X" args={{
    show: true,
    front_end_ui_name: 'NO_X',
    discount_rate: 20,
    discount_code: 'TEST-59788',
  }} />
  <Story name="IMAGE" args={{
    show: true,
    front_end_ui_name: 'IMAGE',
    discount_rate: 20,
    discount_code: 'TEST-59788',
    popup_image_url: 'https://vandra-green.myshopify.com/cdn/shop/files/theme_cover_image.jpg?v=1738686273&width=750',
  }} />
  <Story name="COUNTDOWN" args={{
    show: true,
    front_end_ui_name: 'COUNTDOWN',
    discount_rate: 20,
    discount_code: 'TEST-59788',
    discount_ends_at_time: 1743120000000,
  }} />
  <Story name="MODAL" args={{
    show: true,
    front_end_ui_name: 'MODAL',
    discount_rate: 20,
    discount_code: 'TEST-59788',
  }} />
  <Story name="AUTO_APPLY" args={{
    show: true,
    front_end_ui_name: 'AUTO_APPLY',
    discount_rate: 20,
    discount_code: 'TEST-59788',
  }} />
  <Story name="RENUDGE" args={{
    show: true,
    front_end_ui_name: 'RENUDGE',
    widget_type: 'RETURN',
    discount_rate: 20,
    discount_code: 'TEST-59788',
  }} />
  <Story name="ACTION_BASED_SHOW" args={{
    front_end_ui_name: 'ACTION_BASED_SHOW',
    ui_version_test: 'ACTION_BASED_SHOW',
    discount_rate: 20,
    discount_code: 'TEST-59788',
  }} />