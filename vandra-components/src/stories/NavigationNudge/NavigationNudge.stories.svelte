<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import NavigationNudge from '../../components/navigational-nudge/navigation-nudge.svelte';
    import { fn } from '@storybook/test';
    import svelte from "../../assets/svelte.svg"
    import bg from "../../assets/nav-nug.png"
  
    // More on how to set up stories at: https://storybook.js.org/docs/writing-stories
    const { Story } = defineMeta({
      title: 'Example/NavigationNudge',
      component: NavigationNudge,
      tags: ['autodocs'],
      argTypes: {
        headline: { control: 'text' },
        subheader: { control: 'text' },
        headlineColor: { control: 'color' },
        bodyTextColor: { control: 'color' },
        backgroundColor: { control: 'color' },
        itemBackgroundColor: { control: 'color' },
        font: { control: 'text' },
        answerOptions: { control: 'object' },
        isPreview: { 
          control: 'boolean', 
          description: 'Set to true to avoid making network requests in Storybook' 
        },
        animate: { 
          control: 'boolean', 
          description: 'Whether to show the animation effect' 
        }
      },
      parameters: {
        layout: 'fullscreen',
        backgrounds: {
            default: 'Gray',
            values: [
                // 👇 Add a new value
                { name: '<PERSON>', value: '#CCC' },
                { name: 'Image', value: `url(${bg})`}
            ],
        },
      },
      
    });
  </script>
  
  <!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->
  <Story name="Default" args={{
    headline: "What are you shopping for?",
    subheader: "We'll take you to the right spot!",
    headlineColor: "#358e7f",
    bodyTextColor: "#000000",
    backgroundColor: "#FFFFFF",
    itemBackgroundColor: "#EDEEF0",
    font: "Inter",
    answerOptions: [
      {
      "label": "Dog 🐶",
      "value": "dog",
      "url": "https://www.petplay.com/collections/dog-collection"
    },
    {
      "label": "Cat 🐈",
      "value": "cat",
      "url": "https://www.petplay.com/collections/cat-collection"
    },
    {
      "label": "Both",
      "value": "both",
      "url": "https://www.petplay.com/collections/top-picks-for-paws"
    }
    ],
    show: true,
    isPreview: true,
    animate: true,
    pageViewId: "test-page-view-id"
  }} />
  
  