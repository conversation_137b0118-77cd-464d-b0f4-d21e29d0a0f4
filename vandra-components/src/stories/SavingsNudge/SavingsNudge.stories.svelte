<script module>
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import SavingsNudge from '../../components/savings-nudge/savings-nudge.svelte';
    import { fn } from '@storybook/test';
    import svelte from "../../assets/svelte.svg"
  
    // More on how to set up stories at: https://storybook.js.org/docs/writing-stories
    const { Story } = defineMeta({
      title: 'Example/SavingsNudge',
      component: SavingsNudge,
      tags: ['autodocs'],
      argTypes: {
      },
      parameters: {
        layout: 'fullscreen',
        backgrounds: {
            default: 'Gray',
            values: [
                // 👇 Add a new value
                { name: '<PERSON>', value: '#CCC' },
            ],
        },
      },
      
    });
  </script>
  
  <!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->
  <Story name="SavingsNudgeStory" args={{
    show: true,
    total_savings: "$24.99",
  }} />