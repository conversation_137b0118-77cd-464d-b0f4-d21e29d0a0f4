<script lang="ts" module>
  import { defineMeta } from "@storybook/addon-svelte-csf";
  import CartAbandoned from "../../components/cart-abandoned/cart-abandoned.svelte";
  import svelte from "../../assets/svelte.svg";
  import productImage from "../../assets/CartAbandoned/product.png";

  // More on how to set up stories at: https://storybook.js.org/docs/writing-stories
  const { Story } = defineMeta({
    title: "Example/CartAbandon",
    component: CartAbandoned,
    tags: ["autodocs"],
    argTypes: {
      primaryColor: { control: "color" },
      storeAdImage: {},
      isPreview: { 
        control: "boolean", 
        description: "Set to true to avoid making network requests in Storybook" 
      }
    },
    parameters: {
      layout: "fullscreen",
    },
  });
</script>

<!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->
<Story
  name="Primary"
  args={{
    primaryColor: "#8F0067",
    storeAdImage: productImage,
    hasDiscount: false,
    cartType: "in_session",
    dwellTimeStartCounter: Date.now(),
    pageViewId: "test-page-view-id",
    storeFont: "Inter",
    isPreview: true
  }}
/>
