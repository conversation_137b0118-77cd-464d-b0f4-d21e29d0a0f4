@keyframes slideToLeft {
    0% {
        transform: translateX(100%); /* Start off-screen */
    }
    100% {
        transform: translateX(0); /* End at the original position */
    }
}

@keyframes slideToRight {
    0% {
        transform: translateX(-100%); /* Start off-screen */
    }
    100% {
        transform: translateX(0); /* End at the original position */
    }
}

.slide_to_left_animation {
    animation: slideToLeft 0.3s ease-in-out forwards; /* Define the animation */
}

.slide_to_right_animation {
    animation: slideToRight 0.3s ease-in-out forwards; /* Define the animation */
}

.nudge_container img {
    all: unset; /* Remove all inherited styles */
}