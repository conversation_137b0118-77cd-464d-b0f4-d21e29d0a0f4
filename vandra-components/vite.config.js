import { defineConfig } from 'vite'
import path from "path"
import { svelte } from '@sveltejs/vite-plugin-svelte'

// https://vite.dev/config/
export default defineConfig({
  plugins: [svelte()],
  build: {
    lib: {
      entry: 'src/main.js',
      name: 'vandra<PERSON>om<PERSON>',
      formats: ['iife'], // You may want 'umd' or 'es' depending on your needs
      fileName: (format) => `vandra-components.${format}.js`,
    },
    outDir: "../extensions/vandra-js/assets",
    emptyOutDir: false
  },
  resolve: {
    alias: {
      $lib: path.resolve("./src/lib"),
      'snakecase-keys': path.resolve('node_modules/snakecase-keys'),
    },
  },
  rollupOptions: {
    external: ['svelte'],
    output: {
      minifyInternalExports: false,
      globals: {
        svelte: 'svelte',
        
      },
    },
  }
})
