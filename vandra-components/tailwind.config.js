import { fontFamily, spacing, fontSize } from "tailwindcss/defaultTheme";

function withPx(values) {
  return Object.fromEntries(
    Object.entries(values).map(([key, value]) => {
      if (value === "0") return [key, "0px"];
      const rem = parseFloat(value);
      return [key, `${rem * 16}px`];
    })
  );
}

/** @type {import('tailwindcss').Config} */
const config = {
  darkMode: ["class"],
  // trying to prevent our styles for conflicting with merchant site through prefixes
  prefix: "va-",
  corePlugins: {
    preflight: false,
  },
  content: ["./src/**/*.{html,js,svelte,ts}"],
  safelist: [
    "dark",
    {
      pattern: /^va-(p|px|py|pt|pr|pb|pl)-\d+/,
      variants: ["lg", "hover", "focus", "lg:hover"],
    },
  ],
  theme: {
    spacing: withPx(spacing),
    fontSize: withPx(fontSize),
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border) / <alpha-value>)",
        input: "hsl(var(--input) / <alpha-value>)",
        ring: "hsl(var(--ring) / <alpha-value>)",
        background: "hsl(var(--background) / <alpha-value>)",
        foreground: "hsl(var(--foreground) / <alpha-value>)",
        primary: {
          DEFAULT: "hsl(var(--primary) / <alpha-value>)",
          foreground: "hsl(var(--primary-foreground) / <alpha-value>)",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary) / <alpha-value>)",
          foreground: "hsl(var(--secondary-foreground) / <alpha-value>)",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive) / <alpha-value>)",
          foreground: "hsl(var(--destructive-foreground) / <alpha-value>)",
        },
        muted: {
          DEFAULT: "hsl(var(--muted) / <alpha-value>)",
          foreground: "hsl(var(--muted-foreground) / <alpha-value>)",
        },
        accent: {
          DEFAULT: "hsl(var(--accent) / <alpha-value>)",
          foreground: "hsl(var(--accent-foreground) / <alpha-value>)",
        },
        popover: {
          DEFAULT: "hsl(var(--popover) / <alpha-value>)",
          foreground: "hsl(var(--popover-foreground) / <alpha-value>)",
        },
        card: {
          DEFAULT: "hsl(var(--card) / <alpha-value>)",
          foreground: "hsl(var(--card-foreground) / <alpha-value>)",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        sans: [...fontFamily.sans],
        inter: ["Inter", "serif"],
      },
      boxShadow: {
        custom: "0px 4px 4px 0px #00000040",
      },
      zIndex: {
        max: 10000000000000,
      },
    },
  },
  plugins: [
    require('tailwindcss-line-clamp-no-ellipsis'),
  ],
};

export default config;
