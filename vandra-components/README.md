# Svelte + Vite + Vandra


## Recommended IDE Setup

[VS Code](https://code.visualstudio.com/) + [Svelte](https://marketplace.visualstudio.com/items?itemName=svelte.svelte-vscode).


## Getting Started

Run `npm install`

Run `npm run dev`. This will launch vite in watch mode will build on changes to our vandra-js assets.


### Additional Information

**`.vscode/extensions.json`?**

Other templates indirectly recommend extensions via the README, but this file allows VS Code to prompt the user to install the recommended extension upon opening the project.

**Why enable `checkJs` in the JS template?**

It is likely that most cases of changing variable types in runtime are likely to be accidental, rather than deliberate. This provides advanced typechecking out of the box. Should you like to take advantage of the dynamically-typed nature of JavaScript, it is trivial to change the configuration.

**Why is HMR not preserving my local component state?**

HMR state preservation comes with a number of gotchas! It has been disabled by default in both `svelte-hmr` and `@sveltejs/vite-plugin-svelte` due to its often surprising behavior. You can read the details [here](https://github.com/sveltejs/svelte-hmr/tree/master/packages/svelte-hmr#preservation-of-local-state).

If you have state that's important to retain within a component, consider creating an external store which would not be replaced by HMR.

```js
// store.js
// An extremely simple external store
import { writable } from 'svelte/store'
export default writable(0)
```

# Storybook

**Why is it included**
Provides a way for us to document usage and props for our components. 

# Utils

**Vandra Templating**

We expect new template string values to be of the form `{someTemplateName}`

We handle template variable replacement using the `decodeTemplateString` function in `lib/utils.ts`. The function accepts any template string containing variables in the format `{variableName}` and a replacements object.

Examples of template variables used in our system:
1. {discountAmount} - The discount amount (should be pre-formatted with currency symbol if needed)
2. {savings} - The savings percentage or amount

Usage example:
```typescript
decodeTemplateString("Save {discountAmount} today!", { discountAmount: "$25" })
// Returns: "Save $25 today!"
```

When adding new template strings to the spec we can write more test cases to make sure they decode as expected in the `tests/utils/decodeTemplates.spec.ts`
