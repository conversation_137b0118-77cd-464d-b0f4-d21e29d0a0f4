{"name": "vandra-components", "private": true, "version": "0.0.0", "type": "module", "./src/app.css": "./dist/vandraLib.css", "scripts": {"build": "vite build", "dev": "vite build --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "vitest"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.2", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/addon-svelte-csf": "^5.0.0-next.13", "@storybook/blocks": "^8.4.7", "@storybook/svelte": "^8.4.7", "@storybook/svelte-vite": "^8.4.7", "@storybook/test": "^8.4.7", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tsconfig/svelte": "^5.0.4", "autoprefixer": "^10.4.20", "bits-ui": "^0.21.16", "clsx": "^2.1.1", "postcss": "^8.4.49", "storybook": "^8.4.7", "svelte": "^5.2.7", "svelte-preprocess": "^6.0.3", "tailwind-merge": "^2.5.5", "tailwind-variants": "^0.3.0", "tailwindcss": "^3.4.16", "typescript": "^5.7.3", "vite": "^6.0.1", "vitest": "^3.0.5"}, "dependencies": {"snakecase-keys": "^8.0.1", "tailwindcss-line-clamp-no-ellipsis": "^0.1.3"}}