import argparse
import os
import joblib
import pandas as pd

import logging
import os
import io

# Configure logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Example log message
logger.info("This is a log message from the isotonic postprocess script.")

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model-dir', type=str, default=os.environ.get('SM_MODEL_DIR'))
    parser.add_argument('--input-data', type=str, default=os.environ.get('SM_CHANNEL_INPUT'))
    parser.add_argument('--output-data', type=str, default=os.environ.get('SM_CHANNEL_OUTPUT'))
    return parser.parse_args()

def model_fn(model_dir):
    logger.info("Loading model from: ", model_dir)
    # log the files in the model directory
    for root, dirs, files in os.walk(model_dir):
        for file in files:
            logger.info(root, dirs, file)
    model_path = os.path.join(model_dir, 'isotonic_model.pkl')
    logger.info("Model path: ", model_path)
    model = joblib.load(model_path)
    return model

def input_fn(input_data, request_content_type):
    # Check if the content type is text/csv with optional charset
    if request_content_type.startswith("text/csv"):
        logger.info("Processing input data as CSV with content type: %s", request_content_type)
        logger.info("Raw input data bytes: %s", input_data[:100])
        # Read the input data from a CSV string
        input_df = pd.read_csv(io.BytesIO(input_data), header=None)
        logger.info("Input data shape: %s", input_df.shape)
        return input_df
    else:
        raise ValueError(f"Unsupported content type: {request_content_type}")