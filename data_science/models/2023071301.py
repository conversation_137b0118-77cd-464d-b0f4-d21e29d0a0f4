from datetime import <PERSON><PERSON><PERSON>
import os
from sqlalchemy import func
import sys
import pandas as pd

append_path = os.getcwd().replace("/web", "") + "/web"
sys.path.append(append_path)
from db import cart, click, db, page_activity, page_view, store, user_session

def prep_data_for_model(train_or_predict, time_cutoff, lookback_weeks=4, start_time=0, predict_session_id=None):
    session_record = user_session.query.filter_by(uuid=predict_session_id).first()
    
    # Click data
    click_results = click.query.with_entities(click.page_view_id, page_view.user_session_id, func.count()).\
        outerjoin(page_view, page_view.uuid == click.page_view_id).\
        outerjoin(user_session, page_view.user_session_id == user_session.uuid).\
        filter(user_session.uuid == predict_session_id).\
        group_by(click.page_view_id, page_view.user_session_id).all()
    
    click_dict = dict()
    for click_record in click_results:
        if click_record.user_session_id not in click_dict:
            click_dict[click_record.user_session_id] = 0
        click_dict[click_record.user_session_id] += click_record[2]
    
    # Mouse movement and scroll data
    activity_results = page_activity.query.with_entities(
            page_activity.page_view_id,
            page_view.user_session_id,
            func.sum(page_activity.mouse_move),
            func.sum(page_activity.scroll),
            func.sum(page_activity.dwell_time)
        ).\
        outerjoin(page_view, page_view.uuid == page_activity.page_view_id).\
        outerjoin(user_session, page_view.user_session_id == user_session.uuid).\
        filter(user_session.uuid == predict_session_id).\
        group_by(page_activity.page_view_id, page_view.user_session_id).all()
    
    mouse_dict = dict()
    scroll_dict = dict()
    dwell_time_dict = dict()
    for activity_record in activity_results:
        if activity_record.user_session_id not in mouse_dict:
            mouse_dict[activity_record.user_session_id] = 0
            scroll_dict[activity_record.user_session_id] = 0
            dwell_time_dict[activity_record.user_session_id] = 0
        mouse_dict[activity_record.user_session_id] += activity_record[2]
        scroll_dict[activity_record.user_session_id] += activity_record[3]
        dwell_time_dict[activity_record.user_session_id] += activity_record[4]
    
    # Page views
    page_view_results = page_view.query.with_entities(page_view.user_session_id, func.count()).\
        outerjoin(user_session, page_view.user_session_id == user_session.uuid).\
        filter(user_session.uuid == predict_session_id).\
        group_by(page_view.user_session_id).all()
    
    page_view_dict = dict()
    for page_view_record in page_view_results:
        page_view_dict[page_view_record.user_session_id] = page_view_record[1]
    
    # Build our feature matrix
    
    # basic session stats
    new_input = [
        session_record.time,
        session_record.ad_google or False,
        session_record.ad_bing or False,
        session_record.ad_facebook or False,
        session_record.mobile or False,
        session_record.screen_width or 0
    ]
    
    # Browser
    if session_record.browser == 'Chrome':
        new_input.extend([1, 0, 0, 0, 0])
    elif session_record.browser == 'Safari':
        new_input.extend([0, 1, 0, 0, 0])
    elif session_record.browser == 'Microsoft Edge':
        new_input.extend([0, 0, 1, 0, 0])
    elif session_record.browser == 'Firefox':
        new_input.extend([0, 0, 0, 1, 0])
    else:
        new_input.extend([0, 0, 0, 0, 1])
    
    # Timezone
    if session_record.local_timezone == '-04:00':
        new_input.extend([1, 0, 0, 0, 0, 0, 0, 0, 0])
    elif session_record.local_timezone == '-07:00':
        new_input.extend([0, 1, 0, 0, 0, 0, 0, 0, 0])
    elif session_record.local_timezone == '-05:00':
        new_input.extend([0, 0, 1, 0, 0, 0, 0, 0, 0])
    elif session_record.local_timezone == '-06:00':
        new_input.extend([0, 0, 0, 1, 0, 0, 0, 0, 0])
    elif session_record.local_timezone == '+00:00':
        new_input.extend([0, 0, 0, 0, 1, 0, 0, 0, 0])
    elif session_record.local_timezone == '+02:00':
        new_input.extend([0, 0, 0, 0, 0, 1, 0, 0, 0])
    elif session_record.local_timezone == '+01:00':
        new_input.extend([0, 0, 0, 0, 0, 0, 1, 0, 0])
    elif session_record.local_timezone == '+08:00':
        new_input.extend([0, 0, 0, 0, 0, 0, 0, 1, 0])
    else:
        new_input.extend([0, 0, 0, 0, 0, 0, 0, 0, 1])
    
    # Local time (hour)
    unaware_datetime = session_record.local_time
    if "+" in session_record.local_timezone:
        local_datetime = unaware_datetime + timedelta(hours=float(session_record.local_timezone.replace("+", "").replace(":00", "").replace(":30", ".5")))
    else:
        local_datetime = unaware_datetime - timedelta(hours=float(session_record.local_timezone.replace("-", "").replace(":00", "").replace(":30", ".5")))
    
    if local_datetime.hour >= 0 and local_datetime.hour < 3:
        new_input.extend([1, 0, 0, 0, 0, 0, 0, 0])
    elif local_datetime.hour >= 3 and local_datetime.hour < 6:
        new_input.extend([0, 1, 0, 0, 0, 0, 0, 0])
    elif local_datetime.hour >= 6 and local_datetime.hour < 9:
        new_input.extend([0, 0, 1, 0, 0, 0, 0, 0])
    elif local_datetime.hour >= 9 and local_datetime.hour < 12:
        new_input.extend([0, 0, 0, 1, 0, 0, 0, 0])
    elif local_datetime.hour >= 12 and local_datetime.hour < 15:
        new_input.extend([0, 0, 0, 0, 1, 0, 0, 0])
    elif local_datetime.hour >= 15 and local_datetime.hour < 18:
        new_input.extend([0, 0, 0, 0, 0, 1, 0, 0])
    elif local_datetime.hour >= 18 and local_datetime.hour < 21:
        new_input.extend([0, 0, 0, 0, 0, 0, 1, 0])
    elif local_datetime.hour >= 21 and local_datetime.hour < 24:
        new_input.extend([0, 0, 0, 0, 0, 0, 0, 1])
    else:
        new_input.extend([0, 0, 0, 0, 0, 0, 0, 0])
    
    # OS
    if session_record.os == 'iOS':
        new_input.extend([1, 0, 0, 0, 0, 0])
    elif session_record.os == 'Windows':
        new_input.extend([0, 1, 0, 0, 0, 0])
    elif session_record.os == 'Mac OS X':
        new_input.extend([0, 0, 1, 0, 0, 0])
    elif session_record.os == 'Android':
        new_input.extend([0, 0, 0, 1, 0, 0])
    elif session_record.os == 'Linux':
        new_input.extend([0, 0, 0, 0, 1, 0])
    else:
        new_input.extend([0, 0, 0, 0, 0, 1])
    
    # Country
    if session_record.country_name == 'United States of America':
        new_input.extend([1, 0])
    else:
        new_input.extend([0, 1])
    
    # utm_medium
    if session_record.utm_medium == 'ppc':
        new_input.extend([1, 0, 0, 0, 0, 0, 0])
    elif session_record.utm_medium == 'email':
        new_input.extend([0, 1, 0, 0, 0, 0, 0])
    elif session_record.utm_medium == 'social':
        new_input.extend([0, 0, 1, 0, 0, 0, 0])
    elif session_record.utm_medium == 'product_sync':
        new_input.extend([0, 0, 0, 1, 0, 0, 0])
    elif session_record.utm_medium == 'cpc':
        new_input.extend([0, 0, 0, 0, 1, 0, 0])
    elif session_record.utm_medium in [None, '']:
        new_input.extend([0, 0, 0, 0, 0, 1, 0])
    else:
        new_input.extend([0, 0, 0, 0, 0, 0, 1])
    
    # number of page views
    new_input.append(page_view_dict[session_record.uuid] or 0)
    
    # number of clicks
    new_input.append(click_dict.get(session_record.uuid) or 0)
    
    # mouse movement
    new_input.append(mouse_dict.get(session_record.uuid) or 0)
    
    # scroll
    new_input.append(scroll_dict.get(session_record.uuid) or 0)
    
    joined_table = pd.DataFrame(new_input, index=[str(i) for i in range(len(new_input))]).T
    
    return joined_table