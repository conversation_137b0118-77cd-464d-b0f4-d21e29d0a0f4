import os
import sys
sys.path.append(os.getcwd().replace("/data_science/models", "").replace("/data_science", "").replace("/web/modules", "").replace("/web", "") + "/web")
sys.path.append(os.getcwd().replace("/data_science/models", "").replace("/data_science", "").replace("/web/modules", "").replace("/web", "") + "/data_science")
from db import cart, click, db, page_activity, page_keystrokes, page_view, store, user_session

import datetime
import numpy as np
import pandas as pd
from sqlalchemy import alias, case, cast, func, inspect, or_
from pandas.api.types import CategoricalDtype

from ds_helpers import assign_true_local_time, assign_local_tz_mins, extract_mean_device_year, add_missing_cart_change_types, add_any_missing_page_types, flatten_cols, get_urls_domain, merge_time_since_most_recent_cart_change_of_type, get_browser_name, remove_top_level_column, check_string_has_email_regex
from ds_constants import REQUIRED_PAGE_TYPES, BOT_BROWSERS, TEMP_CODE_SUBSTRINGS


#### Like 2024050101.py but including only `holdout` sessions in training sets (as opposed to only excluding vandra_conversions) ####
#### See Linear VAN-253: https://linear.app/vandra/issue/VAN-253/train-new-intent-model-excluding-any-vandra-shown-as-well-as-vandra ####

def prep_data_for_model(train_or_predict, time_cutoff, latest_time_for_run=9999999999, number_of_user_sessions=150000, start_time=0, end_time=9999999999, lookback_weeks=6, predict_session_id=None):
    if train_or_predict not in ["TRAIN", "PREDICT"]:
        raise ValueError("train_or_predict must be either 'TRAIN' or 'PREDICT'")
    
    if number_of_user_sessions <= 0:
        raise ValueError("number_of_user_sessions arg must be positive")
    
    store_query = store.query.filter(store.use_in_datascience == True).all()
    real_store_ids = [x.uuid for x in store_query]
    del store_query
    
    user_session_alias = alias(user_session)
    
    customer_recent_session_count_subquery = (
        db.session.query(
            user_session.uuid,
            func.count().label('customer_recent_session_count')
        )
        .filter(
            ((user_session.uuid == predict_session_id)|(train_or_predict == 'TRAIN')),
            user_session.time - cast(datetime.timedelta(weeks=lookback_weeks).total_seconds(), db.BigInteger) <= user_session_alias.c.time,
            user_session.time > user_session_alias.c.time,
            user_session.customer_cookie == user_session_alias.c.customer_cookie
            )
        .group_by(user_session.uuid)
        .correlate(user_session_alias)
        .subquery()
    )
    
    used_discount_code_condition = case((user_session_alias.c.conversion_discount_code.isnot(None), 1), else_=0)
    
    customer_recent_conversion_count_subquery = (
        db.session.query(
            user_session.uuid,
            func.sum(used_discount_code_condition).label('customer_recent_conversion_count_with_discount_code'),
            func.sum(1 - used_discount_code_condition).label('customer_recent_conversion_count_without_discount_code')
        )
        .filter(
            ((user_session.uuid == predict_session_id)|(train_or_predict == 'TRAIN')),
            user_session.time - cast(datetime.timedelta(weeks=lookback_weeks).total_seconds(), db.BigInteger) <= user_session_alias.c.time,
            user_session.time > user_session_alias.c.time,
            user_session.customer_cookie == user_session_alias.c.customer_cookie,
            user_session_alias.c.conversion == True
            )
        .group_by(user_session.uuid)
        .correlate(user_session_alias)
        .subquery()
    )
    
    # Pulling user_session data
    user_session_main_subquery_pre = (user_session.query
        .with_entities(func.floor(user_session.device_memory).label('device_memory'),
                       *[c for c in inspect(user_session).columns if c.name not in ['browser_version', 'os_version', 'cart_size', 'cart_token', 'customer_cookie', 'device_memory']])
        .filter(user_session.store_id.in_(real_store_ids))
        .filter(user_session.os != 'Search Bot')
        .filter(~user_session.browser.in_(BOT_BROWSERS))
        .filter(user_session.vandra_conversion == False)
        .filter(((user_session.holdout == True) & (user_session.vandra_shown == False))|(train_or_predict == 'PREDICT'))
        .filter(~or_(*[user_session.conversion_discount_code.ilike('%' + substr + '%') for substr in TEMP_CODE_SUBSTRINGS]) | user_session.conversion_discount_code.is_(None))
        .filter(user_session.time >= start_time)
        .filter(user_session.time <= end_time)
        .filter(~(user_session.domain.contains("shopifypreview") | user_session.domain.contains("myshopify")))
        .filter(user_session.time <= latest_time_for_run) #will currently cause some duplicates for train_or_predict='TRAIN', which will be removed before training in train_model.py
        .order_by(user_session.time.desc())
    )
    
    del real_store_ids
    
    if train_or_predict != "TRAIN":
        user_session_main_subquery = (user_session_main_subquery_pre
        .filter(user_session.uuid == predict_session_id)
        .limit(number_of_user_sessions)
        .subquery()
    )
    else:
        user_session_main_subquery = (user_session_main_subquery_pre
        .limit(number_of_user_sessions)
        .subquery()
    )
    
    user_session_query = (db.session.query(
            user_session_main_subquery,
            customer_recent_session_count_subquery.c.customer_recent_session_count,
            customer_recent_conversion_count_subquery.c.customer_recent_conversion_count_with_discount_code,
            customer_recent_conversion_count_subquery.c.customer_recent_conversion_count_without_discount_code
        )
        .outerjoin(
            customer_recent_session_count_subquery,
            user_session_main_subquery.c.uuid == customer_recent_session_count_subquery.c.uuid
        )
        .outerjoin(
            customer_recent_conversion_count_subquery,
            user_session_main_subquery.c.uuid == customer_recent_conversion_count_subquery.c.uuid
        )
    )
    
    user_session_df_pre_raw = (pd.read_sql(user_session_query.statement, user_session_query.session.connection(),
                                        dtype={'time':'int32',
                                               'device_memory':'Int16',
                                               'domain':'category',
                                               'latitude':'float32',
                                               'longitude':'float32',
                                               'screen_height': 'Float32',
                                               'screen_width': 'Float32'})
    )
    
    min_user_session_time_processed = user_session_df_pre_raw['time'].min() if len(user_session_df_pre_raw) >= number_of_user_sessions else start_time
    
    if len(user_session_df_pre_raw) < number_of_user_sessions and train_or_predict == "TRAIN":
        print(f"Only {len(user_session_df_pre_raw)} user sessions found, less than number_of_user_sessions={number_of_user_sessions}.")
    
    if len(user_session_df_pre_raw) == 0:
        print("No user sessions found.")
        min_user_session_time_processed = start_time
        return pd.DataFrame(), min_user_session_time_processed
    
    user_session_df_pre = (user_session_df_pre_raw
        .assign(
            browser= lambda df_: df_.browser.apply(get_browser_name),
            first_page_type=lambda df_: df_.page.str.lower().str.extract("/(checkouts/|cart|products/|collections/|pages/|blogs/)", expand=False).str.rstrip("/").fillna("otherpgtype")
        )
        .fillna({'customer_recent_session_count': 0,
                 'customer_recent_conversion_count_with_discount_code': 0,
                 'customer_recent_conversion_count_without_discount_code': 0})
        .astype({'browser': 'category', 'os': 'category', 'first_page_type': 'category'})
        .replace({col: {'' : None} for col in ["utm_campaign", "utm_medium", "utm_source", "utm_term", "utm_content"]})
        .pipe(assign_true_local_time)
        .pipe(assign_local_tz_mins)
        .pipe(lambda df_: df_.assign(mean_device_price=df_['price'].apply(lambda x: np.nanmean([float(val.strip()) if val.strip() != 'None' else np.nan for val in x.split(';')]) if x != '' and x is not None else np.nan)))
        .pipe(lambda df_: df_.assign(mean_device_year=df_['device'].apply(extract_mean_device_year)))
        .drop(columns=['local_time', 'price', 'device'])
        .query("~domain.str.contains('shopifypreview')")
        .query("~domain.str.contains('myshopify')")
        .astype({'mean_device_price': 'float16',
                 'mean_device_year': 'float16'})
    )
    
    del user_session_query
    
    if len(user_session_df_pre) == 0:
        print('No user sessions found for the given parameters.')
        return pd.DataFrame(), min_user_session_time_processed
    
    page_view_query = page_view.query.filter(page_view.user_session_id.in_(user_session_df_pre.uuid.tolist()))
    page_view_df_all_real_pre = (pd.read_sql(page_view_query.statement, page_view_query.session.connection(),
                                             dtype = {'time': 'int32',
                                                      'dwell_time': 'Int16',
                                                      'total_scroll': 'Int32',
                                                      'total_mouse_move': 'Int32',
                                                      'local_timezone': 'category',
                                                      'document_has_focus': 'boolean'})
        .query('(not page.str.lower().str.contains("checkouts/")) or page.str.lower().str.contains("/(cart|products/|collections/|pages/|blogs/)")') # Exclude page_views that appear to be from checkouts since we don't collect that data for most merchants
    )
    
    if len(page_view_df_all_real_pre) == 0:
        print('No non-checkout page_views for the given parameters')
        return pd.DataFrame(), min_user_session_time_processed
    
    page_view_df_all_real_pre = (page_view_df_all_real_pre
        .pipe(assign_true_local_time)
        .pipe(assign_local_tz_mins)
        .drop(columns="local_time")
        # for each user_session_id, calculate the min page_view timestamp and the cutoff timestamp and add both to the df
        .assign(min_timestamp=lambda df_: df_.groupby("user_session_id", group_keys=False)["time"].transform('min').astype('int32'),
                min_timestamp_plus_time_diff=lambda df_: df_["min_timestamp"] + time_cutoff)
        .sort_values(["user_session_id", "time"], ascending=True)
        .reset_index(drop=True)
        .fillna({"dwell_time": 0, "total_scroll": 0, "total_mouse_move": 0})
    )
    
    del page_view_query
    
    if len(page_view_df_all_real_pre) == 0:
        print('No page views found for the given user_session(s).')
        return pd.DataFrame(), min_user_session_time_processed
    
    # Will exclude the following sessions with multiple domains:
    # For TeeFury as of 2023-05-04, 29k out of 2.8M page_views have been somewhere other than TeeFury. Those 29k page views correspond to 22k sessions, with 0 conversions.
    # Bots?
    multi_domain_session_ids = set(
        page_view_df_all_real_pre
        .query('time < min_timestamp_plus_time_diff')  # only look at page_views before the cutoff
        .assign(domain=lambda df: df["page"].apply(get_urls_domain))
        .groupby('user_session_id')
        .agg({'domain': 'nunique'})
        .query('domain>1')
        .index
    )
    
    # A minority of sessions have >1000 page views. These are likely bots.
    session_ids_with_over_1k_page_views = set(
        page_view_df_all_real_pre
        .query('time < min_timestamp_plus_time_diff')
        .groupby('user_session_id')
        .agg(uuid_count=('uuid', 'count'))
        .query('uuid_count>1000')
        .index
    )
    
    user_session_df = (
        user_session_df_pre
        .query('uuid not in @multi_domain_session_ids')
        .query('uuid not in @session_ids_with_over_1k_page_views')
    )
    
    del user_session_df_pre
    del multi_domain_session_ids
    del session_ids_with_over_1k_page_views
    
    if len(user_session_df) == 0:
        print('No user sessions found for the given parameters.')
        return pd.DataFrame(), min_user_session_time_processed
    
    page_view_df_all_real = (
        page_view_df_all_real_pre.query('user_session_id.isin(@user_session_df.uuid)')
    )
    
    del page_view_df_all_real_pre
    
    if len(page_view_df_all_real) == 0:
        print('No page views found for the given user_session(s).')
        return pd.DataFrame(), min_user_session_time_processed

    # page views that start before the modeling cutoff time (some of actions
    # associated with these page views may still be after the cutoff time)
    page_view_df_within_time_range_pre = (page_view_df_all_real
        .query('time < min_timestamp_plus_time_diff')
        .reset_index(drop=True)
        .fillna({'dwell_time': 0})
    )
    
    # Pulling keystrokes data
    page_keystrokes_query = (page_keystrokes.query
        .filter(page_keystrokes.time >= start_time) # To speed up the query
        .filter(page_keystrokes.page_view_id.in_(page_view_df_within_time_range_pre.uuid.tolist()))
    )
    
    page_keystrokes_totals = (
        pd.read_sql(page_keystrokes_query.statement, page_keystrokes_query.session.connection())
        .sort_values(by = ['page_view_id', 'time']) # Sort by time to ensure the keystrokes are in the correct order (for concatenate next step)
        .fillna({'keys': ''}) # Replace NaNs (if any) with empty strings to avoid errors in the next steps
        .groupby('page_view_id', group_keys=False)
        .agg(page_view_keys=('keys', lambda x: ''.join(x)))
        .assign(
            count_keystrokes=lambda df_: df_['page_view_keys'].str.len(),
            email_entered=lambda df_: df_['page_view_keys'].apply(check_string_has_email_regex)
        )
        .drop(columns = ['page_view_keys'])
    )
    
    del page_keystrokes_query
    
    # Pulling page_activity data
    page_activity_query = (page_activity.query
        .filter(page_activity.page_view_id.in_(page_view_df_within_time_range_pre.uuid.tolist()))
        .filter((page_activity.dwell_time >= 0) & (page_activity.scroll >= 0) & (page_activity.mouse_move >= 0))
    )
    page_activity_df_pre = pd.read_sql(page_activity_query.statement, page_activity_query.session.connection(),
                                       dtype = {'time':'int32', 'dwell_time': 'Int16', 'scroll': 'Int32', 'mouse_move': 'Int32'})
    
    del page_activity_query
    
    page_activity_df = (page_activity_df_pre
        .merge(page_view_df_within_time_range_pre[['uuid', 'min_timestamp_plus_time_diff']], how='left', left_on='page_view_id', right_on='uuid')
        .drop(columns='uuid_y')
        .rename(columns={'uuid_x': 'uuid'})
        .query('time - dwell_time < min_timestamp_plus_time_diff')
        .assign(page_activity_still_ongoing=lambda df_: df_['time'] >= df_['min_timestamp_plus_time_diff'],
                start_time=lambda df_: df_.time - df_.dwell_time)
    )
    
    del page_activity_df_pre

    page_activity_totals = (page_activity_df
        .query('page_activity_still_ongoing == False')
        .groupby('page_view_id', group_keys=False)
        .agg(total_dwell_time=('dwell_time', 'sum'),
            total_scroll=('scroll', 'sum'),
            total_mouse_move=('mouse_move', 'sum'))
    )
    
    max_page_activity_start_times = (page_activity_df
        .groupby('page_view_id')
        ['start_time']
        .max()
        .rename('max_page_activity_start_time')
    )
    
    page_view_df_within_time_range = (page_view_df_within_time_range_pre
        .drop(columns=['dwell_time', 'total_scroll', 'total_mouse_move'])
        .set_index('uuid')
        .join(page_keystrokes_totals, how='left')
        .join(page_activity_totals, how='left')
        .fillna({
            'total_dwell_time': 0,
            'total_scroll': 0,
            'total_mouse_move': 0,
            'count_keystrokes': 0,
            'email_entered': False
        })
        .reset_index()
    )
    
    del page_view_df_within_time_range_pre
    
    known_multitab_sessions = set(
        page_view_df_within_time_range
        .assign(time_pv1_end=lambda df: df['time'] + df['total_dwell_time'])
        .merge(page_view_df_within_time_range,
            on='user_session_id',
            suffixes=('_pv1', '_pv2'))
        .query('uuid_pv1 != uuid_pv2')
        .query('time_pv2 > time_pv1 and time_pv2 < time_pv1_end')
        ['user_session_id']
    )
    
    # Pulling click data
    click_query = (click.query
        .with_entities(click.uuid, click.time, click.page_view_id)
        .filter(click.page_view_id.in_(page_view_df_all_real.uuid.tolist()))
    )
    
    click_df = pd.read_sql(click_query.statement, click_query.session.connection(),
                           dtype={'time': 'int32'})
    
    del click_query

    # For each page_view that started before the cutoff time, count the number
    # of clicks that occurred before the cutoff time and thus should be included
    # in the modeling
    if len(click_df) == 0:
        # Handle sessions with no clicks recorded
        click_counts = (pd.DataFrame({'page_view_id': page_view_df_within_time_range['uuid'].unique(), 'click_count': 0})
            .set_index('page_view_id')
        )
    
    else:
        # For each page_view that started before the cutoff time, count the number of clicks that occurred before the cutoff time and thus should be included in the modeling
        click_counts = (page_view_df_within_time_range[['uuid', 'min_timestamp_plus_time_diff']]
            .rename(columns={'uuid': 'page_view_id'})
            .merge(click_df, on='page_view_id', how='left', validate='one_to_many')
            .assign(count_click_flag=lambda df_: df_.time < df_.min_timestamp_plus_time_diff)
            .groupby('page_view_id')
            .agg({'count_click_flag': 'sum'})
            .rename(columns={'count_click_flag': 'click_count'})
            .astype('int16')
        )
    
    cart_query = (cart.query.with_entities(cart.uuid, cart.user_session_id,
        cart.time, cart.cart_change_type, cart.item_count, cart.items_subtotal_price,
        cart.original_total_price, cart.total_discount, cart.total_price, cart.total_weight)
        .filter(cart.user_session_id.in_(user_session_df.uuid.tolist()))
        .filter(cart.cart_change_type != 'none')
        .filter((cart.cart_change_type != 'create') | (cart.item_count > 0))
    )
    
    cart_df_pre = (pd.read_sql(cart_query.statement, cart_query.session.connection(),
                               dtype = {'time': 'int32',
                                        'cart_change_type':'category',
                                        'item_count': 'int32',
                                        'items_subtotal_price': 'float32',
                                        'original_total_price': 'float32',
                                        'total_discount': 'float32',
                                        'total_price': 'float32',
                                        'total_weight': 'float32'})
        .merge(user_session_df[['uuid', 'time']].rename(columns={'uuid': 'user_session_id'}), on='user_session_id', how='left', validate='many_to_one', suffixes=('_cart', '_user_session'))
        .query('time_cart < time_user_session + @time_cutoff')
    )

    del cart_query
    
    if len(cart_df_pre) == 0:
        cart_df = (pd.DataFrame({
            'cart_change_create_count': 0,
            'cart_change_add_count': 0,
            'cart_change_remove_count': 0,
            'cart_change_clear_count': 0,
            'most_recent_cart_change_type': None,
            'most_recent_item_count': None,
            'most_recent_items_subtotal_price': None,
            'most_recent_original_total_price': None,
            'most_recent_total_discount': None,
            'most_recent_total_price': None,
            'most_recent_total_weight': None,
            'most_recent_time_cart_create': None,
            'most_recent_time_cart_add': None,
            'most_recent_time_cart_remove': None,
            'most_recent_time_cart_clear': None,
            'time_since_most_recent_cart_change_add': None,
            'time_since_most_recent_cart_change_remove': None,
            'time_since_most_recent_cart_change_clear': None,
            'time_since_most_recent_cart_change_create': None
        }, index=[predict_session_id])
            .rename_axis('user_session_id', axis='index')
        ).astype({'cart_change_create_count': 'Int16',
                  'cart_change_add_count': 'Int16',
                  'cart_change_remove_count': 'Int16',
                  'cart_change_clear_count': 'Int16',
                  'most_recent_cart_change_type': CategoricalDtype(categories=['add', 'clear', 'create', 'remove'], ordered=False),
                  'most_recent_item_count': 'Int32',
                  'most_recent_items_subtotal_price': 'float32',
                  'most_recent_original_total_price': 'float32',
                  'most_recent_total_discount': 'float32',
                  'most_recent_total_price': 'float32',
                  'most_recent_total_weight': 'float32',
                  'most_recent_time_cart_create': 'Int32',
                  'most_recent_time_cart_add': 'Int32',
                  'most_recent_time_cart_remove': 'Int32',
                  'most_recent_time_cart_clear': 'Int32',
                  'time_since_most_recent_cart_change_add': 'Int16',
                  'time_since_most_recent_cart_change_remove': 'Int16',
                  'time_since_most_recent_cart_change_clear': 'Int16',
                  'time_since_most_recent_cart_change_create': 'Int16'})
    else:
        cart_df = (
            cart_df_pre.pivot_table(index='user_session_id',
                                  columns='cart_change_type',
                                  aggfunc={'cart_change_type': 'count'},
                                  fill_value=0)
            .rename(columns={'create': 'cart_change_create_count',
                            'add': 'cart_change_add_count',
                            'remove': 'cart_change_remove_count',
                            'clear': 'cart_change_clear_count'})
            .pipe(remove_top_level_column)
            .rename_axis(None, axis=1)
            .pipe(add_missing_cart_change_types, ['cart_change_create_count', 'cart_change_add_count', 'cart_change_remove_count', 'cart_change_clear_count'])
            [['cart_change_create_count', 'cart_change_add_count', 'cart_change_remove_count', 'cart_change_clear_count']]
            .astype('Int16') #not null here, but will contain nulls after the join in page_view_df_aggd assignment
            # .join(cart_df_pre.loc[cart_df_pre.groupby('user_session_id')['time_cart'].idxmax()].set_index(['user_session_id', 'time_user_session']).add_prefix('most_recent_').reset_index(level=1), how='left', validate='one_to_one')
            # <Replacing previous line with this to work around apparent pandas bug; may revert in future>
            .reset_index()
            .merge(cart_df_pre.loc[cart_df_pre.groupby('user_session_id')['time_cart'].idxmax()].set_index(['user_session_id', 'time_user_session']).add_prefix('most_recent_').reset_index(), how='left', on='user_session_id', validate='one_to_one')
            # </Replacing previous line with this to work around apparent pandas bug; may revert in future>
            .astype({'most_recent_item_count': 'Int32'}) #not null here, but will contain nulls after the join in page_view_df_aggd assignment
            .pipe(merge_time_since_most_recent_cart_change_of_type, cart_df_pre, 'create')
            .pipe(merge_time_since_most_recent_cart_change_of_type, cart_df_pre, 'add')
            .pipe(merge_time_since_most_recent_cart_change_of_type, cart_df_pre, 'remove')
            .pipe(merge_time_since_most_recent_cart_change_of_type, cart_df_pre, 'clear')
            .assign(
                time_since_most_recent_cart_change_add=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_add,
                time_since_most_recent_cart_change_remove=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_remove,
                time_since_most_recent_cart_change_clear=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_clear,
                time_since_most_recent_cart_change_create=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_create
            )
            .astype({'most_recent_time_cart_create':'Int32',
                     'most_recent_time_cart_add':'Int32',
                     'most_recent_time_cart_remove':'Int32',
                     'most_recent_time_cart_clear':'Int32',
                     'time_since_most_recent_cart_change_add':'Int16',
                     'time_since_most_recent_cart_change_remove':'Int16',
                     'time_since_most_recent_cart_change_clear':'Int16',
                     'time_since_most_recent_cart_change_create':'Int16'})
            .drop(columns=['time_user_session', 'most_recent_uuid', 'most_recent_time_cart'])
        )
        
    del cart_df_pre
    
    user_sessions_lasting_beyond_cutoff = (
        page_view_df_all_real
        .rename(columns={'uuid': 'page_view_id'})
        .assign(max_timestamp_known_on_page=lambda df_: df_[['time', 'dwell_time']].sum(axis=1))
        .set_index('page_view_id')
        .join(click_df.set_index('page_view_id'), how='left', validate='one_to_many', lsuffix='_page_view', rsuffix='_click')
        .join(page_activity_df.groupby('page_view_id')['start_time'].max().rename('max_page_activity_start_time'), how='left')
        .reset_index()
        .assign(max_known_time=lambda df_: df_[['max_timestamp_known_on_page', 'time_click', 'max_page_activity_start_time']].max(axis=1))
        .groupby('user_session_id')
        .agg({'max_known_time': 'max',
            'min_timestamp_plus_time_diff': 'min'})  # min_timestamp_plus_time_diff is the same for all rows in each group
        .query('max_known_time >= min_timestamp_plus_time_diff')
        .index
        .tolist()
    )
    
    del page_view_df_all_real
    del click_df
    del page_activity_df
    
    # Aggregate page view data to the user_session level, only including actions that occurred before the modeling cutoff time, and only including sessions that lasted beyond the cutoff time
    page_view_df_aggd_pre = (
        page_view_df_within_time_range
        # May need to address code divergence here:
        # Prediction data may not have enough observations to qualify as crossing
        # the treshold, so we let them through no matter what
        .pipe(lambda x, user_sessions_lasting_beyond_cutoff=user_sessions_lasting_beyond_cutoff: x.query('user_session_id.isin(@user_sessions_lasting_beyond_cutoff)') if train_or_predict == 'TRAIN' else x)
        .merge(click_counts, left_on='uuid', right_index=True, validate='one_to_one')
        .fillna({'click_count': 0})
        .assign(page_lower=page_view_df_within_time_range.page.str.lower(),
                page_type=lambda df_: df_.page_lower.str.extract('/(cart|products/|collections/|pages/|blogs/)', expand=False).str.rstrip('/').fillna('otherpgtype').astype('category'))
        .drop(columns=['page_lower'])
    )
    
    del page_view_df_within_time_range
    del click_counts
    
    page_view_df_aggd = (page_view_df_aggd_pre
        .groupby(['user_session_id', 'page_type'], group_keys=False)
        .agg({
            'total_dwell_time': 'sum',
            'total_scroll': 'sum',
            'total_mouse_move': 'sum',
            'page': ['count', 'nunique'],
            'click_count': [('sum', lambda x: x.sum(min_count=1))],
            'count_keystrokes': 'sum',
            'email_entered': 'any'
        })
        .pipe(flatten_cols)
        .pipe(add_any_missing_page_types, required_page_types=REQUIRED_PAGE_TYPES)
        .fillna({'page_count': 0,
                 'page_nunique': 0})
        .pipe(lambda df_: df_.astype({col:'int16' for col in df_.columns if 'page_count' in col or 'page_nunique' in col} |
                                     {col:'Int16' for col in df_.columns if 'click_count' in col or 'cart_change' in col}))
        .assign(
            dwell_time_nan_if_0=lambda df_: df_.total_dwell_time_sum.replace(0, np.nan),
            total_scroll_per_sec=lambda df_: df_.total_scroll_sum / df_.dwell_time_nan_if_0,
            total_mouse_move_per_sec=lambda df_: df_.total_mouse_move_sum / df_.dwell_time_nan_if_0
        )
        [['total_dwell_time_sum', 'total_scroll_per_sec', 'total_mouse_move_per_sec', 'page_count', 'page_nunique', 'click_count_sum', 'count_keystrokes_sum', 'email_entered_any']]
        .pipe(lambda df_: df_.unstack() if 'page_type' in df_.index.names else df_)
        .pipe(flatten_cols)
        .pipe(lambda df_: df_.assign(**{col: df_[col].fillna(0) for col in df_.columns if 'page_count' in col or 'page_nunique' in col or 'dwell_time' in col}))
        .reset_index()
        .assign(is_multitab_session=lambda df_: df_['user_session_id'].isin(known_multitab_sessions))
        .merge(cart_df, how='left', on='user_session_id', suffixes=(None, '_cart'))
    )
    
    del page_view_df_aggd_pre
    del known_multitab_sessions
    
    feature_list = ['screen_width', 'screen_height', 'browser', 'mobile', 'os',
        'device_memory', 'mean_device_price', 'mean_device_year', 'language',
        'referrer', 'country_name', 'region_name', 'latitude', 'longitude',
        'first_page_type', 'store_id', 'ad_google',
        'ad_facebook', 'ad_tiktok', 'ad_bing', 'ad_doubleclick',
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content',
        'utm_term', 'true_local_time', 'local_timezone_offset_mins',
        'customer_recent_session_count', 'customer_recent_conversion_count_with_discount_code',
        'customer_recent_conversion_count_without_discount_code', 'is_multitab_session',
        'cart_change_create_count',
        'cart_change_add_count',
        'cart_change_remove_count',
        'cart_change_clear_count',
        'most_recent_cart_change_type',
        'most_recent_item_count',
        'most_recent_items_subtotal_price',
        'most_recent_original_total_price',
        'most_recent_total_discount',
        'most_recent_total_price',
        'most_recent_total_weight',
        'time_since_most_recent_cart_change_add',
        'time_since_most_recent_cart_change_remove',
        'time_since_most_recent_cart_change_clear',
        'time_since_most_recent_cart_change_create',
        'conversion',  # target feature for training, unused for prediction
        'time',  # not used (included in H2O dataset uploads for debugging)
        'user_session_id'  # not used (included in H2O dataset uploads for debugging)
    ] + [f'{c}_{page_type}' for c in ['total_dwell_time_sum', 'total_scroll_per_sec',
                                      'total_mouse_move_per_sec', 'page_count',
                                      'page_nunique', 'click_count_sum',
                                      'count_keystrokes_sum', 'email_entered_any']
         for page_type in REQUIRED_PAGE_TYPES]
    
    # Merge page view data with user session data
    joined_table = (user_session_df.merge(page_view_df_aggd, left_on='uuid', right_on='user_session_id', how='right')
        .loc[:, feature_list]
    )
    
    del user_session_df
    del page_view_df_aggd
    del feature_list
    
    return joined_table, min_user_session_time_processed