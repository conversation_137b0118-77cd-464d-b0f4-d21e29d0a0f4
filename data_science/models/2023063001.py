import os
import sys
append_path = os.getcwd().replace("/web", "") + "/web"
sys.path.append(append_path)
from db import cart, click, db, page_activity, page_view, store, user_session

import datetime
from flask import Flask
import numpy as np
import pandas as pd
from sqlalchemy import or_, func, cast, alias
from urllib.parse import parse_qs, urlparse
import re

# Helper functions for feature engineering

'''def get_local_time(local_time, local_timezone):
    true_local_time = None
    if local_time is not None:
        true_local_time = datetime.datetime.fromisoformat(
            local_time.replace("+00", "").replace(" ", "T") + local_timezone
        )
    
    local_timezone_offset_mins = None
    if local_timezone not in [None, ""]:
        sign = -1 if local_timezone[0] == '-' else 1
        hours = local_timezone.split(":")[0]
        minutes = local_timezone.split(":")[1]
        local_timezone_offset_mins = sign * (hours * 60 + minutes)
    
    return true_local_time, local_timezone_offset_mins'''

def assign_true_local_time(df):
    def get_true_local_time(row):
        if row["local_time"] is None:
            return None
        return row["local_time"].tz_convert(row["local_timezone"]).tz_localize(tz=None)
    return df.assign(true_local_time=lambda df_: df_.apply(get_true_local_time, axis=1))
    
def convert_one_local_tz_to_mins(str_tz):
    if not str_tz:
        return None
    sign = -1 if str_tz[0] == '-' else 1
    hours, minutes = map(int, str_tz[1:].split(':'))
    return sign * (hours * 60 + minutes)
    
def assign_local_tz_mins(df):
    return df.assign(local_timezone_offset_mins=df['local_timezone'].apply(convert_one_local_tz_to_mins))
    
def extract_mean_device_year(text):
    if not text:
        return None
    year_pattern = r"\b(?<!\d)(19[7-9]\d|20\d{2})(?!\d)\b"
    matches = re.findall(year_pattern, text)
    current_year = datetime.datetime.now().year
    return np.mean([int(device_year) for device_year in matches if int(device_year) <= current_year and int(device_year) >= 1970])
    
def calc_groups_min_timestamp(group_df, time_diff_in_seconds, filter):
    min_timestamp = group_df['time'].min()
    
    if filter:
        return group_df[group_df['time'] - min_timestamp <= time_diff_in_seconds] \
            .assign(min_timestamp=min_timestamp,
                    min_timestamp_plus_time_diff=min_timestamp + time_diff_in_seconds)
    else:
        return group_df \
            .assign(min_timestamp=min_timestamp,
                    min_timestamp_plus_time_diff=min_timestamp + time_diff_in_seconds)

def add_any_missing_page_types(df, required_page_types):
    return df.reindex(pd.MultiIndex.from_product([df.index.levels[0], required_page_types], names=df.index.names))

def flatten_cols(df):
    new_cols = ['_'.join(map(str, vals)) for vals in df.columns.to_flat_index()]
    new_df = df.copy()
    new_df.columns = new_cols
    return new_df

def get_urls_domain(url):
    url = (url.replace("http://", "").replace("https://", "")
        .replace("www.", "").replace(".myshopify", "").replace(".com", ""))
    return url.split("/")[0]

def join_time_since_most_recent_cart_change_of_type(df, cart_df_pre, cart_change_type):
    return df.join(cart_df_pre
        .loc[cart_df_pre
            .query("cart_change_type == @cart_change_type")
            .groupby("user_session_id")["time_cart"].idxmax(),
            ["user_session_id", "time_user_session"]]
        .rename(columns={"time_user_session": f'most_recent_time_cart_{cart_change_type}'})
        .set_index(["user_session_id"])
        .astype({f'most_recent_time_cart_{cart_change_type}': "Int64"}),
        how="left", validate="one_to_one")

def prep_data_for_model(train_or_predict, time_cutoff, lookback_weeks=2, start_time=0, predict_session_id=None):
    if train_or_predict not in ["TRAIN", "PREDICT"]:
        print("Invalid input")
        return False
    
    store_query = store.query.filter(~or_(*[store.url.contains(test_substring) for test_substring in ["test", "vandra", "rothbaum"]])).all()
    real_store_ids = [x.uuid for x in store_query]
    
    # 0 conversions as of 2023-05-04
    bot_browsers = [
        "+http://ahrefs.com/robot",
        "(+http://www.facebook.com",
        "+https://headline.com/legal",
        "+http://www.baidu.com/search",
        "(+http://www.google.com",
        "Mozilla/5.0 (compatible; Dataprovider.com)",
        "+http://www.semrush.com",
        "+http://cincrawdata.net/bot"
    ]
    
    # Pulling user_session data

    user_session_alias = alias(user_session)
    
    customer_recent_session_count_subquery = \
        db.session.query(
            user_session.uuid,
            func.count().label('customer_recent_session_count')
        ).\
        filter(
            ((user_session.uuid == predict_session_id)|(train_or_predict == 'TRAIN')),
            user_session.time - cast(datetime.timedelta(weeks=lookback_weeks).total_seconds(), db.BigInteger) <= user_session_alias.c.time,
            user_session.time > user_session_alias.c.time,
            user_session.customer_cookie == user_session_alias.c.customer_cookie
        ).\
        group_by(user_session.uuid).\
        correlate(user_session_alias).\
        subquery()
    
    customer_recent_conversion_count_subquery = \
        db.session.query(
            user_session.uuid,
            func.count().label('customer_recent_conversion_count')
        ).\
        filter(
            ((user_session.uuid == predict_session_id)|(train_or_predict == 'TRAIN')),
            user_session.time - cast(datetime.timedelta(weeks=lookback_weeks).total_seconds(), db.BigInteger) <= user_session_alias.c.time,
            user_session.time > user_session_alias.c.time,
            user_session.customer_cookie == user_session_alias.c.customer_cookie,
            user_session_alias.c.conversion == True
        ).\
        group_by(user_session.uuid).\
        correlate(user_session_alias).\
        subquery()
    
    user_session_query = db.session.query(
            user_session,
            customer_recent_session_count_subquery.c.customer_recent_session_count,
            customer_recent_conversion_count_subquery.c.customer_recent_conversion_count).\
        filter(user_session.store_id.in_(real_store_ids)).\
        filter(user_session.os != 'Search Bot').\
        filter(~user_session.browser.in_(bot_browsers)).\
        filter(user_session.vandra_conversion == False).\
        filter(user_session.time >= start_time).\
        filter(~(user_session.domain.contains("shopifypreview") | user_session.domain.contains("myshopify"))).\
        outerjoin(
            customer_recent_session_count_subquery,
            user_session.uuid == customer_recent_session_count_subquery.c.uuid
        ).\
        outerjoin(
            customer_recent_conversion_count_subquery,
            user_session.uuid == customer_recent_conversion_count_subquery.c.uuid
        )
    
    if train_or_predict != "TRAIN":
        user_session_query = user_session_query.filter(user_session.uuid == predict_session_id)
    
    '''user_sessions = user_session_query.all()
    
    user_session_df_pre = pd.DataFrame()
    user_session_df_pre.columns = [a for a in dir(user_sessions[0]) if not a.startswith('__')] + \
        ["page_query", "first_page_type", "true_local_time", "local_timezone_offset_mins"]
    
    session_id_list = []
    for user_session_record in user_sessions:
        session_id_list.append(user_session_record.uuid)
        insert_row = {}
        for attr in [a for a in dir(user_session_record) if not a.startswith('__')]:
            insert_row[attr] = getattr(user_session_record, attr)
        
        if "FBAN/FBIOS" in insert_row["browser"]:
            insert_row["browser"] = "Facebook Browser"
        elif insert_row["browser"] == "Microsft Edge":
            insert_row["browser"] = "Microsoft Edge"
        elif "Mozilla/5.0 (Macintosh" in insert_row["browser"]:
            insert_row["browser"] = "Mozilla5 Mac"
        
        insert_row["page_query"] = parse_qs(urlparse(insert_row["page"]).query)
        
        if "checkouts/" in insert_row["page"].lower():
            insert_row["first_page_type"] = "checkouts"
        elif "cart" in insert_row["page"].lower():
            insert_row["first_page_type"] = "cart"
        elif "products/" in insert_row["page"].lower():
            insert_row["first_page_type"] = "products"
        elif "collections/" in insert_row["page"].lower():
            insert_row["first_page_type"] = "collections"
        elif "pages/" in insert_row["page"].lower():
            insert_row["first_page_type"] = "pages"
        elif "blogs/" in insert_row["page"].lower():
            insert_row["first_page_type"] = "blogs"
        else:
            insert_row["first_page_type"] = "otherpgtype"
        
        for attr in ["utm_campaign", "utm_medium", "utm_source", "utm_term", "utm_content"]:
            if insert_row[attr] == "":
                insert_row[attr] = None
        
        insert_row["true_local_time"], insert_row["local_timezone_offset_mins"] = get_local_time(insert_row["local_time"], insert_row["local_timezone"])
        
        user_session_df_pre = user_session_df_pre.append(insert_row, ignore_index=True)'''
    
    try:
        user_session_df_pre = (pd.read_sql(user_session_query.statement, user_session_query.session.connection())
            .assign(
                browser=lambda df_: df_["browser"].apply(lambda x: np.where("FBAN/FBIOS" in x, "Facebook Browser", np.where(x == "Microsft Edge", "Microsoft Edge", np.where("Mozilla/5.0 (Macintosh" in x, "Mozilla5 Mac", x)))),
                page_query=lambda df_: df_.page.apply(urlparse).apply(lambda urlparsed: urlparsed.query).apply(parse_qs),
                first_page_type=lambda df_: df_.page.str.lower().str.extract("/(checkouts/|cart|products/|collections/|pages/|blogs/)", expand=False).str.rstrip("/").fillna("otherpgtype")
            )
            .fillna({'customer_recent_session_count': 0, 'customer_recent_conversion_count': 0})
            .replace({col: {'' : None} for col in ["utm_campaign", "utm_medium", "utm_source", "utm_term", "utm_content"]})
            .pipe(assign_true_local_time)
            .pipe(assign_local_tz_mins)
            .pipe(lambda df_: df_.assign(mean_device_price=df_['price'].apply(lambda x: np.nanmean([float(val.strip()) if val.strip() != 'None' else np.nan for val in x.split(';')]) if x != '' and x is not None else np.nan)))
            .pipe(lambda df_: df_.assign(mean_device_year=df_['device'].apply(extract_mean_device_year)))
            .drop(columns=["local_time", 'price', 'device'])
            .query("~domain.str.contains('shopifypreview')")
            .query("~domain.str.contains('myshopify')")
        )
    except ValueError as e:
        print(e)
        print("No session found")
        return False
    
    if user_session_df_pre.empty:
        print('No user sessions found for the given parameters.')
        return False
    
    '''page_view_query = page_view.query.filter(page_view.user_session_id.in_(session_id_list)).all()
    page_view_df_all_real_pre = pd.DataFrame()
    page_view_df_all_real_pre.columns = [a for a in dir(page_view_query[0]) if not a.startswith('__')] + \
        ["true_local_time", "local_timezone_offset_mins", "min_timestamp", "min_timestamp_plus_time_diff"]
    
    for page_view_record in page_view_query:
        insert_row = {}
        for attr in [a for a in dir(page_view_record) if not a.startswith('__')]:
            insert_row[attr] = getattr(page_view_record, attr)
        
        insert_row["true_local_time"], insert_row["local_timezone_offset_mins"] = get_local_time(insert_row["local_time"], insert_row["local_timezone"])
        
        del insert_row["local_time"]
        
        insert_row["min_timestamp"] = None
        insert_row["min_timestamp_plus_time_diff"] = insert_row["min_timestamp"] + time_cutoff'''
    
    page_view_query = page_view.query.filter(page_view.user_session_id.in_(user_session_df_pre.uuid.tolist()))
    page_view_df_all_real_pre = (pd.read_sql(page_view_query.statement, page_view_query.session.connection())
        .astype({"dwell_time": "Int64", "total_scroll": "float", "total_mouse_move": "float"})
        .pipe(assign_true_local_time)
        .pipe(assign_local_tz_mins)
        .drop(columns="local_time")
        .groupby("user_session_id", group_keys=False)
        .apply(calc_groups_min_timestamp, time_diff_in_seconds=time_cutoff, filter=False)  # for each user_session_id, calculate the min page_view timestamp and the cutoff timestamp and add both to the df
        .sort_values(["user_session_id", "time"], ascending=True)
        .reset_index(drop=True)
        .fillna({"dwell_time": 0, "total_scroll": 0, "total_mouse_move": 0})
    )
    
    # Will exclude the following sessions with multiple domains:
    # For TeeFury as of 2023-05-04, 29k out of 2.8M page_views have been somewhere other than TeeFury. Those 29k page views correspond to 22k sessions, with 0 conversions.
    # Bots?
    multi_domain_session_ids = set(
        page_view_df_all_real_pre
        .query('time < min_timestamp_plus_time_diff')  # only look at page_views before the cutoff
        .assign(domain=lambda df: df["page"].apply(get_urls_domain))
        .groupby('user_session_id')
        .agg({'domain': 'nunique'})
        .query('domain>1')
        .index
    )
    
    # A minority of sessions have >1000 page views. These are likely bots.
    session_ids_with_over_1k_page_views = set(
        page_view_df_all_real_pre
        .query('time < min_timestamp_plus_time_diff')
        .groupby('user_session_id')
        .agg(uuid_count=('uuid', 'count'))
        .query('uuid_count>1000')
        .index
    )
    
    user_session_df = (
        user_session_df_pre
        .query('uuid not in @multi_domain_session_ids')
        .query('uuid not in @session_ids_with_over_1k_page_views')
    )
    
    if user_session_df.empty:
        print('No user sessions found for the given parameters.')
        return False
    
    page_view_df_all_real = (
        page_view_df_all_real_pre.query('user_session_id.isin(@user_session_df.uuid)')
    )
    
    # page views that start before the modeling cutoff time (some of actions
    # associated with these page views may still be after the cutoff time)
    page_view_df_within_time_range_pre = (page_view_df_all_real
        .query('time < min_timestamp_plus_time_diff')
        .reset_index(drop=True)
        .fillna({'dwell_time': 0})
    )
    
    page_activity_query = page_activity.query.filter(page_activity.page_view_id.in_(page_view_df_within_time_range_pre.uuid.tolist()))
    
    page_activity_df_pre = pd.read_sql(page_activity_query.statement, page_activity_query.session.connection())
    
    page_activity_df = (page_activity_df_pre
        .query('dwell_time >= 0 and scroll >= 0 and mouse_move >= 0')
        .astype({'dwell_time': 'int64', 'time': 'int64'})
        .merge(page_view_df_within_time_range_pre[['uuid', 'min_timestamp_plus_time_diff']], how='left', left_on='page_view_id', right_on='uuid')
        .drop(columns='uuid_y')
        .rename(columns={'uuid_x': 'uuid'})
        .query('time - dwell_time < min_timestamp_plus_time_diff')
        .assign(page_activity_still_ongoing=lambda df_: df_['time'] >= df_['min_timestamp_plus_time_diff'],
            start_time=lambda df_: df_.time - df_.dwell_time)
    )
    
    page_activity_totals = (page_activity_df
        .query('page_activity_still_ongoing == False')
        .groupby('page_view_id', group_keys=False)
        .agg(total_dwell_time=('dwell_time', 'sum'),
            total_scroll=('scroll', 'sum'),
            total_mouse_move=('mouse_move', 'sum'))
    )
    
    page_view_df_within_time_range = (page_view_df_within_time_range_pre
        .drop(columns=['dwell_time', 'total_scroll', 'total_mouse_move'])
        .set_index('uuid')
        .join(page_activity_totals, how='left')
        .fillna({'total_dwell_time': 0, 'total_scroll': 0, 'total_mouse_move': 0})
        .reset_index()
    )
    
    known_multitab_sessions = set(
        page_view_df_within_time_range
        .assign(time_pv1_end=lambda df: df['time'] + df['total_dwell_time'])
        .merge(page_view_df_within_time_range,
            on='user_session_id',
            suffixes=('_pv1', '_pv2'))
        .query('uuid_pv1 != uuid_pv2')
        .query('time_pv2 > time_pv1 and time_pv2 < time_pv1_end')
        ['user_session_id']
    )
    
    # Pulling click data
    click_query = (click.query
        .with_entities(click.uuid, click.time, click.page_view_id)
        .filter(click.page_view_id.in_(page_view_df_all_real.uuid.tolist()))
    )
    
    click_df = pd.read_sql(click_query.statement, click_query.session.connection())
    
    # For each page_view that started before the cutoff time, count the number
    # of clicks that occurred before the cutoff time and thus should be included
    # in the modeling
    if click_df.empty:
        # Handle sessions with no clicks recorded
        click_counts = (pd.DataFrame({'page_view_id': page_view_df_within_time_range['uuid'].unique(), 'click_count': 0})
            .set_index('page_view_id')
        )
    
    else:
        # For each page_view that started before the cutoff time, count the number of clicks that occurred before the cutoff time and thus should be included in the modeling
        click_counts = (page_view_df_within_time_range[['uuid', 'min_timestamp_plus_time_diff']]
            .rename(columns={'uuid': 'page_view_id'})
            .merge(click_df, on='page_view_id', how='left', validate='one_to_many')
            .assign(count_click_flag=lambda df_: df_.time < df_.min_timestamp_plus_time_diff)
            .groupby('page_view_id')
            .agg({'count_click_flag': 'sum'})
            .rename(columns={'count_click_flag': 'click_count'})
        )
    
    cart_query = (cart.query.with_entities(cart.uuid, cart.user_session_id,
        cart.time, cart.cart_change_type, cart.item_count, cart.items_subtotal_price,
        cart.original_total_price, cart.total_discount, cart.total_price, cart.total_weight)
        .filter(cart.user_session_id.in_(user_session_df.uuid.tolist()))
        .filter(cart.cart_change_type != 'none')
    )
    
    cart_df_pre = (pd.read_sql(cart_query.statement, cart_query.session.connection())
        .merge(user_session_df[['uuid', 'time']].rename(columns={'uuid': 'user_session_id'}), on='user_session_id', how='left', validate='many_to_one', suffixes=('_cart', '_user_session'))
        .query('time_cart < time_user_session + @time_cutoff')
    )
    
    if cart_df_pre.empty:
        cart_df = (pd.DataFrame({
            'cart_change_create_count': 0,
            'cart_change_add_count': 0,
            'cart_change_remove_count': 0,
            'cart_change_clear_count': 0,
            'most_recent_cart_change_type': None,
            'most_recent_item_count': None,
            'most_recent_items_subtotal_price': None,
            'most_recent_original_total_price': None,
            'most_recent_total_discount': None,
            'most_recent_total_price': None,
            'most_recent_total_weight': None,
            'most_recent_time_cart_create': None,
            'most_recent_time_cart_add': None,
            'most_recent_time_cart_remove': None,
            'most_recent_time_cart_clear': None,
            'time_since_most_recent_cart_change_add': None,
            'time_since_most_recent_cart_change_remove': None,
            'time_since_most_recent_cart_change_clear': None,
            'time_since_most_recent_cart_change_create': None
        }, index=[predict_session_id])
            .rename_axis('user_session_id', axis='index')
        )
    else:
        cart_df = (
            cart_df_pre
            .groupby('user_session_id')
            .agg(cart_change_create_count=('cart_change_type', lambda cct_: cct_.eq('create').sum()),
                cart_change_add_count=('cart_change_type', lambda cct_: cct_.eq('add').sum()),
                cart_change_remove_count=('cart_change_type', lambda cct_: cct_.eq('remove').sum()),
                cart_change_clear_count=('cart_change_type', lambda cct_: cct_.eq('clear').sum())
            )
            .join(cart_df_pre.loc[cart_df_pre.groupby('user_session_id')['time_cart'].idxmax()].set_index(['user_session_id', 'time_user_session']).add_prefix('most_recent_').reset_index(level=1), how='left', validate='one_to_one')
            .pipe(join_time_since_most_recent_cart_change_of_type, cart_df_pre, 'create')
            .pipe(join_time_since_most_recent_cart_change_of_type, cart_df_pre, 'add')
            .pipe(join_time_since_most_recent_cart_change_of_type, cart_df_pre, 'remove')
            .pipe(join_time_since_most_recent_cart_change_of_type, cart_df_pre, 'clear')
            .assign(
                time_since_most_recent_cart_change_add=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_add,
                time_since_most_recent_cart_change_remove=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_remove,
                time_since_most_recent_cart_change_clear=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_clear,
                time_since_most_recent_cart_change_create=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_create
            )
            .join(user_session_df[['uuid', 'store_id']].set_index('uuid'), how='left', validate='many_to_one')  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
            .assign(most_recent_total_discount=lambda df_: df_.most_recent_total_discount.mask((df_.store_id == '40808216-ff02-4733-b071-1edd783bc78a') & (df_.most_recent_original_total_price > 100000), np.nan),  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
                most_recent_total_price=lambda df_: df_.most_recent_total_price.mask((df_.store_id == '40808216-ff02-4733-b071-1edd783bc78a') & (df_.most_recent_original_total_price > 100000), np.nan),  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
                most_recent_original_total_price=lambda df_: df_.most_recent_original_total_price.mask((df_.store_id == '40808216-ff02-4733-b071-1edd783bc78a') & (df_.most_recent_original_total_price > 100000), np.nan))  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
            .astype({'most_recent_total_discount': 'float64',  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
                'most_recent_total_price': 'float64',  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
                'most_recent_original_total_price': 'float64',  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
                'most_recent_items_subtotal_price': 'float64'})  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
            .drop(columns=['store_id'])  # temp for cheapestees due to $100,000 custom "Print Charge" & "Embrodiery Charge"
            .drop(columns=['time_user_session', 'most_recent_uuid', 'most_recent_time_cart'])
        )
    
    user_sessions_lasting_beyond_cutoff = (
        page_view_df_all_real
        .rename(columns={'uuid': 'page_view_id'})
        .assign(max_timestamp_known_on_page=lambda df_: df_[['time', 'dwell_time']].sum(axis=1))
        .set_index('page_view_id')
        .join(click_df.set_index('page_view_id'), how='left', validate='one_to_many', lsuffix='_page_view', rsuffix='_click')
        .join(page_activity_df.groupby('page_view_id')['start_time'].max().rename('max_page_activity_start_time'), how='left')
        .reset_index()
        .assign(max_known_time=lambda df_: df_[['max_timestamp_known_on_page', 'time_click', 'max_page_activity_start_time']].max(axis=1))
        .groupby('user_session_id')
        .agg({'max_known_time': 'max',
            'min_timestamp_plus_time_diff': 'min'})  # min_timestamp_plus_time_diff is the same for all rows in each group
        .query('max_known_time >= min_timestamp_plus_time_diff')
        .index
        .tolist()
    )
    
    # Aggregate page view data to the user_session level, only including actions that occurred before the modeling cutoff time, and only including sessions that lasted beyond the cutoff time
    page_view_df_aggd_pre = (
        page_view_df_within_time_range
        # May need to address code divergence here:
        # Prediction data may not have enough observations to qualify as crossing
        # the treshold, so we let them through no matter what
        .pipe(lambda x: x.query('user_session_id.isin(@user_sessions_lasting_beyond_cutoff)') if train_or_predict == 'TRAIN' else x)
        .merge(click_counts, left_on='uuid', right_index=True, validate='one_to_one')
        .fillna({'click_count': 0})
        .assign(page_lower=page_view_df_within_time_range.page.str.lower(),
            page_type=lambda df_: df_.page_lower.str.extract('/(checkouts/|cart|products/|collections/|pages/|blogs/)', expand=False).str.rstrip('/').fillna('otherpgtype'))
    )
    
    page_view_df_aggd = (page_view_df_aggd_pre
        .groupby(['user_session_id', 'page_type'], group_keys=False)
        .agg({'total_dwell_time': 'sum',
            'total_scroll': 'sum',
            'total_mouse_move': 'sum',
            'page': ['count', 'nunique'],
            'click_count': [('sum', lambda x: x.sum(min_count=1))]})
        .pipe(flatten_cols)
        .pipe(add_any_missing_page_types, required_page_types=['blogs', 'cart', 'collections', 'otherpgtype', 'pages', 'products'])
        .astype({'total_dwell_time_sum': 'Int64', 'total_scroll_sum': 'float', 'total_mouse_move_sum': 'float'})
        .assign(
            dwell_time_nan_if_0=lambda df_: df_.total_dwell_time_sum.replace(0, np.nan),
            total_scroll_per_sec=lambda df_: df_.total_scroll_sum / df_.dwell_time_nan_if_0,
            total_mouse_move_per_sec=lambda df_: df_.total_mouse_move_sum / df_.dwell_time_nan_if_0
        )
        [['total_dwell_time_sum', 'total_scroll_per_sec', 'total_mouse_move_per_sec', 'page_count', 'page_nunique', 'click_count_sum']]
        .pipe(lambda df_: df_.unstack() if 'page_type' in df_.index.names else df_)
        .pipe(flatten_cols)
        .pipe(lambda df_: df_.assign(**{col: df_[col].fillna(0) for col in df_.columns if 'page_count' in col or 'page_nunique' in col or 'dwell_time' in col}))
        .reset_index()
        .assign(is_multitab_session=lambda df_: df_['user_session_id'].isin(known_multitab_sessions))
        .join(cart_df, how='left', on='user_session_id', rsuffix='_cart')
    )
    
    feature_list = ['screen_width', 'screen_height', 'browser', 'mobile', 'os',
        'device_memory', 'mean_device_price', 'mean_device_year',
        'language', 'referrer', 'country_name', 'region_name',
        'latitude', 'longitude', 'first_page_type',
        'store_id', 'ad_google',
        'ad_facebook', 'ad_tiktok', 'ad_bing', 'ad_doubleclick',
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content',
        'utm_term', 'true_local_time', 'local_timezone_offset_mins',
        'customer_recent_session_count', 'customer_recent_conversion_count',
        'total_dwell_time_sum_blogs', 'total_dwell_time_sum_cart',
        'total_dwell_time_sum_collections', 'total_dwell_time_sum_otherpgtype',
        'total_dwell_time_sum_pages', 'total_dwell_time_sum_products',
        'total_scroll_per_sec_blogs', 'total_scroll_per_sec_cart',
        'total_scroll_per_sec_collections', 'total_scroll_per_sec_otherpgtype',
        'total_scroll_per_sec_pages', 'total_scroll_per_sec_products',
        'total_mouse_move_per_sec_blogs', 'total_mouse_move_per_sec_cart',
        'total_mouse_move_per_sec_collections', 'total_mouse_move_per_sec_otherpgtype',
        'total_mouse_move_per_sec_pages', 'total_mouse_move_per_sec_products',
        'page_count_blogs', 'page_count_cart', 'page_count_collections',
        'page_count_otherpgtype', 'page_count_pages', 'page_count_products',
        'page_nunique_blogs', 'page_nunique_cart', 'page_nunique_collections',
        'page_nunique_otherpgtype', 'page_nunique_pages',
        'page_nunique_products', 'click_count_sum_blogs',
        'click_count_sum_cart', 'click_count_sum_collections',
        'click_count_sum_otherpgtype', 'click_count_sum_pages',
        'click_count_sum_products', 'is_multitab_session',
        'cart_change_create_count',
        'cart_change_add_count',
        'cart_change_remove_count',
        'cart_change_clear_count',
        'most_recent_cart_change_type',
        'most_recent_item_count',
        'most_recent_items_subtotal_price',
        'most_recent_original_total_price',
        'most_recent_total_discount',
        'most_recent_total_price',
        'most_recent_total_weight',
        'time_since_most_recent_cart_change_add',
        'time_since_most_recent_cart_change_remove',
        'time_since_most_recent_cart_change_clear',
        'time_since_most_recent_cart_change_create',
        'conversion',  # target feature for training, unused for prediction
        'time',  # not used (included in H2O dataset uploads for debugging)
        'user_session_id'  # not used (included in H2O dataset uploads for debugging)
    ]
    
    # Merge page view data with user session data
    joined_table = (user_session_df.merge(page_view_df_aggd, left_on='uuid', right_on='user_session_id', how='right')
        .loc[:, feature_list]
    )
    
    return joined_table