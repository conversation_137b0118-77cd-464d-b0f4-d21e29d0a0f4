import os
import sys
sys.path.append(os.getcwd().replace("/data_science/models", "").replace("/data_science", "").replace("/web/modules", "").replace("/web", "") + "/web")
from db import cart, click, db, page_activity, page_view, store, user_session

import datetime
from flask import Flask
import numpy as np
import pandas as pd
from sqlalchemy import or_
from urllib.parse import parse_qs, urlparse

from user_agents import parse
from sqlalchemy import inspect, func
import re
from pandas.api.types import CategoricalDtype

#### LIKE 2023082301.py but iterates through user_sessions ####

# Helper functions for feature engineering

def assign_true_local_time(df):
    def get_true_local_time(row):
        if row["local_time"] is None:
            return None
        return row["local_time"].tz_convert(row["local_timezone"]).tz_localize(tz=None)
    return df.assign(true_local_time=lambda df_: df_.apply(get_true_local_time, axis=1))

def convert_one_local_tz_to_secs(str_tz):
    if not str_tz:
        return None
    sign = -1 if str_tz[0] == '-' else 1
    hours, minutes = map(int, str_tz[1:].split(':'))
    return sign * (hours * 60 + minutes) * 60

def convert_one_local_tz_to_mins(str_tz):
    if not str_tz:
        return None
    sign = -1 if str_tz[0] == '-' else 1
    hours, minutes = map(int, str_tz[1:].split(':'))
    return sign * (hours * 60 + minutes)

def assign_local_tz_secs(df):
    return df.assign(local_timezone_offset_secs=pd.to_timedelta(df['local_timezone'].apply(convert_one_local_tz_to_secs), unit='s'))

def assign_local_tz_mins(df):
    return (df
            .assign(local_timezone_offset_mins=df['local_timezone'].apply(convert_one_local_tz_to_mins).astype('int16'))
            .drop(columns=['local_timezone'])
            )
            
def extract_mean_device_year(text):
    if not text:
        return None
    year_pattern = r"\b(?<!\d)(19[7-9]\d|20\d{2})(?!\d)\b"
    matches = re.findall(year_pattern, text)
    current_year = datetime.datetime.now().year
    return np.mean([int(device_year) for device_year in matches if int(device_year) <= current_year and int(device_year) >= 1970])

def add_missing_cart_change_types(df, column_names):
    for column_name in column_names:
        if column_name not in df.columns:
            df[column_name] = 0
    return df

def add_any_missing_page_types(df, required_page_types):
    return df.reindex(pd.MultiIndex.from_product([df.index.levels[0], required_page_types], names=df.index.names))

def flatten_cols(df):
    new_cols = ['_'.join(map(str, vals)) for vals in df.columns.to_flat_index()]
    new_df = df.copy()
    new_df.columns = new_cols
    return new_df

def get_urls_domain(url):
    url = (url.replace("http://", "").replace("https://", "")
        .replace("www.", "").replace(".myshopify", "").replace(".com", ""))
    return url.split("/")[0]

def join_time_since_most_recent_cart_change_of_type(df, cart_df_pre, cart_change_type):
    return df.join(cart_df_pre
        .loc[cart_df_pre
            .query("cart_change_type == @cart_change_type")
            .groupby("user_session_id")["time_cart"].idxmax(),
            ["user_session_id", "time_user_session"]]
        .rename(columns={"time_user_session": f'most_recent_time_cart_{cart_change_type}'})
        .set_index(["user_session_id"])
        .astype({f'most_recent_time_cart_{cart_change_type}': "Int64"}),
        how="left", validate="one_to_one")

js_recognized_browsers_list = [
    'Opera',
    'Microsoft Internet Explorer',
    'Microsoft Edge',
    'Chrome',
    'Safari',
    'Firefox'
]

def get_browser_name(browser_string):
    if browser_string in js_recognized_browsers_list:
        return browser_string
    else:
        user_agent = parse(browser_string)
        return user_agent.browser.family

def remove_top_level_column(df):
            df.columns = df.columns.droplevel()
            return df

def prep_data_for_model(train_or_predict, time_cutoff, latest_time_for_run=9999999999, number_of_user_sessions=150000, start_time=0, predict_session_id=None):
    if train_or_predict not in ["TRAIN", "PREDICT"]:
        raise ValueError("train_or_predict must be either 'TRAIN' or 'PREDICT'")
    
    if number_of_user_sessions <= 0:
        raise ValueError("number_of_user_sessions arg must be positive")
    
    store_query = store.query.filter(store.use_in_datascience == True).all()
    real_store_ids = [x.uuid for x in store_query]
    del store_query
    
    # 0 conversions as of 2023-05-04
    bot_browsers = [
        "+http://ahrefs.com/robot",
        "(+http://www.facebook.com",
        "+https://headline.com/legal",
        "+http://www.baidu.com/search",
        "(+http://www.google.com",
        "Mozilla/5.0 (compatible; Dataprovider.com)",
        "+http://www.semrush.com",
        "+http://cincrawdata.net/bot"
    ]
    
    # Pulling user_session data
    user_session_query = (user_session.query
        .with_entities(func.floor(user_session.device_memory).label('device_memory'), *[c for c in inspect(user_session).columns if c.name not in ['browser_version', 'os_version', 'cart_size', 'cart_token', 'customer_cookie', 'device_memory']])
        .filter(user_session.store_id.in_(real_store_ids))
        .filter(user_session.os != 'Search Bot')
        .filter(~user_session.browser.in_(bot_browsers))
        .filter(user_session.vandra_conversion == False)
        .filter(user_session.time >= start_time)
        .filter(~(user_session.domain.contains("shopifypreview") | user_session.domain.contains("myshopify")))
        .filter(user_session.time <= latest_time_for_run) #will currently cause some duplicates for train_or_predict='TRAIN', which will be removed before training in train_model.py
        .order_by(user_session.time.desc())
    )
    
    del real_store_ids
    del bot_browsers
    
    if train_or_predict != "TRAIN":
        user_session_query = (user_session_query
        .filter(user_session.uuid == predict_session_id)
        .limit(number_of_user_sessions)
    )
    else:
        user_session_query = (user_session_query
        .limit(number_of_user_sessions)
    )
    
    user_session_df_pre_raw = (pd.read_sql(user_session_query.statement, user_session_query.session.connection(),
                                        dtype={'time':'int32',
                                               'device_memory':'Int16',
                                               'domain':'category',
                                               'latitude':'float32',
                                               'longitude':'float32',
                                               'screen_height': 'Float32',
                                               'screen_width': 'Float32'})
    )
    
    min_user_session_time_processed = user_session_df_pre_raw['time'].min() if len(user_session_df_pre_raw) >= number_of_user_sessions else start_time
    
    if len(user_session_df_pre_raw) < number_of_user_sessions and train_or_predict == "TRAIN":
        print(f"Only {len(user_session_df_pre_raw)} user sessions found, less than number_of_user_sessions={number_of_user_sessions}.")
    
    if len(user_session_df_pre_raw) == 0:
        print("No user sessions found.")
        min_user_session_time_processed = start_time
        return pd.DataFrame(), min_user_session_time_processed
    
    user_session_df_pre = (user_session_df_pre_raw
        .assign(
            browser= lambda df_: df_.browser.apply(get_browser_name),
            first_page_type=lambda df_: df_.page.str.lower().str.extract("/(checkouts/|cart|products/|collections/|pages/|blogs/)", expand=False).str.rstrip("/").fillna("otherpgtype")
        )
        .astype({'browser': 'category', 'os': 'category', 'first_page_type': 'category'})
        .replace({col: {'' : None} for col in ["utm_campaign", "utm_medium", "utm_source", "utm_term", "utm_content"]})
        .pipe(assign_true_local_time)
        .pipe(assign_local_tz_mins)
        .pipe(lambda df_: df_.assign(mean_device_price=df_['price'].apply(lambda x: np.nanmean([float(val.strip()) if val.strip() != 'None' else np.nan for val in x.split(';')]) if x != '' and x is not None else np.nan)))
        .pipe(lambda df_: df_.assign(mean_device_year=df_['device'].apply(extract_mean_device_year)))
        .drop(columns=['local_time', 'price', 'device'])
        .query("~domain.str.contains('shopifypreview')")
        .query("~domain.str.contains('myshopify')")
        .astype({'mean_device_price': 'float16',
                 'mean_device_year': 'float16'})
    )
    
    del user_session_query
    
    if len(user_session_df_pre) == 0:
        print('No user sessions found for the given parameters.')
        return pd.DataFrame(), min_user_session_time_processed
    
    page_view_query = page_view.query.filter(page_view.user_session_id.in_(user_session_df_pre.uuid.tolist()))
    page_view_df_all_real_pre = (pd.read_sql(page_view_query.statement, page_view_query.session.connection(),
                                             dtype = {'time': 'int32',
                                                      'dwell_time': 'Int16',
                                                      'total_scroll': 'Int32',
                                                      'total_mouse_move': 'Int32',
                                                      'local_timezone': 'category',
                                                      'document_has_focus': 'boolean'})
        .pipe(assign_true_local_time)
        .pipe(assign_local_tz_mins)
        .drop(columns="local_time")
        # for each user_session_id, calculate the min page_view timestamp and the cutoff timestamp and add both to the df
        .assign(min_timestamp=lambda df_: df_.groupby("user_session_id", group_keys=False)["time"].transform('min').astype('int32'),
                min_timestamp_plus_time_diff=lambda df_: df_["min_timestamp"] + time_cutoff)  
        .sort_values(["user_session_id", "time"], ascending=True)
        .reset_index(drop=True)
        .fillna({"dwell_time": 0, "total_scroll": 0, "total_mouse_move": 0})
    )
    
    del page_view_query
    
    # Will exclude the following sessions with multiple domains:
    # For TeeFury as of 2023-05-04, 29k out of 2.8M page_views have been somewhere other than TeeFury. Those 29k page views correspond to 22k sessions, with 0 conversions.
    # Bots?
    multi_domain_session_ids = set(
        page_view_df_all_real_pre
        .query('time < min_timestamp_plus_time_diff')  # only look at page_views before the cutoff
        .assign(domain=lambda df: df["page"].apply(get_urls_domain))
        .groupby('user_session_id')
        .agg({'domain': 'nunique'})
        .query('domain>1')
        .index
    )
    
    # A minority of sessions have >1000 page views. These are likely bots.
    session_ids_with_over_1k_page_views = set(
        page_view_df_all_real_pre
        .query('time < min_timestamp_plus_time_diff')
        .groupby('user_session_id')
        .agg(uuid_count=('uuid', 'count'))
        .query('uuid_count>1000')
        .index
    )
    
    user_session_df = (
        user_session_df_pre
        .query('uuid not in @multi_domain_session_ids')
        .query('uuid not in @session_ids_with_over_1k_page_views')
    )
    
    del user_session_df_pre
    del multi_domain_session_ids
    del session_ids_with_over_1k_page_views
    
    if len(user_session_df) == 0:
        print('No user sessions found for the given parameters.')
        return pd.DataFrame(), min_user_session_time_processed
    
    page_view_df_all_real = (
        page_view_df_all_real_pre.query('user_session_id.isin(@user_session_df.uuid)')
    )
    
    del page_view_df_all_real_pre

    # page views that start before the modeling cutoff time (some of actions
    # associated with these page views may still be after the cutoff time)
    page_view_df_within_time_range_pre = (page_view_df_all_real
        .query('time < min_timestamp_plus_time_diff')
        .reset_index(drop=True)
        .fillna({'dwell_time': 0})
    )
    
    page_activity_query = (page_activity.query
        .filter(page_activity.page_view_id.in_(page_view_df_within_time_range_pre.uuid.tolist()))
        .filter((page_activity.dwell_time >= 0) & (page_activity.scroll >= 0) & (page_activity.mouse_move >= 0))
    )
    page_activity_df_pre = pd.read_sql(page_activity_query.statement, page_activity_query.session.connection(),
                                       dtype = {'time':'int32', 'dwell_time': 'Int16', 'scroll': 'Int32', 'mouse_move': 'Int32'})
    
    del page_activity_query

    page_activity_df = (page_activity_df_pre
        .merge(page_view_df_within_time_range_pre[['uuid', 'min_timestamp_plus_time_diff']], how='left', left_on='page_view_id', right_on='uuid')
        .drop(columns='uuid_y')
        .rename(columns={'uuid_x': 'uuid'})
        .query('time - dwell_time < min_timestamp_plus_time_diff')
        .assign(page_activity_still_ongoing=lambda df_: df_['time'] >= df_['min_timestamp_plus_time_diff'],
            start_time=lambda df_: df_.time - df_.dwell_time)
    )
    
    del page_activity_df_pre

    page_activity_totals = (page_activity_df
        .query('page_activity_still_ongoing == False')
        .groupby('page_view_id', group_keys=False)
        .agg(total_dwell_time=('dwell_time', 'sum'),
            total_scroll=('scroll', 'sum'),
            total_mouse_move=('mouse_move', 'sum'))
    )
    
    max_page_activity_start_times = (page_activity_df
        .groupby('page_view_id')
        ['start_time']
        .max()
        .rename('max_page_activity_start_time')
    )
    
    page_view_df_within_time_range = (page_view_df_within_time_range_pre
        .drop(columns=['dwell_time', 'total_scroll', 'total_mouse_move'])
        .set_index('uuid')
        .join(page_activity_totals, how='left')
        .fillna({'total_dwell_time': 0, 'total_scroll': 0, 'total_mouse_move': 0})
        .reset_index()
    )
    
    del page_view_df_within_time_range_pre
    
    known_multitab_sessions = set(
        page_view_df_within_time_range
        .assign(time_pv1_end=lambda df: df['time'] + df['total_dwell_time'])
        .merge(page_view_df_within_time_range,
            on='user_session_id',
            suffixes=('_pv1', '_pv2'))
        .query('uuid_pv1 != uuid_pv2')
        .query('time_pv2 > time_pv1 and time_pv2 < time_pv1_end')
        ['user_session_id']
    )
    
    # Pulling click data
    click_query = (click.query
        .with_entities(click.uuid, click.time, click.page_view_id)
        .filter(click.page_view_id.in_(page_view_df_all_real.uuid.tolist()))
    )
    
    click_df = pd.read_sql(click_query.statement, click_query.session.connection(),
                           dtype={'time': 'int32'})
    
    del click_query

    # For each page_view that started before the cutoff time, count the number
    # of clicks that occurred before the cutoff time and thus should be included
    # in the modeling
    if len(click_df) == 0:
        # Handle sessions with no clicks recorded
        click_counts = (pd.DataFrame({'page_view_id': page_view_df_within_time_range['uuid'].unique(), 'click_count': 0})
            .set_index('page_view_id')
        )
    
    else:
        # For each page_view that started before the cutoff time, count the number of clicks that occurred before the cutoff time and thus should be included in the modeling
        click_counts = (page_view_df_within_time_range[['uuid', 'min_timestamp_plus_time_diff']]
            .rename(columns={'uuid': 'page_view_id'})
            .merge(click_df, on='page_view_id', how='left', validate='one_to_many')
            .assign(count_click_flag=lambda df_: df_.time < df_.min_timestamp_plus_time_diff)
            .groupby('page_view_id')
            .agg({'count_click_flag': 'sum'})
            .rename(columns={'count_click_flag': 'click_count'})
            .astype('int16')
        )
    
    cart_query = (cart.query.with_entities(cart.uuid, cart.user_session_id,
        cart.time, cart.cart_change_type, cart.item_count, cart.items_subtotal_price,
        cart.original_total_price, cart.total_discount, cart.total_price, cart.total_weight)
        .filter(cart.user_session_id.in_(user_session_df.uuid.tolist()))
        .filter(cart.cart_change_type != 'none')
    )
    
    cart_df_pre = (pd.read_sql(cart_query.statement, cart_query.session.connection(),
                               dtype = {'time': 'int32',
                                        'cart_change_type':'category',
                                        'item_count': 'int32',
                                        'items_subtotal_price': 'float32',
                                        'original_total_price': 'float32',
                                        'total_discount': 'float32',
                                        'total_price': 'float32',
                                        'total_weight': 'float32'})
        .merge(user_session_df[['uuid', 'time']].rename(columns={'uuid': 'user_session_id'}), on='user_session_id', how='left', validate='many_to_one', suffixes=('_cart', '_user_session'))
        .query('time_cart < time_user_session + @time_cutoff')
    )

    del cart_query
    
    if len(cart_df_pre) == 0:
        cart_df = (pd.DataFrame({
            'cart_change_create_count': 0,
            'cart_change_add_count': 0,
            'cart_change_remove_count': 0,
            'cart_change_clear_count': 0,
            'most_recent_cart_change_type': None,
            'most_recent_item_count': None,
            'most_recent_items_subtotal_price': None,
            'most_recent_original_total_price': None,
            'most_recent_total_discount': None,
            'most_recent_total_price': None,
            'most_recent_total_weight': None,
            'most_recent_time_cart_create': None,
            'most_recent_time_cart_add': None,
            'most_recent_time_cart_remove': None,
            'most_recent_time_cart_clear': None,
            'time_since_most_recent_cart_change_add': None,
            'time_since_most_recent_cart_change_remove': None,
            'time_since_most_recent_cart_change_clear': None,
            'time_since_most_recent_cart_change_create': None
        }, index=[predict_session_id])
            .rename_axis('user_session_id', axis='index')
        ).astype({'cart_change_create_count': 'Int16',
                  'cart_change_add_count': 'Int16',
                  'cart_change_remove_count': 'Int16',
                  'cart_change_clear_count': 'Int16',
                  'most_recent_cart_change_type': CategoricalDtype(categories=['add', 'clear', 'create', 'remove'], ordered=False),
                  'most_recent_item_count': 'Int32',
                  'most_recent_items_subtotal_price': 'float32',
                  'most_recent_original_total_price': 'float32',
                  'most_recent_total_discount': 'float32',
                  'most_recent_total_price': 'float32',
                  'most_recent_total_weight': 'float32',
                  'most_recent_time_cart_create': 'Int32',
                  'most_recent_time_cart_add': 'Int32',
                  'most_recent_time_cart_remove': 'Int32',
                  'most_recent_time_cart_clear': 'Int32',
                  'time_since_most_recent_cart_change_add': 'Int16',
                  'time_since_most_recent_cart_change_remove': 'Int16',
                  'time_since_most_recent_cart_change_clear': 'Int16',
                  'time_since_most_recent_cart_change_create': 'Int16'})
    else:
        cart_df = (
            cart_df_pre.pivot_table(index='user_session_id', 
                                  columns='cart_change_type', 
                                  aggfunc={'cart_change_type': 'count'},
                                  fill_value=0)
            .rename(columns={'create': 'cart_change_create_count',
                            'add': 'cart_change_add_count',
                            'remove': 'cart_change_remove_count',
                            'clear': 'cart_change_clear_count'})
            .pipe(remove_top_level_column)
            .rename_axis(None, axis=1)
            .pipe(add_missing_cart_change_types, ['cart_change_create_count', 'cart_change_add_count', 'cart_change_remove_count', 'cart_change_clear_count'])
            [['cart_change_create_count', 'cart_change_add_count', 'cart_change_remove_count', 'cart_change_clear_count']]
            .astype('Int16') #not null here, but will contain nulls after the join in page_view_df_aggd assignment
            .join(cart_df_pre.loc[cart_df_pre.groupby('user_session_id')['time_cart'].idxmax()].set_index(['user_session_id', 'time_user_session']).add_prefix('most_recent_').reset_index(level=1), how='left', validate='one_to_one')
            .astype({'most_recent_item_count': 'Int32'}) #not null here, but will contain nulls after the join in page_view_df_aggd assignment
            .pipe(join_time_since_most_recent_cart_change_of_type, cart_df_pre, 'create')
            .pipe(join_time_since_most_recent_cart_change_of_type, cart_df_pre, 'add')
            .pipe(join_time_since_most_recent_cart_change_of_type, cart_df_pre, 'remove')
            .pipe(join_time_since_most_recent_cart_change_of_type, cart_df_pre, 'clear')
            .assign(
                time_since_most_recent_cart_change_add=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_add,
                time_since_most_recent_cart_change_remove=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_remove,
                time_since_most_recent_cart_change_clear=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_clear,
                time_since_most_recent_cart_change_create=lambda df_: df_.time_user_session + time_cutoff - df_.most_recent_time_cart_create
            )
            .astype({'most_recent_time_cart_create':'Int32',
                     'most_recent_time_cart_add':'Int32',
                     'most_recent_time_cart_remove':'Int32',
                     'most_recent_time_cart_clear':'Int32',
                     'time_since_most_recent_cart_change_add':'Int16',
                     'time_since_most_recent_cart_change_remove':'Int16',
                     'time_since_most_recent_cart_change_clear':'Int16',
                     'time_since_most_recent_cart_change_create':'Int16'})
            .drop(columns=['time_user_session', 'most_recent_uuid', 'most_recent_time_cart'])
        )
        
    del cart_df_pre
    
    user_sessions_lasting_beyond_cutoff = (
        page_view_df_all_real
        .rename(columns={'uuid': 'page_view_id'})
        .assign(max_timestamp_known_on_page=lambda df_: df_[['time', 'dwell_time']].sum(axis=1))
        .set_index('page_view_id')
        .join(click_df.set_index('page_view_id'), how='left', validate='one_to_many', lsuffix='_page_view', rsuffix='_click')
        .join(page_activity_df.groupby('page_view_id')['start_time'].max().rename('max_page_activity_start_time'), how='left')
        .reset_index()
        .assign(max_known_time=lambda df_: df_[['max_timestamp_known_on_page', 'time_click', 'max_page_activity_start_time']].max(axis=1))
        .groupby('user_session_id')
        .agg({'max_known_time': 'max',
            'min_timestamp_plus_time_diff': 'min'})  # min_timestamp_plus_time_diff is the same for all rows in each group
        .query('max_known_time >= min_timestamp_plus_time_diff')
        .index
        .tolist()
    )
    
    del page_view_df_all_real
    del click_df
    del page_activity_df
    
    # Aggregate page view data to the user_session level, only including actions that occurred before the modeling cutoff time, and only including sessions that lasted beyond the cutoff time
    page_view_df_aggd_pre = (
        page_view_df_within_time_range
        # May need to address code divergence here:
        # Prediction data may not have enough observations to qualify as crossing
        # the treshold, so we let them through no matter what
        .pipe(lambda x, user_sessions_lasting_beyond_cutoff=user_sessions_lasting_beyond_cutoff: x.query('user_session_id.isin(@user_sessions_lasting_beyond_cutoff)') if train_or_predict == 'TRAIN' else x)
        .merge(click_counts, left_on='uuid', right_index=True, validate='one_to_one')
        .fillna({'click_count': 0})
        .assign(page_lower=page_view_df_within_time_range.page.str.lower(),
                page_type=lambda df_: df_.page_lower.str.extract('/(checkouts/|cart|products/|collections/|pages/|blogs/)', expand=False).str.rstrip('/').fillna('otherpgtype').astype('category'))
        .drop(columns=['page_lower'])
    )
    
    del page_view_df_within_time_range
    del click_counts

    page_view_df_aggd = (page_view_df_aggd_pre
        .groupby(['user_session_id', 'page_type'], group_keys=False)
        .agg({'total_dwell_time': 'sum',
            'total_scroll': 'sum',
            'total_mouse_move': 'sum',
            'page': ['count', 'nunique'],
            'click_count': [('sum', lambda x: x.sum(min_count=1))]})
        .pipe(flatten_cols)
        .pipe(add_any_missing_page_types, required_page_types=['blogs', 'cart', 'collections', 'otherpgtype', 'pages', 'products'])
        .fillna({'page_count':0,
                 'page_nunique':0})
        .pipe(lambda df_: df_.astype({col:'int16' for col in df_.columns if 'page_count' in col or 'page_nunique' in col} |
                                     {col:'Int16' for col in df_.columns if 'click_count' in col or 'cart_change' in col}))
        .assign(
            dwell_time_nan_if_0=lambda df_: df_.total_dwell_time_sum.replace(0, np.nan),
            total_scroll_per_sec=lambda df_: df_.total_scroll_sum / df_.dwell_time_nan_if_0,
            total_mouse_move_per_sec=lambda df_: df_.total_mouse_move_sum / df_.dwell_time_nan_if_0
        )
        [['total_dwell_time_sum', 'total_scroll_per_sec', 'total_mouse_move_per_sec', 'page_count', 'page_nunique', 'click_count_sum']]
        .pipe(lambda df_: df_.unstack() if 'page_type' in df_.index.names else df_)
        .pipe(flatten_cols)
        .pipe(lambda df_: df_.assign(**{col: df_[col].fillna(0) for col in df_.columns if 'page_count' in col or 'page_nunique' in col or 'dwell_time' in col}))
        .reset_index()
        .assign(is_multitab_session=lambda df_: df_['user_session_id'].isin(known_multitab_sessions))
        .join(cart_df, how='left', on='user_session_id', rsuffix='_cart')
    )
    
    del page_view_df_aggd_pre
    del known_multitab_sessions
    
    feature_list = ['screen_width', 'screen_height', 'browser', 'mobile', 'os',
        'device_memory', 'mean_device_price', 'mean_device_year', 'language', 
        'referrer', 'country_name', 'region_name', 'latitude', 'longitude', 
        'first_page_type', 'store_id', 'ad_google',
        'ad_facebook', 'ad_tiktok', 'ad_bing', 'ad_doubleclick',
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content',
        'utm_term', 'true_local_time', 'local_timezone_offset_mins',
        'total_dwell_time_sum_blogs', 'total_dwell_time_sum_cart',
        'total_dwell_time_sum_collections', 'total_dwell_time_sum_otherpgtype',
        'total_dwell_time_sum_pages', 'total_dwell_time_sum_products',
        'total_scroll_per_sec_blogs', 'total_scroll_per_sec_cart',
        'total_scroll_per_sec_collections', 'total_scroll_per_sec_otherpgtype',
        'total_scroll_per_sec_pages', 'total_scroll_per_sec_products',
        'total_mouse_move_per_sec_blogs', 'total_mouse_move_per_sec_cart',
        'total_mouse_move_per_sec_collections', 'total_mouse_move_per_sec_otherpgtype',
        'total_mouse_move_per_sec_pages', 'total_mouse_move_per_sec_products',
        'page_count_blogs', 'page_count_cart', 'page_count_collections',
        'page_count_otherpgtype', 'page_count_pages', 'page_count_products',
        'page_nunique_blogs', 'page_nunique_cart', 'page_nunique_collections',
        'page_nunique_otherpgtype', 'page_nunique_pages',
        'page_nunique_products', 'click_count_sum_blogs',
        'click_count_sum_cart', 'click_count_sum_collections',
        'click_count_sum_otherpgtype', 'click_count_sum_pages',
        'click_count_sum_products', 'is_multitab_session',
        'cart_change_create_count',
        'cart_change_add_count',
        'cart_change_remove_count',
        'cart_change_clear_count',
        'most_recent_cart_change_type',
        'most_recent_item_count',
        'most_recent_items_subtotal_price',
        'most_recent_original_total_price',
        'most_recent_total_discount',
        'most_recent_total_price',
        'most_recent_total_weight',
        'time_since_most_recent_cart_change_add',
        'time_since_most_recent_cart_change_remove',
        'time_since_most_recent_cart_change_clear',
        'time_since_most_recent_cart_change_create',
        'conversion',  # target feature for training, unused for prediction
        'time',  # not used (included in H2O dataset uploads for debugging)
        'user_session_id'  # not used (included in H2O dataset uploads for debugging)
    ]
    
    # Merge page view data with user session data
    joined_table = (user_session_df.merge(page_view_df_aggd, left_on='uuid', right_on='user_session_id', how='right')
        .loc[:, feature_list]
    )
    
    del user_session_df
    del page_view_df_aggd
    del feature_list
    
    return joined_table, min_user_session_time_processed
