import os 
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))    
sys.path.append(os.path.join(current_dir, '..'))
sys.path.append(os.path.join(current_dir, '../web'))

try:
    from web.utils import get_generic_logger
except ModuleNotFoundError:
    from utils import get_generic_logger

logger = get_generic_logger(__name__)

logger.info('current_dir: ' + current_dir)
logger.info('sys.path: ' + str(sys.path))
logger.info('os.getcwd(): ' + os.getcwd())

from sqlalchemy.exc import InvalidRequestError
from sqlalchemy.types import JSON

try:
    from web.app import app, db
except ModuleNotFoundError:
    from app import app, db

try:
    from web.db import scoring_artifact, scoring_layer, scoring_layer_thresholds, store, user_session
except (ModuleNotFoundError, InvalidRequestError):
    from db import scoring_artifact, scoring_layer, scoring_layer_thresholds, store, user_session

try:
    from data_science.prepare_data import prep_data_for_model
except ModuleNotFoundError:
    from prepare_data import prep_data_for_model

try:
    from web.constants import LOW_INTENT_LABEL, ANTI_HOLDOUT_LABEL
except ModuleNotFoundError:
    from constants import LOW_INTENT_LABEL, ANTI_HOLDOUT_LABEL

import sys
import time
import uuid
import pandas as pd
import inspect

import boto3
import json
import pandas as pd
import numpy as np
import pickle
import tarfile

from sagemaker import Session
from sagemaker.sklearn.model import SKLearnModel
from sagemaker.transformer import Transformer
from sklearn.isotonic import IsotonicRegression

from sklearn.model_selection import train_test_split
from utils import get_generic_logger

import neptune
from io import StringIO
from neptune.types import File
import csv


AWS_REGION = "us-east-2"
INPUT_DATASETS_BUCKET_NAME = 'vandra-datasets'
OUTPUT_BUCKET_NAME = "vandra-sagemaker-output-models" # Adjust according to desired prod bucket/folder/file architecture
DEPLOYMENT_INSTANCE_TYPE = 'ml.m5.xlarge' # https://aws.amazon.com/sagemaker/pricing/

def get_arg_default(fn, arg_name):
    args = inspect.signature(fn).parameters
    if arg_name in args:
        return args[arg_name].default
    else:
        raise ValueError(f"Argument '{arg_name}' not found in function signature, or it does not have a default value.")

def upload_file_to_s3(s3_client, local_file_path, s3_file_path, bucket_name='vandra-datasets'):
    try:
        _ = s3_client.upload_file(local_file_path, bucket_name, s3_file_path)
        logger.info(f"File {s3_file_path} uploaded successfully.")
    except ClientError as e:
        logger.error(e)

def write_to_csv_and_s3(train_dataset, test_dataset_calibration, test_dataset, s3_client, new_scoring_artifact_uuid, input_datasets_bucket_name=INPUT_DATASETS_BUCKET_NAME):
    train_dataset.to_csv(f'/tmp/train_dataset_{new_scoring_artifact_uuid}.csv', index=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    
    # Files with headers for reference
    test_dataset_calibration.to_csv(f'/tmp/test_dataset_calibration_{new_scoring_artifact_uuid}.csv', index=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    test_dataset_calibration.drop(columns='conversion').to_csv(f'/tmp/test_dataset_calibration_no_target_{new_scoring_artifact_uuid}.csv', index=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    test_dataset.to_csv(f'/tmp/test_dataset_{new_scoring_artifact_uuid}.csv', index=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    test_dataset.drop(columns='conversion').to_csv(f'/tmp/test_dataset_no_target_{new_scoring_artifact_uuid}.csv', index=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    
    # Headerless files for SageMaker batch transform jobs
    test_dataset_calibration.to_csv(f'/tmp/test_dataset_calibration_no_header_{new_scoring_artifact_uuid}.csv', index=False, header=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    test_dataset_calibration.drop(columns='conversion').to_csv(f'/tmp/test_dataset_calibration_no_target_no_header_{new_scoring_artifact_uuid}.csv', index=False, header=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    test_dataset.to_csv(f'/tmp/test_dataset_no_header_{new_scoring_artifact_uuid}.csv', index=False, header=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    test_dataset.drop(columns='conversion').to_csv(f'/tmp/test_dataset_no_target_no_header_{new_scoring_artifact_uuid}.csv', index=False, header=False, quoting=csv.QUOTE_MINIMAL, quotechar='"', escapechar='\\')
    
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/train_dataset_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/train_dataset.csv')
    
    # Files with headers for reference
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/test_dataset_calibration_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/test_dataset_calibration.csv')
    
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/test_dataset_calibration_no_target_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/test_dataset_calibration_no_target.csv')
    
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/test_dataset_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/test_dataset.csv')
    
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/test_dataset_no_target_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/test_dataset_no_target.csv')
    
    #Headerless files for SageMaker batch transform jobs
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/test_dataset_calibration_no_header_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/test_dataset_calibration_no_header.csv')
    
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/test_dataset_calibration_no_target_no_header_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/test_dataset_calibration_no_target_no_header.csv')
    
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/test_dataset_no_header_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/test_dataset_no_header.csv')
    
    upload_file_to_s3(s3_client=s3_client,
                      local_file_path=f'/tmp/test_dataset_no_target_no_header_{new_scoring_artifact_uuid}.csv',
                      bucket_name=input_datasets_bucket_name,
                      s3_file_path=f'{new_scoring_artifact_uuid}/test_dataset_no_target_no_header.csv')

def get_cmatrix_counts(confusion_matrix, c_p, c_a):
        try:
            return confusion_matrix.query(f'conversion_predicted == {c_p} and conversion == {c_a}')['count'].values[0]
        except IndexError:
            return 0

def calc_metric(scores_base, threshold_to_test, metric, verbose=False):
    confusion_matrix_df = (scores_base
                           .assign(conversion_predicted=lambda df_: df_['pred_conv_probability'] > threshold_to_test)
                           .astype({'conversion_predicted': 'int'})
                           .value_counts(['conversion_predicted', 'conversion'])
                           .reset_index()
                           .rename(columns={0: 'count'})
                           )
    
    # Positive event is conversion, negative is no conversion
    tp_count = get_cmatrix_counts(confusion_matrix_df, 1, 1)
    tn_count = get_cmatrix_counts(confusion_matrix_df, 0, 0)
    fp_count = get_cmatrix_counts(confusion_matrix_df, 1, 0)
    fn_count = get_cmatrix_counts(confusion_matrix_df, 0, 1)
    
    if verbose:
        print('Threshold:', threshold_to_test)
        print('TP Count:', tp_count)
        print('TN Count:', tn_count)
        print('FP Count:', fp_count)
        print('FN Count:', fn_count)
        print(f'Discount-Offer Rate: {tn_count / (tn_count + fp_count):.1%}')
        print('Cannibalization Rate: ' + f'{fn_count/(fn_count + tp_count):.1%}' if (fn_count + tp_count > 0) else None)
    
    # Note: All metrics only consider sessions that reach a decision point, which is all the model was trained on
    if metric == 'discount_offer_rate':
        if (tn_count + fp_count) == 0:
            return np.nan
        return tn_count / (tn_count + fp_count) # Discount Offer Rate: Number of correct decisions to offer discount / Total number who didn't convert and therefore should have been offered discount
    if metric == 'alt_discount_offer_rate':
        return tn_count / (tn_count + fp_count + fn_count + tp_count)  # Alternative/overall Discount Offer Rate: Number of correct decisions to offer discount / Total number of sessions reaching a decision point
    if metric == 'cannibalization_rate':
        return fn_count / (fn_count + tp_count)  # Cannibalization Rate: Number of incorrect decisions to offer discount / Total number who converted and therefore should not have been offered discount
    if metric == 'total_count':
        return fn_count + tp_count + tn_count + fp_count # Total Count: Total number of sessions reaching a decision point

def find_threshold(scores_base, cannibalization_target, threshold_min=0.001, threshold_max=0.6, tolerance=0.000000001):
    while threshold_max - threshold_min > tolerance:
        mid = (threshold_min + threshold_max) / 2
        if calc_metric(scores_base=scores_base, threshold_to_test=mid, metric='cannibalization_rate') > cannibalization_target:
            threshold_max = mid
        else:
            threshold_min = mid
    return threshold_min

def run_batch_transform(boto_sagemaker_client,
                        s3_client,
                        transform_job_name,
                        model_name,
                        s3_input_datasets_bucket,
                        new_scoring_artifact_uuid,
                        s3_output_bucket_and_file,
                        s3_output_file,
                        model_type,
                        sagemaker_session=None):
    if model_type not in ['main_model', 'pipeline_model']:
        raise ValueError('type must be one of "main_model" or "pipeline_model".')

    transform_input = {
        "DataSource": {
            "S3DataSource": {
                "S3DataType": "S3Prefix",
                "S3Uri": f'{s3_input_datasets_bucket}/{new_scoring_artifact_uuid}/test_dataset_{"calibration_" if model_type == "main_model" else ""}no_target_no_header.csv'
            }
        },
        "ContentType": "text/csv",
        "CompressionType": "None",
        "SplitType": "Line"
    }
    
    transform_output = {
        "S3OutputPath": f'{s3_output_bucket_and_file}/{transform_job_name}'
    }
    
    transform_resources = {
        "InstanceType": "ml.m5.4xlarge",
        "InstanceCount": 1
    }
    
    if model_type == 'main_model':
        transform_job_response = boto_sagemaker_client.create_transform_job(
            TransformJobName=transform_job_name,
            ModelName=model_name,
            TransformInput=transform_input,
            TransformOutput=transform_output,
            TransformResources=transform_resources
        )
    elif model_type == 'pipeline_model':
        transform_job = Transformer(
            model_name = model_name,
            instance_count = 1,
            instance_type = 'ml.m4.xlarge',
            strategy = 'MultiRecord',
            assemble_with = 'Line',
            output_path = transform_output['S3OutputPath'],
            base_transform_job_name='inference-pipelines-batch',
            sagemaker_session=sagemaker_session,
            accept = 'text/csv'
        )
        
        transform_job.transform(
            data = transform_input['DataSource']['S3DataSource']['S3Uri'],
            content_type = 'text/csv',
            split_type = 'Line'
        )
        
    # Check status of batch transform job:
    print('JobStatus')
    print('----------')
    
    describe_response = boto_sagemaker_client.describe_transform_job(TransformJobName=transform_job_name)
    job_run_status = describe_response['TransformJobStatus']
    print(job_run_status)
    
    timer_current = 0
    while job_run_status not in ('Failed', 'Completed', 'Stopped'):
        describe_response = boto_sagemaker_client.describe_transform_job(TransformJobName=transform_job_name)
        job_run_status = describe_response['TransformJobStatus']
        print(str(timer_current) + ' - ' + job_run_status)
        timer_current += 15
        time.sleep(15)
    
    print(f"Batch Transform Status: {job_run_status}")
    print(f"Batch Transform took {timer_current} seconds")

    # Get results of batch transform job:
    local_inference_results_path = f"{model_name}-{model_type}-inference-results.csv"
    s3_client.download_file(OUTPUT_BUCKET_NAME, f'{s3_output_file}/{transform_job_name}/test_dataset_{"calibration_" if model_type == "main_model" else ""}no_target_no_header.csv.out', local_inference_results_path)
    return pd.read_csv(local_inference_results_path, names=['pred_conv_probability'], header=None)

def train_model(filename,
                time_cutoff=71,
                start_time=int(time.time())-(604800*9),
                end_time=int(time.time()),
                cannibalization_rate_target=0.17,
                cannibalization_rate_target_antiholdout=0.7,
                test_split_method='randomized',
                ds_split_time_cutoff=None,
                ds_split_time_cutoff2=None,
                config_scorer='Recall',
                default_low_med_threshold=0.01,
                default_med_high_threshold=0.05,
                feat_eng_batch_size=150000):
    global app
    
    #### 0. ARG CHECKS AND INITIALIZE APP ####
    if test_split_method not in ['no_test', 'randomized', 'time_based']:
        raise ValueError('test_split_method must be one of "no_test", "randomized", or "time_based" (though only "no_test" is currently implemented for SageMaker models).')
    
    if test_split_method != 'time_based' and (isinstance(ds_split_time_cutoff, int) or isinstance(ds_split_time_cutoff2, int)):
        raise ValueError('ds_split_time_cutoff and ds_split_time_cutoff2 should only be specified if test_split_method is "time_based".')
    
    if test_split_method == 'time_based' and not (isinstance(ds_split_time_cutoff, int) and isinstance(ds_split_time_cutoff2, int)):
        raise ValueError('ds_split_time_cutoff and ds_split_time_cutoff2 must be specified if test_split_method is "time_based".')
    
    if config_scorer not in ['Accuracy', 'AUC', 'BalancedAccuracy', 'F1', 'Precision', 'Recall']:
        raise ValueError('config_scorer must be one of "Accuracy", "AUC", "BalancedAccuracy", "F1", "Precision", or "Recall".')
    
    #### I. CONNECT TO AWS ####
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=app.config["SES_ACCESS_KEY"],
        aws_secret_access_key=app.config["SES_SECRET"],
        region_name=AWS_REGION
    )
    
    boto_session = boto3.session.Session(
        aws_access_key_id=app.config["SES_ACCESS_KEY"],
        aws_secret_access_key=app.config["SES_SECRET"],
        region_name=AWS_REGION
    )
    boto_sagemaker_client = boto_session.client(
        service_name="sagemaker",
        region_name=AWS_REGION
    )
    
    sagemaker_session = Session(boto_session=boto_session)
    
    sagemaker_role = "arn:aws:iam::************:role/vandra_sagemaker_role"
    s3_input_datasets_bucket = "s3://" + INPUT_DATASETS_BUCKET_NAME
    s3_output_bucket = "s3://" + OUTPUT_BUCKET_NAME
    s3_output_file = "model_output_file"
    s3_output_bucket_and_file = f'{s3_output_bucket}/{s3_output_file}'
    
    #### II. CREATE MODELING DATASET ####

    neptune_expmnt = neptune.init_run(
        project="vandra-neptune-workspace/vandra-intent", 
        dependencies="infer", 
        monitoring_namespace="monitoring"
    )
    
    neptune_expmnt_run_start_time = time.time()
    new_scoring_artifact_uuid = str(uuid.uuid4())
    platform_expmnt_id = str(uuid.uuid4()).replace('-', '')
    
    neptune_expmnt['parameters'] = {
        'neptune_experiment_id': new_scoring_artifact_uuid,
        'time': neptune_expmnt_run_start_time,
        'filename': filename,
        'time_cutoff': time_cutoff,
        'train_start_time': start_time,
        'train_end_time': end_time,
        'test_split_method': test_split_method,
        'platform': 'sagemaker',
        'objective_metric': config_scorer,
        'platform_experiment_id': platform_expmnt_id,
        'description': os.environ.get("MODEL_DESCRIPTION") if os.environ.get("MODEL_DESCRIPTION") not in [None, ""] else None,
        'ds_split_time_cutoff': ds_split_time_cutoff,
        'ds_split_time_cutoff2': ds_split_time_cutoff2
    }

    neptune_expmnt['model/status'] = 'PYTHON_FEAT_ENG'
    min_user_session_time_processed = neptune_expmnt_run_start_time
    neptune_expmnt['model/min_user_session_time_processed'] = neptune_expmnt_run_start_time
    
    joined_table = pd.DataFrame()
    
    while min_user_session_time_processed > start_time:
        read_replica_engine = db.engines['read_replica'].execution_options(postgresql_readonly=True)
        with read_replica_engine.connect() as db_read_rep_conn:
            prep_data_kwargs = {
                'train_or_predict': 'TRAIN',
                'predict_session_id': None,
                'time_cutoff': time_cutoff,
                'start_time': start_time,
                'end_time': end_time,
                'latest_time_for_run': int(min_user_session_time_processed),
                'db_connection': db_read_rep_conn,
                **({'feat_eng_batch_size': feat_eng_batch_size} if 'feat_eng_batch_size' in inspect.signature(prep_data_for_model[filename]).parameters else {}), # switched arg name from 'number_of_user_sessions' to 'feat_eng_batch_size' for some feature engineering files, intend to use the latter going forward
                **({'number_of_user_sessions': feat_eng_batch_size} if 'number_of_user_sessions' in inspect.signature(prep_data_for_model[filename]).parameters else {}) # switched arg name from 'number_of_user_sessions' to 'feat_eng_batch_size' for some feature engineering files, intend to use the latter going forward
            }
            
            joined_table_iter, min_user_session_time_processed = prep_data_for_model[filename](**prep_data_kwargs)
        
        if len(joined_table_iter) == 0:
            neptune_expmnt['model/min_user_session_time_processed'] = int(min_user_session_time_processed)
            continue
        joined_table = pd.concat([joined_table, joined_table_iter])
        del joined_table_iter
        
        neptune_expmnt['model/min_user_session_time_processed'] = int(min_user_session_time_processed)
    
    joined_table = joined_table.drop_duplicates(subset='user_session_id', keep='first')
    
    if test_split_method == 'no_test':
        raise NotImplementedError('split method "no_test" not yet implemented for SageMaker models. SageMaker doesn’t currently seem to have the ability to provide validation predictions / CV out-of-fold predictions, so we use a test set for initial threshold-setting.')
        
    elif test_split_method == 'randomized':
        train_dataset, test_dataset_both = train_test_split(joined_table, test_size=0.35, random_state=1234, stratify=joined_table['conversion'])
        test_dataset_calibration, test_dataset = train_test_split(test_dataset_both, test_size=0.5, random_state=1234, stratify=test_dataset_both['conversion'])
        del test_dataset_both
        train_dataset = train_dataset.reset_index(drop=True)
        test_dataset_calibration = test_dataset_calibration.reset_index(drop=True)
        test_dataset = test_dataset.reset_index(drop=True)
        write_to_csv_and_s3(train_dataset, test_dataset_calibration, test_dataset, s3_client, new_scoring_artifact_uuid)
        
    elif test_split_method == 'time_based':
        train_dataset = (joined_table
                         .query('time < @ds_split_time_cutoff')
                         .reset_index(drop=True)
                         )
        test_dataset_calibration = (joined_table
                                    .query('time >= @ds_split_time_cutoff and time < @ds_split_time_cutoff2')
                                    .reset_index(drop=True)
                                    )
                                    
        test_dataset = (joined_table
                        .query('time >= @ds_split_time_cutoff2')
                        .reset_index(drop=True)
                        )
                        
        write_to_csv_and_s3(train_dataset, test_dataset_calibration, test_dataset, s3_client, new_scoring_artifact_uuid)
        
    #### III. CREATE AND STORE SAGEMAKER EXPERIMENT (MAIN MODEL) ####
    neptune_expmnt['model/status'] = 'SAGEMAKER_TRAINING'
    
    target_attribute_name = "conversion"

    feature_spec = {
        "FeatureAttributeNames": [col for col in train_dataset.columns if col not in [target_attribute_name, 'user_session_id', 'time']] # `true_local_time` is used instead of `time`
    }
    
    with open('feature_spec.json', 'w') as f:
        json.dump(feature_spec, f)
        
    s3_client.upload_file('feature_spec.json', INPUT_DATASETS_BUCKET_NAME, f'{new_scoring_artifact_uuid}/feature_spec.json')

    input_data_config = [
        {
            "DataSource": {
                "S3DataSource": {
                    "S3DataType": "S3Prefix",
                    "S3Uri": f'{s3_input_datasets_bucket}/{new_scoring_artifact_uuid}/train_dataset'
                }
            },
            "TargetAttributeName": target_attribute_name,
            "ChannelType": "training",
            "ContentType": "text/csv;header=present"
        }
    ]
    
    output_data_config = {"S3OutputPath": s3_output_bucket_and_file}
    
    auto_ml_job_config = {"CompletionCriteria": {"MaxCandidates": 5},
                          "CandidateGenerationConfig": {
                              "FeatureSpecificationS3Uri": f"{s3_input_datasets_bucket}/{new_scoring_artifact_uuid}/feature_spec.json"
                          }#, # Want to get this working, but not a blocker
                        #   'SecurityConfig': {
                        #       'VpcConfig': {
                        #           'SecurityGroupIds': [
                        #               'sg-0c1bb7566e29a5339',
                        #           ],
                        #           'Subnets': [
                        #               'subnet-0d6d12254dfe47050',
                        #               'subnet-01748a008ad7e34cc',
                        #           ]
                        #       }
                        #   }
                 }
    
    # Regression: MAE, MSE, R2, RMSE
    # Classification: Accuracy, AUC, BalancedAccuracy, F1, Precision, Recall
    auto_ml_objective = {"MetricName": config_scorer}
    
    auto_ml_job_name = platform_expmnt_id
    
    boto_sagemaker_client.create_auto_ml_job(
        AutoMLJobName=auto_ml_job_name,
        InputDataConfig=input_data_config,
        OutputDataConfig=output_data_config,
        AutoMLJobConfig=auto_ml_job_config,
        RoleArn=sagemaker_role,
        ProblemType="BinaryClassification",
        AutoMLJobObjective=auto_ml_objective
    )
    
    boto_sagemaker_client.add_tags(
        ResourceArn=f'arn:aws:sagemaker:us-east-2:************:automl-job/{auto_ml_job_name}',
        Tags=[
            {"Key": "platform_expmnt_id", "Value": platform_expmnt_id}
        ]
    )
    
    # Check status of training job:
    [job["AutoMLJobName"] + ": " + job["AutoMLJobStatus"] + " - " + job["AutoMLJobSecondaryStatus"] for job in boto_sagemaker_client.list_auto_ml_jobs()['AutoMLJobSummaries'] if job['AutoMLJobStatus'] == 'InProgress']
    describe_response = boto_sagemaker_client.describe_auto_ml_job(AutoMLJobName=auto_ml_job_name)
    print(describe_response["AutoMLJobStatus"] + " - " + describe_response["AutoMLJobSecondaryStatus"])
    job_run_status = describe_response["AutoMLJobStatus"]
    
    timer_current = 0
    while job_run_status not in ("Failed", "Completed", "Stopped"):
        describe_response = boto_sagemaker_client.describe_auto_ml_job(AutoMLJobName=auto_ml_job_name)
        job_run_status = describe_response["AutoMLJobStatus"]
        
        print(
            str(timer_current) + " - " + describe_response["AutoMLJobStatus"] + " - " + describe_response["AutoMLJobSecondaryStatus"]
        )
        timer_current += 30
        time.sleep(30)
    
    # Show best model trained:
    best_candidate = boto_sagemaker_client.describe_auto_ml_job(AutoMLJobName=auto_ml_job_name)["BestCandidate"]
    best_candidate['InferenceContainers'][1]['Environment'].update({'SAGEMAKER_INFERENCE_OUTPUT': 'probability'})
    best_candidate['InferenceContainers'][2]['Environment'].update({'SAGEMAKER_INFERENCE_INPUT': 'probability'})
    best_candidate['InferenceContainers'][2]['Environment'].update({'SAGEMAKER_INFERENCE_OUTPUT': 'probability'})
    best_candidate_name = best_candidate["CandidateName"]
    print(best_candidate)
    print("CandidateName: " + best_candidate_name)
    print("FinalAutoMLJobObjectiveMetricName: " + best_candidate["FinalAutoMLJobObjectiveMetric"]["MetricName"])
    print("FinalAutoMLJobObjectiveMetricValue: " + str(best_candidate["FinalAutoMLJobObjectiveMetric"]["Value"]))
    
    
    # Create best model:
    main_model_name = f'{auto_ml_job_name}-M'
    pipeline_model_name = f'{auto_ml_job_name}-P'

    main_model = boto_sagemaker_client.create_model(
        Containers=best_candidate["InferenceContainers"],
        ModelName=main_model_name,
        ExecutionRoleArn=sagemaker_role
    )
    
    print("Model ARN corresponding to the best candidate is : {}".format(main_model["ModelArn"]))
    
    #### IV. MAIN MODEL BATCH TRANSFORM ####
    neptune_expmnt['model/status'] = 'SAGEMAKER_SCORING_MAIN_MODEL'
    
    # Batch inference with batch transform:
    transform_job_name = main_model_name + "-transform" + str(int(time.time()))[-6:]

    test_preds_main_model = run_batch_transform(boto_sagemaker_client=boto_sagemaker_client,
                                     s3_client=s3_client,
                                     transform_job_name=transform_job_name,
                                     model_name=main_model_name,
                                     s3_input_datasets_bucket=s3_input_datasets_bucket,
                                     new_scoring_artifact_uuid=new_scoring_artifact_uuid,
                                     s3_output_bucket_and_file=s3_output_bucket_and_file,
                                     s3_output_file=s3_output_file,
                                     model_type='main_model')
    
    if len(test_dataset_calibration) != len(test_preds_main_model):
        raise ValueError(f'Length of test_dataset_calibration ({len(test_dataset_calibration)}) and test_preds_main_model ({len(test_preds_main_model)}) are not equal as expected')
    
    scores_base_test_calibration = pd.concat([test_dataset_calibration, test_preds_main_model], axis=1)[['user_session_id', 'store_id', 'pred_conv_probability', 'conversion']]

    #### V. CREATING ISOTONIC REGRESSION AND SAGEMAKER MODEL PIPELINE ####
    # apply isotonic regression to calibrate the scores:
    iso_reg = IsotonicRegression(out_of_bounds='clip')
    iso_reg.fit(scores_base_test_calibration['pred_conv_probability'], scores_base_test_calibration['conversion'])

    os.makedirs(f'tmp/{new_scoring_artifact_uuid}/', exist_ok=True)
    
    iso_model_path_no_ext = f'tmp/{new_scoring_artifact_uuid}/isotonic_model'
    
    with open(f'{iso_model_path_no_ext}.pkl', 'wb') as f:
        pickle.dump(iso_reg, f)
    
    with tarfile.open(f'{iso_model_path_no_ext}.tar.gz', mode='w:gz') as tar:
        tar.add(f'{iso_model_path_no_ext}.pkl', arcname=f'isotonic_model.pkl')
    
    s3_client.upload_file(f'{iso_model_path_no_ext}.tar.gz', INPUT_DATASETS_BUCKET_NAME, f'{new_scoring_artifact_uuid}/models/isotonic_model.tar.gz')
    
    #create isotonic model as SageMaker entity:
    isotonic_model = SKLearnModel(
        model_data=f'{s3_input_datasets_bucket}/{new_scoring_artifact_uuid}/models/isotonic_model.tar.gz',
        role=sagemaker_role,
        entry_point='../data_science/isotonic_postprocess.py',
        framework_version='1.2-1',
        sagemaker_session=sagemaker_session
    )
    
    isotonic_container = isotonic_model.prepare_container_def(instance_type='ml.c4.xlarge')
    
    pipeline_model = boto_sagemaker_client.create_model(
        Containers=best_candidate["InferenceContainers"]+[isotonic_container],
        ModelName=pipeline_model_name,
        ExecutionRoleArn=sagemaker_role
    )
    
    #### VI. BATCH TRANSFORM WITH SAGEMAKER MODEL PIPELINE ####
    neptune_expmnt['model/status'] = 'SAGEMAKER_SCORING_MODEL_PIPELINE'
    
    test_preds_pipeline_model = run_batch_transform(boto_sagemaker_client=boto_sagemaker_client,
                                     s3_client=s3_client,
                                     transform_job_name=transform_job_name,
                                     model_name=pipeline_model_name,
                                     s3_input_datasets_bucket=s3_input_datasets_bucket,
                                     new_scoring_artifact_uuid=new_scoring_artifact_uuid,
                                     s3_output_bucket_and_file=s3_output_bucket_and_file,
                                     s3_output_file=s3_output_file,
                                     model_type='pipeline_model',
                                     sagemaker_session=sagemaker_session)
    
    scores_base_test = pd.concat([test_dataset, test_preds_pipeline_model], axis=1)[['user_session_id', 'store_id', 'pred_conv_probability', 'conversion']]
    
    print(f'transform_job_name: {transform_job_name}')
    print(f'Threshold: {find_threshold(scores_base_test, cannibalization_target = 0.15)}')
    print(f'Offer Rate at 15% Cannibalization: {calc_metric(scores_base_test, find_threshold(scores_base_test, cannibalization_target = 0.15), "discount_offer_rate")}')
    print(f'Mean Scores by Actual Conversion: {scores_base_test.groupby("conversion").agg({"pred_conv_probability":"mean"})}')
    
    
    #### VII. INITIAL MODEL CALIBRATION: SETTING STORE-SPECIFIC THRESHOLDS AND WRITING EVERYTHING TO DB ####
    neptune_expmnt['model/status'] = 'SETTING_THRESHOLDS'
    
    thresholds_dict = dict()
    
    with read_replica_engine.connect() as db_read_rep_conn:
        store_df_for_thresholds_query = (
            store.query
            .filter(store.use_in_datascience == True)
        )
        store_df_for_thresholds = (pd.read_sql(store_df_for_thresholds_query.statement, db_read_rep_conn))
    
    for merchant_store_id in store_df_for_thresholds.uuid:
        if test_split_method in ['randomized', 'time_based']:
            scores_base_test_merchant = scores_base_test.query('store_id == @merchant_store_id')
            if len(scores_base_test_merchant.query('conversion')) == 0:
                print(f'No conversions in test set for store_id {merchant_store_id}. Will use default_low_med_threshold and default_med_high_threshold for this store.')
                
                thresholds_dict[merchant_store_id] = [
                    {'intent_only': {'low_med_threshold': default_low_med_threshold, 'med_high_threshold': default_med_high_threshold}},
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    0,
                    True
                ]
            else:
                low_med_threshold = find_threshold(scores_base=scores_base_test_merchant, cannibalization_target=cannibalization_rate_target)
                med_high_threshold = find_threshold(scores_base=scores_base_test_merchant, cannibalization_target=cannibalization_rate_target_antiholdout)
                
                thresholds_dict[merchant_store_id] = [
                    {'intent_only': {'low_med_threshold': low_med_threshold, 'med_high_threshold': med_high_threshold}},
                    calc_metric(scores_base_test_merchant, low_med_threshold, 'cannibalization_rate'),
                    calc_metric(scores_base_test_merchant, low_med_threshold, metric='discount_offer_rate'),
                    cannibalization_rate_target,
                    calc_metric(scores_base_test_merchant, med_high_threshold, 'cannibalization_rate'),
                    calc_metric(scores_base_test_merchant, med_high_threshold, metric='discount_offer_rate'),
                    cannibalization_rate_target_antiholdout,
                    calc_metric(scores_base_test_merchant, low_med_threshold, metric='total_count'), #same for both thresholds
                    True
                ]
    
    new_scoring_layer_uuid = uuid.uuid4()
    thresholds_df_uuids = [uuid.uuid4() for _ in range(len(thresholds_dict))]
    thresholds_df = (pd.DataFrame.from_dict(thresholds_dict, orient='index', columns=['scoring_artifact_thresholds',
                                                                                     'expected_cannibalization_rate_low_med',
                                                                                     'expected_discount_offer_rate_low_med',
                                                                                     'cannibalization_rate_target_low_med',
                                                                                     'expected_cannibalization_rate_med_high',
                                                                                     'expected_discount_offer_rate_med_high',
                                                                                     'cannibalization_rate_target_med_high',
                                                                                     'total_test_set_count',
                                                                                     'active']) 
        .reset_index(names='store_id')
        .assign(uuid = thresholds_df_uuids,
                time = time.time(),
                scoring_layer_id = new_scoring_layer_uuid)
    )
    
    thresholds_csv_buffer = StringIO()
    thresholds_df.to_csv(thresholds_csv_buffer, index=False)
    neptune_expmnt["thresholds/thresholds-csv-buffer"].upload(File.from_stream(thresholds_csv_buffer, extension="csv"))
    
    new_scoring_artifact = scoring_artifact()
    new_scoring_artifact.uuid = new_scoring_artifact_uuid
    new_scoring_artifact.time = time.time()
    new_scoring_artifact.component_type = 'intent'
    new_scoring_artifact.artifact_metadata = {'pipeline_file': filename}
    
    new_scoring_layer = scoring_layer()
    new_scoring_layer.uuid = new_scoring_layer_uuid
    new_scoring_layer.time = time.time()
    new_scoring_layer.active = True
    new_scoring_layer.store_specific = False
    new_scoring_layer.run_predictions = False
    new_scoring_layer.live_decisioning = False
    new_scoring_layer.scoring_algorithm_type = 'intent_only'    
    new_scoring_layer.scoring_time_cutoff = time_cutoff
    new_scoring_layer.scoring_artifacts = {'intent': new_scoring_artifact_uuid}
    
    if os.environ.get("MODEL_DESCRIPTION") not in [None, ""]:
        new_scoring_artifact.description = os.environ["MODEL_DESCRIPTION"]
        new_scoring_layer.description = os.environ["MODEL_DESCRIPTION"]
    
    db.session.add(new_scoring_artifact)
    db.session.add(new_scoring_layer)
    db.session.commit()

    (thresholds_df
     [['uuid', 'time', 'store_id', 'scoring_layer_id', 'active', 'scoring_artifact_thresholds']]
    .to_sql('scoring_layer_thresholds', con=db.engine, if_exists='append', index=False, dtype={"scoring_artifact_thresholds": JSON})
    )
    
    #### VIII. DEPLOYING MODEL PIPELINE ####
    neptune_expmnt['model/status'] = 'SAGEMAKER_DEPLOYING'
    deployment_external_reference = uuid.uuid4()
    
    endpoint_name = f'{deployment_external_reference}-endpoint'
    endpoint_config_name = f'{endpoint_name}-config'
    
    endpoint_config_response = boto_sagemaker_client.create_endpoint_config(
       EndpointConfigName=endpoint_config_name,
       ProductionVariants=[
           {
               "VariantName": "variant1",
               "ModelName": pipeline_model_name,
               "InstanceType": DEPLOYMENT_INSTANCE_TYPE,
               "InitialInstanceCount": 1
           }
       ]
    )
    
    # Creates an endpoint that needs to be disabled when it's done being used
    # AWS -> SageMaker -> Inference (left bar) -> Endpoints
    create_endpoint_response = boto_sagemaker_client.create_endpoint(
        EndpointName=endpoint_name,
        EndpointConfigName=endpoint_config_name
    )
    
    boto_sagemaker_client.add_tags(
        ResourceArn=f'arn:aws:sagemaker:us-east-2:************:endpoint/{endpoint_name}',
        Tags=[
            {"Key": "deployment_external_reference", "Value": deployment_external_reference,
             "Key": "platform_expmnt_id", "Value": platform_expmnt_id}
        ]
    )
    
    print(create_endpoint_response)
    
    timer_current = 0
    while boto_sagemaker_client.describe_endpoint(EndpointName=endpoint_name)["EndpointStatus"] not in ["InService", "Failed", "UpdateRollbackFailed"]:
        print(str(timer_current) + " - " + boto_sagemaker_client.describe_endpoint(EndpointName=endpoint_name)["EndpointStatus"])
        timer_current += 30
        time.sleep(30)

    neptune_expmnt['model/deployment_external_reference'] = deployment_external_reference
    neptune_expmnt['model/status'] = 'SAGEMAKER_DEPLOYED'
    new_scoring_artifact.deployment_external_reference = deployment_external_reference
    db.session.commit()

FILENAME = os.environ["DS_FILENAME"]
TIME_CUTOFF = int(os.environ.get("TIME_CUTOFF", get_arg_default(train_model, 'time_cutoff')))
START_TIME = int(os.environ.get("START_TIME", get_arg_default(train_model, 'start_time')))
END_TIME = int(os.environ.get("END_TIME", get_arg_default(train_model, 'end_time')))
CANNIBALIZATION_RATE_TARGET = float(os.environ.get("CANNIBALIZATION_RATE_TARGET", get_arg_default(train_model, 'cannibalization_rate_target')))
CANNIBALIZATION_RATE_TARGET_ANTIHOLDOUT = float(os.environ.get("CANNIBALIZATION_RATE_TARGET_ANTIHOLDOUT", get_arg_default(train_model, 'cannibalization_rate_target_antiholdout')))
TEST_SPLIT_METHOD = os.environ.get("TEST_SPLIT_METHOD", get_arg_default(train_model, 'test_split_method'))
DS_SPLIT_TIME_CUTOFF = os.environ.get("DS_SPLIT_TIME_CUTOFF", get_arg_default(train_model, 'ds_split_time_cutoff'))
if DS_SPLIT_TIME_CUTOFF not in [None, "None", ""]:
    DS_SPLIT_TIME_CUTOFF = int(DS_SPLIT_TIME_CUTOFF)
DS_SPLIT_TIME_CUTOFF2 = os.environ.get("DS_SPLIT_TIME_CUTOFF2", get_arg_default(train_model, 'ds_split_time_cutoff2'))
if DS_SPLIT_TIME_CUTOFF2 not in [None, "None", ""]:
    DS_SPLIT_TIME_CUTOFF2 = int(DS_SPLIT_TIME_CUTOFF2)
CONFIG_SCORER = os.environ.get("CONFIG_SCORER", get_arg_default(train_model, 'config_scorer'))
DEFAULT_LOW_MED_THRESHOLD = float(os.environ.get("DEFAULT_LOW_MED_THRESHOLD", get_arg_default(train_model, 'default_low_med_threshold')))
DEFAULT_MED_HIGH_THRESHOLD = float(os.environ.get("DEFAULT_MED_HIGH_THRESHOLD", get_arg_default(train_model, 'default_med_high_threshold')))
FEAT_ENG_BATCH_SIZE = int(os.environ.get("FEAT_ENG_BATCH_SIZE", get_arg_default(train_model, 'feat_eng_batch_size')))

# Kill the process if we are running in a container
if "DS_FILENAME" in os.environ:
    with app.app_context():
        train_model(
            filename=FILENAME,
            time_cutoff=TIME_CUTOFF,
            start_time=START_TIME,
            end_time=END_TIME,
            cannibalization_rate_target=CANNIBALIZATION_RATE_TARGET,
            cannibalization_rate_target_antiholdout=CANNIBALIZATION_RATE_TARGET_ANTIHOLDOUT,
            test_split_method=TEST_SPLIT_METHOD,
            ds_split_time_cutoff=DS_SPLIT_TIME_CUTOFF,
            ds_split_time_cutoff2=DS_SPLIT_TIME_CUTOFF2,
            config_scorer=CONFIG_SCORER,
            default_low_med_threshold=DEFAULT_LOW_MED_THRESHOLD,
            default_med_high_threshold=DEFAULT_MED_HIGH_THRESHOLD,
            feat_eng_batch_size=FEAT_ENG_BATCH_SIZE
            )
        db.session.remove()
        sys.exit(0)