import pandas as pd
import numpy as np
import re
import datetime
from user_agents import parse
from ds_constants import JS_RECOGNIZED_BROWSERS_LIST

def assign_true_local_time(df):
    def get_true_local_time(row):
        if row["local_time"] is None:
            return None
        return row["local_time"].tz_convert(row["local_timezone"]).tz_localize(tz=None)
    return df.assign(true_local_time=lambda df_: df_.apply(get_true_local_time, axis=1))

def convert_one_local_tz_to_secs(str_tz):
    if not str_tz:
        return None
    sign = -1 if str_tz[0] == '-' else 1
    hours, minutes = map(int, str_tz[1:].split(':'))
    return sign * (hours * 60 + minutes) * 60

def convert_one_local_tz_to_mins(str_tz):
    if not str_tz:
        return None
    sign = -1 if str_tz[0] == '-' else 1
    hours, minutes = map(int, str_tz[1:].split(':'))
    return sign * (hours * 60 + minutes)

def assign_local_tz_secs(df):
    return df.assign(local_timezone_offset_secs=pd.to_timedelta(df['local_timezone'].apply(convert_one_local_tz_to_secs), unit='s'))

def assign_local_tz_mins(df):
    return (df
            .assign(local_timezone_offset_mins=df['local_timezone'].apply(convert_one_local_tz_to_mins).astype('int16'))
            .drop(columns=['local_timezone'])
            )
            
def extract_mean_device_year(text):
    if not text:
        return None
    year_pattern = r"\b(?<!\d)(19[7-9]\d|20\d{2})(?!\d)\b"
    matches = re.findall(year_pattern, text)
    current_year = datetime.datetime.now().year
    return np.mean([int(device_year) for device_year in matches if int(device_year) <= current_year and int(device_year) >= 1970])

def add_missing_cart_change_types(df, column_names):
    for column_name in column_names:
        if column_name not in df.columns:
            df[column_name] = 0
    return df

def add_any_missing_page_types(df, required_page_types):
    return df.reindex(pd.MultiIndex.from_product([df.index.levels[0], required_page_types], names=df.index.names))

def flatten_cols(df):
    new_cols = ['_'.join(map(str, vals)) for vals in df.columns.to_flat_index()]
    new_df = df.copy()
    new_df.columns = new_cols
    return new_df

def get_urls_domain(url):
    url = (url.replace("http://", "").replace("https://", "")
        .replace("www.", "").replace(".myshopify", "").replace(".com", ""))
    return url.split("/")[0]

def merge_time_since_most_recent_cart_change_of_type(df, cart_df_pre, cart_change_type):
    return df.merge(cart_df_pre
        .loc[cart_df_pre
             .query('cart_change_type == @cart_change_type')
             .groupby('user_session_id')['time_cart'].idxmax(),
             ['user_session_id', 'time_cart']]
        .rename(columns={'time_cart': f'most_recent_time_cart_{cart_change_type}'})
        .astype({f'most_recent_time_cart_{cart_change_type}': 'Int64'}),
        how='left',
        on='user_session_id',
        validate='one_to_one')

def get_browser_name(browser_string):
    if browser_string in JS_RECOGNIZED_BROWSERS_LIST:
        return browser_string
    else:
        user_agent = parse(browser_string)
        return user_agent.browser.family

def remove_top_level_column(df):
            df.columns = df.columns.droplevel()
            return df
        
def check_string_has_email_regex(check_string: str) -> bool:
    pattern = r'^.{1,}@(?=.{4,}$)'
    match = re.search(pattern, check_string)
    return bool(match)
