import os
import sys
sys.path.append(os.getcwd().replace("/web", "").replace("/data_science", "") + "/web")
sys.path.append(os.getcwd().replace("/web", "").replace("/data_science", ""))

try:
    from web.app import app, db
except ModuleNotFoundError:
    from app import app, db

try:
    from web.db import model_version, store, model_store_threshold
except ModuleNotFoundError:
    from db import model_version, store, model_store_threshold

import datetime
from flask import Flask
import h2o_engine_manager
import h2o_authn
from itertools import count

try:
    from data_science.prepare_data import prep_data_for_model
except ModuleNotFoundError:
    from prepare_data import prep_data_for_model

import sys
import time
import uuid
import pandas as pd
import inspect

EXPERIMENT_BASENAME = "ITERATIVE ALLMERCHANTSEQUALWEIGHT"
PROJECT_NAME = "VANDRA PROJECT2"
H2O_ENVIRONMENT_URL = 'https://5457922.dedicated.h2o.ai'
TOKEN_ENDPOINT_URL = "https://auth.5457922.dedicated.h2o.ai/auth/realms/hac/protocol/openid-connect/token"
WEIGHTED_MEAN_DECISION_THRESHOLD_ALLOWABLE_RANGE = (0.001, 0.045)
DELETE_EXPMNTS_WITH_UNSATISFACTORY_THRESHOLDS = False
MAX_RETRAINS_BEFORE_FAILING = 4

def get_arg_default(fn, arg_name):
    args = inspect.signature(fn).parameters
    if arg_name in args:
        return args[arg_name].default
    else:
        raise ValueError(f"Argument '{arg_name}' not found in function signature, or it does not have a default value.")

def train_model(filename,
                time_cutoff=65,
                start_time=int(time.time())-(604800*6),
                end_time=int(time.time()),
                dai_instance_name='vandra-instance-20240502',
                cannibalization_rate_target=0.2,
                cannibalization_rate_target_antiholdout = 0.6,
                test_split_method='no_test',
                ds_split_time_cutoff=None,
                config_accuracy = 8,
                config_time = 8,
                config_interpretability = 1,
                config_scorer = 'F2'):
    global app
    
    #### 0. ARG CHECKS AND INITIALIZE APP ####
    if test_split_method not in ['no_test', 'randomized', 'time_based']:
        raise ValueError('test_split_method must be one of "no_test", "randomized", or "time_based".')
    
    if test_split_method != 'time_based' and isinstance(ds_split_time_cutoff, int):
        raise ValueError('ds_split_time_cutoff should only be specified if test_split_method is "time_based".')
    
    if test_split_method == 'time_based' and not isinstance(ds_split_time_cutoff, int):
        raise ValueError('ds_split_time_cutoff must be specified if test_split_method is "time_based".')
    
    app = Flask(__name__)
    app.config.from_pyfile(os.getcwd().replace("/web", "").replace("/data_science", "") + "/web/config.py")
    if os.environ.get("SQLALCHEMY_DATABASE_URI") not in [None, ""]:
        app.config["SQLALCHEMY_DATABASE_URI"] = os.environ["SQLALCHEMY_DATABASE_URI"]
        app.config["H2O_REFRESH_TOKEN"] = os.environ["H2O_REFRESH_TOKEN"]
        app.config["H2O_DAI_USERNAME"] = os.environ["H2O_DAI_USERNAME"]
        app.config["H2O_ENDPOINT_PASSWORD"] = os.environ["H2O_ENDPOINT_PASSWORD"]
    
    db.init_app(app)
    app.config['SESSION_SQLALCHEMY'] = db
    
    #### I. CONNECT TO H2O ####
    # Setting up the token provider using an existing refresh token
    token_provider = h2o_authn.TokenProvider(
        refresh_token=app.config["H2O_REFRESH_TOKEN"],
        client_id="hac-platform-public",
        token_endpoint_url=TOKEN_ENDPOINT_URL
    )
    
    # Connect to H2O AI Engine Manager
    aiem = h2o_engine_manager.login(
        environment=H2O_ENVIRONMENT_URL,
        token_provider=token_provider
    )
    
    engine = aiem.dai_engine_client.get_engine(
        workspace_id="default",
        engine_id=dai_instance_name
    )
    
    # This return immediately and does not wait for the engine to be ready.
    engine.resume()
    
    # This call blocks until the engine is ready.
    # It may take a few minutes.
    engine.wait()
    
    dai_client = engine.connect()

    #### II. CREATE MODELING DATASET ####
    
    new_model_version = model_version()
    new_model_version.uuid = str(uuid.uuid4())
    new_model_version.time = time.time()
    new_model_version.filename = filename
    new_model_version.decision_threshold = 0.01
    new_model_version.holdout_percent = 0.2
    new_model_version.time_cutoff = time_cutoff
    new_model_version.run_predictions = False
    new_model_version.live_version = False
    new_model_version.store_specific_model = False
    new_model_version.status = 'PYTHON_FEAT_ENG'
    new_model_version.train_start_time = start_time
    new_model_version.train_end_time = end_time
    new_model_version.test_split_method = test_split_method
    
    if os.environ.get("MODEL_DESCRIPTION") not in [None, ""]:
        new_model_version.description = os.environ["MODEL_DESCRIPTION"]
    
    min_user_session_time_processed = new_model_version.time
    new_model_version_uuid = new_model_version.uuid
    
    with app.app_context():
        db.session.add(new_model_version)
        db.session.commit()

        i = 0
        while min_user_session_time_processed > start_time:
            joined_table, min_user_session_time_processed = prep_data_for_model[filename](train_or_predict='TRAIN', predict_session_id=None,
                    time_cutoff=time_cutoff, start_time=start_time, end_time=end_time, latest_time_for_run=int(min_user_session_time_processed))
            
            if len(joined_table) == 0:
                new_model_version.min_user_session_time_processed = int(min_user_session_time_processed)
                db.session.commit()
                continue
                
            ds_iter = dai_client.datasets.create(
                data=joined_table,
                data_source="upload",
                name=f'joined_table_{new_model_version_uuid}_{i}',
            )
            ds_iter_path = '/dai-data/' + ds_iter.file_path
            if i>0:
                ds_cumulative_new = ds_cumulative.modify_by_code(
f'''
# read and validate 2d dataset
X2 = dt.fread("{ds_iter_path}")
if X2.shape[1] != X.shape[1]:
    raise ValueError("Datasets must have equal number of columns")

# bind datasets
X.rbind(X2)

return X
''',
                names = [f"cumulative_dataset_{new_model_version_uuid}"]
                )[f"cumulative_dataset_{new_model_version_uuid}"]
                
                ds_iter.delete()
                ds_cumulative.delete()
                ds_cumulative = ds_cumulative_new
            else:
                ds_cumulative = ds_iter
            i += 1
            
            new_model_version.min_user_session_time_processed = int(min_user_session_time_processed)
            db.session.commit()
        
        ds_cumulative_dd = ds_cumulative.modify_by_code( #drop duplicate user_session_ids created during iteration
'''
from datatable import by, sort
key_cols = ['user_session_id'] # solely determines uniqueness
return X[-1, :, by(*key_cols)] #select last row from each group according to key_cols
''',
        names = [f"cumulative_dataset_{new_model_version_uuid}"]
        )[f"cumulative_dataset_{new_model_version_uuid}"]
        
        ds_cumulative.delete()
        
        #### III. CREATE AND STORE H2O EXPERIMENT ####
        new_model_version.status = 'H2O_TRAINING'
        db.session.commit()
        
        cur_dt = datetime.datetime.now()
        
        if test_split_method == 'no_test':
            train_dataset = ds_cumulative_dd
            test_dataset = None
            
        elif test_split_method == 'randomized':
            ds_split = ds_cumulative_dd.split_to_train_test(train_size=0.8,
                target_column="conversion",
                train_name=f'ds_train_{cur_dt}',
                test_name=f'ds_test_{cur_dt}')
            
            train_dataset = ds_split['train_dataset']
            test_dataset = ds_split['test_dataset']
            
        elif test_split_method == 'time_based':
            ds_split = ds_cumulative_dd.modify_by_code(
f"""
import pandas as pd

# Change date column from DataTable to Pandas
time_pd = X['time'].to_pandas()

dt_train = X[time_pd['time'] < {ds_split_time_cutoff}, :]
dt_test = X[time_pd['time'] >= {ds_split_time_cutoff}, :]

return [dt_train,dt_test]
""",
                names=[f'ds_train_time_split_at_{ds_split_time_cutoff}_on_{cur_dt}', f'ds_test_time_split_at_{ds_split_time_cutoff}_on_{cur_dt}']
            )
            
            train_dataset = ds_split[f'ds_train_time_split_at_{ds_split_time_cutoff}_on_{cur_dt}']
            test_dataset = ds_split[f'ds_test_time_split_at_{ds_split_time_cutoff}_on_{cur_dt}']
            
        # Creating experiment
        experiment_args = {
            'name': f'UPTO1st{time_cutoff}s {EXPERIMENT_BASENAME} (test_split_method={test_split_method}, ds_split_time_cutoff={ds_split_time_cutoff}) Scorer={config_scorer} {config_accuracy}/{config_time}/{config_interpretability} started {datetime.datetime.now()}',
            'train_dataset': train_dataset,
            'test_dataset': test_dataset,
            'target_column': "conversion",
            'weight_column': None,
            'fold_column': None,
            'task': "classification",
            'accuracy': config_accuracy,
            'time': config_time,
            'interpretability': config_interpretability,
            'scorer': config_scorer,
            'enable_gpus': True,
            'seed': 1234,
            'drop_columns': ["time", "user_session_id"],
            'enable_tensorflow': False,
            'imbalance_sampling_method': "off"
        }
        
        expmnt = dai_client.experiments.create_async(**experiment_args) if hasattr(sys, 'ps1') else dai_client.experiments.create(**experiment_args)
        
        #### IV. CALCULATE STORE-SPECIFIC THRESHOLDS ####
        def get_cmatrix_counts(confusion_matrix, c_p, c_a):
            try:
                return confusion_matrix.query(f'conversion_predicted == {c_p} and conversion == {c_a}')['count'].values[0]
            except IndexError:
                return 0
        
        def calc_metric(scores_base, threshold_to_test, metric, verbose = False):
            confusion_matrix_df = (scores_base
                                   .assign(conversion_predicted=lambda df_: df_['conversion.1'] > threshold_to_test)
                                   .astype({'conversion_predicted': 'int'})
                                   .value_counts(['conversion_predicted', 'conversion'])
                                   .reset_index()
                                   .rename(columns={0: 'count'})
                                   )
            
            #positive event is conversion, negative is no conversion
            tp_count = get_cmatrix_counts(confusion_matrix_df, 1, 1)
            tn_count = get_cmatrix_counts(confusion_matrix_df, 0, 0)
            fp_count = get_cmatrix_counts(confusion_matrix_df, 1, 0)
            fn_count = get_cmatrix_counts(confusion_matrix_df, 0, 1)
            
            if verbose:
                print('Threshold:', threshold_to_test)
                print('TP Count:', tp_count)
                print('TN Count:', tn_count)
                print('FP Count:', fp_count)
                print('FN Count:', fn_count)
                print(f'Discount-Offer Rate: {tn_count / (tn_count + fp_count):.1%}')
                print('Cannibalization Rate: ' + f'{fn_count/(fn_count + tp_count):.1%}' if (fn_count + tp_count > 0) else None)
            
            # Note: All metrics only consider sessions that reach a decision point, which is all the model was trained on
            if metric == 'discount_offer_rate':
                return tn_count / (tn_count + fp_count) # Discount Offer Rate: Number of correct decisions to offer discount / Total number who didn't convert and therefore should have been offered discount
            if metric == 'alt_discount_offer_rate':
                return tn_count / (tn_count + fp_count + fn_count + tp_count)  # Alternative/overall Discount Offer Rate: Number of correct decisions to offer discount / Total number of sessions reaching a decision point
            if metric == 'cannibalization_rate':
                return fn_count / (fn_count + tp_count)  # Cannibalization Rate: Number of incorrect decisions to offer discount / Total number who converted and therefore should not have been offered discount
            if metric == 'total_count':
                return fn_count + tp_count + tn_count + fp_count # Total Count: Total number of sessions reaching a decision point
        
        def find_threshold(scores_base, cannibalization_target, threshold_min=0.001, threshold_max=0.6, tolerance=0.000000001):
            while threshold_max - threshold_min > tolerance:
                mid = (threshold_min + threshold_max) / 2
                if calc_metric(scores_base=scores_base, threshold_to_test=mid, metric='cannibalization_rate') > cannibalization_target:
                    threshold_max = mid
                else:
                    threshold_min = mid
            return threshold_min
        
        i = 0
        thresholds_are_satisfactory = False
        
        while not thresholds_are_satisfactory:
            if i > MAX_RETRAINS_BEFORE_FAILING:
                raise RuntimeError(f'Maximum number of retrainings ({MAX_RETRAINS_BEFORE_FAILING}) reached. Will not attempt to retrain again.')
            
            if i > 0:
                expmnt_old = expmnt
                expmnt = expmnt_old.retrain_async() if hasattr(sys, 'ps1') else expmnt_old.retrain()
                if DELETE_EXPMNTS_WITH_UNSATISFACTORY_THRESHOLDS:
                    expmnt_old.delete()
            
            train_preds_filepath = expmnt.artifacts.download(only='train_predictions', overwrite=True)
            train_dataset_filepath = expmnt.datasets['train_dataset'].download(overwrite=True)
            scores_base_train = pd.concat([pd.read_csv(train_preds_filepath['train_predictions']), pd.read_csv(train_dataset_filepath)[['conversion', 'store_id']]], axis=1)
            
            if test_split_method == 'randomized':
                test_preds_filepath = expmnt.artifacts.download(only='test_predictions', overwrite=True)
                test_dataset_filepath = expmnt.datasets['test_dataset'].download(overwrite=True)
                scores_base_test = pd.concat([pd.read_csv(test_preds_filepath['test_predictions']), pd.read_csv(test_dataset_filepath)[['conversion', 'store_id']]], axis=1)
            
            thresholds_dict = dict()
            
            store_df_for_thresholds = pd.DataFrame(
                db.session
                .query(store.uuid)
                .filter(store.use_in_datascience == True)
                .all()
            )
            
            for merchant_store_id in store_df_for_thresholds.uuid:
                scores_base_train = pd.concat([pd.read_csv(train_preds_filepath['train_predictions']), pd.read_csv(train_dataset_filepath)[['conversion', 'store_id']]], axis=1).query('store_id == @merchant_store_id')
                if len(scores_base_train) == 0:
                    print(f'No data in training set for store_id {merchant_store_id}. Will use model_version.decision_threshold default for this store.')
                    continue
                elif len(scores_base_train.query('conversion == 1')) == 0:
                    print(f'No conversions in training set for store_id {merchant_store_id}. Will use model_version.decision_threshold default for this store.')
                    continue
                
                if test_split_method == 'randomized':
                    scores_base_test = pd.concat([pd.read_csv(test_preds_filepath['test_predictions']), pd.read_csv(test_dataset_filepath)[['conversion', 'store_id']]], axis=1).query('store_id == @merchant_store_id')
                    if len(scores_base_test.query('conversion == 1')) == 0:
                        print(f'No conversions in test set for store_id {merchant_store_id}. Will use model_version.decision_threshold default for this store.')
                        continue
                
                threshold_train = find_threshold(scores_base=scores_base_train, cannibalization_target=cannibalization_rate_target)
                threshold_train_antiholdout = find_threshold(scores_base=scores_base_train, cannibalization_target=cannibalization_rate_target_antiholdout)
                
                if test_split_method in ['no_test', 'time_based']:
                    thresholds_dict[merchant_store_id, 'low-intent'] = [None, threshold_train, calc_metric(scores_base_train, threshold_train, 'cannibalization_rate'), calc_metric(scores_base_train, threshold_train, metric='discount_offer_rate'), calc_metric(scores_base_train, threshold_train, metric='total_count'), cannibalization_rate_target, True, 'low-intent']
                    thresholds_dict[merchant_store_id, 'anti-holdout'] = [None, threshold_train_antiholdout, calc_metric(scores_base_train, threshold_train_antiholdout, 'cannibalization_rate'), calc_metric(scores_base_train, threshold_train_antiholdout, metric='discount_offer_rate'), calc_metric(scores_base_train, threshold_train_antiholdout, metric='total_count'), cannibalization_rate_target_antiholdout, True, 'anti-holdout']
                
                elif test_split_method == 'randomized':
                    threshold_test = find_threshold(scores_base=scores_base_test, cannibalization_target=cannibalization_rate_target)
                    threshold_test_antiholdout = find_threshold(scores_base=scores_base_test, cannibalization_target=cannibalization_rate_target_antiholdout)
                
                    if threshold_train < threshold_test:
                        more_restrictive = 'train'
                        decision_threshold = threshold_train
                    elif threshold_train > threshold_test:
                        more_restrictive = 'test'
                        decision_threshold = threshold_test
                    else:
                        more_restrictive = 'tie'
                        decision_threshold = threshold_train
                        
                    if threshold_train_antiholdout < threshold_test_antiholdout:
                        more_restrictive_antiholdout = 'train'
                        decision_threshold_antiholdout = threshold_train_antiholdout
                    elif threshold_train_antiholdout > threshold_test_antiholdout:
                        more_restrictive_antiholdout = 'test'
                        decision_threshold_antiholdout = threshold_test_antiholdout
                    else:
                        more_restrictive_antiholdout = 'tie'
                        decision_threshold_antiholdout = threshold_train_antiholdout
                    
                    thresholds_dict[merchant_store_id, 'low-intent'] = [more_restrictive, decision_threshold, calc_metric(scores_base_test, decision_threshold, 'cannibalization_rate'), calc_metric(scores_base_test, decision_threshold, metric='discount_offer_rate'), calc_metric(scores_base_test, decision_threshold, metric='total_count'), cannibalization_rate_target, True, 'low-intent']
                    thresholds_dict[merchant_store_id, 'anti-holdout'] = [more_restrictive_antiholdout, decision_threshold_antiholdout, calc_metric(scores_base_test, decision_threshold_antiholdout, 'cannibalization_rate'), calc_metric(scores_base_test, decision_threshold_antiholdout, metric='discount_offer_rate'), calc_metric(scores_base_test, decision_threshold_antiholdout, metric='total_count'), cannibalization_rate_target_antiholdout, True, 'anti-holdout']
            
            result_table_uuids = [uuid.uuid4() for _ in range(len(thresholds_dict))]
            result_table = (pd.DataFrame.from_dict(thresholds_dict, orient='index', columns=['more_restrictive_set', 'decision_threshold', 'expected_cannibalization_rate', 'expected_discount_offer_rate', 'total_test_set_count', 'cannibalization_rate_target', 'active', 'threshold_type'])
                            
                            .reset_index()  # Convert index to columns
                            .assign(index=lambda x: x['index'].apply(lambda x: x[0]))  # Extract the first element of the tuple as a new column
                            .set_index('index')  # Set the new column as the index
                            .rename_axis(None)

                            .assign(uuid = result_table_uuids,
                                    time=time.time(),
                                    model_version_id=new_model_version_uuid,
                                    test_more_restrictive = lambda df_: df_['more_restrictive_set'] == 'test' if test_split_method == 'randomized' else None)
                            .drop(columns=['more_restrictive_set'])
                            )
            
            weighted_mean_decision_threshold = (result_table
                                                .query('threshold_type == "low-intent"')
                                                .assign(weight=lambda df_: df_['total_test_set_count'] / df_['total_test_set_count'].sum())
                                                .assign(weighted_decision_threshold=lambda df_: df_['weight'] * df_['decision_threshold'])
                                                ['weighted_decision_threshold']
                                                .sum()
                                                )
            
            if weighted_mean_decision_threshold < WEIGHTED_MEAN_DECISION_THRESHOLD_ALLOWABLE_RANGE[0]:
                print(f'Weighted mean decision threshold of {weighted_mean_decision_threshold} is less than the minimum allowable threshold of {WEIGHTED_MEAN_DECISION_THRESHOLD_ALLOWABLE_RANGE[0]}. Will retrain model with the same data sets and recalculate thresholds.')
                thresholds_are_satisfactory = False
            elif weighted_mean_decision_threshold > WEIGHTED_MEAN_DECISION_THRESHOLD_ALLOWABLE_RANGE[1]:
                print(f'Weighted mean decision threshold of {weighted_mean_decision_threshold} is greater than the maximum allowable threshold of {WEIGHTED_MEAN_DECISION_THRESHOLD_ALLOWABLE_RANGE[1]}. Will retrain model with the same data sets and recalculate thresholds.')
                thresholds_are_satisfactory = False
            else:
                print(f'Weighted mean decision threshold of {weighted_mean_decision_threshold} is within the allowable range of {WEIGHTED_MEAN_DECISION_THRESHOLD_ALLOWABLE_RANGE}.')
                thresholds_are_satisfactory = True
                break
            
            i += 1
        
        result_table.to_sql('model_store_threshold', con=db.engine, if_exists='append', index=True, index_label='store_id')

        #### V. BUILD MOJO SCORING PIPELINE AND LINK H2O MODEL ####

        #TODO: BUILD MOJO SCORING PIPELINE
        new_model_version.h2o_experiment_id = expmnt.key
        new_model_version.status = 'H2O_LINKING_MODEL'
        db.session.commit()
        
        # Finding or creating a project in DAI.
        prj_dict = {prj.name: prj.key for prj in dai_client.projects.list()}
        if PROJECT_NAME in prj_dict.keys():
            prj = dai_client.projects.get(prj_dict[PROJECT_NAME])
            if len(prj.experiments) >= 100:  # currently, H2O limits projects to 100 experiments each
                for i in count(1):
                    if (PROJECT_NAME + ' ' + str(i)) not in prj_dict.keys():
                        prj = dai_client.projects.create(PROJECT_NAME + ' ' + str(i))
                        break
                    
                    prj = dai_client.projects.get(prj_dict[PROJECT_NAME + ' ' + str(i)])
                    
                    if len(prj.experiments) < 100:  # currently, H2O limits projects to 100 experiments each
                        break
        
        else:
            prj = dai_client.projects.create(PROJECT_NAME)
        
        # Linking the dataset to the project.
        prj.link_dataset(train_dataset, dataset_type="train_dataset")
        if test_split_method == 'randomized':
            prj.link_dataset(test_dataset, dataset_type="test_dataset")
        
        # Linking the experiment to the project.
        prj.link_experiment(expmnt)
        
        new_model_version.deployment_id = uuid.uuid4()
        new_model_version.status = 'READY_TO_DEPLOY'
        db.session.commit()

FILENAME = os.environ["DS_FILENAME"]
TIME_CUTOFF = int(os.environ.get("TIME_CUTOFF", get_arg_default(train_model, 'time_cutoff')))
START_TIME = int(os.environ.get("START_TIME", get_arg_default(train_model, 'start_time')))
END_TIME = int(os.environ.get("END_TIME", get_arg_default(train_model, 'end_time')))
DAI_INSTANCE_NAME = os.environ.get("DAI_INSTANCE_NAME", get_arg_default(train_model, 'dai_instance_name'))
CANNIBALIZATION_RATE_TARGET = float(os.environ.get("CANNIBALIZATION_RATE_TARGET", get_arg_default(train_model, 'cannibalization_rate_target')))
CANNIBALIZATION_RATE_TARGET_ANTIHOLDOUT = float(os.environ.get("CANNIBALIZATION_RATE_TARGET_ANTIHOLDOUT", get_arg_default(train_model, 'cannibalization_rate_target_antiholdout')))
TEST_SPLIT_METHOD = os.environ.get("TEST_SPLIT_METHOD", get_arg_default(train_model, 'test_split_method'))
DS_SPLIT_TIME_CUTOFF = os.environ.get("DS_SPLIT_TIME_CUTOFF", get_arg_default(train_model, 'ds_split_time_cutoff'))
if DS_SPLIT_TIME_CUTOFF not in [None, "None", ""]:
    DS_SPLIT_TIME_CUTOFF = int(DS_SPLIT_TIME_CUTOFF)
CONFIG_ACCURACY = int(os.environ.get("CONFIG_ACCURACY", get_arg_default(train_model, 'config_accuracy')))
CONFIG_TIME = int(os.environ.get("CONFIG_TIME", get_arg_default(train_model, 'config_time')))
CONFIG_INTERPRETABILITY = int(os.environ.get("CONFIG_INTERPRETABILITY", get_arg_default(train_model, 'config_interpretability')))
CONFIG_SCORER = os.environ.get("CONFIG_SCORER", get_arg_default(train_model, 'config_scorer'))


# Kill the process if we are running in a container
if "DS_FILENAME" in os.environ:
    train_model(
        filename=FILENAME,
        time_cutoff=TIME_CUTOFF,
        start_time=START_TIME,
        end_time=END_TIME,
        dai_instance_name=DAI_INSTANCE_NAME,
        cannibalization_rate_target=CANNIBALIZATION_RATE_TARGET,
        cannibalization_rate_target_antiholdout=CANNIBALIZATION_RATE_TARGET_ANTIHOLDOUT,
        test_split_method=TEST_SPLIT_METHOD,
        ds_split_time_cutoff=DS_SPLIT_TIME_CUTOFF,
        config_accuracy=CONFIG_ACCURACY,
        config_time=CONFIG_TIME,
        config_interpretability=CONFIG_INTERPRETABILITY,
        config_scorer=CONFIG_SCORER
        )
    sys.exit(0)