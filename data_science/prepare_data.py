import importlib
import os
import sys
sys.path.append(os.getcwd().replace("/web", ""))

prep_data_for_model = dict()

for module in os.listdir(os.getcwd().replace("/web", "").replace("/data_science", "") + "/data_science/models"):
    if module == '__init__.py' or module[-3:] != '.py':
        continue
    try:
        importlib.import_module('data_science.models.' + module[:-3])
        prep_data_for_model[module[:-3]] = getattr(sys.modules['data_science.models.' + module[:-3]], 'prep_data_for_model')
    except ModuleNotFoundError as _:
        importlib.import_module('models.' + module[:-3])
        prep_data_for_model[module[:-3]] = getattr(sys.modules['models.' + module[:-3]], 'prep_data_for_model')