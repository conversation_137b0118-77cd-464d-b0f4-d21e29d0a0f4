asn1crypto==1.5.1
bcrypt==4.0.1
cachelib==0.9.0
certifi==2022.9.24
cffi==1.15.1
charset-normalizer==2.1.1
click==8.1.3
cryptography==38.0.3
filelock==3.8.0
Flask==2.2.2
idna==3.4
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.1
oscrypto==1.3.0
pyactiveresource==2.2.2
pycparser==2.21
pycryptodomex==3.15.0
PyJWT==2.6.0
pyOpenSSL==22.1.0
pytz==2022.6
PyYAML==6.0
requests==2.28.1
ShopifyAPI==12.4.0
six==1.16.0
Flask-SQLAlchemy==3.0.3
typing_extensions==4.4.0
urllib3==1.26.12
Werkzeug==2.2.2
Flask-Migrate
psycopg2-binary
boto3
argparse
numpy==1.26.4
pandas==2.2.0
user_agents==2.2.0
SQLAlchemy==2.0.20
sentry-sdk[flask]
uWSGI==2.0.21
python-dotenv
rq>=1.15.1
datatable==1.1.0
python-dotenv
flask-cors
gunicorn
trycourier==6.1.0
gevent
awslambdaric #needed for aws lambda functions
sqlalchemy-json
ffmpeg-python
scikit-learn