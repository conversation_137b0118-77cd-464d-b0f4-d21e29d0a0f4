# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "7156e8adadd3171f0b21b994e2dd1a1a"
name = "Vandra Orange"
handle = "vandra-orange"
application_url = "https://vandra-merchant-orange-7f0e2a8e935e.herokuapp.com/dashboard"
embedded = true

[build]
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_checkouts,read_discounts,read_orders,read_price_rules,read_products,read_themes,write_checkouts,write_discounts,write_orders,write_price_rules,write_products"

[auth]
redirect_urls = [ ]

[webhooks]
api_version = "2022-07"

  [[webhooks.subscriptions]]
  uri = "https://staging-orange.vandra.ai/shop_delete_request"
  compliance_topics = [ "shop/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://staging-orange.vandra.ai/user_data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "https://staging-orange.vandra.ai/user_delete_request"
  compliance_topics = [ "customers/redact" ]
